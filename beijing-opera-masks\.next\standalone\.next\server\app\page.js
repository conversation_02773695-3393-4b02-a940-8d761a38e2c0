(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},1204:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\2-AIprogram\\3-Augment\\1-test1\\beijing-opera-masks\\src\\app\\page.tsx","default")},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:a=>{"use strict";a.exports=require("path")},4786:(a,b,c)=>{Promise.resolve().then(c.bind(c,1204))},5798:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,1204)),"E:\\2-AIprogram\\3-Augment\\1-test1\\beijing-opera-masks\\src\\app\\page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,4431)),"E:\\2-AIprogram\\3-Augment\\1-test1\\beijing-opera-masks\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["E:\\2-AIprogram\\3-Augment\\1-test1\\beijing-opera-masks\\src\\app\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},7215:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>n});var d=c(687),e=c(3210),f=c(6189),g=c(883),h=c(7225);function i({mask:a,width:b=300,height:c=300,className:e}){let f={width:b,height:c,viewBox:"0 0 300 300",className:e};switch(a.id){case"guanyu":return(0,d.jsxs)("svg",{...f,children:[(0,d.jsx)("defs",{children:(0,d.jsxs)("radialGradient",{id:"redFace",cx:"50%",cy:"40%",r:"60%",children:[(0,d.jsx)("stop",{offset:"0%",stopColor:"#FF4444"}),(0,d.jsx)("stop",{offset:"100%",stopColor:"#CC0000"})]})}),(0,d.jsx)("ellipse",{cx:"150",cy:"150",rx:"120",ry:"140",fill:"url(#redFace)",stroke:"#8B0000",strokeWidth:"3"}),(0,d.jsx)("path",{d:"M 80 100 Q 120 80 160 100",stroke:"#000",strokeWidth:"8",fill:"none",strokeLinecap:"round"}),(0,d.jsx)("path",{d:"M 140 100 Q 180 80 220 100",stroke:"#000",strokeWidth:"8",fill:"none",strokeLinecap:"round"}),(0,d.jsx)("ellipse",{cx:"110",cy:"130",rx:"20",ry:"15",fill:"white",stroke:"#000",strokeWidth:"2"}),(0,d.jsx)("ellipse",{cx:"190",cy:"130",rx:"20",ry:"15",fill:"white",stroke:"#000",strokeWidth:"2"}),(0,d.jsx)("circle",{cx:"110",cy:"130",r:"8",fill:"#000"}),(0,d.jsx)("circle",{cx:"190",cy:"130",r:"8",fill:"#000"}),(0,d.jsx)("path",{d:"M 150 150 L 150 180 M 140 185 L 160 185",stroke:"#000",strokeWidth:"4",strokeLinecap:"round"}),(0,d.jsx)("ellipse",{cx:"150",cy:"210",rx:"15",ry:"8",fill:"#8B0000",stroke:"#000",strokeWidth:"2"}),(0,d.jsx)("path",{d:"M 100 220 Q 150 240 200 220",stroke:"#000",strokeWidth:"3",fill:"none"}),(0,d.jsx)("path",{d:"M 90 230 Q 150 250 210 230",stroke:"#000",strokeWidth:"3",fill:"none"}),(0,d.jsx)("path",{d:"M 150 80 L 160 100 L 140 100 Z",fill:"#FFD700",stroke:"#000",strokeWidth:"2"})]});case"caocao":return(0,d.jsxs)("svg",{...f,children:[(0,d.jsx)("defs",{children:(0,d.jsxs)("radialGradient",{id:"whiteFace",cx:"50%",cy:"40%",r:"60%",children:[(0,d.jsx)("stop",{offset:"0%",stopColor:"#FFFFFF"}),(0,d.jsx)("stop",{offset:"100%",stopColor:"#F0F0F0"})]})}),(0,d.jsx)("ellipse",{cx:"150",cy:"150",rx:"120",ry:"140",fill:"url(#whiteFace)",stroke:"#CCC",strokeWidth:"3"}),(0,d.jsx)("path",{d:"M 70 90 Q 120 70 170 90",stroke:"#000",strokeWidth:"10",fill:"none"}),(0,d.jsx)("path",{d:"M 130 90 Q 180 70 230 90",stroke:"#000",strokeWidth:"10",fill:"none"}),(0,d.jsx)("ellipse",{cx:"110",cy:"130",rx:"18",ry:"12",fill:"#000"}),(0,d.jsx)("ellipse",{cx:"190",cy:"130",rx:"18",ry:"12",fill:"#000"}),(0,d.jsx)("circle",{cx:"110",cy:"130",r:"6",fill:"white"}),(0,d.jsx)("circle",{cx:"190",cy:"130",r:"6",fill:"white"}),(0,d.jsx)("path",{d:"M 150 150 L 150 180",stroke:"#000",strokeWidth:"3"}),(0,d.jsx)("path",{d:"M 130 200 Q 150 210 170 200",stroke:"#000",strokeWidth:"4",fill:"none"}),(0,d.jsx)("path",{d:"M 120 170 Q 140 175 160 170",stroke:"#666",strokeWidth:"2",fill:"none"}),(0,d.jsx)("path",{d:"M 140 170 Q 160 175 180 170",stroke:"#666",strokeWidth:"2",fill:"none"})]});case"zhangfei":return(0,d.jsxs)("svg",{...f,children:[(0,d.jsx)("defs",{children:(0,d.jsxs)("radialGradient",{id:"blackFace",cx:"50%",cy:"40%",r:"60%",children:[(0,d.jsx)("stop",{offset:"0%",stopColor:"#333333"}),(0,d.jsx)("stop",{offset:"100%",stopColor:"#000000"})]})}),(0,d.jsx)("ellipse",{cx:"150",cy:"150",rx:"120",ry:"140",fill:"url(#blackFace)",stroke:"#000",strokeWidth:"3"}),(0,d.jsx)("path",{d:"M 80 100 Q 120 80 160 100",stroke:"white",strokeWidth:"6",fill:"none"}),(0,d.jsx)("path",{d:"M 140 100 Q 180 80 220 100",stroke:"white",strokeWidth:"6",fill:"none"}),(0,d.jsx)("ellipse",{cx:"110",cy:"130",rx:"20",ry:"15",fill:"white"}),(0,d.jsx)("ellipse",{cx:"190",cy:"130",rx:"20",ry:"15",fill:"white"}),(0,d.jsx)("circle",{cx:"110",cy:"130",r:"8",fill:"#000"}),(0,d.jsx)("circle",{cx:"190",cy:"130",r:"8",fill:"#000"}),(0,d.jsx)("path",{d:"M 150 150 L 150 180",stroke:"white",strokeWidth:"4"}),(0,d.jsx)("ellipse",{cx:"150",cy:"210",rx:"18",ry:"10",fill:"#FF0000",stroke:"white",strokeWidth:"2"}),(0,d.jsx)("path",{d:"M 100 220 Q 150 240 200 220",stroke:"white",strokeWidth:"4",fill:"none"}),(0,d.jsx)("path",{d:"M 90 235 Q 150 255 210 235",stroke:"white",strokeWidth:"4",fill:"none"}),(0,d.jsx)("circle",{cx:"150",cy:"90",r:"8",fill:"white"})]});case"huangzhong":return(0,d.jsxs)("svg",{...f,children:[(0,d.jsx)("defs",{children:(0,d.jsxs)("radialGradient",{id:"yellowFace",cx:"50%",cy:"40%",r:"60%",children:[(0,d.jsx)("stop",{offset:"0%",stopColor:"#FFD700"}),(0,d.jsx)("stop",{offset:"100%",stopColor:"#DAA520"})]})}),(0,d.jsx)("ellipse",{cx:"150",cy:"150",rx:"120",ry:"140",fill:"url(#yellowFace)",stroke:"#B8860B",strokeWidth:"3"}),(0,d.jsx)("path",{d:"M 85 105 Q 120 85 155 105",stroke:"#8B4513",strokeWidth:"6",fill:"none"}),(0,d.jsx)("path",{d:"M 145 105 Q 180 85 215 105",stroke:"#8B4513",strokeWidth:"6",fill:"none"}),(0,d.jsx)("ellipse",{cx:"115",cy:"135",rx:"18",ry:"12",fill:"white",stroke:"#8B4513",strokeWidth:"2"}),(0,d.jsx)("ellipse",{cx:"185",cy:"135",rx:"18",ry:"12",fill:"white",stroke:"#8B4513",strokeWidth:"2"}),(0,d.jsx)("circle",{cx:"115",cy:"135",r:"7",fill:"#8B4513"}),(0,d.jsx)("circle",{cx:"185",cy:"135",r:"7",fill:"#8B4513"}),(0,d.jsx)("path",{d:"M 150 155 L 150 180",stroke:"#8B4513",strokeWidth:"3"}),(0,d.jsx)("ellipse",{cx:"150",cy:"205",rx:"12",ry:"6",fill:"#8B4513"}),(0,d.jsx)("path",{d:"M 110 225 Q 150 240 190 225",stroke:"#8B4513",strokeWidth:"3",fill:"none"}),(0,d.jsx)("path",{d:"M 100 160 Q 120 165 140 160",stroke:"#B8860B",strokeWidth:"2",fill:"none"}),(0,d.jsx)("path",{d:"M 160 160 Q 180 165 200 160",stroke:"#B8860B",strokeWidth:"2",fill:"none"})]});case"diaochan":return(0,d.jsxs)("svg",{...f,children:[(0,d.jsx)("defs",{children:(0,d.jsxs)("radialGradient",{id:"pinkFace",cx:"50%",cy:"40%",r:"60%",children:[(0,d.jsx)("stop",{offset:"0%",stopColor:"#FFE4E1"}),(0,d.jsx)("stop",{offset:"100%",stopColor:"#FFC0CB"})]})}),(0,d.jsx)("ellipse",{cx:"150",cy:"150",rx:"115",ry:"135",fill:"url(#pinkFace)",stroke:"#FF69B4",strokeWidth:"2"}),(0,d.jsx)("path",{d:"M 95 110 Q 125 100 155 110",stroke:"#8B4513",strokeWidth:"3",fill:"none"}),(0,d.jsx)("path",{d:"M 145 110 Q 175 100 205 110",stroke:"#8B4513",strokeWidth:"3",fill:"none"}),(0,d.jsx)("ellipse",{cx:"120",cy:"135",rx:"15",ry:"10",fill:"white",stroke:"#8B4513",strokeWidth:"1"}),(0,d.jsx)("ellipse",{cx:"180",cy:"135",rx:"15",ry:"10",fill:"white",stroke:"#8B4513",strokeWidth:"1"}),(0,d.jsx)("circle",{cx:"120",cy:"135",r:"5",fill:"#8B4513"}),(0,d.jsx)("circle",{cx:"180",cy:"135",r:"5",fill:"#8B4513"}),(0,d.jsx)("path",{d:"M 110 130 L 105 125",stroke:"#8B4513",strokeWidth:"1"}),(0,d.jsx)("path",{d:"M 130 130 L 135 125",stroke:"#8B4513",strokeWidth:"1"}),(0,d.jsx)("path",{d:"M 170 130 L 165 125",stroke:"#8B4513",strokeWidth:"1"}),(0,d.jsx)("path",{d:"M 190 130 L 195 125",stroke:"#8B4513",strokeWidth:"1"}),(0,d.jsx)("path",{d:"M 150 155 L 150 170",stroke:"#FF69B4",strokeWidth:"2"}),(0,d.jsx)("ellipse",{cx:"150",cy:"190",rx:"8",ry:"5",fill:"#FF1493"}),(0,d.jsx)("path",{d:"M 150 85 L 155 95 L 150 100 L 145 95 Z",fill:"#FF1493"}),(0,d.jsx)("ellipse",{cx:"100",cy:"160",rx:"12",ry:"8",fill:"#FF69B4",opacity:"0.3"}),(0,d.jsx)("ellipse",{cx:"200",cy:"160",rx:"12",ry:"8",fill:"#FF69B4",opacity:"0.3"})]});default:return(0,d.jsxs)("svg",{...f,children:[(0,d.jsx)("defs",{children:(0,d.jsxs)("radialGradient",{id:"defaultFace",cx:"50%",cy:"40%",r:"60%",children:[(0,d.jsx)("stop",{offset:"0%",stopColor:a.mainColors[0]}),(0,d.jsx)("stop",{offset:"100%",stopColor:a.mainColors[1]||a.mainColors[0]})]})}),(0,d.jsx)("ellipse",{cx:"150",cy:"150",rx:"120",ry:"140",fill:"url(#defaultFace)",stroke:"#333",strokeWidth:"3"}),(0,d.jsx)("path",{d:"M 90 110 Q 120 90 150 110",stroke:"#000",strokeWidth:"4",fill:"none"}),(0,d.jsx)("path",{d:"M 150 110 Q 180 90 210 110",stroke:"#000",strokeWidth:"4",fill:"none"}),(0,d.jsx)("ellipse",{cx:"120",cy:"140",rx:"15",ry:"10",fill:"white",stroke:"#000",strokeWidth:"2"}),(0,d.jsx)("ellipse",{cx:"180",cy:"140",rx:"15",ry:"10",fill:"white",stroke:"#000",strokeWidth:"2"}),(0,d.jsx)("circle",{cx:"120",cy:"140",r:"6",fill:"#000"}),(0,d.jsx)("circle",{cx:"180",cy:"140",r:"6",fill:"#000"}),(0,d.jsx)("path",{d:"M 150 160 L 150 180",stroke:"#000",strokeWidth:"3"}),(0,d.jsx)("ellipse",{cx:"150",cy:"200",rx:"10",ry:"6",fill:"#000"})]})}}var j=c(8036),k=c(284);function l({className:a,style:b}){let{theme:c,toggleTheme:e,colors:f}=(0,k.W4)();return(0,d.jsx)("button",{onClick:e,className:a,style:{backgroundColor:"transparent",border:`2px solid ${f.border}`,borderRadius:"0.5rem",padding:"0.5rem",cursor:"pointer",display:"flex",alignItems:"center",justifyContent:"center",transition:"all 0.3s ease",color:f.textPrimary,...b},onMouseEnter:a=>{a.currentTarget.style.borderColor=f.primary,a.currentTarget.style.backgroundColor=f.backgroundTertiary},onMouseLeave:a=>{a.currentTarget.style.borderColor=f.border,a.currentTarget.style.backgroundColor="transparent"},title:"light"===c?"切换到深色模式":"切换到浅色模式",children:"light"===c?(0,d.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,d.jsx)("path",{d:"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"})}):(0,d.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,d.jsx)("circle",{cx:"12",cy:"12",r:"5"}),(0,d.jsx)("path",{d:"M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"})]})})}var m=c(1465);function n(){let[a,b]=(0,e.useState)(null),c=(0,f.useRouter)(),{colors:k,styles:n}=(0,m.D)(),{filter:o,updateFilter:p,addToRecentlyViewed:q,toggleFavorite:r,isFavorite:s,getRecentlyViewedMasks:t}=(0,h.A)(),u=(0,e.useMemo)(()=>{var a;return a=g.hu,a.filter(a=>{if(o.roleCategory&&a.roleCategory!==o.roleCategory||o.colorCategory&&a.colorCategory!==o.colorCategory||o.difficulty&&a.difficulty!==o.difficulty)return!1;if(o.searchTerm){let b=o.searchTerm.toLowerCase();if(![a.name,a.character,a.culturalBackground.origin,a.culturalBackground.personality,...a.tags].join(" ").toLowerCase().includes(b))return!1}return!0})},[o]);t(g.hu);let v=()=>{b(null)};return(0,d.jsxs)("div",{style:{minHeight:"100vh",backgroundColor:"#F9FAFB",fontFamily:'"Noto Sans SC", sans-serif'},children:[(0,d.jsx)("header",{style:{...n.navigation,position:"sticky",top:0,zIndex:40},children:(0,d.jsxs)("div",{style:{maxWidth:"1200px",margin:"0 auto",padding:"1rem 1.5rem",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,d.jsx)("h1",{style:{...j._R.pageTitle,color:k.textPrimary},children:"京剧脸谱文化展示平台"}),(0,d.jsx)(l,{})]})}),(0,d.jsxs)("main",{style:{padding:"2rem 1.5rem",backgroundColor:k.background,minHeight:"100vh"},children:[(0,d.jsxs)("section",{style:{textAlign:"center",padding:"3rem 0",background:`linear-gradient(135deg, ${k.primary}10 0%, transparent 50%, ${k.secondary}10 100%)`,borderRadius:"0.75rem",marginBottom:"3rem",border:`1px solid ${k.border}`},children:[(0,d.jsx)(j.OB,{style:{marginBottom:"1rem"},children:"探索京剧脸谱的艺术魅力"}),(0,d.jsx)(j.a3,{style:{fontSize:"1.125rem",maxWidth:"32rem",margin:"0 auto 2rem"},children:"深入了解中国传统戏曲文化，感受脸谱艺术的独特魅力与深厚内涵"})]}),(0,d.jsx)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(280px, 1fr))",gap:"2rem",maxWidth:"1200px",margin:"0 auto"},children:u.map(a=>(0,d.jsxs)("div",{onClick:()=>{b(a)},style:{...n.card,borderRadius:"0.75rem",border:`2px solid ${k.secondary}`,overflow:"hidden",cursor:"pointer",transition:"all 0.3s ease",position:"relative"},onMouseEnter:a=>{a.currentTarget.style.transform="scale(1.05)",a.currentTarget.style.boxShadow=`0 20px 25px -5px ${k.shadowHover}`},onMouseLeave:a=>{a.currentTarget.style.transform="scale(1)",a.currentTarget.style.boxShadow=`0 10px 15px -3px ${k.shadow}`},children:[(0,d.jsxs)("div",{style:{aspectRatio:"1",position:"relative",backgroundColor:"white",display:"flex",alignItems:"center",justifyContent:"center"},children:[(0,d.jsx)(i,{mask:a,width:280,height:280}),(0,d.jsx)("div",{style:{position:"absolute",bottom:0,left:0,right:0,background:"linear-gradient(to top, rgba(0,0,0,0.8) 0%, transparent 100%)",color:"white",padding:"2rem 1rem 0.5rem",fontSize:"1.25rem",fontWeight:"bold",textAlign:"center",fontFamily:'"Noto Serif SC", serif'},children:a.character}),(0,d.jsx)("div",{style:{position:"absolute",top:"0.75rem",left:"0.75rem",backgroundColor:"rgba(0,0,0,0.8)",color:"white",padding:"0.25rem 0.5rem",borderRadius:"9999px",fontSize:"0.75rem",fontWeight:"500"},children:a.roleCategory}),(0,d.jsx)("div",{style:{position:"absolute",top:"0.75rem",right:"0.75rem",backgroundColor:"rgba(0,0,0,0.8)",color:"white",padding:"0.25rem 0.5rem",borderRadius:"9999px",fontSize:"0.75rem",fontWeight:"500"},children:a.colorCategory})]}),(0,d.jsxs)("div",{style:{padding:"1rem"},children:[(0,d.jsx)("h3",{style:{fontSize:"1.125rem",fontWeight:"600",color:"#1F2937",marginBottom:"0.5rem",fontFamily:'"Noto Serif SC", serif'},children:a.name}),(0,d.jsx)("p",{style:{fontSize:"0.875rem",color:"#6B7280",marginBottom:"0.75rem",display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical",overflow:"hidden"},children:a.culturalBackground.personality}),(0,d.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"0.5rem",marginBottom:"0.75rem"},children:[(0,d.jsx)("span",{style:{fontSize:"0.75rem",color:"#6B7280"},children:"主要色彩:"}),(0,d.jsx)("div",{style:{display:"flex",gap:"0.25rem"},children:a.mainColors.slice(0,3).map((a,b)=>(0,d.jsx)("div",{style:{width:"1rem",height:"1rem",borderRadius:"50%",backgroundColor:a,border:"1px solid #D1D5DB"},title:a},b))})]}),(0,d.jsx)("div",{style:{display:"flex",flexWrap:"wrap",gap:"0.25rem"},children:a.tags.slice(0,3).map((a,b)=>(0,d.jsx)("span",{style:{padding:"0.25rem 0.5rem",fontSize:"0.75rem",backgroundColor:"#F3F4F6",color:"#6B7280",borderRadius:"9999px"},children:a},b))})]})]},a.id))})]}),a&&(0,d.jsx)("div",{style:{position:"fixed",inset:0,zIndex:50,display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"rgba(0, 0, 0, 0.5)",backdropFilter:"blur(4px)"},onClick:v,children:(0,d.jsxs)("div",{style:{backgroundColor:"white",borderRadius:"0.75rem",boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1)",border:"2px solid #F59E0B",maxWidth:"90vw",maxHeight:"90vh",overflow:"auto",position:"relative"},onClick:a=>a.stopPropagation(),children:[(0,d.jsx)("button",{onClick:v,style:{position:"absolute",top:"1rem",right:"1rem",backgroundColor:"transparent",border:"none",fontSize:"1.5rem",cursor:"pointer",zIndex:10,color:"#6B7280"},children:"\xd7"}),(0,d.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"1.5rem",padding:"1.5rem"},children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{style:{aspectRatio:"1",background:`linear-gradient(135deg, ${a.mainColors[0]} 0%, ${a.mainColors[1]||a.mainColors[0]} 100%)`,borderRadius:"0.5rem",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"3rem",fontWeight:"bold",marginBottom:"1rem"},children:a.character}),(0,d.jsx)("div",{style:{display:"flex",gap:"0.5rem"},children:a.mainColors.map((a,b)=>(0,d.jsx)("div",{style:{width:"2rem",height:"2rem",borderRadius:"50%",backgroundColor:a,border:"2px solid #D1D5DB"},title:a},b))})]}),(0,d.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"1.5rem"},children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{style:{fontSize:"1.5rem",fontWeight:"bold",color:"#1F2937",marginBottom:"0.5rem",fontFamily:'"Noto Serif SC", serif'},children:a.name}),(0,d.jsx)("p",{style:{color:"#6B7280",fontWeight:"500"},children:a.character})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{style:{fontSize:"1.125rem",fontWeight:"600",color:"#1F2937",marginBottom:"0.5rem"},children:"基本信息"}),(0,d.jsxs)("div",{style:{fontSize:"0.875rem",display:"flex",flexDirection:"column",gap:"0.5rem"},children:[(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{style:{fontWeight:"500"},children:"行当:"})," ",a.roleCategory]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{style:{fontWeight:"500"},children:"颜色分类:"})," ",a.colorCategory]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{style:{fontWeight:"500"},children:"绘制难度:"})," ",a.difficulty]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{style:{fontSize:"1.125rem",fontWeight:"600",color:"#1F2937",marginBottom:"0.5rem"},children:"文化背景"}),(0,d.jsxs)("div",{style:{fontSize:"0.875rem",color:"#6B7280",display:"flex",flexDirection:"column",gap:"0.5rem"},children:[(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{style:{fontWeight:"500"},children:"历史起源:"})," ",a.culturalBackground.origin]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{style:{fontWeight:"500"},children:"性格特点:"})," ",a.culturalBackground.personality]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{style:{fontWeight:"500"},children:"象征意义:"})," ",a.culturalBackground.symbolism]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{style:{fontSize:"1.125rem",fontWeight:"600",color:"#1F2937",marginBottom:"0.5rem"},children:"相关剧目"}),(0,d.jsx)("div",{style:{fontSize:"0.875rem",display:"flex",flexDirection:"column",gap:"0.5rem"},children:a.relatedOperas.map((a,b)=>(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{style:{fontWeight:"500"},children:a.name}),(0,d.jsx)("p",{style:{color:"#6B7280"},children:a.description})]},b))})]}),(0,d.jsx)("div",{style:{display:"flex",flexWrap:"wrap",gap:"0.5rem",marginBottom:"1.5rem"},children:a.tags.map((a,b)=>(0,d.jsx)("span",{style:{padding:"0.5rem 0.75rem",fontSize:"0.75rem",backgroundColor:"rgba(245, 158, 11, 0.2)",color:"#F59E0B",border:"1px solid #F59E0B",borderRadius:"9999px"},children:a},b))}),(0,d.jsx)("button",{onClick:()=>{q(a.id),c.push(`/mask/${a.id}`)},style:{width:"100%",backgroundColor:"#B91C1C",color:"white",padding:"0.75rem 1.5rem",borderRadius:"0.5rem",border:"none",cursor:"pointer",fontSize:"1rem",fontWeight:"600",transition:"all 0.2s ease"},onMouseEnter:a=>{a.currentTarget.style.backgroundColor="#991B1B"},onMouseLeave:a=>{a.currentTarget.style.backgroundColor="#B91C1C"},children:"查看完整详情 →"})]})]})]})})]})}},8346:(a,b,c)=>{Promise.resolve().then(c.bind(c,7215))},8354:a=>{"use strict";a.exports=require("util")},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[985,45,198,782],()=>b(b.s=5798));module.exports=c})();