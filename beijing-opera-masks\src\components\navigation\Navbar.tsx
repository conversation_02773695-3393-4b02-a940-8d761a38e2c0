'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/components/providers/ThemeProvider';
import { AuthModal } from '@/components/auth/AuthModal';
import { ThemeToggle } from '@/components/ui/ThemeToggle';

interface NavbarProps {
  onAddMask?: () => void;
}

export function Navbar({ onAddMask }: NavbarProps) {
  const { user, profile, signOut } = useAuth();
  const { colors } = useTheme();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');
  const [showUserMenu, setShowUserMenu] = useState(false);

  const handleAuthClick = (mode: 'login' | 'register') => {
    setAuthMode(mode);
    setShowAuthModal(true);
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      setShowUserMenu(false);
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  return (
    <>
      <nav style={{
        backgroundColor: colors.background,
        borderBottom: `1px solid ${colors.border}`,
        padding: '1rem 0',
        position: 'sticky',
        top: 0,
        zIndex: 100,
        backdropFilter: 'blur(10px)',
        backgroundColor: colors.background + 'F0'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '0 1rem',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          {/* Logo */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <span style={{
              fontSize: '1.5rem',
              fontWeight: 'bold',
              color: colors.primary,
              fontFamily: '"Ma Shan Zheng", cursive'
            }}>
              🎭 京剧脸谱
            </span>
          </div>

          {/* Navigation Links */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '1rem'
          }}>
            {user && (
              <button
                onClick={onAddMask}
                style={{
                  padding: '0.5rem 1rem',
                  backgroundColor: colors.primary,
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}
              >
                ➕ 添加脸谱
              </button>
            )}

            <ThemeToggle />

            {/* User Menu */}
            {user ? (
              <div style={{ position: 'relative' }}>
                <button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    padding: '0.5rem',
                    backgroundColor: 'transparent',
                    border: `1px solid ${colors.border}`,
                    borderRadius: '6px',
                    cursor: 'pointer',
                    color: colors.text
                  }}
                >
                  <div style={{
                    width: '32px',
                    height: '32px',
                    borderRadius: '50%',
                    backgroundColor: colors.primary,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '0.875rem',
                    fontWeight: 'bold'
                  }}>
                    {profile?.display_name?.[0] || profile?.username?.[0] || '用'}
                  </div>
                  <span style={{ fontSize: '0.875rem' }}>
                    {profile?.display_name || profile?.username || '用户'}
                  </span>
                  <span style={{ fontSize: '0.75rem' }}>▼</span>
                </button>

                {showUserMenu && (
                  <div style={{
                    position: 'absolute',
                    top: '100%',
                    right: 0,
                    marginTop: '0.5rem',
                    backgroundColor: colors.background,
                    border: `1px solid ${colors.border}`,
                    borderRadius: '8px',
                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                    minWidth: '200px',
                    zIndex: 1000
                  }}>
                    <div style={{
                      padding: '0.75rem 1rem',
                      borderBottom: `1px solid ${colors.border}`
                    }}>
                      <div style={{
                        fontSize: '0.875rem',
                        fontWeight: '500',
                        color: colors.text
                      }}>
                        {profile?.display_name || profile?.username}
                      </div>
                      <div style={{
                        fontSize: '0.75rem',
                        color: colors.textSecondary
                      }}>
                        贡献积分: {profile?.contribution_points || 0}
                      </div>
                    </div>

                    <div style={{ padding: '0.5rem 0' }}>
                      <button
                        onClick={() => {
                          setShowUserMenu(false);
                          // TODO: 导航到个人资料页面
                        }}
                        style={{
                          width: '100%',
                          padding: '0.75rem 1rem',
                          backgroundColor: 'transparent',
                          border: 'none',
                          textAlign: 'left',
                          cursor: 'pointer',
                          color: colors.text,
                          fontSize: '0.875rem'
                        }}
                      >
                        📊 我的贡献
                      </button>
                      <button
                        onClick={() => {
                          setShowUserMenu(false);
                          // TODO: 导航到收藏页面
                        }}
                        style={{
                          width: '100%',
                          padding: '0.75rem 1rem',
                          backgroundColor: 'transparent',
                          border: 'none',
                          textAlign: 'left',
                          cursor: 'pointer',
                          color: colors.text,
                          fontSize: '0.875rem'
                        }}
                      >
                        ❤️ 我的收藏
                      </button>
                      <button
                        onClick={() => {
                          setShowUserMenu(false);
                          // TODO: 导航到设置页面
                        }}
                        style={{
                          width: '100%',
                          padding: '0.75rem 1rem',
                          backgroundColor: 'transparent',
                          border: 'none',
                          textAlign: 'left',
                          cursor: 'pointer',
                          color: colors.text,
                          fontSize: '0.875rem'
                        }}
                      >
                        ⚙️ 账户设置
                      </button>
                    </div>

                    <div style={{
                      borderTop: `1px solid ${colors.border}`,
                      padding: '0.5rem 0'
                    }}>
                      <button
                        onClick={handleSignOut}
                        style={{
                          width: '100%',
                          padding: '0.75rem 1rem',
                          backgroundColor: 'transparent',
                          border: 'none',
                          textAlign: 'left',
                          cursor: 'pointer',
                          color: '#DC2626',
                          fontSize: '0.875rem'
                        }}
                      >
                        🚪 退出登录
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div style={{
                display: 'flex',
                gap: '0.5rem'
              }}>
                <button
                  onClick={() => handleAuthClick('login')}
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: 'transparent',
                    border: `1px solid ${colors.border}`,
                    borderRadius: '6px',
                    color: colors.text,
                    cursor: 'pointer',
                    fontSize: '0.875rem'
                  }}
                >
                  登录
                </button>
                <button
                  onClick={() => handleAuthClick('register')}
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: colors.primary,
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    fontSize: '0.875rem'
                  }}
                >
                  注册
                </button>
              </div>
            )}
          </div>
        </div>
      </nav>

      {/* Click outside to close user menu */}
      {showUserMenu && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 99
          }}
          onClick={() => setShowUserMenu(false)}
        />
      )}

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        initialMode={authMode}
      />
    </>
  );
}
