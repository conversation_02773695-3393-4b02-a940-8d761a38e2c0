@echo off
echo 🎭 京剧脸谱文化展示平台 - 公网访问启动脚本
echo ================================================

echo.
echo 📋 当前状态检查...
echo.

REM 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js
    pause
    exit /b 1
)

echo ✅ Node.js 已安装: 
node --version

REM 检查npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm 未安装
    pause
    exit /b 1
)

echo ✅ npm 已安装: 
npm --version

echo.
echo 🚀 启动应用程序...
echo.

REM 安装依赖（如果需要）
if not exist "node_modules" (
    echo 📦 安装依赖包...
    npm install
)

echo.
echo 🎭 启动京剧脸谱文化展示平台...
echo.
echo ✅ 本地访问地址: http://localhost:3002
echo.
echo 📋 获取公网访问地址的方法:
echo.
echo 方案1 - 使用Ngrok (推荐):
echo   1. 下载 ngrok: https://ngrok.com/
echo   2. 在新终端运行: ngrok http 3002
echo   3. 获得公网URL: https://xxx.ngrok.io
echo.
echo 方案2 - 局域网访问:
echo   获取本机IP: ipconfig ^| findstr "IPv4"
echo   访问地址: http://你的IP:3002
echo.
echo 方案3 - 免费域名:
echo   DuckDNS: https://www.duckdns.org/
echo   No-IP: https://www.noip.com/
echo.

REM 获取本机IP地址
echo 🌐 本机IP地址信息:
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr "IPv4"') do (
    echo   局域网访问: http://%%a:3002
)

echo.
echo 🎉 应用程序启动中...
echo 📱 功能特色:
echo   • 9个真实京剧脸谱角色
echo   • 响应式设计 (手机/平板/电脑)
echo   • 明暗主题切换
echo   • 角色详情和文化介绍
echo   • 模态对话框交互
echo.
echo ⚠️  按 Ctrl+C 停止服务器
echo.

REM 启动开发服务器
npm start
