'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { operaMasks } from '@/data/masks';
import { OperaMask } from '@/types/mask';
import { MaskDrawingAnimation } from '@/components/animation/MaskDrawingAnimation';
import { Breadcrumb } from '@/components/navigation/Breadcrumb';
import { useAppState } from '@/hooks/useAppState';

export default function MaskDetailPage() {
  const params = useParams();
  const router = useRouter();
  const maskId = params.id as string;
  const [showAnimation, setShowAnimation] = useState(false);
  const [isAnimationPlaying, setIsAnimationPlaying] = useState(false);
  const { addToRecentlyViewed, toggleFavorite, isFavorite } = useAppState();

  const mask = operaMasks.find(m => m.id === maskId);

  // 记录访问
  useEffect(() => {
    if (mask) {
      addToRecentlyViewed(mask.id);
    }
  }, [mask, addToRecentlyViewed]);
  
  if (!mask) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#F9FAFB',
        fontFamily: '"Noto Sans SC", sans-serif'
      }}>
        <div style={{ textAlign: 'center' }}>
          <h1 style={{ fontSize: '2rem', color: '#1F2937', marginBottom: '1rem' }}>
            脸谱未找到
          </h1>
          <p style={{ color: '#6B7280', marginBottom: '2rem' }}>
            抱歉，您访问的脸谱不存在。
          </p>
          <button
            onClick={() => router.push('/')}
            style={{
              backgroundColor: '#B91C1C',
              color: 'white',
              padding: '0.75rem 1.5rem',
              borderRadius: '0.5rem',
              border: 'none',
              cursor: 'pointer',
              fontSize: '1rem'
            }}
          >
            返回首页
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#F9FAFB',
      fontFamily: '"Noto Sans SC", sans-serif'
    }}>
      {/* 头部导航 */}
      <header style={{
        backgroundColor: 'white',
        borderBottom: '2px solid #F59E0B',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        position: 'sticky',
        top: 0,
        zIndex: 40
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '1rem 1.5rem',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <button
            onClick={() => router.push('/')}
            style={{
              backgroundColor: 'transparent',
              border: '2px solid #B91C1C',
              color: '#B91C1C',
              padding: '0.5rem 1rem',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              fontSize: '0.875rem',
              fontWeight: '500'
            }}
          >
            ← 返回首页
          </button>
          <h1 style={{
            fontSize: '1.5rem',
            fontWeight: 'bold',
            color: '#1F2937',
            fontFamily: '"Noto Serif SC", serif'
          }}>
            {mask.name}
          </h1>
          <button
            onClick={() => setShowAnimation(!showAnimation)}
            style={{
              backgroundColor: showAnimation ? '#EF4444' : '#10B981',
              color: 'white',
              border: 'none',
              padding: '0.5rem 1rem',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              fontSize: '0.875rem',
              fontWeight: '500'
            }}
          >
            {showAnimation ? '关闭动画' : '绘制动画'}
          </button>
        </div>
      </header>
      
      {/* 主要内容 */}
      <main style={{ padding: '2rem 1.5rem' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          {/* 面包屑导航 */}
          <Breadcrumb
            items={[
              { label: '首页', href: '/' },
              { label: '脸谱详情', current: true }
            ]}
          />
        </div>

        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '3rem',
          alignItems: 'start'
        }}>
          {/* 左侧：脸谱图片和基本信息 */}
          <div style={{ position: 'sticky', top: '120px' }}>
            {/* 脸谱图片或动画 */}
            {showAnimation ? (
              <MaskDrawingAnimation
                mask={mask}
                isPlaying={isAnimationPlaying}
                onPlayStateChange={setIsAnimationPlaying}
                speed={1}
                style={{ marginBottom: '2rem' }}
              />
            ) : (
              <div style={{
                aspectRatio: '1',
                background: `linear-gradient(135deg, ${mask.mainColors[0]} 0%, ${mask.mainColors[1] || mask.mainColors[0]} 100%)`,
                borderRadius: '1rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: '4rem',
                fontWeight: 'bold',
                marginBottom: '2rem',
                position: 'relative',
                boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
                border: '3px solid #F59E0B'
              }}>
                <div style={{
                  position: 'absolute',
                  inset: 0,
                  background: 'linear-gradient(to top, rgba(0,0,0,0.3) 0%, transparent 100%)',
                  borderRadius: '1rem'
                }} />
                <span style={{ position: 'relative', zIndex: 10 }}>
                  {mask.character}
                </span>

                {/* 分类标签 */}
                <div style={{
                  position: 'absolute',
                  top: '1rem',
                  left: '1rem',
                  backgroundColor: 'rgba(0,0,0,0.8)',
                  color: 'white',
                  padding: '0.5rem 1rem',
                  borderRadius: '9999px',
                  fontSize: '0.875rem',
                  fontWeight: '600'
                }}>
                  {mask.roleCategory}
                </div>

                <div style={{
                  position: 'absolute',
                  top: '1rem',
                  right: '1rem',
                  backgroundColor: 'rgba(0,0,0,0.8)',
                  color: 'white',
                  padding: '0.5rem 1rem',
                  borderRadius: '9999px',
                  fontSize: '0.875rem',
                  fontWeight: '600'
                }}>
                  {mask.colorCategory}
                </div>

                {/* 受欢迎程度星级 */}
                <div style={{
                  position: 'absolute',
                  bottom: '1rem',
                  right: '1rem',
                  display: 'flex',
                  gap: '0.25rem'
                }}>
                  {Array.from({ length: 5 }).map((_, i) => (
                    <span
                      key={i}
                      style={{
                        color: i < Math.floor(mask.popularity / 2) ? '#F59E0B' : 'rgba(255,255,255,0.5)',
                        fontSize: '1.25rem'
                      }}
                    >
                      ★
                    </span>
                  ))}
                </div>
              </div>
            )}
            
            {/* 主要颜色 */}
            <div style={{
              backgroundColor: 'white',
              padding: '1.5rem',
              borderRadius: '1rem',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
              border: '2px solid #F59E0B'
            }}>
              <h3 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                color: '#1F2937',
                marginBottom: '1rem',
                fontFamily: '"Noto Serif SC", serif'
              }}>
                主要色彩
              </h3>
              <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
                {mask.mainColors.map((color, index) => (
                  <div key={index} style={{ textAlign: 'center' }}>
                    <div
                      style={{
                        width: '3rem',
                        height: '3rem',
                        borderRadius: '50%',
                        backgroundColor: color,
                        border: '3px solid #D1D5DB',
                        marginBottom: '0.5rem',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                      }}
                    />
                    <span style={{
                      fontSize: '0.75rem',
                      color: '#6B7280',
                      fontFamily: 'monospace'
                    }}>
                      {color}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
          
          {/* 右侧：详细信息 */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
            {/* 基本信息 */}
            <div style={{
              backgroundColor: 'white',
              padding: '2rem',
              borderRadius: '1rem',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
              border: '2px solid #F59E0B'
            }}>
              <h2 style={{
                fontSize: '1.5rem',
                fontWeight: 'bold',
                color: '#1F2937',
                marginBottom: '1.5rem',
                fontFamily: '"Noto Serif SC", serif'
              }}>
                基本信息
              </h2>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                <div>
                  <span style={{ fontWeight: '600', color: '#374151' }}>角色名称：</span>
                  <span style={{ color: '#6B7280' }}>{mask.character}</span>
                </div>
                <div>
                  <span style={{ fontWeight: '600', color: '#374151' }}>行当分类：</span>
                  <span style={{ color: '#6B7280' }}>{mask.roleCategory}</span>
                </div>
                <div>
                  <span style={{ fontWeight: '600', color: '#374151' }}>颜色分类：</span>
                  <span style={{ color: '#6B7280' }}>{mask.colorCategory}</span>
                </div>
                <div>
                  <span style={{ fontWeight: '600', color: '#374151' }}>绘制难度：</span>
                  <span style={{ color: '#6B7280' }}>
                    {mask.difficulty === 'easy' ? '简单' : mask.difficulty === 'medium' ? '中等' : '困难'}
                  </span>
                </div>
                <div>
                  <span style={{ fontWeight: '600', color: '#374151' }}>受欢迎程度：</span>
                  <span style={{ color: '#6B7280' }}>{mask.popularity}/10</span>
                </div>
                <div>
                  <span style={{ fontWeight: '600', color: '#374151' }}>历史时期：</span>
                  <span style={{ color: '#6B7280' }}>{mask.culturalBackground.historicalPeriod}</span>
                </div>
              </div>
            </div>
            
            {/* 文化背景 */}
            <div style={{
              backgroundColor: 'white',
              padding: '2rem',
              borderRadius: '1rem',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
              border: '2px solid #F59E0B'
            }}>
              <h2 style={{
                fontSize: '1.5rem',
                fontWeight: 'bold',
                color: '#1F2937',
                marginBottom: '1.5rem',
                fontFamily: '"Noto Serif SC", serif'
              }}>
                文化背景
              </h2>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
                <div>
                  <h3 style={{ fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                    历史起源
                  </h3>
                  <p style={{ color: '#6B7280', lineHeight: '1.6' }}>
                    {mask.culturalBackground.origin}
                  </p>
                </div>
                <div>
                  <h3 style={{ fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                    性格特点
                  </h3>
                  <p style={{ color: '#6B7280', lineHeight: '1.6' }}>
                    {mask.culturalBackground.personality}
                  </p>
                </div>
                <div>
                  <h3 style={{ fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                    象征意义
                  </h3>
                  <p style={{ color: '#6B7280', lineHeight: '1.6' }}>
                    {mask.culturalBackground.symbolism}
                  </p>
                </div>
              </div>
            </div>
            
            {/* 色彩寓意 */}
            <div style={{
              backgroundColor: 'white',
              padding: '2rem',
              borderRadius: '1rem',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
              border: '2px solid #F59E0B'
            }}>
              <h2 style={{
                fontSize: '1.5rem',
                fontWeight: 'bold',
                color: '#1F2937',
                marginBottom: '1.5rem',
                fontFamily: '"Noto Serif SC", serif'
              }}>
                色彩寓意
              </h2>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                {Object.entries(mask.colorMeaning).map(([color, meaning]) => (
                  <div key={color} style={{
                    padding: '1rem',
                    backgroundColor: '#F9FAFB',
                    borderRadius: '0.5rem',
                    borderLeft: '4px solid #F59E0B'
                  }}>
                    <span style={{ fontWeight: '600', color: '#374151' }}>{color}：</span>
                    <span style={{ color: '#6B7280' }}>{meaning}</span>
                  </div>
                ))}
              </div>
            </div>
            
            {/* 相关剧目 */}
            <div style={{
              backgroundColor: 'white',
              padding: '2rem',
              borderRadius: '1rem',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
              border: '2px solid #F59E0B'
            }}>
              <h2 style={{
                fontSize: '1.5rem',
                fontWeight: 'bold',
                color: '#1F2937',
                marginBottom: '1.5rem',
                fontFamily: '"Noto Serif SC", serif'
              }}>
                相关剧目
              </h2>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
                {mask.relatedOperas.map((opera, index) => (
                  <div key={index} style={{
                    padding: '1.5rem',
                    backgroundColor: '#F9FAFB',
                    borderRadius: '0.75rem',
                    border: '1px solid #E5E7EB'
                  }}>
                    <h3 style={{
                      fontSize: '1.125rem',
                      fontWeight: '600',
                      color: '#1F2937',
                      marginBottom: '0.5rem',
                      fontFamily: '"Noto Serif SC", serif'
                    }}>
                      {opera.name}
                    </h3>
                    <p style={{ color: '#6B7280', marginBottom: '0.5rem', lineHeight: '1.6' }}>
                      {opera.description}
                    </p>
                    <span style={{
                      fontSize: '0.875rem',
                      color: '#F59E0B',
                      fontWeight: '500'
                    }}>
                      {opera.period}
                    </span>
                  </div>
                ))}
              </div>
            </div>
            
            {/* 标签 */}
            <div style={{
              backgroundColor: 'white',
              padding: '2rem',
              borderRadius: '1rem',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
              border: '2px solid #F59E0B'
            }}>
              <h2 style={{
                fontSize: '1.5rem',
                fontWeight: 'bold',
                color: '#1F2937',
                marginBottom: '1.5rem',
                fontFamily: '"Noto Serif SC", serif'
              }}>
                相关标签
              </h2>
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.75rem' }}>
                {mask.tags.map((tag, index) => (
                  <span
                    key={index}
                    style={{
                      padding: '0.75rem 1rem',
                      fontSize: '0.875rem',
                      backgroundColor: 'rgba(245, 158, 11, 0.1)',
                      color: '#F59E0B',
                      border: '2px solid #F59E0B',
                      borderRadius: '9999px',
                      fontWeight: '500'
                    }}
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
