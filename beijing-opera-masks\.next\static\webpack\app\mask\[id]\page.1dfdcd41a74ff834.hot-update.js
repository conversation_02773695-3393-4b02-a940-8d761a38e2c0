"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/mask/[id]/page",{

/***/ "(app-pages-browser)/./src/hooks/useLocalStorage.ts":
/*!**************************************!*\
  !*** ./src/hooks/useLocalStorage.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFavorites: () => (/* binding */ useFavorites),\n/* harmony export */   useLocalStorage: () => (/* binding */ useLocalStorage),\n/* harmony export */   useRecentlyViewed: () => (/* binding */ useRecentlyViewed)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_useEffect_useState_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=useEffect,useState!=!react */ \"(app-pages-browser)/__barrel_optimize__?names=useEffect,useState!=!./node_modules/next/dist/compiled/react/index.js\");\n\n// 通用的localStorage hook\nfunction useLocalStorage(key, initialValue) {\n    // 添加客户端渲染状态\n    const [isClient, setIsClient] = (0,_barrel_optimize_names_useEffect_useState_react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // 获取初始值 - 服务器端始终返回初始值\n    const [storedValue, setStoredValue] = (0,_barrel_optimize_names_useEffect_useState_react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialValue);\n    // 客户端挂载后读取localStorage\n    (0,_barrel_optimize_names_useEffect_useState_react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useLocalStorage.useEffect\": ()=>{\n            setIsClient(true);\n            try {\n                const item = window.localStorage.getItem(key);\n                if (item) {\n                    setStoredValue(JSON.parse(item));\n                }\n            } catch (error) {\n                console.error('Error reading localStorage key \"'.concat(key, '\":'), error);\n            }\n        }\n    }[\"useLocalStorage.useEffect\"], [\n        key\n    ]);\n    // 设置值的函数\n    const setValue = (value)=>{\n        try {\n            const valueToStore = value instanceof Function ? value(storedValue) : value;\n            setStoredValue(valueToStore);\n            if (isClient) {\n                window.localStorage.setItem(key, JSON.stringify(valueToStore));\n            }\n        } catch (error) {\n            console.error('Error setting localStorage key \"'.concat(key, '\":'), error);\n        }\n    };\n    return [\n        storedValue,\n        setValue,\n        isClient\n    ];\n}\n// 收藏功能hook\nfunction useFavorites() {\n    const [favorites, setFavorites] = useLocalStorage('opera-mask-favorites', []);\n    const toggleFavorite = (maskId)=>{\n        setFavorites((prev)=>{\n            if (prev.includes(maskId)) {\n                return prev.filter((id)=>id !== maskId);\n            } else {\n                return [\n                    ...prev,\n                    maskId\n                ];\n            }\n        });\n    };\n    const isFavorite = (maskId)=>{\n        return favorites.includes(maskId);\n    };\n    return {\n        favorites,\n        toggleFavorite,\n        isFavorite\n    };\n}\n// 最近浏览功能hook\nfunction useRecentlyViewed() {\n    const [recentlyViewed, setRecentlyViewed] = useLocalStorage('opera-mask-recent', []);\n    const addToRecentlyViewed = (maskId)=>{\n        setRecentlyViewed((prev)=>{\n            // 移除已存在的项目\n            const filtered = prev.filter((id)=>id !== maskId);\n            // 添加到开头，限制最多10个\n            return [\n                maskId,\n                ...filtered\n            ].slice(0, 10);\n        });\n    };\n    return {\n        recentlyViewed,\n        addToRecentlyViewed\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useLocalStorage.ts\n"));

/***/ })

});