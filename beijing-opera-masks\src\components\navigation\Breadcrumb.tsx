'use client';

import React from 'react';
import { useRouter } from 'next/navigation';

interface BreadcrumbItem {
  label: string;
  href?: string;
  current?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

export function Breadcrumb({ items, className }: BreadcrumbProps) {
  const router = useRouter();
  
  const handleClick = (href?: string) => {
    if (href) {
      router.push(href);
    }
  };
  
  return (
    <nav
      aria-label="面包屑导航"
      className={className}
      style={{
        padding: '1rem 0',
        borderBottom: '1px solid #E5E7EB'
      }}
    >
      <ol style={{
        display: 'flex',
        alignItems: 'center',
        gap: '0.5rem',
        fontSize: '0.875rem',
        color: '#6B7280'
      }}>
        {items.map((item, index) => (
          <li key={index} style={{ display: 'flex', alignItems: 'center' }}>
            {index > 0 && (
              <span style={{ margin: '0 0.5rem', color: '#D1D5DB' }}>
                /
              </span>
            )}
            {item.current ? (
              <span style={{
                color: '#1F2937',
                fontWeight: '500',
                fontFamily: '"Noto Serif SC", serif'
              }}>
                {item.label}
              </span>
            ) : (
              <button
                onClick={() => handleClick(item.href)}
                style={{
                  background: 'none',
                  border: 'none',
                  color: '#6B7280',
                  cursor: 'pointer',
                  textDecoration: 'none',
                  fontSize: 'inherit',
                  padding: 0,
                  transition: 'color 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.color = '#B91C1C';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.color = '#6B7280';
                }}
              >
                {item.label}
              </button>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}
