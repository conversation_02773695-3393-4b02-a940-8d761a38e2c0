{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/data/masks.ts"], "sourcesContent": ["import { OperaMask } from '@/types/mask';\n\nexport const operaMasks: OperaMask[] = [\n  {\n    id: 'guanyu',\n    name: '关羽脸谱',\n    character: '关羽',\n    roleCategory: '净',\n    colorCategory: '红脸',\n    mainColors: ['#DC143C', '#FFD700', '#000000'],\n    culturalBackground: {\n      origin: '三国时期蜀汉名将，被尊为武圣',\n      personality: '忠义勇武，刚正不阿，义薄云天',\n      symbolism: '忠诚、正义、勇敢的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '红色': '忠勇正义，赤胆忠心',\n      '金色': '神圣威严，地位崇高',\n      '黑色': '刚毅坚定，不可动摇'\n    },\n    relatedOperas: [\n      { name: '单刀会', description: '关羽单刀赴会的故事', period: '三国' },\n      { name: '华容道', description: '关羽义释曹操', period: '三国' },\n      { name: '走麦城', description: '关羽败走麦城', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=关羽',\n      fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=关羽脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹红色底色', duration: 1000, color: '#DC143C' },\n      { id: 2, name: '眉毛', description: '绘制浓眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1200, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘鼻梁线条', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加金色装饰', duration: 1000, color: '#FFD700' }\n    ],\n    difficulty: 'medium',\n    popularity: 10,\n    tags: ['三国', '武将', '忠义', '经典']\n  },\n  {\n    id: 'baogong',\n    name: '包拯脸谱',\n    character: '包拯',\n    roleCategory: '净',\n    colorCategory: '黑脸',\n    mainColors: ['#000000', '#FFFFFF', '#FFD700'],\n    culturalBackground: {\n      origin: '北宋名臣，以清廉公正著称',\n      personality: '铁面无私，执法如山，清正廉洁',\n      symbolism: '公正执法，清廉为民的象征',\n      historicalPeriod: '北宋时期'\n    },\n    colorMeaning: {\n      '黑色': '公正严明，铁面无私',\n      '白色': '清廉正直，一尘不染',\n      '金色': '威严庄重，地位尊崇'\n    },\n    relatedOperas: [\n      { name: '铡美案', description: '包拯铡驸马陈世美', period: '宋代' },\n      { name: '打龙袍', description: '包拯怒斥宋仁宗', period: '宋代' },\n      { name: '赤桑镇', description: '包拯审案的故事', period: '宋代' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/000000/FFFFFF?text=包拯',\n      fullSize: 'https://via.placeholder.com/600x600/000000/FFFFFF?text=包拯脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹黑色底色', duration: 1000, color: '#000000' },\n      { id: 2, name: '额头', description: '绘制白色月牙', duration: 800, color: '#FFFFFF' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1000, color: '#FFFFFF' },\n      { id: 4, name: '鼻翼', description: '描绘鼻翼线条', duration: 600, color: '#FFFFFF' },\n      { id: 5, name: '装饰', description: '添加金色细节', duration: 800, color: '#FFD700' }\n    ],\n    difficulty: 'easy',\n    popularity: 9,\n    tags: ['宋代', '清官', '正义', '经典']\n  },\n  {\n    id: 'caocao',\n    name: '曹操脸谱',\n    character: '曹操',\n    roleCategory: '净',\n    colorCategory: '白脸',\n    mainColors: ['#FFFFFF', '#000000', '#DC143C'],\n    culturalBackground: {\n      origin: '东汉末年政治家、军事家、文学家',\n      personality: '奸诈狡猾，野心勃勃，但才华横溢',\n      symbolism: '奸诈、权谋、复杂人性的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '白色': '奸诈狡猾，阴险毒辣',\n      '黑色': '深沉城府，心机深重',\n      '红色': '暴戾之气，杀伐果断'\n    },\n    relatedOperas: [\n      { name: '捉放曹', description: '陈宫捉放曹操', period: '三国' },\n      { name: '击鼓骂曹', description: '祢衡击鼓骂曹', period: '三国' },\n      { name: '群英会', description: '曹操与群雄斗智', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/FFFFFF/000000?text=曹操',\n      fullSize: 'https://via.placeholder.com/600x600/FFFFFF/000000?text=曹操脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹白色底色', duration: 1000, color: '#FFFFFF' },\n      { id: 2, name: '眉毛', description: '绘制黑色浓眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1200, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘鼻梁阴影', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加红色细节', duration: 1000, color: '#DC143C' }\n    ],\n    difficulty: 'medium',\n    popularity: 8,\n    tags: ['三国', '奸雄', '复杂', '经典']\n  },\n  {\n    id: 'zhangfei',\n    name: '张飞脸谱',\n    character: '张飞',\n    roleCategory: '净',\n    colorCategory: '黑脸',\n    mainColors: ['#000000', '#FFFFFF', '#DC143C'],\n    culturalBackground: {\n      origin: '三国时期蜀汉名将，刘备义弟',\n      personality: '勇猛粗犷，嫉恶如仇，忠义豪爽',\n      symbolism: '勇猛、正直、豪爽的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '黑色': '刚直勇猛，正气凛然',\n      '白色': '纯真豪爽，心无城府',\n      '红色': '热血沸腾，义气冲天'\n    },\n    relatedOperas: [\n      { name: '长坂坡', description: '张飞大战长坂坡', period: '三国' },\n      { name: '古城会', description: '张飞误会关羽', period: '三国' },\n      { name: '芦花荡', description: '张飞智取芦花荡', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/000000/FFFFFF?text=张飞',\n      fullSize: 'https://via.placeholder.com/600x600/000000/FFFFFF?text=张飞脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹黑色底色', duration: 1000, color: '#000000' },\n      { id: 2, name: '眉毛', description: '绘制白色粗眉', duration: 800, color: '#FFFFFF' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1000, color: '#FFFFFF' },\n      { id: 4, name: '鼻翼', description: '描绘鼻翼线条', duration: 600, color: '#FFFFFF' },\n      { id: 5, name: '装饰', description: '添加红色装饰', duration: 800, color: '#DC143C' }\n    ],\n    difficulty: 'easy',\n    popularity: 8,\n    tags: ['三国', '武将', '勇猛', '豪爽']\n  },\n  {\n    id: 'doulujin',\n    name: '窦尔敦脸谱',\n    character: '窦尔敦',\n    roleCategory: '净',\n    colorCategory: '蓝脸',\n    mainColors: ['#0066CC', '#FFFFFF', '#FFD700'],\n    culturalBackground: {\n      origin: '清代绿林好汉，盗侠传奇人物',\n      personality: '刚强勇猛，侠肝义胆，不畏强权',\n      symbolism: '刚强、勇敢、反抗精神的象征',\n      historicalPeriod: '清代'\n    },\n    colorMeaning: {\n      '蓝色': '刚强勇猛，桀骜不驯',\n      '白色': '正直豪爽，光明磊落',\n      '金色': '英雄气概，不凡身份'\n    },\n    relatedOperas: [\n      { name: '盗御马', description: '窦尔敦盗取御马', period: '清代' },\n      { name: '连环套', description: '窦尔敦中计被擒', period: '清代' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/0066CC/FFFFFF?text=窦尔敦',\n      fullSize: 'https://via.placeholder.com/600x600/0066CC/FFFFFF?text=窦尔敦脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹蓝色底色', duration: 1000, color: '#0066CC' },\n      { id: 2, name: '眉毛', description: '绘制白色眉毛', duration: 800, color: '#FFFFFF' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1000, color: '#FFFFFF' },\n      { id: 4, name: '鼻梁', description: '描绘鼻梁线条', duration: 600, color: '#FFFFFF' },\n      { id: 5, name: '装饰', description: '添加金色装饰', duration: 800, color: '#FFD700' }\n    ],\n    difficulty: 'medium',\n    popularity: 7,\n    tags: ['清代', '绿林', '侠客', '勇猛']\n  },\n  {\n    id: 'yangqilang',\n    name: '杨七郎脸谱',\n    character: '杨七郎',\n    roleCategory: '生',\n    colorCategory: '红脸',\n    mainColors: ['#DC143C', '#FFD700', '#000000'],\n    culturalBackground: {\n      origin: '北宋杨家将中的七子杨延嗣',\n      personality: '英勇善战，忠君爱国，血性男儿',\n      symbolism: '忠勇、牺牲、家国情怀的象征',\n      historicalPeriod: '北宋时期'\n    },\n    colorMeaning: {\n      '红色': '忠勇热血，为国捐躯',\n      '金色': '英雄本色，光耀门第',\n      '黑色': '刚毅果敢，义无反顾'\n    },\n    relatedOperas: [\n      { name: '杨家将', description: '杨家将抗辽的故事', period: '宋代' },\n      { name: '四郎探母', description: '杨四郎探望母亲', period: '宋代' },\n      { name: '穆桂英挂帅', description: '穆桂英率军出征', period: '宋代' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=杨七郎',\n      fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=杨七郎脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹红色底色', duration: 1000, color: '#DC143C' },\n      { id: 2, name: '眉毛', description: '绘制黑色剑眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘鼻梁线条', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加金色装饰', duration: 800, color: '#FFD700' }\n    ],\n    difficulty: 'medium',\n    popularity: 7,\n    tags: ['宋代', '杨家将', '忠勇', '英雄']\n  },\n  {\n    id: 'yangguifei',\n    name: '杨贵妃脸谱',\n    character: '杨贵妃',\n    roleCategory: '旦',\n    colorCategory: '红脸',\n    mainColors: ['#FFB6C1', '#FFD700', '#DC143C'],\n    culturalBackground: {\n      origin: '唐代著名美女，唐玄宗宠妃',\n      personality: '美丽动人，聪慧机敏，但也任性娇纵',\n      symbolism: '美丽、爱情、悲剧的象征',\n      historicalPeriod: '唐代'\n    },\n    colorMeaning: {\n      '粉红色': '娇美动人，温柔如水',\n      '金色': '富贵荣华，地位尊贵',\n      '红色': '热情如火，爱情炽烈'\n    },\n    relatedOperas: [\n      { name: '贵妃醉酒', description: '杨贵妃醉酒的故事', period: '唐代' },\n      { name: '长生殿', description: '唐玄宗与杨贵妃的爱情', period: '唐代' },\n      { name: '马嵬坡', description: '杨贵妃马嵬坡之死', period: '唐代' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/FFB6C1/FFFFFF?text=杨贵妃',\n      fullSize: 'https://via.placeholder.com/600x600/FFB6C1/FFFFFF?text=杨贵妃脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹粉色底色', duration: 1000, color: '#FFB6C1' },\n      { id: 2, name: '眉毛', description: '绘制柳叶眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画凤眼轮廓', duration: 1200, color: '#000000' },\n      { id: 4, name: '唇部', description: '描绘樱桃小口', duration: 600, color: '#DC143C' },\n      { id: 5, name: '装饰', description: '添加金色花钿', duration: 1000, color: '#FFD700' }\n    ],\n    difficulty: 'hard',\n    popularity: 9,\n    tags: ['唐代', '美女', '爱情', '悲剧']\n  },\n  {\n    id: 'wusong',\n    name: '武松脸谱',\n    character: '武松',\n    roleCategory: '净',\n    colorCategory: '红脸',\n    mainColors: ['#DC143C', '#000000', '#FFD700'],\n    culturalBackground: {\n      origin: '水浒传中的英雄好汉，行者武松',\n      personality: '勇猛无畏，嫉恶如仇，义薄云天',\n      symbolism: '正义、勇敢、反抗精神的象征',\n      historicalPeriod: '北宋时期'\n    },\n    colorMeaning: {\n      '红色': '正义凛然，热血沸腾',\n      '黑色': '刚毅果敢，不屈不挠',\n      '金色': '英雄本色，光明磊落'\n    },\n    relatedOperas: [\n      { name: '武松打虎', description: '武松景阳冈打虎', period: '宋代' },\n      { name: '狮子楼', description: '武松杀西门庆', period: '宋代' },\n      { name: '十字坡', description: '武松遇孙二娘', period: '宋代' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=武松',\n      fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=武松脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹红色底色', duration: 1000, color: '#DC143C' },\n      { id: 2, name: '眉毛', description: '绘制浓黑剑眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画虎目轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘挺直鼻梁', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加金色装饰', duration: 800, color: '#FFD700' }\n    ],\n    difficulty: 'medium',\n    popularity: 9,\n    tags: ['水浒', '英雄', '正义', '勇猛']\n  },\n  {\n    id: 'jianggan',\n    name: '蒋干脸谱',\n    character: '蒋干',\n    roleCategory: '丑',\n    colorCategory: '白脸',\n    mainColors: ['#FFFFFF', '#000000', '#808080'],\n    culturalBackground: {\n      origin: '三国时期人物，曹操谋士',\n      personality: '自作聪明，好事多磨，常弄巧成拙',\n      symbolism: '愚蠢、自负、滑稽的象征',\n      historicalPeriod: '三国时期'\n    },\n    colorMeaning: {\n      '白色': '愚蠢无知，自以为是',\n      '黑色': '心机不深，容易上当',\n      '灰色': '平庸无能，不值一提'\n    },\n    relatedOperas: [\n      { name: '群英会', description: '蒋干中计盗书', period: '三国' },\n      { name: '借东风', description: '诸葛亮借东风', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/FFFFFF/000000?text=蒋干',\n      fullSize: 'https://via.placeholder.com/600x600/FFFFFF/000000?text=蒋干脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹白色底色', duration: 1000, color: '#FFFFFF' },\n      { id: 2, name: '眉毛', description: '绘制细眉', duration: 600, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画小眼轮廓', duration: 800, color: '#000000' },\n      { id: 4, name: '鼻部', description: '描绘尖鼻', duration: 400, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加滑稽装饰', duration: 600, color: '#808080' }\n    ],\n    difficulty: 'easy',\n    popularity: 6,\n    tags: ['三国', '丑角', '滑稽', '愚蠢']\n  },\n  {\n    id: 'liubei',\n    name: '刘备脸谱',\n    character: '刘备',\n    roleCategory: '生',\n    colorCategory: '红脸',\n    mainColors: ['#DC143C', '#FFD700', '#000000'],\n    culturalBackground: {\n      origin: '三国时期蜀汉开国皇帝',\n      personality: '仁德宽厚，礼贤下士，志向远大',\n      symbolism: '仁德、理想、领袖风范的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '红色': '仁德之心，爱民如子',\n      '金色': '帝王之相，天命所归',\n      '黑色': '深沉稳重，胸怀大志'\n    },\n    relatedOperas: [\n      { name: '三顾茅庐', description: '刘备三顾茅庐请诸葛亮', period: '三国' },\n      { name: '甘露寺', description: '刘备招亲', period: '三国' },\n      { name: '白帝城', description: '刘备托孤', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=刘备',\n      fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=刘备脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹红色底色', duration: 1000, color: '#DC143C' },\n      { id: 2, name: '眉毛', description: '绘制慈眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画慈目轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '胡须', description: '描绘长须', duration: 1200, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加金色装饰', duration: 800, color: '#FFD700' }\n    ],\n    difficulty: 'medium',\n    popularity: 8,\n    tags: ['三国', '帝王', '仁德', '领袖']\n  },\n  {\n    id: 'huangzhong',\n    name: '黄忠脸谱',\n    character: '黄忠',\n    roleCategory: '净',\n    colorCategory: '黄脸',\n    mainColors: ['#FFD700', '#000000', '#DC143C'],\n    culturalBackground: {\n      origin: '三国时期蜀汉五虎上将之一',\n      personality: '老当益壮，勇猛善射，忠心耿耿',\n      symbolism: '老骥伏枥、壮心不已的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '黄色': '老成持重，经验丰富',\n      '黑色': '刚毅坚定，不服老迈',\n      '红色': '壮心不已，热血依然'\n    },\n    relatedOperas: [\n      { name: '定军山', description: '黄忠定军山斩夏侯渊', period: '三国' },\n      { name: '战长沙', description: '黄忠战关羽', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/FFD700/000000?text=黄忠',\n      fullSize: 'https://via.placeholder.com/600x600/FFD700/000000?text=黄忠脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹黄色底色', duration: 1000, color: '#FFD700' },\n      { id: 2, name: '眉毛', description: '绘制白眉', duration: 800, color: '#FFFFFF' },\n      { id: 3, name: '眼部', description: '勾画老目轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '胡须', description: '描绘白须', duration: 1200, color: '#FFFFFF' },\n      { id: 5, name: '装饰', description: '添加红色装饰', duration: 800, color: '#DC143C' }\n    ],\n    difficulty: 'medium',\n    popularity: 7,\n    tags: ['三国', '老将', '勇猛', '忠诚']\n  },\n  {\n    id: 'machao',\n    name: '马超脸谱',\n    character: '马超',\n    roleCategory: '净',\n    colorCategory: '银脸',\n    mainColors: ['#C0C0C0', '#000000', '#DC143C'],\n    culturalBackground: {\n      origin: '三国时期蜀汉五虎上将之一，西凉马腾之子',\n      personality: '英勇善战，威风凛凛，有万夫不当之勇',\n      symbolism: '英勇、威武、西北豪杰的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '银色': '英武不凡，光芒四射',\n      '黑色': '刚毅果敢，威风凛凛',\n      '红色': '热血沸腾，勇猛无敌'\n    },\n    relatedOperas: [\n      { name: '战渭南', description: '马超大战曹操', period: '三国' },\n      { name: '取成都', description: '马超助刘备取成都', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/C0C0C0/000000?text=马超',\n      fullSize: 'https://via.placeholder.com/600x600/C0C0C0/000000?text=马超脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹银色底色', duration: 1000, color: '#C0C0C0' },\n      { id: 2, name: '眉毛', description: '绘制黑色剑眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画鹰目轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘挺直鼻梁', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加红色装饰', duration: 800, color: '#DC143C' }\n    ],\n    difficulty: 'hard',\n    popularity: 7,\n    tags: ['三国', '西凉', '英武', '威猛']\n  },\n  {\n    id: 'zhaoyun',\n    name: '赵云脸谱',\n    character: '赵云',\n    roleCategory: '生',\n    colorCategory: '白脸',\n    mainColors: ['#FFFFFF', '#000000', '#4169E1'],\n    culturalBackground: {\n      origin: '三国时期蜀汉五虎上将之一，常山赵子龙',\n      personality: '英勇善战，忠心耿耿，智勇双全',\n      symbolism: '忠诚、勇敢、完美武将的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '白色': '纯洁忠诚，品格高尚',\n      '黑色': '刚毅果敢，意志坚定',\n      '蓝色': '冷静睿智，深谋远虑'\n    },\n    relatedOperas: [\n      { name: '长坂坡', description: '赵云长坂坡救阿斗', period: '三国' },\n      { name: '截江夺斗', description: '赵云截江救阿斗', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/FFFFFF/000000?text=赵云',\n      fullSize: 'https://via.placeholder.com/600x600/FFFFFF/000000?text=赵云脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹白色底色', duration: 1000, color: '#FFFFFF' },\n      { id: 2, name: '眉毛', description: '绘制黑色剑眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画英目轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘挺直鼻梁', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加蓝色装饰', duration: 800, color: '#4169E1' }\n    ],\n    difficulty: 'medium',\n    popularity: 9,\n    tags: ['三国', '完美', '忠诚', '英勇']\n  },\n  {\n    id: 'sunwukong',\n    name: '孙悟空脸谱',\n    character: '孙悟空',\n    roleCategory: '净',\n    colorCategory: '金脸',\n    mainColors: ['#FFD700', '#DC143C', '#000000'],\n    culturalBackground: {\n      origin: '西游记中的齐天大圣，花果山美猴王',\n      personality: '机智勇敢，神通广大，桀骜不驯',\n      symbolism: '反抗精神、智慧勇敢的象征',\n      historicalPeriod: '神话传说'\n    },\n    colorMeaning: {\n      '金色': '神通广大，法力无边',\n      '红色': '火眼金睛，热情如火',\n      '黑色': '桀骜不驯，不畏权威'\n    },\n    relatedOperas: [\n      { name: '大闹天宫', description: '孙悟空大闹天宫', period: '神话' },\n      { name: '三打白骨精', description: '孙悟空三打白骨精', period: '神话' },\n      { name: '真假美猴王', description: '真假美猴王大战', period: '神话' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/FFD700/000000?text=孙悟空',\n      fullSize: 'https://via.placeholder.com/600x600/FFD700/000000?text=孙悟空脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹金色底色', duration: 1000, color: '#FFD700' },\n      { id: 2, name: '眉毛', description: '绘制火焰眉', duration: 1000, color: '#DC143C' },\n      { id:3, name: '眼部', description: '勾画火眼金睛', duration: 1200, color: '#DC143C' },\n      { id: 4, name: '鼻部', description: '描绘猴鼻', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加神话装饰', duration: 1000, color: '#000000' }\n    ],\n    difficulty: 'hard',\n    popularity: 10,\n    tags: ['西游记', '神话', '反抗', '智慧']\n  }\n];\n\n// 按分类组织的脸谱数据\nexport const masksByRole = {\n  '生': operaMasks.filter(mask => mask.roleCategory === '生'),\n  '旦': operaMasks.filter(mask => mask.roleCategory === '旦'),\n  '净': operaMasks.filter(mask => mask.roleCategory === '净'),\n  '丑': operaMasks.filter(mask => mask.roleCategory === '丑')\n};\n\nexport const masksByColor = {\n  '红脸': operaMasks.filter(mask => mask.colorCategory === '红脸'),\n  '黑脸': operaMasks.filter(mask => mask.colorCategory === '黑脸'),\n  '白脸': operaMasks.filter(mask => mask.colorCategory === '白脸'),\n  '蓝脸': operaMasks.filter(mask => mask.colorCategory === '蓝脸'),\n  '绿脸': operaMasks.filter(mask => mask.colorCategory === '绿脸'),\n  '黄脸': operaMasks.filter(mask => mask.colorCategory === '黄脸'),\n  '金脸': operaMasks.filter(mask => mask.colorCategory === '金脸'),\n  '银脸': operaMasks.filter(mask => mask.colorCategory === '银脸')\n};\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,aAA0B;IACrC;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAa,QAAQ;YAAK;YACtD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;SACpD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;SAC9E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAY,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;YACpD;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;SACrD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAQ,aAAa;gBAAU,QAAQ;YAAK;YACpD;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;SACrD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;SAC9E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;YACpD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;SACrD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;YACpD;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;SACrD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAY,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAQ,aAAa;gBAAW,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAS,aAAa;gBAAW,QAAQ;YAAK;SACvD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAO;YAAM;SAAK;IACjC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,OAAO;YACP,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAQ,aAAa;gBAAY,QAAQ;YAAK;YACtD;gBAAE,MAAM;gBAAO,aAAa;gBAAc,QAAQ;YAAK;YACvD;gBAAE,MAAM;gBAAO,aAAa;gBAAY,QAAQ;YAAK;SACtD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAS,UAAU;gBAAK,OAAO;YAAU;YAC3E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;SAC9E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAQ,aAAa;gBAAW,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;SACpD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;SACpD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAQ,aAAa;gBAAc,QAAQ;YAAK;YACxD;gBAAE,MAAM;gBAAO,aAAa;gBAAQ,QAAQ;YAAK;YACjD;gBAAE,MAAM;gBAAO,aAAa;gBAAQ,QAAQ;YAAK;SAClD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAM,OAAO;YAAU;YAC3E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAa,QAAQ;YAAK;YACtD;gBAAE,MAAM;gBAAO,aAAa;gBAAS,QAAQ;YAAK;SACnD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAM,OAAO;YAAU;YAC3E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAO,aAAa;gBAAY,QAAQ;YAAK;SACtD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAY,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAQ,aAAa;gBAAW,QAAQ;YAAK;SACtD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAQ,aAAa;gBAAW,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAS,aAAa;gBAAY,QAAQ;YAAK;YACvD;gBAAE,MAAM;gBAAS,aAAa;gBAAW,QAAQ;YAAK;SACvD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAS,UAAU;gBAAM,OAAO;YAAU;YAC5E;gBAAE,IAAG;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;SAC9E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAO;YAAM;YAAM;SAAK;IACjC;CACD;AAGM,MAAM,cAAc;IACzB,KAAK,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,YAAY,KAAK;IACrD,KAAK,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,YAAY,KAAK;IACrD,KAAK,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,YAAY,KAAK;IACrD,KAAK,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,YAAY,KAAK;AACvD;AAEO,MAAM,eAAe;IAC1B,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;AACzD", "debugId": null}}, {"offset": {"line": 1277, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/hooks/useAppState.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { MaskFilter, OperaMask } from '@/types/mask';\n\ninterface AppState {\n  // 筛选状态\n  filter: MaskFilter;\n  // 最近查看的脸谱\n  recentlyViewed: string[];\n  // 收藏的脸谱\n  favorites: string[];\n  // 搜索历史\n  searchHistory: string[];\n}\n\nconst STORAGE_KEY = 'beijing-opera-masks-state';\n\nconst defaultState: AppState = {\n  filter: {},\n  recentlyViewed: [],\n  favorites: [],\n  searchHistory: []\n};\n\nexport function useAppState() {\n  const [state, setState] = useState<AppState>(defaultState);\n  \n  // 从localStorage加载状态\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      try {\n        const saved = localStorage.getItem(STORAGE_KEY);\n        if (saved) {\n          const parsedState = JSON.parse(saved);\n          setState(prevState => ({ ...prevState, ...parsedState }));\n        }\n      } catch (error) {\n        console.warn('Failed to load app state from localStorage:', error);\n      }\n    }\n  }, []);\n  \n  // 保存状态到localStorage\n  const saveState = (newState: Partial<AppState>) => {\n    const updatedState = { ...state, ...newState };\n    setState(updatedState);\n    \n    if (typeof window !== 'undefined') {\n      try {\n        localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedState));\n      } catch (error) {\n        console.warn('Failed to save app state to localStorage:', error);\n      }\n    }\n  };\n  \n  // 更新筛选条件\n  const updateFilter = (filter: MaskFilter) => {\n    saveState({ filter });\n  };\n  \n  // 添加到最近查看\n  const addToRecentlyViewed = (maskId: string) => {\n    const updated = [maskId, ...state.recentlyViewed.filter(id => id !== maskId)].slice(0, 10);\n    saveState({ recentlyViewed: updated });\n  };\n  \n  // 切换收藏状态\n  const toggleFavorite = (maskId: string) => {\n    const updated = state.favorites.includes(maskId)\n      ? state.favorites.filter(id => id !== maskId)\n      : [...state.favorites, maskId];\n    saveState({ favorites: updated });\n  };\n  \n  // 添加搜索历史\n  const addToSearchHistory = (searchTerm: string) => {\n    if (!searchTerm.trim()) return;\n    \n    const updated = [\n      searchTerm,\n      ...state.searchHistory.filter(term => term !== searchTerm)\n    ].slice(0, 10);\n    saveState({ searchHistory: updated });\n  };\n  \n  // 清除搜索历史\n  const clearSearchHistory = () => {\n    saveState({ searchHistory: [] });\n  };\n  \n  // 检查是否收藏\n  const isFavorite = (maskId: string) => {\n    return state.favorites.includes(maskId);\n  };\n  \n  // 获取最近查看的脸谱\n  const getRecentlyViewedMasks = (masks: OperaMask[]) => {\n    return state.recentlyViewed\n      .map(id => masks.find(mask => mask.id === id))\n      .filter(Boolean) as OperaMask[];\n  };\n  \n  // 获取收藏的脸谱\n  const getFavoriteMasks = (masks: OperaMask[]) => {\n    return state.favorites\n      .map(id => masks.find(mask => mask.id === id))\n      .filter(Boolean) as OperaMask[];\n  };\n  \n  return {\n    // 状态\n    filter: state.filter,\n    recentlyViewed: state.recentlyViewed,\n    favorites: state.favorites,\n    searchHistory: state.searchHistory,\n    \n    // 操作\n    updateFilter,\n    addToRecentlyViewed,\n    toggleFavorite,\n    addToSearchHistory,\n    clearSearchHistory,\n    \n    // 辅助函数\n    isFavorite,\n    getRecentlyViewedMasks,\n    getFavoriteMasks\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAgBA,MAAM,cAAc;AAEpB,MAAM,eAAyB;IAC7B,QAAQ,CAAC;IACT,gBAAgB,EAAE;IAClB,WAAW,EAAE;IACb,eAAe,EAAE;AACnB;AAEO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IAE7C,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;;IAWF,GAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,YAAY,CAAC;QACjB,MAAM,eAAe;YAAE,GAAG,KAAK;YAAE,GAAG,QAAQ;QAAC;QAC7C,SAAS;QAET;;IAOF;IAEA,SAAS;IACT,MAAM,eAAe,CAAC;QACpB,UAAU;YAAE;QAAO;IACrB;IAEA,UAAU;IACV,MAAM,sBAAsB,CAAC;QAC3B,MAAM,UAAU;YAAC;eAAW,MAAM,cAAc,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;SAAQ,CAAC,KAAK,CAAC,GAAG;QACvF,UAAU;YAAE,gBAAgB;QAAQ;IACtC;IAEA,SAAS;IACT,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,MAAM,SAAS,CAAC,QAAQ,CAAC,UACrC,MAAM,SAAS,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO,UACpC;eAAI,MAAM,SAAS;YAAE;SAAO;QAChC,UAAU;YAAE,WAAW;QAAQ;IACjC;IAEA,SAAS;IACT,MAAM,qBAAqB,CAAC;QAC1B,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,MAAM,UAAU;YACd;eACG,MAAM,aAAa,CAAC,MAAM,CAAC,CAAA,OAAQ,SAAS;SAChD,CAAC,KAAK,CAAC,GAAG;QACX,UAAU;YAAE,eAAe;QAAQ;IACrC;IAEA,SAAS;IACT,MAAM,qBAAqB;QACzB,UAAU;YAAE,eAAe,EAAE;QAAC;IAChC;IAEA,SAAS;IACT,MAAM,aAAa,CAAC;QAClB,OAAO,MAAM,SAAS,CAAC,QAAQ,CAAC;IAClC;IAEA,YAAY;IACZ,MAAM,yBAAyB,CAAC;QAC9B,OAAO,MAAM,cAAc,CACxB,GAAG,CAAC,CAAA,KAAM,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,KACzC,MAAM,CAAC;IACZ;IAEA,UAAU;IACV,MAAM,mBAAmB,CAAC;QACxB,OAAO,MAAM,SAAS,CACnB,GAAG,CAAC,CAAA,KAAM,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,KACzC,MAAM,CAAC;IACZ;IAEA,OAAO;QACL,KAAK;QACL,QAAQ,MAAM,MAAM;QACpB,gBAAgB,MAAM,cAAc;QACpC,WAAW,MAAM,SAAS;QAC1B,eAAe,MAAM,aAAa;QAElC,KAAK;QACL;QACA;QACA;QACA;QACA;QAEA,OAAO;QACP;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1384, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/utils/maskUtils.ts"], "sourcesContent": ["import { OperaMask, MaskFilter, RoleCategory, ColorCategory } from '@/types/mask';\nimport { operaMasks } from '@/data/masks';\n\n/**\n * 根据筛选条件过滤脸谱\n */\nexport function filterMasks(masks: OperaMask[], filter: MaskFilter): OperaMask[] {\n  return masks.filter(mask => {\n    // 按行当筛选\n    if (filter.roleCategory && mask.roleCategory !== filter.roleCategory) {\n      return false;\n    }\n    \n    // 按颜色筛选\n    if (filter.colorCategory && mask.colorCategory !== filter.colorCategory) {\n      return false;\n    }\n    \n    // 按难度筛选\n    if (filter.difficulty && mask.difficulty !== filter.difficulty) {\n      return false;\n    }\n    \n    // 按搜索词筛选\n    if (filter.searchTerm) {\n      const searchTerm = filter.searchTerm.toLowerCase();\n      const searchableText = [\n        mask.name,\n        mask.character,\n        mask.culturalBackground.origin,\n        mask.culturalBackground.personality,\n        ...mask.tags\n      ].join(' ').toLowerCase();\n      \n      if (!searchableText.includes(searchTerm)) {\n        return false;\n      }\n    }\n    \n    return true;\n  });\n}\n\n/**\n * 按受欢迎程度排序脸谱\n */\nexport function sortMasksByPopularity(masks: OperaMask[], ascending = false): OperaMask[] {\n  return [...masks].sort((a, b) => {\n    return ascending ? a.popularity - b.popularity : b.popularity - a.popularity;\n  });\n}\n\n/**\n * 按名称排序脸谱\n */\nexport function sortMasksByName(masks: OperaMask[], ascending = true): OperaMask[] {\n  return [...masks].sort((a, b) => {\n    return ascending ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name);\n  });\n}\n\n/**\n * 获取所有行当分类\n */\nexport function getAllRoleCategories(): RoleCategory[] {\n  return ['生', '旦', '净', '丑'];\n}\n\n/**\n * 获取所有颜色分类\n */\nexport function getAllColorCategories(): ColorCategory[] {\n  return ['红脸', '黑脸', '白脸', '蓝脸', '绿脸', '黄脸', '金脸', '银脸'];\n}\n\n/**\n * 根据行当分组脸谱\n */\nexport function groupMasksByRole(masks: OperaMask[]): Record<RoleCategory, OperaMask[]> {\n  const groups: Record<RoleCategory, OperaMask[]> = {\n    '生': [],\n    '旦': [],\n    '净': [],\n    '丑': []\n  };\n  \n  masks.forEach(mask => {\n    groups[mask.roleCategory].push(mask);\n  });\n  \n  return groups;\n}\n\n/**\n * 根据颜色分组脸谱\n */\nexport function groupMasksByColor(masks: OperaMask[]): Record<ColorCategory, OperaMask[]> {\n  const groups: Record<ColorCategory, OperaMask[]> = {\n    '红脸': [],\n    '黑脸': [],\n    '白脸': [],\n    '蓝脸': [],\n    '绿脸': [],\n    '黄脸': [],\n    '金脸': [],\n    '银脸': []\n  };\n  \n  masks.forEach(mask => {\n    groups[mask.colorCategory].push(mask);\n  });\n  \n  return groups;\n}\n\n/**\n * 获取随机脸谱\n */\nexport function getRandomMasks(count: number, excludeIds: string[] = []): OperaMask[] {\n  const availableMasks = operaMasks.filter(mask => !excludeIds.includes(mask.id));\n  const shuffled = [...availableMasks].sort(() => Math.random() - 0.5);\n  return shuffled.slice(0, count);\n}\n\n/**\n * 根据ID获取脸谱\n */\nexport function getMaskById(id: string): OperaMask | undefined {\n  return operaMasks.find(mask => mask.id === id);\n}\n\n/**\n * 获取相关脸谱（基于标签相似度）\n */\nexport function getRelatedMasks(maskId: string, count: number = 3): OperaMask[] {\n  const currentMask = getMaskById(maskId);\n  if (!currentMask) return [];\n  \n  const otherMasks = operaMasks.filter(mask => mask.id !== maskId);\n  \n  // 计算标签相似度\n  const masksWithScore = otherMasks.map(mask => {\n    const commonTags = mask.tags.filter(tag => currentMask.tags.includes(tag));\n    const score = commonTags.length;\n    return { mask, score };\n  });\n  \n  // 按相似度排序并返回前几个\n  return masksWithScore\n    .sort((a, b) => b.score - a.score)\n    .slice(0, count)\n    .map(item => item.mask);\n}\n\n/**\n * 获取脸谱统计信息\n */\nexport function getMaskStatistics() {\n  const roleStats = groupMasksByRole(operaMasks);\n  const colorStats = groupMasksByColor(operaMasks);\n  \n  return {\n    total: operaMasks.length,\n    byRole: Object.entries(roleStats).map(([role, masks]) => ({\n      category: role,\n      count: masks.length\n    })),\n    byColor: Object.entries(colorStats).map(([color, masks]) => ({\n      category: color,\n      count: masks.length\n    })),\n    averagePopularity: operaMasks.reduce((sum, mask) => sum + mask.popularity, 0) / operaMasks.length,\n    difficultyDistribution: {\n      easy: operaMasks.filter(m => m.difficulty === 'easy').length,\n      medium: operaMasks.filter(m => m.difficulty === 'medium').length,\n      hard: operaMasks.filter(m => m.difficulty === 'hard').length\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AACA;;AAKO,SAAS,YAAY,KAAkB,EAAE,MAAkB;IAChE,OAAO,MAAM,MAAM,CAAC,CAAA;QAClB,QAAQ;QACR,IAAI,OAAO,YAAY,IAAI,KAAK,YAAY,KAAK,OAAO,YAAY,EAAE;YACpE,OAAO;QACT;QAEA,QAAQ;QACR,IAAI,OAAO,aAAa,IAAI,KAAK,aAAa,KAAK,OAAO,aAAa,EAAE;YACvE,OAAO;QACT;QAEA,QAAQ;QACR,IAAI,OAAO,UAAU,IAAI,KAAK,UAAU,KAAK,OAAO,UAAU,EAAE;YAC9D,OAAO;QACT;QAEA,SAAS;QACT,IAAI,OAAO,UAAU,EAAE;YACrB,MAAM,aAAa,OAAO,UAAU,CAAC,WAAW;YAChD,MAAM,iBAAiB;gBACrB,KAAK,IAAI;gBACT,KAAK,SAAS;gBACd,KAAK,kBAAkB,CAAC,MAAM;gBAC9B,KAAK,kBAAkB,CAAC,WAAW;mBAChC,KAAK,IAAI;aACb,CAAC,IAAI,CAAC,KAAK,WAAW;YAEvB,IAAI,CAAC,eAAe,QAAQ,CAAC,aAAa;gBACxC,OAAO;YACT;QACF;QAEA,OAAO;IACT;AACF;AAKO,SAAS,sBAAsB,KAAkB,EAAE,YAAY,KAAK;IACzE,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,OAAO,YAAY,EAAE,UAAU,GAAG,EAAE,UAAU,GAAG,EAAE,UAAU,GAAG,EAAE,UAAU;IAC9E;AACF;AAKO,SAAS,gBAAgB,KAAkB,EAAE,YAAY,IAAI;IAClE,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,OAAO,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;IAC/E;AACF;AAKO,SAAS;IACd,OAAO;QAAC;QAAK;QAAK;QAAK;KAAI;AAC7B;AAKO,SAAS;IACd,OAAO;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;AACzD;AAKO,SAAS,iBAAiB,KAAkB;IACjD,MAAM,SAA4C;QAChD,KAAK,EAAE;QACP,KAAK,EAAE;QACP,KAAK,EAAE;QACP,KAAK,EAAE;IACT;IAEA,MAAM,OAAO,CAAC,CAAA;QACZ,MAAM,CAAC,KAAK,YAAY,CAAC,CAAC,IAAI,CAAC;IACjC;IAEA,OAAO;AACT;AAKO,SAAS,kBAAkB,KAAkB;IAClD,MAAM,SAA6C;QACjD,MAAM,EAAE;QACR,MAAM,EAAE;QACR,MAAM,EAAE;QACR,MAAM,EAAE;QACR,MAAM,EAAE;QACR,MAAM,EAAE;QACR,MAAM,EAAE;QACR,MAAM,EAAE;IACV;IAEA,MAAM,OAAO,CAAC,CAAA;QACZ,MAAM,CAAC,KAAK,aAAa,CAAC,CAAC,IAAI,CAAC;IAClC;IAEA,OAAO;AACT;AAKO,SAAS,eAAe,KAAa,EAAE,aAAuB,EAAE;IACrE,MAAM,iBAAiB,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAA,OAAQ,CAAC,WAAW,QAAQ,CAAC,KAAK,EAAE;IAC7E,MAAM,WAAW;WAAI;KAAe,CAAC,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK;IAChE,OAAO,SAAS,KAAK,CAAC,GAAG;AAC3B;AAKO,SAAS,YAAY,EAAU;IACpC,OAAO,oHAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;AAC7C;AAKO,SAAS,gBAAgB,MAAc,EAAE,QAAgB,CAAC;IAC/D,MAAM,cAAc,YAAY;IAChC,IAAI,CAAC,aAAa,OAAO,EAAE;IAE3B,MAAM,aAAa,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAEzD,UAAU;IACV,MAAM,iBAAiB,WAAW,GAAG,CAAC,CAAA;QACpC,MAAM,aAAa,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,YAAY,IAAI,CAAC,QAAQ,CAAC;QACrE,MAAM,QAAQ,WAAW,MAAM;QAC/B,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,eAAe;IACf,OAAO,eACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG,OACT,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;AAC1B;AAKO,SAAS;IACd,MAAM,YAAY,iBAAiB,oHAAA,CAAA,aAAU;IAC7C,MAAM,aAAa,kBAAkB,oHAAA,CAAA,aAAU;IAE/C,OAAO;QACL,OAAO,oHAAA,CAAA,aAAU,CAAC,MAAM;QACxB,QAAQ,OAAO,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,GAAK,CAAC;gBACxD,UAAU;gBACV,OAAO,MAAM,MAAM;YACrB,CAAC;QACD,SAAS,OAAO,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,GAAK,CAAC;gBAC3D,UAAU;gBACV,OAAO,MAAM,MAAM;YACrB,CAAC;QACD,mBAAmB,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE,KAAK,oHAAA,CAAA,aAAU,CAAC,MAAM;QACjG,wBAAwB;YACtB,MAAM,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,QAAQ,MAAM;YAC5D,QAAQ,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,UAAU,MAAM;YAChE,MAAM,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,QAAQ,MAAM;QAC9D;IACF;AACF", "debugId": null}}, {"offset": {"line": 1543, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/utils/maskImageGenerator.ts"], "sourcesContent": ["// 京剧脸谱图片生成器\n// 使用Canvas API生成高质量的脸谱图片\n\nexport interface MaskImageConfig {\n  id: string;\n  name: string;\n  character: string;\n  mainColors: string[];\n  faceShape: 'oval' | 'round' | 'angular';\n  eyeStyle: 'normal' | 'fierce' | 'gentle';\n  decorations: string[];\n}\n\nexport class MaskImageGenerator {\n  private canvas: HTMLCanvasElement;\n  private ctx: CanvasRenderingContext2D;\n\n  constructor(width: number = 300, height: number = 300) {\n    this.canvas = document.createElement('canvas');\n    this.canvas.width = width;\n    this.canvas.height = height;\n    this.ctx = this.canvas.getContext('2d')!;\n  }\n\n  generateMaskImage(config: MaskImageConfig): string {\n    const { ctx, canvas } = this;\n    const centerX = canvas.width / 2;\n    const centerY = canvas.height / 2;\n\n    // 清空画布\n    ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n    // 设置高质量渲染\n    ctx.imageSmoothingEnabled = true;\n    ctx.imageSmoothingQuality = 'high';\n\n    // 设置统一的背景\n    const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, Math.max(canvas.width, canvas.height) / 2);\n    gradient.addColorStop(0, '#FFFFFF');\n    gradient.addColorStop(1, '#F8F9FA');\n    ctx.fillStyle = gradient;\n    ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n    // 绘制脸部轮廓\n    this.drawFaceShape(config, centerX, centerY);\n\n    // 绘制五官（按正确顺序）\n    this.drawEyebrows(config, centerX, centerY);\n    this.drawEyes(config, centerX, centerY);\n    this.drawNose(config, centerX, centerY);\n    this.drawMouth(config, centerX, centerY);\n\n    // 绘制装饰\n    this.drawDecorations(config, centerX, centerY);\n\n    // 移除角色名称绘制，让脸谱图片更加纯净\n    // this.drawCharacterName(config, centerX, centerY);\n\n    return canvas.toDataURL('image/png', 0.95);\n  }\n\n  private drawFaceShape(config: MaskImageConfig, centerX: number, centerY: number) {\n    const { ctx } = this;\n\n    // 创建更精美的渐变效果\n    const gradient = ctx.createRadialGradient(centerX, centerY - 40, 0, centerX, centerY, 130);\n    gradient.addColorStop(0, config.mainColors[0]);\n    gradient.addColorStop(0.7, config.mainColors[1] || config.mainColors[0]);\n    gradient.addColorStop(1, this.darkenColor(config.mainColors[1] || config.mainColors[0], 0.2));\n\n    ctx.fillStyle = gradient;\n    ctx.strokeStyle = '#2D3748';\n    ctx.lineWidth = 4;\n    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';\n    ctx.shadowBlur = 8;\n    ctx.shadowOffsetX = 2;\n    ctx.shadowOffsetY = 2;\n\n    ctx.beginPath();\n    if (config.faceShape === 'oval') {\n      ctx.ellipse(centerX, centerY - 10, 105, 125, 0, 0, 2 * Math.PI);\n    } else if (config.faceShape === 'round') {\n      ctx.arc(centerX, centerY - 5, 115, 0, 2 * Math.PI);\n    } else {\n      // angular - 更精细的棱角脸型\n      ctx.moveTo(centerX - 95, centerY - 110);\n      ctx.lineTo(centerX + 95, centerY - 110);\n      ctx.lineTo(centerX + 115, centerY - 10);\n      ctx.lineTo(centerX + 95, centerY + 125);\n      ctx.lineTo(centerX - 95, centerY + 125);\n      ctx.lineTo(centerX - 115, centerY - 10);\n      ctx.closePath();\n    }\n    ctx.fill();\n    ctx.stroke();\n\n    // 重置阴影\n    ctx.shadowColor = 'transparent';\n    ctx.shadowBlur = 0;\n    ctx.shadowOffsetX = 0;\n    ctx.shadowOffsetY = 0;\n  }\n\n  // 辅助函数：加深颜色\n  private darkenColor(color: string, factor: number): string {\n    if (color.startsWith('#')) {\n      const hex = color.slice(1);\n      const r = parseInt(hex.substr(0, 2), 16);\n      const g = parseInt(hex.substr(2, 2), 16);\n      const b = parseInt(hex.substr(4, 2), 16);\n\n      const newR = Math.floor(r * (1 - factor));\n      const newG = Math.floor(g * (1 - factor));\n      const newB = Math.floor(b * (1 - factor));\n\n      return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;\n    }\n    return color;\n  }\n\n  private drawEyes(config: MaskImageConfig, centerX: number, centerY: number) {\n    const { ctx } = this;\n    const eyeY = centerY - 20;\n    const eyeWidth = config.eyeStyle === 'fierce' ? 25 : 20;\n    const eyeHeight = config.eyeStyle === 'gentle' ? 12 : 15;\n\n    // 左眼\n    ctx.fillStyle = 'white';\n    ctx.strokeStyle = '#000';\n    ctx.lineWidth = 2;\n    ctx.beginPath();\n    ctx.ellipse(centerX - 35, eyeY, eyeWidth, eyeHeight, 0, 0, 2 * Math.PI);\n    ctx.fill();\n    ctx.stroke();\n\n    // 右眼\n    ctx.beginPath();\n    ctx.ellipse(centerX + 35, eyeY, eyeWidth, eyeHeight, 0, 0, 2 * Math.PI);\n    ctx.fill();\n    ctx.stroke();\n\n    // 眼珠\n    ctx.fillStyle = '#000';\n    ctx.beginPath();\n    ctx.arc(centerX - 35, eyeY, 8, 0, 2 * Math.PI);\n    ctx.fill();\n    ctx.beginPath();\n    ctx.arc(centerX + 35, eyeY, 8, 0, 2 * Math.PI);\n    ctx.fill();\n  }\n\n  private drawEyebrows(config: MaskImageConfig, centerX: number, centerY: number) {\n    const { ctx } = this;\n    const browY = centerY - 50;\n    \n    ctx.strokeStyle = '#000';\n    ctx.lineWidth = config.eyeStyle === 'fierce' ? 8 : 5;\n    ctx.lineCap = 'round';\n\n    // 左眉\n    ctx.beginPath();\n    if (config.eyeStyle === 'fierce') {\n      ctx.moveTo(centerX - 60, browY);\n      ctx.quadraticCurveTo(centerX - 35, browY - 15, centerX - 10, browY);\n    } else {\n      ctx.moveTo(centerX - 55, browY);\n      ctx.quadraticCurveTo(centerX - 35, browY - 10, centerX - 15, browY);\n    }\n    ctx.stroke();\n\n    // 右眉\n    ctx.beginPath();\n    if (config.eyeStyle === 'fierce') {\n      ctx.moveTo(centerX + 10, browY);\n      ctx.quadraticCurveTo(centerX + 35, browY - 15, centerX + 60, browY);\n    } else {\n      ctx.moveTo(centerX + 15, browY);\n      ctx.quadraticCurveTo(centerX + 35, browY - 10, centerX + 55, browY);\n    }\n    ctx.stroke();\n  }\n\n  private drawNose(config: MaskImageConfig, centerX: number, centerY: number) {\n    const { ctx } = this;\n    \n    ctx.strokeStyle = '#000';\n    ctx.lineWidth = 3;\n    ctx.lineCap = 'round';\n\n    ctx.beginPath();\n    ctx.moveTo(centerX, centerY + 10);\n    ctx.lineTo(centerX, centerY + 40);\n    ctx.moveTo(centerX - 8, centerY + 35);\n    ctx.lineTo(centerX + 8, centerY + 35);\n    ctx.stroke();\n  }\n\n  private drawMouth(config: MaskImageConfig, centerX: number, centerY: number) {\n    const { ctx } = this;\n    const mouthY = centerY + 60;\n    \n    ctx.fillStyle = config.mainColors[0] === '#FFFFFF' ? '#FF0000' : '#8B0000';\n    ctx.strokeStyle = '#000';\n    ctx.lineWidth = 2;\n\n    ctx.beginPath();\n    ctx.ellipse(centerX, mouthY, 15, 8, 0, 0, 2 * Math.PI);\n    ctx.fill();\n    ctx.stroke();\n  }\n\n  private drawDecorations(config: MaskImageConfig, centerX: number, centerY: number) {\n    const { ctx } = this;\n    \n    // 根据角色添加特殊装饰\n    if (config.decorations.includes('forehead-mark')) {\n      ctx.fillStyle = '#FFD700';\n      ctx.strokeStyle = '#000';\n      ctx.lineWidth = 2;\n      ctx.beginPath();\n      ctx.moveTo(centerX, centerY - 80);\n      ctx.lineTo(centerX + 10, centerY - 60);\n      ctx.lineTo(centerX - 10, centerY - 60);\n      ctx.closePath();\n      ctx.fill();\n      ctx.stroke();\n    }\n\n    if (config.decorations.includes('cheek-patterns')) {\n      ctx.strokeStyle = config.mainColors[1] || '#000';\n      ctx.lineWidth = 2;\n      \n      // 左脸装饰\n      ctx.beginPath();\n      ctx.arc(centerX - 70, centerY + 20, 15, 0, 2 * Math.PI);\n      ctx.stroke();\n      \n      // 右脸装饰\n      ctx.beginPath();\n      ctx.arc(centerX + 70, centerY + 20, 15, 0, 2 * Math.PI);\n      ctx.stroke();\n    }\n\n    if (config.decorations.includes('beard')) {\n      ctx.strokeStyle = '#000';\n      ctx.lineWidth = 4;\n      ctx.lineCap = 'round';\n      \n      // 胡须线条\n      for (let i = 0; i < 3; i++) {\n        ctx.beginPath();\n        ctx.moveTo(centerX - 60 + i * 20, centerY + 80 + i * 5);\n        ctx.quadraticCurveTo(centerX, centerY + 100 + i * 5, centerX + 60 - i * 20, centerY + 80 + i * 5);\n        ctx.stroke();\n      }\n    }\n  }\n\n  // 已移除角色名称绘制函数，让脸谱图片更加纯净\n  // private drawCharacterName(config: MaskImageConfig, centerX: number, centerY: number) {\n  //   // 功能已移除，角色名称现在显示在卡片的文字区域\n  // }\n}\n\n// 预定义的脸谱配置\nexport const maskConfigs: Record<string, MaskImageConfig> = {\n  guanyu: {\n    id: 'guanyu',\n    name: '关羽',\n    character: '关羽',\n    mainColors: ['#DC2626', '#991B1B'],\n    faceShape: 'oval',\n    eyeStyle: 'fierce',\n    decorations: ['forehead-mark', 'beard']\n  },\n  caocao: {\n    id: 'caocao',\n    name: '曹操',\n    character: '曹操',\n    mainColors: ['#FFFFFF', '#F3F4F6'],\n    faceShape: 'angular',\n    eyeStyle: 'fierce',\n    decorations: ['cheek-patterns']\n  },\n  zhangfei: {\n    id: 'zhangfei',\n    name: '张飞',\n    character: '张飞',\n    mainColors: ['#1F2937', '#000000'],\n    faceShape: 'round',\n    eyeStyle: 'fierce',\n    decorations: ['beard']\n  },\n  huangzhong: {\n    id: 'huangzhong',\n    name: '黄忠',\n    character: '黄忠',\n    mainColors: ['#FFD700', '#F59E0B'],\n    faceShape: 'oval',\n    eyeStyle: 'normal',\n    decorations: ['forehead-mark', 'beard']\n  },\n  diaochan: {\n    id: 'diaochan',\n    name: '貂蝉',\n    character: '貂蝉',\n    mainColors: ['#FFC0CB', '#F8BBD9'],\n    faceShape: 'oval',\n    eyeStyle: 'gentle',\n    decorations: ['forehead-mark']\n  },\n  baozhen: {\n    id: 'baozhen',\n    name: '包拯',\n    character: '包拯',\n    mainColors: ['#000000', '#1F2937'],\n    faceShape: 'round',\n    eyeStyle: 'fierce',\n    decorations: ['forehead-mark']\n  },\n  douerdun: {\n    id: 'douerdun',\n    name: '窦尔敦',\n    character: '窦尔敦',\n    mainColors: ['#1E40AF', '#3B82F6'],\n    faceShape: 'angular',\n    eyeStyle: 'fierce',\n    decorations: ['cheek-patterns']\n  },\n  dianwei: {\n    id: 'dianwei',\n    name: '典韦',\n    character: '典韦',\n    mainColors: ['#7C2D12', '#DC2626'],\n    faceShape: 'round',\n    eyeStyle: 'fierce',\n    decorations: ['beard']\n  },\n  likui: {\n    id: 'likui',\n    name: '李逵',\n    character: '李逵',\n    mainColors: ['#1F2937', '#000000'],\n    faceShape: 'round',\n    eyeStyle: 'fierce',\n    decorations: ['beard']\n  },\n  sunwukong: {\n    id: 'sunwukong',\n    name: '孙悟空',\n    character: '孙悟空',\n    mainColors: ['#F59E0B', '#FFD700'],\n    faceShape: 'oval',\n    eyeStyle: 'fierce',\n    decorations: ['forehead-mark', 'cheek-patterns']\n  },\n  zhubaijie: {\n    id: 'zhubaijie',\n    name: '猪八戒',\n    character: '猪八戒',\n    mainColors: ['#EC4899', '#F472B6'],\n    faceShape: 'round',\n    eyeStyle: 'normal',\n    decorations: ['cheek-patterns']\n  },\n  baigu: {\n    id: 'baigu',\n    name: '白骨精',\n    character: '白骨精',\n    mainColors: ['#F3F4F6', '#E5E7EB'],\n    faceShape: 'angular',\n    eyeStyle: 'fierce',\n    decorations: ['forehead-mark']\n  },\n  huajiangjun: {\n    id: 'huajiangjun',\n    name: '花脸将军',\n    character: '花脸将军',\n    mainColors: ['#7C3AED', '#A855F7'],\n    faceShape: 'oval',\n    eyeStyle: 'fierce',\n    decorations: ['forehead-mark', 'cheek-patterns']\n  },\n  qingyi: {\n    id: 'qingyi',\n    name: '青衣花旦',\n    character: '青衣',\n    mainColors: ['#10B981', '#34D399'],\n    faceShape: 'oval',\n    eyeStyle: 'gentle',\n    decorations: ['forehead-mark']\n  },\n  xiaosheng: {\n    id: 'xiaosheng',\n    name: '小生角色',\n    character: '小生',\n    mainColors: ['#3B82F6', '#60A5FA'],\n    faceShape: 'oval',\n    eyeStyle: 'gentle',\n    decorations: []\n  }\n};\n"], "names": [], "mappings": "AAAA,YAAY;AACZ,yBAAyB;;;;;AAYlB,MAAM;IACH,OAA0B;IAC1B,IAA8B;IAEtC,YAAY,QAAgB,GAAG,EAAE,SAAiB,GAAG,CAAE;QACrD,IAAI,CAAC,MAAM,GAAG,SAAS,aAAa,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG;QACpB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;QACrB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IACpC;IAEA,kBAAkB,MAAuB,EAAU;QACjD,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,IAAI;QAC5B,MAAM,UAAU,OAAO,KAAK,GAAG;QAC/B,MAAM,UAAU,OAAO,MAAM,GAAG;QAEhC,OAAO;QACP,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QAE/C,UAAU;QACV,IAAI,qBAAqB,GAAG;QAC5B,IAAI,qBAAqB,GAAG;QAE5B,UAAU;QACV,MAAM,WAAW,IAAI,oBAAoB,CAAC,SAAS,SAAS,GAAG,SAAS,SAAS,KAAK,GAAG,CAAC,OAAO,KAAK,EAAE,OAAO,MAAM,IAAI;QACzH,SAAS,YAAY,CAAC,GAAG;QACzB,SAAS,YAAY,CAAC,GAAG;QACzB,IAAI,SAAS,GAAG;QAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QAE9C,SAAS;QACT,IAAI,CAAC,aAAa,CAAC,QAAQ,SAAS;QAEpC,cAAc;QACd,IAAI,CAAC,YAAY,CAAC,QAAQ,SAAS;QACnC,IAAI,CAAC,QAAQ,CAAC,QAAQ,SAAS;QAC/B,IAAI,CAAC,QAAQ,CAAC,QAAQ,SAAS;QAC/B,IAAI,CAAC,SAAS,CAAC,QAAQ,SAAS;QAEhC,OAAO;QACP,IAAI,CAAC,eAAe,CAAC,QAAQ,SAAS;QAEtC,qBAAqB;QACrB,oDAAoD;QAEpD,OAAO,OAAO,SAAS,CAAC,aAAa;IACvC;IAEQ,cAAc,MAAuB,EAAE,OAAe,EAAE,OAAe,EAAE;QAC/E,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI;QAEpB,aAAa;QACb,MAAM,WAAW,IAAI,oBAAoB,CAAC,SAAS,UAAU,IAAI,GAAG,SAAS,SAAS;QACtF,SAAS,YAAY,CAAC,GAAG,OAAO,UAAU,CAAC,EAAE;QAC7C,SAAS,YAAY,CAAC,KAAK,OAAO,UAAU,CAAC,EAAE,IAAI,OAAO,UAAU,CAAC,EAAE;QACvE,SAAS,YAAY,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,UAAU,CAAC,EAAE,IAAI,OAAO,UAAU,CAAC,EAAE,EAAE;QAExF,IAAI,SAAS,GAAG;QAChB,IAAI,WAAW,GAAG;QAClB,IAAI,SAAS,GAAG;QAChB,IAAI,WAAW,GAAG;QAClB,IAAI,UAAU,GAAG;QACjB,IAAI,aAAa,GAAG;QACpB,IAAI,aAAa,GAAG;QAEpB,IAAI,SAAS;QACb,IAAI,OAAO,SAAS,KAAK,QAAQ;YAC/B,IAAI,OAAO,CAAC,SAAS,UAAU,IAAI,KAAK,KAAK,GAAG,GAAG,IAAI,KAAK,EAAE;QAChE,OAAO,IAAI,OAAO,SAAS,KAAK,SAAS;YACvC,IAAI,GAAG,CAAC,SAAS,UAAU,GAAG,KAAK,GAAG,IAAI,KAAK,EAAE;QACnD,OAAO;YACL,qBAAqB;YACrB,IAAI,MAAM,CAAC,UAAU,IAAI,UAAU;YACnC,IAAI,MAAM,CAAC,UAAU,IAAI,UAAU;YACnC,IAAI,MAAM,CAAC,UAAU,KAAK,UAAU;YACpC,IAAI,MAAM,CAAC,UAAU,IAAI,UAAU;YACnC,IAAI,MAAM,CAAC,UAAU,IAAI,UAAU;YACnC,IAAI,MAAM,CAAC,UAAU,KAAK,UAAU;YACpC,IAAI,SAAS;QACf;QACA,IAAI,IAAI;QACR,IAAI,MAAM;QAEV,OAAO;QACP,IAAI,WAAW,GAAG;QAClB,IAAI,UAAU,GAAG;QACjB,IAAI,aAAa,GAAG;QACpB,IAAI,aAAa,GAAG;IACtB;IAEA,YAAY;IACJ,YAAY,KAAa,EAAE,MAAc,EAAU;QACzD,IAAI,MAAM,UAAU,CAAC,MAAM;YACzB,MAAM,MAAM,MAAM,KAAK,CAAC;YACxB,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;YACrC,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;YACrC,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;YAErC,MAAM,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM;YACvC,MAAM,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM;YACvC,MAAM,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM;YAEvC,OAAO,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,OAAO,KAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,OAAO,KAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,MAAM;QAC3H;QACA,OAAO;IACT;IAEQ,SAAS,MAAuB,EAAE,OAAe,EAAE,OAAe,EAAE;QAC1E,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI;QACpB,MAAM,OAAO,UAAU;QACvB,MAAM,WAAW,OAAO,QAAQ,KAAK,WAAW,KAAK;QACrD,MAAM,YAAY,OAAO,QAAQ,KAAK,WAAW,KAAK;QAEtD,KAAK;QACL,IAAI,SAAS,GAAG;QAChB,IAAI,WAAW,GAAG;QAClB,IAAI,SAAS,GAAG;QAChB,IAAI,SAAS;QACb,IAAI,OAAO,CAAC,UAAU,IAAI,MAAM,UAAU,WAAW,GAAG,GAAG,IAAI,KAAK,EAAE;QACtE,IAAI,IAAI;QACR,IAAI,MAAM;QAEV,KAAK;QACL,IAAI,SAAS;QACb,IAAI,OAAO,CAAC,UAAU,IAAI,MAAM,UAAU,WAAW,GAAG,GAAG,IAAI,KAAK,EAAE;QACtE,IAAI,IAAI;QACR,IAAI,MAAM;QAEV,KAAK;QACL,IAAI,SAAS,GAAG;QAChB,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,UAAU,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,EAAE;QAC7C,IAAI,IAAI;QACR,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,UAAU,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,EAAE;QAC7C,IAAI,IAAI;IACV;IAEQ,aAAa,MAAuB,EAAE,OAAe,EAAE,OAAe,EAAE;QAC9E,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI;QACpB,MAAM,QAAQ,UAAU;QAExB,IAAI,WAAW,GAAG;QAClB,IAAI,SAAS,GAAG,OAAO,QAAQ,KAAK,WAAW,IAAI;QACnD,IAAI,OAAO,GAAG;QAEd,KAAK;QACL,IAAI,SAAS;QACb,IAAI,OAAO,QAAQ,KAAK,UAAU;YAChC,IAAI,MAAM,CAAC,UAAU,IAAI;YACzB,IAAI,gBAAgB,CAAC,UAAU,IAAI,QAAQ,IAAI,UAAU,IAAI;QAC/D,OAAO;YACL,IAAI,MAAM,CAAC,UAAU,IAAI;YACzB,IAAI,gBAAgB,CAAC,UAAU,IAAI,QAAQ,IAAI,UAAU,IAAI;QAC/D;QACA,IAAI,MAAM;QAEV,KAAK;QACL,IAAI,SAAS;QACb,IAAI,OAAO,QAAQ,KAAK,UAAU;YAChC,IAAI,MAAM,CAAC,UAAU,IAAI;YACzB,IAAI,gBAAgB,CAAC,UAAU,IAAI,QAAQ,IAAI,UAAU,IAAI;QAC/D,OAAO;YACL,IAAI,MAAM,CAAC,UAAU,IAAI;YACzB,IAAI,gBAAgB,CAAC,UAAU,IAAI,QAAQ,IAAI,UAAU,IAAI;QAC/D;QACA,IAAI,MAAM;IACZ;IAEQ,SAAS,MAAuB,EAAE,OAAe,EAAE,OAAe,EAAE;QAC1E,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI;QAEpB,IAAI,WAAW,GAAG;QAClB,IAAI,SAAS,GAAG;QAChB,IAAI,OAAO,GAAG;QAEd,IAAI,SAAS;QACb,IAAI,MAAM,CAAC,SAAS,UAAU;QAC9B,IAAI,MAAM,CAAC,SAAS,UAAU;QAC9B,IAAI,MAAM,CAAC,UAAU,GAAG,UAAU;QAClC,IAAI,MAAM,CAAC,UAAU,GAAG,UAAU;QAClC,IAAI,MAAM;IACZ;IAEQ,UAAU,MAAuB,EAAE,OAAe,EAAE,OAAe,EAAE;QAC3E,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI;QACpB,MAAM,SAAS,UAAU;QAEzB,IAAI,SAAS,GAAG,OAAO,UAAU,CAAC,EAAE,KAAK,YAAY,YAAY;QACjE,IAAI,WAAW,GAAG;QAClB,IAAI,SAAS,GAAG;QAEhB,IAAI,SAAS;QACb,IAAI,OAAO,CAAC,SAAS,QAAQ,IAAI,GAAG,GAAG,GAAG,IAAI,KAAK,EAAE;QACrD,IAAI,IAAI;QACR,IAAI,MAAM;IACZ;IAEQ,gBAAgB,MAAuB,EAAE,OAAe,EAAE,OAAe,EAAE;QACjF,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI;QAEpB,aAAa;QACb,IAAI,OAAO,WAAW,CAAC,QAAQ,CAAC,kBAAkB;YAChD,IAAI,SAAS,GAAG;YAChB,IAAI,WAAW,GAAG;YAClB,IAAI,SAAS,GAAG;YAChB,IAAI,SAAS;YACb,IAAI,MAAM,CAAC,SAAS,UAAU;YAC9B,IAAI,MAAM,CAAC,UAAU,IAAI,UAAU;YACnC,IAAI,MAAM,CAAC,UAAU,IAAI,UAAU;YACnC,IAAI,SAAS;YACb,IAAI,IAAI;YACR,IAAI,MAAM;QACZ;QAEA,IAAI,OAAO,WAAW,CAAC,QAAQ,CAAC,mBAAmB;YACjD,IAAI,WAAW,GAAG,OAAO,UAAU,CAAC,EAAE,IAAI;YAC1C,IAAI,SAAS,GAAG;YAEhB,OAAO;YACP,IAAI,SAAS;YACb,IAAI,GAAG,CAAC,UAAU,IAAI,UAAU,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE;YACtD,IAAI,MAAM;YAEV,OAAO;YACP,IAAI,SAAS;YACb,IAAI,GAAG,CAAC,UAAU,IAAI,UAAU,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE;YACtD,IAAI,MAAM;QACZ;QAEA,IAAI,OAAO,WAAW,CAAC,QAAQ,CAAC,UAAU;YACxC,IAAI,WAAW,GAAG;YAClB,IAAI,SAAS,GAAG;YAChB,IAAI,OAAO,GAAG;YAEd,OAAO;YACP,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBAC1B,IAAI,SAAS;gBACb,IAAI,MAAM,CAAC,UAAU,KAAK,IAAI,IAAI,UAAU,KAAK,IAAI;gBACrD,IAAI,gBAAgB,CAAC,SAAS,UAAU,MAAM,IAAI,GAAG,UAAU,KAAK,IAAI,IAAI,UAAU,KAAK,IAAI;gBAC/F,IAAI,MAAM;YACZ;QACF;IACF;AAMF;AAGO,MAAM,cAA+C;IAC1D,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;YAAiB;SAAQ;IACzC;IACA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;SAAiB;IACjC;IACA,UAAU;QACR,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;SAAQ;IACxB;IACA,YAAY;QACV,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;YAAiB;SAAQ;IACzC;IACA,UAAU;QACR,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;SAAgB;IAChC;IACA,SAAS;QACP,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;SAAgB;IAChC;IACA,UAAU;QACR,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;SAAiB;IACjC;IACA,SAAS;QACP,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;SAAQ;IACxB;IACA,OAAO;QACL,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;SAAQ;IACxB;IACA,WAAW;QACT,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;YAAiB;SAAiB;IAClD;IACA,WAAW;QACT,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;SAAiB;IACjC;IACA,OAAO;QACL,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;SAAgB;IAChC;IACA,aAAa;QACX,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;YAAiB;SAAiB;IAClD;IACA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;SAAgB;IAChC;IACA,WAAW;QACT,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa,EAAE;IACjB;AACF", "debugId": null}}, {"offset": {"line": 1973, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/mask/MaskImage.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { OperaMask } from '@/types/mask';\nimport { MaskImageGenerator, maskConfigs } from '@/utils/maskImageGenerator';\n\ninterface MaskImageProps {\n  mask: OperaMask;\n  width?: number;\n  height?: number;\n  className?: string;\n}\n\n// 真实脸谱图片URL映射\n// 为特定角色使用真实的京剧脸谱图片\nconst maskImageUrls: Record<string, string> = {\n  // 已添加真实脸谱图片的角色\n  'guanyu': 'https://d.bmcx.com/lianpu/d/0072.jpg', // 关羽红脸真实图片\n  'baogong': 'https://d.bmcx.com/lianpu/d/0018.jpg', // 包拯黑脸真实图片\n  'caocao': 'https://d.bmcx.com/lianpu/d/0028.jpg', // 曹操白脸真实图片\n  'zhangfei': 'https://d.bmcx.com/lianpu/d/0324.jpg', // 张飞黑脸真实图片\n  'doulujin': 'https://d.bmcx.com/lianpu/d/0056.jpg', // 窦尔敦蓝脸真实图片\n  'yangqilang': 'https://img1.baidu.com/it/u=348325659,1868481632&fm=253&fmt=auto&app=138&f=JPEG?w=351&h=441', // 杨七郎真实图片\n  'jianggan': 'https://d.bmcx.com/lianpu/d/0117.jpg', // 蒋干白脸真实图片\n  'liubei': 'https://img1.baidu.com/it/u=93431987,3113680563&fm=253&fmt=auto&app=138&f=JPEG?w=412&h=502', // 刘备真实图片\n  'sunwukong': 'https://d.bmcx.com/lianpu/d/0234.jpg', // 孙悟空金脸真实图片\n\n  // 其他角色暂时使用Canvas生成，后续可以逐步替换\n  'huangzhong': '', // 黄忠黄脸 - 使用Canvas生成\n  'diaochan': '', // 貂蝉 - 使用Canvas生成\n  'dianwei': '', // 典韦 - 使用Canvas生成\n  'likui': '', // 李逵 - 使用Canvas生成\n  'zhubaijie': '', // 猪八戒 - 使用Canvas生成\n  'baigu': '', // 白骨精 - 使用Canvas生成\n  'huajiangjun': '', // 花脸将军 - 使用Canvas生成\n  'qingyi': '', // 青衣花旦 - 使用Canvas生成\n  'xiaosheng': '', // 小生角色 - 使用Canvas生成\n  'yangguifei': '', // 杨贵妃 - 使用Canvas生成\n  'machao': '' // 马超 - 使用Canvas生成\n};\n\nexport function MaskImage({ mask, width = 300, height = 300, className }: MaskImageProps) {\n  const [canvasImage, setCanvasImage] = useState<string | null>(null);\n  const [realImage, setRealImage] = useState<string | null>(null);\n  const [imageError, setImageError] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // 获取真实脸谱图片URL\n  const realImageUrl = maskImageUrls[mask.id];\n\n  // 生成Canvas图片作为后备方案\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const config = maskConfigs[mask.id];\n      if (config) {\n        const generator = new MaskImageGenerator(width, height);\n        const generatedImage = generator.generateMaskImage(config);\n        setCanvasImage(generatedImage);\n      } else {\n        // 如果没有配置，使用默认配置\n        const defaultConfig = {\n          id: mask.id,\n          name: mask.name,\n          character: mask.character,\n          mainColors: mask.mainColors,\n          faceShape: 'oval' as const,\n          eyeStyle: 'normal' as const,\n          decorations: ['forehead-mark']\n        };\n        const generator = new MaskImageGenerator(width, height);\n        const generatedImage = generator.generateMaskImage(defaultConfig);\n        setCanvasImage(generatedImage);\n      }\n    }\n  }, [mask.id, mask.name, mask.character, mask.mainColors, width, height]);\n\n  // 如果有真实图片URL，尝试加载真实图片\n  useEffect(() => {\n    if (realImageUrl) {\n      const img = new Image();\n      // 对于外部图片，尝试不设置crossOrigin以避免CORS问题\n      if (realImageUrl.startsWith('http')) {\n        // 外部图片不设置crossOrigin\n      } else {\n        img.crossOrigin = 'anonymous';\n      }\n\n      // 设置加载超时\n      const timeout = setTimeout(() => {\n        setImageError(true);\n        setRealImage(null);\n        setIsLoading(false);\n        console.warn(`图片加载超时: ${realImageUrl}`);\n      }, 5000); // 5秒超时\n\n      img.onload = () => {\n        clearTimeout(timeout);\n        setRealImage(realImageUrl);\n        setImageError(false);\n        setIsLoading(false);\n        console.log(`✅ 成功加载真实脸谱图片: ${mask.name} - ${realImageUrl}`);\n      };\n\n      img.onerror = (error) => {\n        clearTimeout(timeout);\n        setImageError(true);\n        setRealImage(null);\n        setIsLoading(false);\n        console.warn(`❌ 真实脸谱图片加载失败，回退到Canvas生成: ${mask.name} - ${realImageUrl}`, error);\n      };\n\n      console.log(`🔄 开始加载真实脸谱图片: ${mask.name} - ${realImageUrl}`);\n      img.src = realImageUrl;\n\n      // 清理函数\n      return () => {\n        clearTimeout(timeout);\n      };\n    } else {\n      // 没有真实图片URL，直接使用Canvas生成的图片\n      setIsLoading(false);\n    }\n  }, [realImageUrl, mask.name]);\n\n  // 如果正在加载，显示加载状态\n  if (isLoading) {\n    return renderLoadingState();\n  }\n\n  // 优先使用真实图片，如果加载失败或没有真实图片则使用Canvas生成的图片\n  if (realImage && !imageError) {\n    return renderRealImage();\n  } else if (canvasImage) {\n    return renderCanvasImage();\n  } else {\n    return renderLoadingState();\n  }\n\n  // 渲染加载状态\n  function renderLoadingState() {\n    return (\n      <div\n        className={className}\n        style={{\n          width,\n          height,\n          position: 'relative',\n          overflow: 'hidden',\n          borderRadius: '0.5rem',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          backgroundColor: '#F3F4F6',\n          color: '#9CA3AF'\n        }}\n      >\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🎭</div>\n          <div style={{ fontSize: '0.875rem' }}>生成中...</div>\n        </div>\n      </div>\n    );\n  }\n\n  // 真实图片渲染\n  function renderRealImage() {\n    return (\n      <div\n        className={className}\n        style={{\n          width,\n          height,\n          position: 'relative',\n          overflow: 'hidden',\n          borderRadius: '0.5rem',\n          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',\n          transition: 'transform 0.3s ease, box-shadow 0.3s ease'\n        }}\n        title={`${mask.name} - 真实脸谱图片`}\n      >\n        <img\n          src={realImage!}\n          alt={`${mask.name} - ${mask.character}`}\n          style={{\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover',\n            display: 'block'\n          }}\n        />\n\n        {/* 真实图片标识 */}\n        <div\n          style={{\n            position: 'absolute',\n            top: '8px',\n            right: '8px',\n            background: 'rgba(34, 197, 94, 0.8)',\n            color: 'white',\n            fontSize: '10px',\n            padding: '2px 6px',\n            borderRadius: '4px',\n            fontWeight: 'bold'\n          }}\n        >\n          真实脸谱\n        </div>\n\n        {/* 统一的悬停效果 */}\n        <div\n          style={{\n            position: 'absolute',\n            inset: 0,\n            background: 'transparent',\n            transition: 'background 0.3s ease'\n          }}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.background = 'rgba(0, 0, 0, 0.1)';\n            e.currentTarget.parentElement!.style.transform = 'scale(1.02)';\n            e.currentTarget.parentElement!.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.2)';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.background = 'transparent';\n            e.currentTarget.parentElement!.style.transform = 'scale(1)';\n            e.currentTarget.parentElement!.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';\n          }}\n        />\n      </div>\n    );\n  }\n\n  // Canvas生成的图片渲染\n  function renderCanvasImage() {\n    return (\n      <div\n        className={className}\n        style={{\n          width,\n          height,\n          position: 'relative',\n          overflow: 'hidden',\n          borderRadius: '0.5rem',\n          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',\n          transition: 'transform 0.3s ease, box-shadow 0.3s ease'\n        }}\n        title={`${mask.name} - Canvas生成脸谱`}\n      >\n        <img\n          src={canvasImage!}\n          alt={`${mask.name} - ${mask.character}`}\n          style={{\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover',\n            display: 'block'\n          }}\n        />\n\n        {/* Canvas生成标识 */}\n        <div\n          style={{\n            position: 'absolute',\n            top: '8px',\n            right: '8px',\n            background: 'rgba(59, 130, 246, 0.8)',\n            color: 'white',\n            fontSize: '10px',\n            padding: '2px 6px',\n            borderRadius: '4px',\n            fontWeight: 'bold'\n          }}\n        >\n          AI生成\n        </div>\n\n        {/* 统一的悬停效果 */}\n        <div\n          style={{\n            position: 'absolute',\n            inset: 0,\n            background: 'transparent',\n            transition: 'background 0.3s ease'\n          }}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.background = 'rgba(0, 0, 0, 0.1)';\n            e.currentTarget.parentElement!.style.transform = 'scale(1.02)';\n            e.currentTarget.parentElement!.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.2)';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.background = 'transparent';\n            e.currentTarget.parentElement!.style.transform = 'scale(1)';\n            e.currentTarget.parentElement!.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';\n          }}\n        />\n      </div>\n    );\n  }\n\n  // SVG后备方案\n  function generateFallbackSVG() {\n    const baseProps = {\n      width,\n      height,\n      viewBox: \"0 0 300 300\",\n      className,\n      style: { borderRadius: '0.5rem' }\n    };\n\n    // 简化的SVG后备方案\n    return (\n      <svg {...baseProps}>\n        <defs>\n          <radialGradient id={`gradient-${mask.id}`} cx=\"50%\" cy=\"40%\" r=\"60%\">\n            <stop offset=\"0%\" stopColor={mask.mainColors[0]} />\n            <stop offset=\"100%\" stopColor={mask.mainColors[1] || mask.mainColors[0]} />\n          </radialGradient>\n        </defs>\n\n        {/* 脸部轮廓 */}\n        <ellipse\n          cx=\"150\"\n          cy=\"150\"\n          rx=\"120\"\n          ry=\"140\"\n          fill={`url(#gradient-${mask.id})`}\n          stroke=\"#333\"\n          strokeWidth=\"3\"\n        />\n\n        {/* 基础五官 */}\n        <path d=\"M 90 110 Q 120 90 150 110\" stroke=\"#000\" strokeWidth=\"4\" fill=\"none\"/>\n        <path d=\"M 150 110 Q 180 90 210 110\" stroke=\"#000\" strokeWidth=\"4\" fill=\"none\"/>\n        <ellipse cx=\"120\" cy=\"140\" rx=\"15\" ry=\"10\" fill=\"white\" stroke=\"#000\" strokeWidth=\"2\"/>\n        <ellipse cx=\"180\" cy=\"140\" rx=\"15\" ry=\"10\" fill=\"white\" stroke=\"#000\" strokeWidth=\"2\"/>\n        <circle cx=\"120\" cy=\"140\" r=\"6\" fill=\"#000\"/>\n        <circle cx=\"180\" cy=\"140\" r=\"6\" fill=\"#000\"/>\n        <path d=\"M 150 160 L 150 180\" stroke=\"#000\" strokeWidth=\"3\"/>\n        <ellipse cx=\"150\" cy=\"200\" rx=\"10\" ry=\"6\" fill=\"#000\"/>\n\n        {/* 角色名称 */}\n        <text\n          x=\"150\"\n          y=\"260\"\n          textAnchor=\"middle\"\n          fill=\"white\"\n          fontSize=\"24\"\n          fontWeight=\"bold\"\n          fontFamily=\"serif\"\n          stroke=\"#000\"\n          strokeWidth=\"1\"\n        >\n          {mask.character}\n        </text>\n      </svg>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAaA,cAAc;AACd,mBAAmB;AACnB,MAAM,gBAAwC;IAC5C,eAAe;IACf,UAAU;IACV,WAAW;IACX,UAAU;IACV,YAAY;IACZ,YAAY;IACZ,cAAc;IACd,YAAY;IACZ,UAAU;IACV,aAAa;IAEb,4BAA4B;IAC5B,cAAc;IACd,YAAY;IACZ,WAAW;IACX,SAAS;IACT,aAAa;IACb,SAAS;IACT,eAAe;IACf,UAAU;IACV,aAAa;IACb,cAAc;IACd,UAAU,GAAG,kBAAkB;AACjC;AAEO,SAAS,UAAU,EAAE,IAAI,EAAE,QAAQ,GAAG,EAAE,SAAS,GAAG,EAAE,SAAS,EAAkB;IACtF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,cAAc;IACd,MAAM,eAAe,aAAa,CAAC,KAAK,EAAE,CAAC;IAE3C,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;;IAsBF,GAAG;QAAC,KAAK,EAAE;QAAE,KAAK,IAAI;QAAE,KAAK,SAAS;QAAE,KAAK,UAAU;QAAE;QAAO;KAAO;IAEvE,sBAAsB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc;YAChB,MAAM,MAAM,IAAI;YAChB,mCAAmC;YACnC,IAAI,aAAa,UAAU,CAAC,SAAS;YACnC,qBAAqB;YACvB,OAAO;gBACL,IAAI,WAAW,GAAG;YACpB;YAEA,SAAS;YACT,MAAM,UAAU,WAAW;gBACzB,cAAc;gBACd,aAAa;gBACb,aAAa;gBACb,QAAQ,IAAI,CAAC,CAAC,QAAQ,EAAE,cAAc;YACxC,GAAG,OAAO,OAAO;YAEjB,IAAI,MAAM,GAAG;gBACX,aAAa;gBACb,aAAa;gBACb,cAAc;gBACd,aAAa;gBACb,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE,cAAc;YAC5D;YAEA,IAAI,OAAO,GAAG,CAAC;gBACb,aAAa;gBACb,cAAc;gBACd,aAAa;gBACb,aAAa;gBACb,QAAQ,IAAI,CAAC,CAAC,0BAA0B,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE,cAAc,EAAE;YAC3E;YAEA,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE,cAAc;YAC3D,IAAI,GAAG,GAAG;YAEV,OAAO;YACP,OAAO;gBACL,aAAa;YACf;QACF,OAAO;YACL,4BAA4B;YAC5B,aAAa;QACf;IACF,GAAG;QAAC;QAAc,KAAK,IAAI;KAAC;IAE5B,gBAAgB;IAChB,IAAI,WAAW;QACb,OAAO;IACT;IAEA,uCAAuC;IACvC,IAAI,aAAa,CAAC,YAAY;QAC5B,OAAO;IACT,OAAO,IAAI,aAAa;QACtB,OAAO;IACT,OAAO;QACL,OAAO;IACT;;;IAEA,SAAS;IACT,SAAS;QACP,qBACE,8OAAC;YACC,WAAW;YACX,OAAO;gBACL;gBACA;gBACA,UAAU;gBACV,UAAU;gBACV,cAAc;gBACd,SAAS;gBACT,YAAY;gBACZ,gBAAgB;gBAChB,iBAAiB;gBACjB,OAAO;YACT;sBAEA,cAAA,8OAAC;gBAAI,OAAO;oBAAE,WAAW;gBAAS;;kCAChC,8OAAC;wBAAI,OAAO;4BAAE,UAAU;4BAAQ,cAAc;wBAAS;kCAAG;;;;;;kCAC1D,8OAAC;wBAAI,OAAO;4BAAE,UAAU;wBAAW;kCAAG;;;;;;;;;;;;;;;;;IAI9C;IAEA,SAAS;IACT,SAAS;QACP,qBACE,8OAAC;YACC,WAAW;YACX,OAAO;gBACL;gBACA;gBACA,UAAU;gBACV,UAAU;gBACV,cAAc;gBACd,WAAW;gBACX,YAAY;YACd;YACA,OAAO,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC;;8BAE9B,8OAAC;oBACC,KAAK;oBACL,KAAK,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,KAAK,SAAS,EAAE;oBACvC,OAAO;wBACL,OAAO;wBACP,QAAQ;wBACR,WAAW;wBACX,SAAS;oBACX;;;;;;8BAIF,8OAAC;oBACC,OAAO;wBACL,UAAU;wBACV,KAAK;wBACL,OAAO;wBACP,YAAY;wBACZ,OAAO;wBACP,UAAU;wBACV,SAAS;wBACT,cAAc;wBACd,YAAY;oBACd;8BACD;;;;;;8BAKD,8OAAC;oBACC,OAAO;wBACL,UAAU;wBACV,OAAO;wBACP,YAAY;wBACZ,YAAY;oBACd;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;wBACnC,EAAE,aAAa,CAAC,aAAa,CAAE,KAAK,CAAC,SAAS,GAAG;wBACjD,EAAE,aAAa,CAAC,aAAa,CAAE,KAAK,CAAC,SAAS,GAAG;oBACnD;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;wBACnC,EAAE,aAAa,CAAC,aAAa,CAAE,KAAK,CAAC,SAAS,GAAG;wBACjD,EAAE,aAAa,CAAC,aAAa,CAAE,KAAK,CAAC,SAAS,GAAG;oBACnD;;;;;;;;;;;;IAIR;IAEA,gBAAgB;IAChB,SAAS;QACP,qBACE,8OAAC;YACC,WAAW;YACX,OAAO;gBACL;gBACA;gBACA,UAAU;gBACV,UAAU;gBACV,cAAc;gBACd,WAAW;gBACX,YAAY;YACd;YACA,OAAO,GAAG,KAAK,IAAI,CAAC,aAAa,CAAC;;8BAElC,8OAAC;oBACC,KAAK;oBACL,KAAK,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,KAAK,SAAS,EAAE;oBACvC,OAAO;wBACL,OAAO;wBACP,QAAQ;wBACR,WAAW;wBACX,SAAS;oBACX;;;;;;8BAIF,8OAAC;oBACC,OAAO;wBACL,UAAU;wBACV,KAAK;wBACL,OAAO;wBACP,YAAY;wBACZ,OAAO;wBACP,UAAU;wBACV,SAAS;wBACT,cAAc;wBACd,YAAY;oBACd;8BACD;;;;;;8BAKD,8OAAC;oBACC,OAAO;wBACL,UAAU;wBACV,OAAO;wBACP,YAAY;wBACZ,YAAY;oBACd;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;wBACnC,EAAE,aAAa,CAAC,aAAa,CAAE,KAAK,CAAC,SAAS,GAAG;wBACjD,EAAE,aAAa,CAAC,aAAa,CAAE,KAAK,CAAC,SAAS,GAAG;oBACnD;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;wBACnC,EAAE,aAAa,CAAC,aAAa,CAAE,KAAK,CAAC,SAAS,GAAG;wBACjD,EAAE,aAAa,CAAC,aAAa,CAAE,KAAK,CAAC,SAAS,GAAG;oBACnD;;;;;;;;;;;;IAIR;IAEA,UAAU;IACV,SAAS;QACP,MAAM,YAAY;YAChB;YACA;YACA,SAAS;YACT;YACA,OAAO;gBAAE,cAAc;YAAS;QAClC;QAEA,aAAa;QACb,qBACE,8OAAC;YAAK,GAAG,SAAS;;8BAChB,8OAAC;8BACC,cAAA,8OAAC;wBAAe,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;wBAAE,IAAG;wBAAM,IAAG;wBAAM,GAAE;;0CAC7D,8OAAC;gCAAK,QAAO;gCAAK,WAAW,KAAK,UAAU,CAAC,EAAE;;;;;;0CAC/C,8OAAC;gCAAK,QAAO;gCAAO,WAAW,KAAK,UAAU,CAAC,EAAE,IAAI,KAAK,UAAU,CAAC,EAAE;;;;;;;;;;;;;;;;;8BAK3E,8OAAC;oBACC,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,MAAM,CAAC,cAAc,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;oBACjC,QAAO;oBACP,aAAY;;;;;;8BAId,8OAAC;oBAAK,GAAE;oBAA4B,QAAO;oBAAO,aAAY;oBAAI,MAAK;;;;;;8BACvE,8OAAC;oBAAK,GAAE;oBAA6B,QAAO;oBAAO,aAAY;oBAAI,MAAK;;;;;;8BACxE,8OAAC;oBAAQ,IAAG;oBAAM,IAAG;oBAAM,IAAG;oBAAK,IAAG;oBAAK,MAAK;oBAAQ,QAAO;oBAAO,aAAY;;;;;;8BAClF,8OAAC;oBAAQ,IAAG;oBAAM,IAAG;oBAAM,IAAG;oBAAK,IAAG;oBAAK,MAAK;oBAAQ,QAAO;oBAAO,aAAY;;;;;;8BAClF,8OAAC;oBAAO,IAAG;oBAAM,IAAG;oBAAM,GAAE;oBAAI,MAAK;;;;;;8BACrC,8OAAC;oBAAO,IAAG;oBAAM,IAAG;oBAAM,GAAE;oBAAI,MAAK;;;;;;8BACrC,8OAAC;oBAAK,GAAE;oBAAsB,QAAO;oBAAO,aAAY;;;;;;8BACxD,8OAAC;oBAAQ,IAAG;oBAAM,IAAG;oBAAM,IAAG;oBAAK,IAAG;oBAAI,MAAK;;;;;;8BAG/C,8OAAC;oBACC,GAAE;oBACF,GAAE;oBACF,YAAW;oBACX,MAAK;oBACL,UAAS;oBACT,YAAW;oBACX,YAAW;oBACX,QAAO;oBACP,aAAY;8BAEX,KAAK,SAAS;;;;;;;;;;;;IAIvB;AACF", "debugId": null}}, {"offset": {"line": 2469, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/ui/Typography.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\n// 字体样式配置\nexport const fontStyles = {\n  // 主标题 - 使用马善政字体（传统书法风格）\n  mainTitle: {\n    fontFamily: 'var(--font-ma-shan-zheng), \"<PERSON>\", \"<PERSON>Ti\", \"楷体\", cursive',\n    fontSize: '2.5rem',\n    fontWeight: '400',\n    lineHeight: '1.2',\n    color: '#B91C1C',\n    textShadow: '2px 2px 4px rgba(0,0,0,0.1)'\n  },\n  \n  // 页面标题 - 使用思源宋体\n  pageTitle: {\n    fontFamily: 'var(--font-noto-serif-sc), \"Noto Serif SC\", \"SimSun\", \"宋体\", serif',\n    fontSize: '2rem',\n    fontWeight: '700',\n    lineHeight: '1.3',\n    color: '#1F2937'\n  },\n  \n  // 章节标题\n  sectionTitle: {\n    fontFamily: 'var(--font-noto-serif-sc), \"Noto Serif SC\", \"SimSun\", \"宋体\", serif',\n    fontSize: '1.5rem',\n    fontWeight: '600',\n    lineHeight: '1.4',\n    color: '#374151'\n  },\n  \n  // 子标题\n  subtitle: {\n    fontFamily: 'var(--font-noto-serif-sc), \"Noto Serif SC\", \"SimSun\", \"宋体\", serif',\n    fontSize: '1.25rem',\n    fontWeight: '500',\n    lineHeight: '1.5',\n    color: '#4B5563'\n  },\n  \n  // 正文 - 使用思源黑体\n  body: {\n    fontFamily: 'var(--font-noto-sans-sc), \"Noto Sans SC\", \"Microsoft YaHei\", \"微软雅黑\", sans-serif',\n    fontSize: '1rem',\n    fontWeight: '400',\n    lineHeight: '1.6',\n    color: '#6B7280'\n  },\n  \n  // 重要文本\n  emphasis: {\n    fontFamily: 'var(--font-noto-serif-sc), \"Noto Serif SC\", \"SimSun\", \"宋体\", serif',\n    fontSize: '1rem',\n    fontWeight: '600',\n    lineHeight: '1.6',\n    color: '#374151'\n  },\n  \n  // 小字说明\n  caption: {\n    fontFamily: 'var(--font-noto-sans-sc), \"Noto Sans SC\", \"Microsoft YaHei\", \"微软雅黑\", sans-serif',\n    fontSize: '0.875rem',\n    fontWeight: '400',\n    lineHeight: '1.5',\n    color: '#9CA3AF'\n  },\n  \n  // 按钮文字\n  button: {\n    fontFamily: 'var(--font-noto-sans-sc), \"Noto Sans SC\", \"Microsoft YaHei\", \"微软雅黑\", sans-serif',\n    fontSize: '0.875rem',\n    fontWeight: '500',\n    lineHeight: '1.4'\n  }\n};\n\n// 字体组件\ninterface TypographyProps {\n  variant: keyof typeof fontStyles;\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n  as?: keyof React.JSX.IntrinsicElements;\n}\n\nexport function Typography({ \n  variant, \n  children, \n  className, \n  style, \n  as = 'div' \n}: TypographyProps) {\n  const Component = as;\n  const variantStyle = fontStyles[variant];\n  \n  return (\n    <Component\n      className={className}\n      style={{\n        ...variantStyle,\n        ...style\n      }}\n    >\n      {children}\n    </Component>\n  );\n}\n\n// 预定义的字体组件\nexport function MainTitle({ children, ...props }: Omit<TypographyProps, 'variant'>) {\n  return <Typography variant=\"mainTitle\" as=\"h1\" {...props}>{children}</Typography>;\n}\n\nexport function PageTitle({ children, ...props }: Omit<TypographyProps, 'variant'>) {\n  return <Typography variant=\"pageTitle\" as=\"h1\" {...props}>{children}</Typography>;\n}\n\nexport function SectionTitle({ children, ...props }: Omit<TypographyProps, 'variant'>) {\n  return <Typography variant=\"sectionTitle\" as=\"h2\" {...props}>{children}</Typography>;\n}\n\nexport function Subtitle({ children, ...props }: Omit<TypographyProps, 'variant'>) {\n  return <Typography variant=\"subtitle\" as=\"h3\" {...props}>{children}</Typography>;\n}\n\nexport function BodyText({ children, ...props }: Omit<TypographyProps, 'variant'>) {\n  return <Typography variant=\"body\" as=\"p\" {...props}>{children}</Typography>;\n}\n\nexport function EmphasisText({ children, ...props }: Omit<TypographyProps, 'variant'>) {\n  return <Typography variant=\"emphasis\" as=\"span\" {...props}>{children}</Typography>;\n}\n\nexport function CaptionText({ children, ...props }: Omit<TypographyProps, 'variant'>) {\n  return <Typography variant=\"caption\" as=\"span\" {...props}>{children}</Typography>;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAKO,MAAM,aAAa;IACxB,wBAAwB;IACxB,WAAW;QACT,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,YAAY;IACd;IAEA,gBAAgB;IAChB,WAAW;QACT,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,OAAO;IACT;IAEA,OAAO;IACP,cAAc;QACZ,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,OAAO;IACT;IAEA,MAAM;IACN,UAAU;QACR,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,OAAO;IACT;IAEA,cAAc;IACd,MAAM;QACJ,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,OAAO;IACT;IAEA,OAAO;IACP,UAAU;QACR,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,OAAO;IACT;IAEA,OAAO;IACP,SAAS;QACP,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,OAAO;IACT;IAEA,OAAO;IACP,QAAQ;QACN,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,YAAY;IACd;AACF;AAWO,SAAS,WAAW,EACzB,OAAO,EACP,QAAQ,EACR,SAAS,EACT,KAAK,EACL,KAAK,KAAK,EACM;IAChB,MAAM,YAAY;IAClB,MAAM,eAAe,UAAU,CAAC,QAAQ;IAExC,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;YACL,GAAG,YAAY;YACf,GAAG,KAAK;QACV;kBAEC;;;;;;AAGP;AAGO,SAAS,UAAU,EAAE,QAAQ,EAAE,GAAG,OAAyC;IAChF,qBAAO,8OAAC;QAAW,SAAQ;QAAY,IAAG;QAAM,GAAG,KAAK;kBAAG;;;;;;AAC7D;AAEO,SAAS,UAAU,EAAE,QAAQ,EAAE,GAAG,OAAyC;IAChF,qBAAO,8OAAC;QAAW,SAAQ;QAAY,IAAG;QAAM,GAAG,KAAK;kBAAG;;;;;;AAC7D;AAEO,SAAS,aAAa,EAAE,QAAQ,EAAE,GAAG,OAAyC;IACnF,qBAAO,8OAAC;QAAW,SAAQ;QAAe,IAAG;QAAM,GAAG,KAAK;kBAAG;;;;;;AAChE;AAEO,SAAS,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAyC;IAC/E,qBAAO,8OAAC;QAAW,SAAQ;QAAW,IAAG;QAAM,GAAG,KAAK;kBAAG;;;;;;AAC5D;AAEO,SAAS,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAyC;IAC/E,qBAAO,8OAAC;QAAW,SAAQ;QAAO,IAAG;QAAK,GAAG,KAAK;kBAAG;;;;;;AACvD;AAEO,SAAS,aAAa,EAAE,QAAQ,EAAE,GAAG,OAAyC;IACnF,qBAAO,8OAAC;QAAW,SAAQ;QAAW,IAAG;QAAQ,GAAG,KAAK;kBAAG;;;;;;AAC9D;AAEO,SAAS,YAAY,EAAE,QAAQ,EAAE,GAAG,OAAyC;IAClF,qBAAO,8OAAC;QAAW,SAAQ;QAAU,IAAG;QAAQ,GAAG,KAAK;kBAAG;;;;;;AAC7D", "debugId": null}}, {"offset": {"line": 2653, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/ui/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useTheme } from '@/components/providers/ThemeProvider';\n\ninterface ThemeToggleProps {\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function ThemeToggle({ className, style }: ThemeToggleProps) {\n  const { theme, toggleTheme, colors } = useTheme();\n  \n  return (\n    <button\n      onClick={toggleTheme}\n      className={className}\n      style={{\n        backgroundColor: 'transparent',\n        border: `2px solid ${colors.border}`,\n        borderRadius: '0.5rem',\n        padding: '0.5rem',\n        cursor: 'pointer',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        transition: 'all 0.3s ease',\n        color: colors.textPrimary,\n        ...style\n      }}\n      onMouseEnter={(e) => {\n        e.currentTarget.style.borderColor = colors.primary;\n        e.currentTarget.style.backgroundColor = colors.backgroundTertiary;\n      }}\n      onMouseLeave={(e) => {\n        e.currentTarget.style.borderColor = colors.border;\n        e.currentTarget.style.backgroundColor = 'transparent';\n      }}\n      title={theme === 'light' ? '切换到深色模式' : '切换到浅色模式'}\n    >\n      {theme === 'light' ? (\n        // 月亮图标 (深色模式)\n        <svg\n          width=\"20\"\n          height=\"20\"\n          viewBox=\"0 0 24 24\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        >\n          <path d=\"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\" />\n        </svg>\n      ) : (\n        // 太阳图标 (浅色模式)\n        <svg\n          width=\"20\"\n          height=\"20\"\n          viewBox=\"0 0 24 24\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        >\n          <circle cx=\"12\" cy=\"12\" r=\"5\" />\n          <path d=\"M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42\" />\n        </svg>\n      )}\n    </button>\n  );\n}\n\n// 带文字的主题切换组件\nexport function ThemeToggleWithText({ className, style }: ThemeToggleProps) {\n  const { theme, toggleTheme, colors } = useTheme();\n  \n  return (\n    <button\n      onClick={toggleTheme}\n      className={className}\n      style={{\n        backgroundColor: 'transparent',\n        border: `2px solid ${colors.primary}`,\n        borderRadius: '0.5rem',\n        padding: '0.5rem 1rem',\n        cursor: 'pointer',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem',\n        transition: 'all 0.3s ease',\n        color: colors.primary,\n        fontSize: '0.875rem',\n        fontWeight: '500',\n        ...style\n      }}\n      onMouseEnter={(e) => {\n        e.currentTarget.style.backgroundColor = colors.primary;\n        e.currentTarget.style.color = colors.background;\n      }}\n      onMouseLeave={(e) => {\n        e.currentTarget.style.backgroundColor = 'transparent';\n        e.currentTarget.style.color = colors.primary;\n      }}\n    >\n      {theme === 'light' ? (\n        <>\n          <svg\n            width=\"16\"\n            height=\"16\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            strokeWidth=\"2\"\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n          >\n            <path d=\"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\" />\n          </svg>\n          深色模式\n        </>\n      ) : (\n        <>\n          <svg\n            width=\"16\"\n            height=\"16\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            strokeWidth=\"2\"\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n          >\n            <circle cx=\"12\" cy=\"12\" r=\"5\" />\n            <path d=\"M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42\" />\n          </svg>\n          浅色模式\n        </>\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAUO,SAAS,YAAY,EAAE,SAAS,EAAE,KAAK,EAAoB;IAChE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAE9C,qBACE,8OAAC;QACC,SAAS;QACT,WAAW;QACX,OAAO;YACL,iBAAiB;YACjB,QAAQ,CAAC,UAAU,EAAE,OAAO,MAAM,EAAE;YACpC,cAAc;YACd,SAAS;YACT,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,gBAAgB;YAChB,YAAY;YACZ,OAAO,OAAO,WAAW;YACzB,GAAG,KAAK;QACV;QACA,cAAc,CAAC;YACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG,OAAO,OAAO;YAClD,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,OAAO,kBAAkB;QACnE;QACA,cAAc,CAAC;YACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG,OAAO,MAAM;YACjD,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;QAC1C;QACA,OAAO,UAAU,UAAU,YAAY;kBAEtC,UAAU,UACT,cAAc;sBACd,8OAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,QAAO;YACP,aAAY;YACZ,eAAc;YACd,gBAAe;sBAEf,cAAA,8OAAC;gBAAK,GAAE;;;;;;;;;;mBAGV,cAAc;sBACd,8OAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,QAAO;YACP,aAAY;YACZ,eAAc;YACd,gBAAe;;8BAEf,8OAAC;oBAAO,IAAG;oBAAK,IAAG;oBAAK,GAAE;;;;;;8BAC1B,8OAAC;oBAAK,GAAE;;;;;;;;;;;;;;;;;AAKlB;AAGO,SAAS,oBAAoB,EAAE,SAAS,EAAE,KAAK,EAAoB;IACxE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAE9C,qBACE,8OAAC;QACC,SAAS;QACT,WAAW;QACX,OAAO;YACL,iBAAiB;YACjB,QAAQ,CAAC,UAAU,EAAE,OAAO,OAAO,EAAE;YACrC,cAAc;YACd,SAAS;YACT,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,KAAK;YACL,YAAY;YACZ,OAAO,OAAO,OAAO;YACrB,UAAU;YACV,YAAY;YACZ,GAAG,KAAK;QACV;QACA,cAAc,CAAC;YACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,OAAO,OAAO;YACtD,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO,UAAU;QACjD;QACA,cAAc,CAAC;YACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;YACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO,OAAO;QAC9C;kBAEC,UAAU,wBACT;;8BACE,8OAAC;oBACC,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,gBAAe;8BAEf,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;gBACJ;;yCAIR;;8BACE,8OAAC;oBACC,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,gBAAe;;sCAEf,8OAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;;;;;;sCAC1B,8OAAC;4BAAK,GAAE;;;;;;;;;;;;gBACJ;;;;;;;;AAMhB", "debugId": null}}, {"offset": {"line": 2849, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/decorations/TraditionalDecorations.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\n// 传统云纹装饰\nexport function CloudPattern({ \n  width = 100, \n  height = 50, \n  color = '#F59E0B',\n  opacity = 0.3,\n  className \n}: {\n  width?: number;\n  height?: number;\n  color?: string;\n  opacity?: number;\n  className?: string;\n}) {\n  return (\n    <svg\n      width={width}\n      height={height}\n      viewBox=\"0 0 100 50\"\n      className={className}\n      style={{ opacity }}\n    >\n      <path\n        d=\"M10,25 Q20,10 35,25 Q50,40 65,25 Q80,10 90,25\"\n        stroke={color}\n        strokeWidth=\"2\"\n        fill=\"none\"\n        strokeLinecap=\"round\"\n      />\n      <path\n        d=\"M15,35 Q25,20 40,35 Q55,50 70,35 Q85,20 95,35\"\n        stroke={color}\n        strokeWidth=\"1.5\"\n        fill=\"none\"\n        strokeLinecap=\"round\"\n        opacity=\"0.7\"\n      />\n    </svg>\n  );\n}\n\n// 传统回纹装饰\nexport function GeometricPattern({\n  size = 60,\n  color = '#B91C1C',\n  opacity = 0.2,\n  className\n}: {\n  size?: number;\n  color?: string;\n  opacity?: number;\n  className?: string;\n}) {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 60 60\"\n      className={className}\n      style={{ opacity }}\n    >\n      <rect x=\"10\" y=\"10\" width=\"40\" height=\"40\" stroke={color} strokeWidth=\"2\" fill=\"none\" />\n      <rect x=\"20\" y=\"20\" width=\"20\" height=\"20\" stroke={color} strokeWidth=\"1.5\" fill=\"none\" />\n      <circle cx=\"30\" cy=\"30\" r=\"5\" stroke={color} strokeWidth=\"1\" fill=\"none\" />\n      <path d=\"M15,15 L45,45 M45,15 L15,45\" stroke={color} strokeWidth=\"0.5\" opacity=\"0.5\" />\n    </svg>\n  );\n}\n\n// 传统花卉装饰\nexport function FloralPattern({\n  size = 80,\n  color = '#DC2626',\n  opacity = 0.25,\n  className\n}: {\n  size?: number;\n  color?: string;\n  opacity?: number;\n  className?: string;\n}) {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 80 80\"\n      className={className}\n      style={{ opacity }}\n    >\n      <g transform=\"translate(40,40)\">\n        {/* 花瓣 */}\n        {[0, 60, 120, 180, 240, 300].map((angle, index) => (\n          <g key={index} transform={`rotate(${angle})`}>\n            <ellipse\n              cx=\"0\"\n              cy=\"-20\"\n              rx=\"8\"\n              ry=\"15\"\n              fill={color}\n              opacity=\"0.6\"\n            />\n          </g>\n        ))}\n        {/* 花心 */}\n        <circle cx=\"0\" cy=\"0\" r=\"6\" fill={color} opacity=\"0.8\" />\n      </g>\n    </svg>\n  );\n}\n\n// 传统边框装饰\nexport function TraditionalBorder({\n  width = '100%',\n  height = 4,\n  color = '#F59E0B',\n  className\n}: {\n  width?: string | number;\n  height?: number;\n  color?: string;\n  className?: string;\n}) {\n  return (\n    <div\n      className={className}\n      style={{\n        width,\n        height: `${height}px`,\n        background: `linear-gradient(90deg, transparent 0%, ${color} 20%, ${color} 80%, transparent 100%)`,\n        position: 'relative'\n      }}\n    >\n      <div\n        style={{\n          position: 'absolute',\n          top: '50%',\n          left: '50%',\n          transform: 'translate(-50%, -50%)',\n          width: '60px',\n          height: '8px',\n          background: color,\n          borderRadius: '4px'\n        }}\n      />\n    </div>\n  );\n}\n\n// 水墨风格背景装饰\nexport function InkWashBackground({\n  className,\n  opacity = 0.05\n}: {\n  className?: string;\n  opacity?: number;\n}) {\n  return (\n    <div\n      className={className}\n      style={{\n        position: 'absolute',\n        inset: 0,\n        opacity,\n        pointerEvents: 'none',\n        background: `\n          radial-gradient(circle at 20% 30%, rgba(185, 28, 28, 0.3) 0%, transparent 50%),\n          radial-gradient(circle at 80% 70%, rgba(245, 158, 11, 0.2) 0%, transparent 50%),\n          radial-gradient(circle at 60% 20%, rgba(31, 41, 55, 0.1) 0%, transparent 40%)\n        `\n      }}\n    />\n  );\n}\n\n// 动画装饰容器\nexport function AnimatedDecoration({\n  children,\n  delay = 0,\n  duration = 2000,\n  className\n}: {\n  children: React.ReactNode;\n  delay?: number;\n  duration?: number;\n  className?: string;\n}) {\n  return (\n    <div\n      className={className}\n      style={{\n        animation: `fadeInUp ${duration}ms ease-out ${delay}ms both`\n      }}\n    >\n      {children}\n      <style jsx>{`\n        @keyframes fadeInUp {\n          from {\n            opacity: 0;\n            transform: translateY(30px);\n          }\n          to {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n      `}</style>\n    </div>\n  );\n}\n\n// 悬浮动画装饰\nexport function FloatingDecoration({\n  children,\n  duration = 3000,\n  className\n}: {\n  children: React.ReactNode;\n  duration?: number;\n  className?: string;\n}) {\n  return (\n    <div\n      className={className}\n      style={{\n        animation: `float ${duration}ms ease-in-out infinite`\n      }}\n    >\n      {children}\n      <style jsx>{`\n        @keyframes float {\n          0%, 100% {\n            transform: translateY(0px);\n          }\n          50% {\n            transform: translateY(-10px);\n          }\n        }\n      `}</style>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;AAKO,SAAS,aAAa,EAC3B,QAAQ,GAAG,EACX,SAAS,EAAE,EACX,QAAQ,SAAS,EACjB,UAAU,GAAG,EACb,SAAS,EAOV;IACC,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,WAAW;QACX,OAAO;YAAE;QAAQ;;0BAEjB,8OAAC;gBACC,GAAE;gBACF,QAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,eAAc;;;;;;0BAEhB,8OAAC;gBACC,GAAE;gBACF,QAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,eAAc;gBACd,SAAQ;;;;;;;;;;;;AAIhB;AAGO,SAAS,iBAAiB,EAC/B,OAAO,EAAE,EACT,QAAQ,SAAS,EACjB,UAAU,GAAG,EACb,SAAS,EAMV;IACC,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,WAAW;QACX,OAAO;YAAE;QAAQ;;0BAEjB,8OAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAK,OAAM;gBAAK,QAAO;gBAAK,QAAQ;gBAAO,aAAY;gBAAI,MAAK;;;;;;0BAC/E,8OAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAK,OAAM;gBAAK,QAAO;gBAAK,QAAQ;gBAAO,aAAY;gBAAM,MAAK;;;;;;0BACjF,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAI,QAAQ;gBAAO,aAAY;gBAAI,MAAK;;;;;;0BAClE,8OAAC;gBAAK,GAAE;gBAA8B,QAAQ;gBAAO,aAAY;gBAAM,SAAQ;;;;;;;;;;;;AAGrF;AAGO,SAAS,cAAc,EAC5B,OAAO,EAAE,EACT,QAAQ,SAAS,EACjB,UAAU,IAAI,EACd,SAAS,EAMV;IACC,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,WAAW;QACX,OAAO;YAAE;QAAQ;kBAEjB,cAAA,8OAAC;YAAE,WAAU;;gBAEV;oBAAC;oBAAG;oBAAI;oBAAK;oBAAK;oBAAK;iBAAI,CAAC,GAAG,CAAC,CAAC,OAAO,sBACvC,8OAAC;wBAAc,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;kCAC1C,cAAA,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,MAAM;4BACN,SAAQ;;;;;;uBAPJ;;;;;8BAYV,8OAAC;oBAAO,IAAG;oBAAI,IAAG;oBAAI,GAAE;oBAAI,MAAM;oBAAO,SAAQ;;;;;;;;;;;;;;;;;AAIzD;AAGO,SAAS,kBAAkB,EAChC,QAAQ,MAAM,EACd,SAAS,CAAC,EACV,QAAQ,SAAS,EACjB,SAAS,EAMV;IACC,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;YACL;YACA,QAAQ,GAAG,OAAO,EAAE,CAAC;YACrB,YAAY,CAAC,uCAAuC,EAAE,MAAM,MAAM,EAAE,MAAM,uBAAuB,CAAC;YAClG,UAAU;QACZ;kBAEA,cAAA,8OAAC;YACC,OAAO;gBACL,UAAU;gBACV,KAAK;gBACL,MAAM;gBACN,WAAW;gBACX,OAAO;gBACP,QAAQ;gBACR,YAAY;gBACZ,cAAc;YAChB;;;;;;;;;;;AAIR;AAGO,SAAS,kBAAkB,EAChC,SAAS,EACT,UAAU,IAAI,EAIf;IACC,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;YACL,UAAU;YACV,OAAO;YACP;YACA,eAAe;YACf,YAAY,CAAC;;;;QAIb,CAAC;QACH;;;;;;AAGN;AAGO,SAAS,mBAAmB,EACjC,QAAQ,EACR,QAAQ,CAAC,EACT,WAAW,IAAI,EACf,SAAS,EAMV;IACC,qBACE,8OAAC;QAEC,OAAO;YACL,WAAW,CAAC,SAAS,EAAE,SAAS,YAAY,EAAE,MAAM,OAAO,CAAC;QAC9D;mDAHW;;YAKV;;;;;;;;;;;AAeP;AAGO,SAAS,mBAAmB,EACjC,QAAQ,EACR,WAAW,IAAI,EACf,SAAS,EAKV;IACC,qBACE,8OAAC;QAEC,OAAO;YACL,WAAW,CAAC,MAAM,EAAE,SAAS,uBAAuB,CAAC;QACvD;mDAHW;;YAKV;;;;;;;;;;;AAaP", "debugId": null}}, {"offset": {"line": 3122, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useMemo } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { operaMasks } from '@/data/masks';\nimport { OperaMask, MaskFilter } from '@/types/mask';\nimport { useAppState } from '@/hooks/useAppState';\nimport { filterMasks } from '@/utils/maskUtils';\nimport { MaskImage } from '@/components/mask/MaskImage';\nimport { MainTitle, SectionTitle, BodyText, fontStyles } from '@/components/ui/Typography';\nimport { ThemeToggle } from '@/components/ui/ThemeToggle';\nimport { useTheme } from '@/components/providers/ThemeProvider';\nimport {\n  CloudPattern,\n  GeometricPattern,\n  FloralPattern,\n  TraditionalBorder,\n  InkWashBackground,\n  AnimatedDecoration,\n  FloatingDecoration\n} from '@/components/decorations/TraditionalDecorations';\n\nexport default function Home() {\n  const [selectedMask, setSelectedMask] = useState<OperaMask | null>(null);\n  const router = useRouter();\n  const { colors, styles } = useTheme();\n  const {\n    filter,\n    updateFilter,\n    addToRecentlyViewed,\n    toggleFavorite,\n    isFavorite,\n    getRecentlyViewedMasks\n  } = useAppState();\n\n  // 过滤脸谱\n  const filteredMasks = useMemo(() => {\n    return filterMasks(operaMasks, filter);\n  }, [filter]);\n\n  // 最近查看的脸谱\n  const recentlyViewedMasks = getRecentlyViewedMasks(operaMasks);\n\n  const handleMaskClick = (mask: OperaMask) => {\n    setSelectedMask(mask);\n  };\n\n  const handleViewDetails = (mask: OperaMask) => {\n    addToRecentlyViewed(mask.id);\n    router.push(`/mask/${mask.id}`);\n  };\n\n  const handleFilterChange = (newFilter: MaskFilter) => {\n    updateFilter(newFilter);\n  };\n\n  const handleCloseModal = () => {\n    setSelectedMask(null);\n  };\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      backgroundColor: colors.background,\n      color: colors.textPrimary,\n      fontFamily: '\"Noto Sans SC\", sans-serif',\n      position: 'relative',\n      overflow: 'hidden'\n    }}>\n      {/* 水墨风格背景 */}\n      <InkWashBackground />\n\n      {/* 装饰性云纹 */}\n      <div style={{ position: 'absolute', top: '5rem', left: '2rem', zIndex: 0 }}>\n        <FloatingDecoration duration={4000}>\n          <CloudPattern width={120} height={60} color={colors.secondary} opacity={0.2} />\n        </FloatingDecoration>\n      </div>\n\n      <div style={{ position: 'absolute', top: '10rem', right: '5rem', zIndex: 0 }}>\n        <FloatingDecoration duration={5000}>\n          <GeometricPattern size={80} color={colors.primary} opacity={0.15} />\n        </FloatingDecoration>\n      </div>\n\n      <div style={{ position: 'absolute', bottom: '10rem', left: '25%', zIndex: 0 }}>\n        <FloatingDecoration duration={6000}>\n          <FloralPattern size={100} color={colors.secondary} opacity={0.2} />\n        </FloatingDecoration>\n      </div>\n      {/* 头部 */}\n      <header style={{\n        ...styles.navigation,\n        position: 'sticky',\n        top: 0,\n        zIndex: 40\n      }}>\n        <div style={{\n          maxWidth: '1200px',\n          margin: '0 auto',\n          padding: '1rem 1.5rem',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        }}>\n          <h1 style={{\n            ...fontStyles.pageTitle,\n            color: colors.textPrimary\n          }}>\n            京剧脸谱文化展示平台\n          </h1>\n          <ThemeToggle />\n        </div>\n      </header>\n\n      {/* 主要内容 */}\n      <main style={{\n        padding: '2rem 1.5rem',\n        backgroundColor: colors.background,\n        minHeight: '100vh'\n      }}>\n        {/* 英雄区域 */}\n        <AnimatedDecoration delay={300}>\n          <section style={{\n            textAlign: 'center',\n            padding: '3rem 2rem',\n            background: `linear-gradient(135deg, ${colors.primary}10 0%, transparent 50%, ${colors.secondary}10 100%)`,\n            borderRadius: '0.75rem',\n            marginBottom: '3rem',\n            border: `2px solid ${colors.border}`,\n            position: 'relative',\n            zIndex: 10,\n            boxShadow: `0 8px 32px ${colors.shadow}`\n          }}>\n            {/* 顶部装饰边框 */}\n            <div style={{ marginBottom: '2rem' }}>\n              <TraditionalBorder color={colors.secondary} height={6} />\n            </div>\n\n            <AnimatedDecoration delay={600}>\n              <MainTitle style={{\n                marginBottom: '1rem',\n                textShadow: `2px 2px 4px ${colors.shadow}`\n              }}>\n                探索京剧脸谱的艺术魅力\n              </MainTitle>\n            </AnimatedDecoration>\n\n            <AnimatedDecoration delay={900}>\n              <div\n                className=\"hero-description\"\n                style={{\n                  maxWidth: '50rem',\n                  margin: '0 auto 2rem',\n                  padding: '0 1rem'\n                }}\n              >\n                <BodyText style={{\n                  fontSize: '1.125rem',\n                  lineHeight: '1.6',\n                  textAlign: 'center',\n                  display: 'block'\n                }}>\n                  深入了解中国传统戏曲文化，感受脸谱艺术的独特魅力与深厚内涵\n                </BodyText>\n              </div>\n\n              {/* 内联样式用于响应式设计 */}\n              <style jsx>{`\n                .hero-description {\n                  white-space: nowrap;\n                  overflow: hidden;\n                  text-overflow: ellipsis;\n                }\n\n                @media (max-width: 768px) {\n                  .hero-description {\n                    white-space: normal;\n                    overflow: visible;\n                    text-overflow: unset;\n                  }\n                }\n\n                @media (max-width: 480px) {\n                  .hero-description {\n                    padding: 0 0.5rem;\n                  }\n                }\n              `}</style>\n            </AnimatedDecoration>\n\n            {/* 底部装饰边框 */}\n            <div style={{ marginTop: '2rem' }}>\n              <TraditionalBorder color={colors.primary} height={4} />\n            </div>\n\n            {/* 角落装饰 */}\n            <div style={{ position: 'absolute', top: '1rem', left: '1rem' }}>\n              <GeometricPattern size={40} color={colors.secondary} opacity={0.3} />\n            </div>\n            <div style={{ position: 'absolute', top: '1rem', right: '1rem' }}>\n              <GeometricPattern size={40} color={colors.secondary} opacity={0.3} />\n            </div>\n          </section>\n        </AnimatedDecoration>\n\n        {/* 脸谱网格 */}\n        <AnimatedDecoration delay={1200}>\n          <div style={{\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',\n            gap: '2rem',\n            maxWidth: '1200px',\n            margin: '0 auto',\n            position: 'relative',\n            zIndex: 10\n          }}>\n            {filteredMasks.map((mask, index) => (\n              <AnimatedDecoration\n                key={mask.id}\n                delay={1500 + index * 100}\n                duration={800}\n              >\n                <div\n                  onClick={() => handleMaskClick(mask)}\n                  style={{\n                    ...styles.card,\n                    borderRadius: '1rem',\n                    border: `2px solid ${colors.secondary}`,\n                    overflow: 'hidden',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                    position: 'relative',\n                    boxShadow: `0 4px 6px -1px ${colors.shadow}, 0 2px 4px -1px ${colors.shadow}`\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'translateY(-4px) scale(1.02)';\n                    e.currentTarget.style.boxShadow = `0 20px 25px -5px ${colors.shadowHover}, 0 10px 10px -5px ${colors.shadowHover}`;\n                    e.currentTarget.style.borderColor = colors.primary;\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                    e.currentTarget.style.boxShadow = `0 4px 6px -1px ${colors.shadow}, 0 2px 4px -1px ${colors.shadow}`;\n                    e.currentTarget.style.borderColor = colors.secondary;\n                  }}\n                >\n              {/* 脸谱图片 - 纯净显示，无覆盖层 */}\n              <div style={{\n                aspectRatio: '1',\n                position: 'relative',\n                backgroundColor: '#F8F9FA',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                padding: '1rem'\n              }}>\n                <MaskImage\n                  mask={mask}\n                  width={260}\n                  height={260}\n                />\n\n                {/* 分类标签 - 优化位置和样式 */}\n                <div style={{\n                  position: 'absolute',\n                  top: '0.75rem',\n                  left: '0.75rem',\n                  backgroundColor: 'rgba(0,0,0,0.85)',\n                  color: 'white',\n                  padding: '0.375rem 0.75rem',\n                  borderRadius: '1rem',\n                  fontSize: '0.75rem',\n                  fontWeight: '600',\n                  backdropFilter: 'blur(4px)',\n                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                }}>\n                  {mask.roleCategory}\n                </div>\n\n                <div style={{\n                  position: 'absolute',\n                  top: '0.75rem',\n                  right: '0.75rem',\n                  backgroundColor: 'rgba(0,0,0,0.85)',\n                  color: 'white',\n                  padding: '0.375rem 0.75rem',\n                  borderRadius: '1rem',\n                  fontSize: '0.75rem',\n                  fontWeight: '600',\n                  backdropFilter: 'blur(4px)',\n                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                }}>\n                  {mask.colorCategory}\n                </div>\n              </div>\n\n              {/* 脸谱信息 - 重新设计布局 */}\n              <div style={{\n                padding: '1.25rem',\n                backgroundColor: colors.background,\n                borderTop: `1px solid ${colors.border}`\n              }}>\n                {/* 角色名称 - 移到卡片信息区域 */}\n                <div style={{\n                  textAlign: 'center',\n                  marginBottom: '1rem',\n                  paddingBottom: '0.75rem',\n                  borderBottom: `1px solid ${colors.border}`\n                }}>\n                  <h2 style={{\n                    fontSize: '1.5rem',\n                    fontWeight: '700',\n                    color: colors.textPrimary,\n                    marginBottom: '0.25rem',\n                    fontFamily: '\"Noto Serif SC\", serif'\n                  }}>\n                    {mask.character}\n                  </h2>\n                  <h3 style={{\n                    fontSize: '1rem',\n                    fontWeight: '500',\n                    color: colors.textSecondary,\n                    fontFamily: '\"Noto Serif SC\", serif'\n                  }}>\n                    {mask.name}\n                  </h3>\n                </div>\n\n                {/* 角色描述 */}\n                <p style={{\n                  fontSize: '0.875rem',\n                  color: colors.textSecondary,\n                  marginBottom: '1rem',\n                  lineHeight: '1.5',\n                  display: '-webkit-box',\n                  WebkitLineClamp: 2,\n                  WebkitBoxOrient: 'vertical',\n                  overflow: 'hidden'\n                }}>\n                  {mask.culturalBackground.personality}\n                </p>\n\n                {/* 主要颜色 */}\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '1rem'\n                }}>\n                  <span style={{\n                    fontSize: '0.875rem',\n                    color: colors.textSecondary,\n                    fontWeight: '500'\n                  }}>\n                    主要色彩:\n                  </span>\n                  <div style={{ display: 'flex', gap: '0.375rem' }}>\n                    {mask.mainColors.slice(0, 3).map((color, index) => (\n                      <div\n                        key={index}\n                        style={{\n                          width: '1.25rem',\n                          height: '1.25rem',\n                          borderRadius: '50%',\n                          backgroundColor: color,\n                          border: `2px solid ${colors.border}`,\n                          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'\n                        }}\n                        title={color}\n                      />\n                    ))}\n                  </div>\n                </div>\n\n                {/* 标签 */}\n                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.375rem' }}>\n                  {mask.tags.slice(0, 3).map((tag, index) => (\n                    <span\n                      key={index}\n                      style={{\n                        padding: '0.375rem 0.75rem',\n                        fontSize: '0.75rem',\n                        fontWeight: '500',\n                        backgroundColor: colors.backgroundTertiary,\n                        color: colors.textTertiary,\n                        borderRadius: '1rem',\n                        border: `1px solid ${colors.border}`,\n                        transition: 'all 0.2s ease'\n                      }}\n                    >\n                      {tag}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            </div>\n              </AnimatedDecoration>\n          ))}\n        </div>\n        </AnimatedDecoration>\n      </main>\n\n      {/* 简单的模态框 */}\n      {selectedMask && (\n        <div\n          style={{\n            position: 'fixed',\n            inset: 0,\n            zIndex: 50,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            backdropFilter: 'blur(4px)'\n          }}\n          onClick={handleCloseModal}\n        >\n          <div\n            style={{\n              backgroundColor: 'white',\n              borderRadius: '0.75rem',\n              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',\n              border: '2px solid #F59E0B',\n              maxWidth: '90vw',\n              maxHeight: '90vh',\n              overflow: 'auto',\n              position: 'relative'\n            }}\n            onClick={(e) => e.stopPropagation()}\n          >\n            {/* 关闭按钮 */}\n            <button\n              onClick={handleCloseModal}\n              style={{\n                position: 'absolute',\n                top: '1rem',\n                right: '1rem',\n                backgroundColor: 'transparent',\n                border: 'none',\n                fontSize: '1.5rem',\n                cursor: 'pointer',\n                zIndex: 10,\n                color: '#6B7280'\n              }}\n            >\n              ×\n            </button>\n\n            <div style={{\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: '1.5rem',\n              padding: '1.5rem'\n            }}>\n              {/* 脸谱图片 */}\n              <div>\n                <div style={{\n                  aspectRatio: '1',\n                  marginBottom: '1rem',\n                  borderRadius: '0.5rem',\n                  overflow: 'hidden',\n                  border: '2px solid #F59E0B'\n                }}>\n                  {/* 使用MaskImage组件显示脸谱 */}\n                  <MaskImage\n                    mask={selectedMask}\n                    width={300}\n                    height={300}\n                    className=\"modal-mask-image\"\n                  />\n                </div>\n                <div style={{ display: 'flex', gap: '0.5rem' }}>\n                  {selectedMask.mainColors.map((color, index) => (\n                    <div\n                      key={index}\n                      style={{\n                        width: '2rem',\n                        height: '2rem',\n                        borderRadius: '50%',\n                        backgroundColor: color,\n                        border: '2px solid #D1D5DB'\n                      }}\n                      title={color}\n                    />\n                  ))}\n                </div>\n              </div>\n\n              {/* 脸谱信息 */}\n              <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n                <div>\n                  <h2 style={{\n                    fontSize: '1.5rem',\n                    fontWeight: 'bold',\n                    color: '#1F2937',\n                    marginBottom: '0.5rem',\n                    fontFamily: '\"Noto Serif SC\", serif'\n                  }}>\n                    {selectedMask.name}\n                  </h2>\n                  <p style={{ color: '#6B7280', fontWeight: '500' }}>\n                    {selectedMask.character}\n                  </p>\n                </div>\n\n                <div>\n                  <h3 style={{\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#1F2937',\n                    marginBottom: '0.5rem'\n                  }}>\n                    基本信息\n                  </h3>\n                  <div style={{ fontSize: '0.875rem', display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>\n                    <p><span style={{ fontWeight: '500' }}>行当:</span> {selectedMask.roleCategory}</p>\n                    <p><span style={{ fontWeight: '500' }}>颜色分类:</span> {selectedMask.colorCategory}</p>\n                    <p><span style={{ fontWeight: '500' }}>绘制难度:</span> {selectedMask.difficulty}</p>\n                  </div>\n                </div>\n\n                <div>\n                  <h3 style={{\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#1F2937',\n                    marginBottom: '0.5rem'\n                  }}>\n                    文化背景\n                  </h3>\n                  <div style={{ fontSize: '0.875rem', color: '#6B7280', display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>\n                    <p><span style={{ fontWeight: '500' }}>历史起源:</span> {selectedMask.culturalBackground.origin}</p>\n                    <p><span style={{ fontWeight: '500' }}>性格特点:</span> {selectedMask.culturalBackground.personality}</p>\n                    <p><span style={{ fontWeight: '500' }}>象征意义:</span> {selectedMask.culturalBackground.symbolism}</p>\n                  </div>\n                </div>\n\n                <div>\n                  <h3 style={{\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#1F2937',\n                    marginBottom: '0.5rem'\n                  }}>\n                    相关剧目\n                  </h3>\n                  <div style={{ fontSize: '0.875rem', display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>\n                    {selectedMask.relatedOperas.map((opera, index) => (\n                      <div key={index}>\n                        <p style={{ fontWeight: '500' }}>{opera.name}</p>\n                        <p style={{ color: '#6B7280' }}>{opera.description}</p>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.5rem', marginBottom: '1.5rem' }}>\n                  {selectedMask.tags.map((tag, index) => (\n                    <span\n                      key={index}\n                      style={{\n                        padding: '0.5rem 0.75rem',\n                        fontSize: '0.75rem',\n                        backgroundColor: 'rgba(245, 158, 11, 0.2)',\n                        color: '#F59E0B',\n                        border: '1px solid #F59E0B',\n                        borderRadius: '9999px'\n                      }}\n                    >\n                      {tag}\n                    </span>\n                  ))}\n                </div>\n\n                {/* 查看详情按钮 */}\n                <button\n                  onClick={() => handleViewDetails(selectedMask)}\n                  style={{\n                    width: '100%',\n                    backgroundColor: '#B91C1C',\n                    color: 'white',\n                    padding: '0.75rem 1.5rem',\n                    borderRadius: '0.5rem',\n                    border: 'none',\n                    cursor: 'pointer',\n                    fontSize: '1rem',\n                    fontWeight: '600',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.backgroundColor = '#991B1B';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.backgroundColor = '#B91C1C';\n                  }}\n                >\n                  查看完整详情 →\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAsBe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IACnE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAClC,MAAM,EACJ,MAAM,EACN,YAAY,EACZ,mBAAmB,EACnB,cAAc,EACd,UAAU,EACV,sBAAsB,EACvB,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAEd,OAAO;IACP,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,OAAO,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD,EAAE,oHAAA,CAAA,aAAU,EAAE;IACjC,GAAG;QAAC;KAAO;IAEX,UAAU;IACV,MAAM,sBAAsB,uBAAuB,oHAAA,CAAA,aAAU;IAE7D,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;IAClB;IAEA,MAAM,oBAAoB,CAAC;QACzB,oBAAoB,KAAK,EAAE;QAC3B,OAAO,IAAI,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;IAChC;IAEA,MAAM,qBAAqB,CAAC;QAC1B,aAAa;IACf;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAI,OAAO;YACV,WAAW;YACX,iBAAiB,OAAO,UAAU;YAClC,OAAO,OAAO,WAAW;YACzB,YAAY;YACZ,UAAU;YACV,UAAU;QACZ;;0BAEE,8OAAC,2JAAA,CAAA,oBAAiB;;;;;0BAGlB,8OAAC;gBAAI,OAAO;oBAAE,UAAU;oBAAY,KAAK;oBAAQ,MAAM;oBAAQ,QAAQ;gBAAE;0BACvE,cAAA,8OAAC,2JAAA,CAAA,qBAAkB;oBAAC,UAAU;8BAC5B,cAAA,8OAAC,2JAAA,CAAA,eAAY;wBAAC,OAAO;wBAAK,QAAQ;wBAAI,OAAO,OAAO,SAAS;wBAAE,SAAS;;;;;;;;;;;;;;;;0BAI5E,8OAAC;gBAAI,OAAO;oBAAE,UAAU;oBAAY,KAAK;oBAAS,OAAO;oBAAQ,QAAQ;gBAAE;0BACzE,cAAA,8OAAC,2JAAA,CAAA,qBAAkB;oBAAC,UAAU;8BAC5B,cAAA,8OAAC,2JAAA,CAAA,mBAAgB;wBAAC,MAAM;wBAAI,OAAO,OAAO,OAAO;wBAAE,SAAS;;;;;;;;;;;;;;;;0BAIhE,8OAAC;gBAAI,OAAO;oBAAE,UAAU;oBAAY,QAAQ;oBAAS,MAAM;oBAAO,QAAQ;gBAAE;0BAC1E,cAAA,8OAAC,2JAAA,CAAA,qBAAkB;oBAAC,UAAU;8BAC5B,cAAA,8OAAC,2JAAA,CAAA,gBAAa;wBAAC,MAAM;wBAAK,OAAO,OAAO,SAAS;wBAAE,SAAS;;;;;;;;;;;;;;;;0BAIhE,8OAAC;gBAAO,OAAO;oBACb,GAAG,OAAO,UAAU;oBACpB,UAAU;oBACV,KAAK;oBACL,QAAQ;gBACV;0BACE,cAAA,8OAAC;oBAAI,OAAO;wBACV,UAAU;wBACV,QAAQ;wBACR,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,YAAY;oBACd;;sCACE,8OAAC;4BAAG,OAAO;gCACT,GAAG,sIAAA,CAAA,aAAU,CAAC,SAAS;gCACvB,OAAO,OAAO,WAAW;4BAC3B;sCAAG;;;;;;sCAGH,8OAAC,uIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0BAKhB,8OAAC;gBAAK,OAAO;oBACX,SAAS;oBACT,iBAAiB,OAAO,UAAU;oBAClC,WAAW;gBACb;;kCAEE,8OAAC,2JAAA,CAAA,qBAAkB;wBAAC,OAAO;kCACzB,cAAA,8OAAC;4BAAQ,OAAO;gCACd,WAAW;gCACX,SAAS;gCACT,YAAY,CAAC,wBAAwB,EAAE,OAAO,OAAO,CAAC,wBAAwB,EAAE,OAAO,SAAS,CAAC,QAAQ,CAAC;gCAC1G,cAAc;gCACd,cAAc;gCACd,QAAQ,CAAC,UAAU,EAAE,OAAO,MAAM,EAAE;gCACpC,UAAU;gCACV,QAAQ;gCACR,WAAW,CAAC,WAAW,EAAE,OAAO,MAAM,EAAE;4BAC1C;;8CAEE,8OAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;8CACjC,cAAA,8OAAC,2JAAA,CAAA,oBAAiB;wCAAC,OAAO,OAAO,SAAS;wCAAE,QAAQ;;;;;;;;;;;8CAGtD,8OAAC,2JAAA,CAAA,qBAAkB;oCAAC,OAAO;8CACzB,cAAA,8OAAC,sIAAA,CAAA,YAAS;wCAAC,OAAO;4CAChB,cAAc;4CACd,YAAY,CAAC,YAAY,EAAE,OAAO,MAAM,EAAE;wCAC5C;kDAAG;;;;;;;;;;;8CAKL,8OAAC,2JAAA,CAAA,qBAAkB;oCAAC,OAAO;;sDACzB,8OAAC;4CAEC,OAAO;gDACL,UAAU;gDACV,QAAQ;gDACR,SAAS;4CACX;sFALU;sDAOV,cAAA,8OAAC,sIAAA,CAAA,WAAQ;gDAAC,OAAO;oDACf,UAAU;oDACV,YAAY;oDACZ,WAAW;oDACX,SAAS;gDACX;0DAAG;;;;;;;;;;;;;;;;;;;;;8CA8BP,8OAAC;oCAAI,OAAO;wCAAE,WAAW;oCAAO;8CAC9B,cAAA,8OAAC,2JAAA,CAAA,oBAAiB;wCAAC,OAAO,OAAO,OAAO;wCAAE,QAAQ;;;;;;;;;;;8CAIpD,8OAAC;oCAAI,OAAO;wCAAE,UAAU;wCAAY,KAAK;wCAAQ,MAAM;oCAAO;8CAC5D,cAAA,8OAAC,2JAAA,CAAA,mBAAgB;wCAAC,MAAM;wCAAI,OAAO,OAAO,SAAS;wCAAE,SAAS;;;;;;;;;;;8CAEhE,8OAAC;oCAAI,OAAO;wCAAE,UAAU;wCAAY,KAAK;wCAAQ,OAAO;oCAAO;8CAC7D,cAAA,8OAAC,2JAAA,CAAA,mBAAgB;wCAAC,MAAM;wCAAI,OAAO,OAAO,SAAS;wCAAE,SAAS;;;;;;;;;;;;;;;;;;;;;;kCAMpE,8OAAC,2JAAA,CAAA,qBAAkB;wBAAC,OAAO;kCACzB,cAAA,8OAAC;4BAAI,OAAO;gCACV,SAAS;gCACT,qBAAqB;gCACrB,KAAK;gCACL,UAAU;gCACV,QAAQ;gCACR,UAAU;gCACV,QAAQ;4BACV;sCACG,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,8OAAC,2JAAA,CAAA,qBAAkB;oCAEjB,OAAO,OAAO,QAAQ;oCACtB,UAAU;8CAEV,cAAA,8OAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,OAAO;4CACL,GAAG,OAAO,IAAI;4CACd,cAAc;4CACd,QAAQ,CAAC,UAAU,EAAE,OAAO,SAAS,EAAE;4CACvC,UAAU;4CACV,QAAQ;4CACR,YAAY;4CACZ,UAAU;4CACV,WAAW,CAAC,eAAe,EAAE,OAAO,MAAM,CAAC,iBAAiB,EAAE,OAAO,MAAM,EAAE;wCAC/E;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4CAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,iBAAiB,EAAE,OAAO,WAAW,CAAC,mBAAmB,EAAE,OAAO,WAAW,EAAE;4CAClH,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG,OAAO,OAAO;wCACpD;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4CAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,eAAe,EAAE,OAAO,MAAM,CAAC,iBAAiB,EAAE,OAAO,MAAM,EAAE;4CACpG,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG,OAAO,SAAS;wCACtD;;0DAGJ,8OAAC;gDAAI,OAAO;oDACV,aAAa;oDACb,UAAU;oDACV,iBAAiB;oDACjB,SAAS;oDACT,YAAY;oDACZ,gBAAgB;oDAChB,SAAS;gDACX;;kEACE,8OAAC,uIAAA,CAAA,YAAS;wDACR,MAAM;wDACN,OAAO;wDACP,QAAQ;;;;;;kEAIV,8OAAC;wDAAI,OAAO;4DACV,UAAU;4DACV,KAAK;4DACL,MAAM;4DACN,iBAAiB;4DACjB,OAAO;4DACP,SAAS;4DACT,cAAc;4DACd,UAAU;4DACV,YAAY;4DACZ,gBAAgB;4DAChB,WAAW;wDACb;kEACG,KAAK,YAAY;;;;;;kEAGpB,8OAAC;wDAAI,OAAO;4DACV,UAAU;4DACV,KAAK;4DACL,OAAO;4DACP,iBAAiB;4DACjB,OAAO;4DACP,SAAS;4DACT,cAAc;4DACd,UAAU;4DACV,YAAY;4DACZ,gBAAgB;4DAChB,WAAW;wDACb;kEACG,KAAK,aAAa;;;;;;;;;;;;0DAKvB,8OAAC;gDAAI,OAAO;oDACV,SAAS;oDACT,iBAAiB,OAAO,UAAU;oDAClC,WAAW,CAAC,UAAU,EAAE,OAAO,MAAM,EAAE;gDACzC;;kEAEE,8OAAC;wDAAI,OAAO;4DACV,WAAW;4DACX,cAAc;4DACd,eAAe;4DACf,cAAc,CAAC,UAAU,EAAE,OAAO,MAAM,EAAE;wDAC5C;;0EACE,8OAAC;gEAAG,OAAO;oEACT,UAAU;oEACV,YAAY;oEACZ,OAAO,OAAO,WAAW;oEACzB,cAAc;oEACd,YAAY;gEACd;0EACG,KAAK,SAAS;;;;;;0EAEjB,8OAAC;gEAAG,OAAO;oEACT,UAAU;oEACV,YAAY;oEACZ,OAAO,OAAO,aAAa;oEAC3B,YAAY;gEACd;0EACG,KAAK,IAAI;;;;;;;;;;;;kEAKd,8OAAC;wDAAE,OAAO;4DACR,UAAU;4DACV,OAAO,OAAO,aAAa;4DAC3B,cAAc;4DACd,YAAY;4DACZ,SAAS;4DACT,iBAAiB;4DACjB,iBAAiB;4DACjB,UAAU;wDACZ;kEACG,KAAK,kBAAkB,CAAC,WAAW;;;;;;kEAItC,8OAAC;wDAAI,OAAO;4DACV,SAAS;4DACT,YAAY;4DACZ,KAAK;4DACL,cAAc;wDAChB;;0EACE,8OAAC;gEAAK,OAAO;oEACX,UAAU;oEACV,OAAO,OAAO,aAAa;oEAC3B,YAAY;gEACd;0EAAG;;;;;;0EAGH,8OAAC;gEAAI,OAAO;oEAAE,SAAS;oEAAQ,KAAK;gEAAW;0EAC5C,KAAK,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACvC,8OAAC;wEAEC,OAAO;4EACL,OAAO;4EACP,QAAQ;4EACR,cAAc;4EACd,iBAAiB;4EACjB,QAAQ,CAAC,UAAU,EAAE,OAAO,MAAM,EAAE;4EACpC,WAAW;wEACb;wEACA,OAAO;uEATF;;;;;;;;;;;;;;;;kEAgBb,8OAAC;wDAAI,OAAO;4DAAE,SAAS;4DAAQ,UAAU;4DAAQ,KAAK;wDAAW;kEAC9D,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC;gEAEC,OAAO;oEACL,SAAS;oEACT,UAAU;oEACV,YAAY;oEACZ,iBAAiB,OAAO,kBAAkB;oEAC1C,OAAO,OAAO,YAAY;oEAC1B,cAAc;oEACd,QAAQ,CAAC,UAAU,EAAE,OAAO,MAAM,EAAE;oEACpC,YAAY;gEACd;0EAEC;+DAZI;;;;;;;;;;;;;;;;;;;;;;mCA/JN,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;YAwLrB,8BACC,8OAAC;gBACC,OAAO;oBACL,UAAU;oBACV,OAAO;oBACP,QAAQ;oBACR,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,iBAAiB;oBACjB,gBAAgB;gBAClB;gBACA,SAAS;0BAET,cAAA,8OAAC;oBACC,OAAO;wBACL,iBAAiB;wBACjB,cAAc;wBACd,WAAW;wBACX,QAAQ;wBACR,UAAU;wBACV,WAAW;wBACX,UAAU;wBACV,UAAU;oBACZ;oBACA,SAAS,CAAC,IAAM,EAAE,eAAe;;sCAGjC,8OAAC;4BACC,SAAS;4BACT,OAAO;gCACL,UAAU;gCACV,KAAK;gCACL,OAAO;gCACP,iBAAiB;gCACjB,QAAQ;gCACR,UAAU;gCACV,QAAQ;gCACR,QAAQ;gCACR,OAAO;4BACT;sCACD;;;;;;sCAID,8OAAC;4BAAI,OAAO;gCACV,SAAS;gCACT,qBAAqB;gCACrB,KAAK;gCACL,SAAS;4BACX;;8CAEE,8OAAC;;sDACC,8OAAC;4CAAI,OAAO;gDACV,aAAa;gDACb,cAAc;gDACd,cAAc;gDACd,UAAU;gDACV,QAAQ;4CACV;sDAEE,cAAA,8OAAC,uIAAA,CAAA,YAAS;gDACR,MAAM;gDACN,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;sDAGd,8OAAC;4CAAI,OAAO;gDAAE,SAAS;gDAAQ,KAAK;4CAAS;sDAC1C,aAAa,UAAU,CAAC,GAAG,CAAC,CAAC,OAAO,sBACnC,8OAAC;oDAEC,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,iBAAiB;wDACjB,QAAQ;oDACV;oDACA,OAAO;mDARF;;;;;;;;;;;;;;;;8CAeb,8OAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,eAAe;wCAAU,KAAK;oCAAS;;sDACpE,8OAAC;;8DACC,8OAAC;oDAAG,OAAO;wDACT,UAAU;wDACV,YAAY;wDACZ,OAAO;wDACP,cAAc;wDACd,YAAY;oDACd;8DACG,aAAa,IAAI;;;;;;8DAEpB,8OAAC;oDAAE,OAAO;wDAAE,OAAO;wDAAW,YAAY;oDAAM;8DAC7C,aAAa,SAAS;;;;;;;;;;;;sDAI3B,8OAAC;;8DACC,8OAAC;oDAAG,OAAO;wDACT,UAAU;wDACV,YAAY;wDACZ,OAAO;wDACP,cAAc;oDAChB;8DAAG;;;;;;8DAGH,8OAAC;oDAAI,OAAO;wDAAE,UAAU;wDAAY,SAAS;wDAAQ,eAAe;wDAAU,KAAK;oDAAS;;sEAC1F,8OAAC;;8EAAE,8OAAC;oEAAK,OAAO;wEAAE,YAAY;oEAAM;8EAAG;;;;;;gEAAU;gEAAE,aAAa,YAAY;;;;;;;sEAC5E,8OAAC;;8EAAE,8OAAC;oEAAK,OAAO;wEAAE,YAAY;oEAAM;8EAAG;;;;;;gEAAY;gEAAE,aAAa,aAAa;;;;;;;sEAC/E,8OAAC;;8EAAE,8OAAC;oEAAK,OAAO;wEAAE,YAAY;oEAAM;8EAAG;;;;;;gEAAY;gEAAE,aAAa,UAAU;;;;;;;;;;;;;;;;;;;sDAIhF,8OAAC;;8DACC,8OAAC;oDAAG,OAAO;wDACT,UAAU;wDACV,YAAY;wDACZ,OAAO;wDACP,cAAc;oDAChB;8DAAG;;;;;;8DAGH,8OAAC;oDAAI,OAAO;wDAAE,UAAU;wDAAY,OAAO;wDAAW,SAAS;wDAAQ,eAAe;wDAAU,KAAK;oDAAS;;sEAC5G,8OAAC;;8EAAE,8OAAC;oEAAK,OAAO;wEAAE,YAAY;oEAAM;8EAAG;;;;;;gEAAY;gEAAE,aAAa,kBAAkB,CAAC,MAAM;;;;;;;sEAC3F,8OAAC;;8EAAE,8OAAC;oEAAK,OAAO;wEAAE,YAAY;oEAAM;8EAAG;;;;;;gEAAY;gEAAE,aAAa,kBAAkB,CAAC,WAAW;;;;;;;sEAChG,8OAAC;;8EAAE,8OAAC;oEAAK,OAAO;wEAAE,YAAY;oEAAM;8EAAG;;;;;;gEAAY;gEAAE,aAAa,kBAAkB,CAAC,SAAS;;;;;;;;;;;;;;;;;;;sDAIlG,8OAAC;;8DACC,8OAAC;oDAAG,OAAO;wDACT,UAAU;wDACV,YAAY;wDACZ,OAAO;wDACP,cAAc;oDAChB;8DAAG;;;;;;8DAGH,8OAAC;oDAAI,OAAO;wDAAE,UAAU;wDAAY,SAAS;wDAAQ,eAAe;wDAAU,KAAK;oDAAS;8DACzF,aAAa,aAAa,CAAC,GAAG,CAAC,CAAC,OAAO,sBACtC,8OAAC;;8EACC,8OAAC;oEAAE,OAAO;wEAAE,YAAY;oEAAM;8EAAI,MAAM,IAAI;;;;;;8EAC5C,8OAAC;oEAAE,OAAO;wEAAE,OAAO;oEAAU;8EAAI,MAAM,WAAW;;;;;;;2DAF1C;;;;;;;;;;;;;;;;sDAQhB,8OAAC;4CAAI,OAAO;gDAAE,SAAS;gDAAQ,UAAU;gDAAQ,KAAK;gDAAU,cAAc;4CAAS;sDACpF,aAAa,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC3B,8OAAC;oDAEC,OAAO;wDACL,SAAS;wDACT,UAAU;wDACV,iBAAiB;wDACjB,OAAO;wDACP,QAAQ;wDACR,cAAc;oDAChB;8DAEC;mDAVI;;;;;;;;;;sDAgBX,8OAAC;4CACC,SAAS,IAAM,kBAAkB;4CACjC,OAAO;gDACL,OAAO;gDACP,iBAAiB;gDACjB,OAAO;gDACP,SAAS;gDACT,cAAc;gDACd,QAAQ;gDACR,QAAQ;gDACR,UAAU;gDACV,YAAY;gDACZ,YAAY;4CACd;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4CAC1C;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4CAC1C;sDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}]}