"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_masks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/masks */ \"(app-pages-browser)/./src/data/masks.ts\");\n/* harmony import */ var _services_maskService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/maskService */ \"(app-pages-browser)/./src/services/maskService.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers/ThemeProvider */ \"(app-pages-browser)/./src/components/providers/ThemeProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Home() {\n    var _selectedMask_images, _selectedMask_images1, _selectedMask_culturalBackground;\n    _s();\n    const [masks, setMasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_data_masks__WEBPACK_IMPORTED_MODULE_2__.operaMasks);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedMask, setSelectedMask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { colors } = (0,_components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    // 加载脸谱数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const loadMasks = {\n                \"Home.useEffect.loadMasks\": async ()=>{\n                    if (!(0,_lib_supabase__WEBPACK_IMPORTED_MODULE_4__.isSupabaseConfigured)()) {\n                        console.log('Using static mask data');\n                        return;\n                    }\n                    setLoading(true);\n                    try {\n                        const maskData = await _services_maskService__WEBPACK_IMPORTED_MODULE_3__.MaskService.getAllApprovedMasks();\n                        setMasks(maskData);\n                    } catch (error) {\n                        console.error('Error loading masks:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"Home.useEffect.loadMasks\"];\n            loadMasks();\n        }\n    }[\"Home.useEffect\"], []);\n    const handleMaskClick = (mask)=>{\n        setSelectedMask(mask);\n    };\n    const closeModal = ()=>{\n        setSelectedMask(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            backgroundColor: colors.background,\n            color: colors.text,\n            padding: '2rem'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center',\n                    marginBottom: '3rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            fontSize: '2.5rem',\n                            fontWeight: 'bold',\n                            marginBottom: '1rem',\n                            background: 'linear-gradient(135deg, #DC2626, #B91C1C)',\n                            WebkitBackgroundClip: 'text',\n                            WebkitTextFillColor: 'transparent',\n                            fontFamily: '\"Ma Shan Zheng\", cursive'\n                        },\n                        children: \"\\uD83C\\uDFAD 京剧脸谱文化展示\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            fontSize: '1.125rem',\n                            color: colors.textSecondary,\n                            maxWidth: '600px',\n                            margin: '0 auto'\n                        },\n                        children: \"探索中国传统京剧脸谱艺术的魅力，了解每个角色背后的文化内涵\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center',\n                    padding: '2rem',\n                    color: colors.textSecondary\n                },\n                children: \"正在加载脸谱数据...\"\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                    gap: '2rem',\n                    maxWidth: '1200px',\n                    margin: '0 auto'\n                },\n                children: masks.map((mask)=>{\n                    var _mask_images, _mask_images1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: ()=>handleMaskClick(mask),\n                        style: {\n                            backgroundColor: colors.cardBackground,\n                            borderRadius: '12px',\n                            padding: '1.5rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.3s ease',\n                            border: \"1px solid \".concat(colors.border),\n                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: '100%',\n                                    height: '200px',\n                                    borderRadius: '8px',\n                                    overflow: 'hidden',\n                                    marginBottom: '1rem'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: ((_mask_images = mask.images) === null || _mask_images === void 0 ? void 0 : _mask_images.fullSize) || mask.imageUrl || ((_mask_images1 = mask.images) === null || _mask_images1 === void 0 ? void 0 : _mask_images1.thumbnail),\n                                    alt: mask.name,\n                                    style: {\n                                        width: '100%',\n                                        height: '100%',\n                                        objectFit: 'cover'\n                                    },\n                                    onError: (e)=>{\n                                        // 图片加载失败时的备用处理\n                                        const target = e.target;\n                                        target.src = \"https://via.placeholder.com/300x300/DC143C/FFFFFF?text=\".concat(encodeURIComponent(mask.name));\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    fontSize: '1.25rem',\n                                    fontWeight: 'bold',\n                                    marginBottom: '0.5rem',\n                                    color: colors.textPrimary\n                                },\n                                children: mask.name\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: '0.875rem',\n                                    color: colors.textSecondary,\n                                    marginBottom: '1rem'\n                                },\n                                children: [\n                                    \"角色: \",\n                                    mask.character\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            (mask.colorTheme || mask.mainColors) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '0.5rem',\n                                    marginBottom: '1rem'\n                                },\n                                children: (mask.colorTheme || mask.mainColors || []).slice(0, 3).map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: '20px',\n                                            height: '20px',\n                                            borderRadius: '50%',\n                                            backgroundColor: color,\n                                            border: '1px solid rgba(0,0,0,0.1)'\n                                        },\n                                        title: color\n                                    }, index, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 15\n                            }, this),\n                            (mask.personalityTraits || mask.tags) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexWrap: 'wrap',\n                                    gap: '0.5rem'\n                                },\n                                children: (mask.personalityTraits || mask.tags || []).slice(0, 3).map((trait, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: '0.75rem',\n                                            padding: '0.25rem 0.5rem',\n                                            backgroundColor: colors.primary + '20',\n                                            color: colors.primary,\n                                            borderRadius: '12px'\n                                        },\n                                        children: trait\n                                    }, index, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, mask.id, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            selectedMask && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    zIndex: 1000,\n                    padding: '1rem'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: colors.background,\n                        borderRadius: '12px',\n                        padding: '2rem',\n                        maxWidth: '600px',\n                        width: '100%',\n                        maxHeight: '80vh',\n                        overflow: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center',\n                                marginBottom: '1.5rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        fontSize: '1.5rem',\n                                        fontWeight: 'bold',\n                                        color: colors.textPrimary\n                                    },\n                                    children: selectedMask.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: closeModal,\n                                    style: {\n                                        background: 'none',\n                                        border: 'none',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer',\n                                        color: colors.textSecondary\n                                    },\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: ((_selectedMask_images = selectedMask.images) === null || _selectedMask_images === void 0 ? void 0 : _selectedMask_images.fullSize) || selectedMask.imageUrl || ((_selectedMask_images1 = selectedMask.images) === null || _selectedMask_images1 === void 0 ? void 0 : _selectedMask_images1.thumbnail),\n                            alt: selectedMask.name,\n                            style: {\n                                width: '100%',\n                                height: '300px',\n                                objectFit: 'cover',\n                                borderRadius: '8px',\n                                marginBottom: '1.5rem'\n                            },\n                            onError: (e)=>{\n                                const target = e.target;\n                                target.src = \"https://via.placeholder.com/600x300/DC143C/FFFFFF?text=\".concat(encodeURIComponent(selectedMask.name));\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '0.875rem',\n                                color: colors.textSecondary,\n                                lineHeight: '1.6'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"角色:\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 18\n                                        }, this),\n                                        \" \",\n                                        selectedMask.character\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"文化背景:\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 18\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        marginLeft: '1rem',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: typeof selectedMask.culturalBackground === 'string' ? selectedMask.culturalBackground : ((_selectedMask_culturalBackground = selectedMask.culturalBackground) === null || _selectedMask_culturalBackground === void 0 ? void 0 : _selectedMask_culturalBackground.origin) || '传统京剧脸谱艺术'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this),\n                                (selectedMask.personalityTraits || selectedMask.tags) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"特征:\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 22\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                flexWrap: 'wrap',\n                                                gap: '0.5rem',\n                                                marginLeft: '1rem'\n                                            },\n                                            children: (selectedMask.personalityTraits || selectedMask.tags || []).map((trait, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.75rem',\n                                                        padding: '0.25rem 0.5rem',\n                                                        backgroundColor: colors.primary + '20',\n                                                        color: colors.primary,\n                                                        borderRadius: '12px'\n                                                    },\n                                                    children: trait\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"Vfoqi3Tz0LvR925DWBUyKoanCxA=\", false, function() {\n    return [\n        _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_5__.useTheme\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});