# Supabase错误修复指南
## 解决数据库配置中的类型冲突和ID格式问题

### 🚨 问题总结

您遇到的两个错误已经分析并提供了完整的解决方案：

1. **枚举类型冲突**: `type "role_type" already exists`
2. **UUID格式错误**: `invalid input syntax for type uuid: "guanyu"`

---

## 🛠️ 完整解决方案

### 步骤1: 清理现有数据库结构

1. **在Supabase SQL编辑器中执行清理脚本**
   - 打开Supabase项目仪表板
   - 点击左侧 **"SQL Editor"**
   - 点击 **"New query"**
   - 复制 `supabase/migrations/000_cleanup.sql` 的全部内容
   - 粘贴到编辑器中
   - 点击 **"Run"** 执行

2. **验证清理结果**
   - 执行成功后会显示: `Database cleanup completed`
   - 点击左侧 **"Table Editor"** 确认所有表已删除

### 步骤2: 执行修正的架构脚本

1. **创建新的SQL查询**
   - 在SQL编辑器中点击 **"New query"**
   - 复制 `supabase/migrations/001_initial_schema_fixed.sql` 的全部内容
   - 粘贴到编辑器中
   - 点击 **"Run"** 执行

2. **验证表结构创建**
   - 执行成功后会显示: `Database schema created successfully`
   - 在 **"Table Editor"** 中确认以下表已创建：
     - ✅ `masks` (主键类型: TEXT)
     - ✅ `drawing_steps`
     - ✅ `user_profiles`
     - ✅ `user_likes`
     - ✅ `user_favorites`

### 步骤3: 导入修正的种子数据

1. **创建新的SQL查询**
   - 点击 **"New query"**
   - 复制 `supabase/migrations/002_seed_data_fixed.sql` 的全部内容
   - 粘贴到编辑器中
   - 点击 **"Run"** 执行

2. **验证数据导入**
   - 执行成功后会显示: `Seed data inserted successfully`
   - 点击 `masks` 表，确认有9条记录
   - 点击 `drawing_steps` 表，确认有绘制步骤数据

### 步骤4: 验证数据库配置

1. **检查脸谱数据**
   ```sql
   SELECT id, name, character, role_type, is_official 
   FROM masks 
   WHERE is_official = true;
   ```
   应该返回9条官方脸谱记录。

2. **检查绘制步骤**
   ```sql
   SELECT mask_id, COUNT(*) as step_count 
   FROM drawing_steps 
   GROUP BY mask_id;
   ```
   应该显示关羽、包拯、曹操各有6个绘制步骤。

3. **检查RLS策略**
   ```sql
   SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
   FROM pg_policies 
   WHERE schemaname = 'public';
   ```
   应该显示所有表的安全策略。

---

## 🔧 关键修复说明

### 修复1: 数据库架构调整

**原问题**: UUID主键与字符串ID不兼容
**解决方案**: 将masks表的主键改为TEXT类型

```sql
-- 修改前 (有问题)
id UUID DEFAULT gen_random_uuid() PRIMARY KEY,

-- 修改后 (正确)
id TEXT PRIMARY KEY,
```

### 修复2: 种子数据格式调整

**原问题**: 字符串ID无法插入UUID字段
**解决方案**: 使用字符串ID直接插入

```sql
-- 修改前 (有问题)
INSERT INTO masks (id, ...) VALUES ('guanyu', ...);  -- UUID字段接收字符串

-- 修改后 (正确)
INSERT INTO masks (id, ...) VALUES ('guanyu', ...);  -- TEXT字段接收字符串
```

### 修复3: 外键关系调整

**调整内容**: 所有引用masks.id的外键都改为TEXT类型

```sql
-- drawing_steps表
mask_id TEXT REFERENCES masks(id) ON DELETE CASCADE,

-- user_likes表
mask_id TEXT REFERENCES masks(id) ON DELETE CASCADE,

-- user_favorites表
mask_id TEXT REFERENCES masks(id) ON DELETE CASCADE,
```

---

## 📊 数据验证清单

执行完所有脚本后，请验证以下内容：

### 表结构验证
- [ ] ✅ `masks` 表存在，主键为TEXT类型
- [ ] ✅ `drawing_steps` 表存在，mask_id为TEXT类型
- [ ] ✅ `user_profiles` 表存在
- [ ] ✅ `user_likes` 表存在，mask_id为TEXT类型
- [ ] ✅ `user_favorites` 表存在，mask_id为TEXT类型

### 数据验证
- [ ] ✅ masks表有9条官方脸谱记录
- [ ] ✅ drawing_steps表有绘制步骤数据
- [ ] ✅ 所有脸谱的is_official和is_approved都为true

### 功能验证
- [ ] ✅ 行级安全策略已启用
- [ ] ✅ 触发器正常工作
- [ ] ✅ 索引已创建

---

## 🧪 测试数据库连接

### 更新测试脚本

原有的测试脚本仍然有效，运行以下命令测试：

```bash
node test-database-connection.js
```

### 预期测试结果

```
🔍 开始测试数据库连接...

📋 检查环境变量:
✅ NEXT_PUBLIC_SUPABASE_URL: https://your-project.supabase.co
✅ NEXT_PUBLIC_SUPABASE_ANON_KEY: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...

🔗 测试1: 检查数据库连接
✅ 数据库连接成功

📊 测试2: 检查表结构
✅ 表 masks 存在且可访问
✅ 表 drawing_steps 存在且可访问
✅ 表 user_profiles 存在且可访问
✅ 表 user_likes 存在且可访问
✅ 表 user_favorites 存在且可访问

📝 测试3: 检查初始数据
✅ 找到 9 个官方脸谱:
   - 关羽脸谱 (关羽)
   - 包拯脸谱 (包拯)
   - 曹操脸谱 (曹操)
   - 张飞脸谱 (张飞)
   - 窦尔敦脸谱 (窦尔敦)
   - 杨七郎脸谱 (杨七郎)
   - 蒋干脸谱 (蒋干)
   - 刘备脸谱 (刘备)
   - 孙悟空脸谱 (孙悟空)

🎨 测试4: 检查绘制步骤数据
✅ 找到绘制步骤数据

🔒 测试5: 检查行级安全策略
✅ RLS策略正常工作（未认证用户无法插入数据）

🎉 数据库连接测试完成！
```

---

## 🚀 下一步操作

### 1. 重启应用程序

```bash
# 停止当前服务器 (Ctrl+C)
# 重新启动
npx next dev -H 0.0.0.0 -p 3002
```

### 2. 测试用户注册

1. 访问: https://received-title-pairs-employees.trycloudflare.com
2. 点击 "注册" 按钮
3. 填写注册信息并提交
4. 验证注册成功

### 3. 测试脸谱功能

1. 登录后点击 "添加脸谱"
2. 填写脸谱信息并提交
3. 验证脸谱添加成功

### 4. 测试社区功能

1. 点击脸谱的点赞按钮
2. 添加脸谱到收藏
3. 验证统计数据更新

---

## 🔍 故障排除

### 如果清理脚本执行失败

```sql
-- 手动删除表（如果清理脚本失败）
DROP TABLE IF EXISTS user_favorites CASCADE;
DROP TABLE IF EXISTS user_likes CASCADE;
DROP TABLE IF EXISTS drawing_steps CASCADE;
DROP TABLE IF EXISTS user_profiles CASCADE;
DROP TABLE IF EXISTS masks CASCADE;
DROP TYPE IF EXISTS role_type CASCADE;
```

### 如果架构脚本执行失败

1. 检查是否有语法错误
2. 确认清理脚本已成功执行
3. 逐段执行脚本，定位问题

### 如果种子数据插入失败

1. 确认架构脚本已成功执行
2. 检查数据格式是否正确
3. 验证外键约束是否正确

---

## 📞 技术支持

### 常见错误解决

**错误**: `relation "masks" does not exist`
**解决**: 确认架构脚本已正确执行

**错误**: `insert or update on table violates foreign key constraint`
**解决**: 检查外键关系和数据完整性

**错误**: `new row violates row-level security policy`
**解决**: 这是正常的，表示RLS策略正常工作

### 联系支持

如果遇到其他问题，请提供：
1. 具体的错误信息
2. 执行的SQL语句
3. Supabase项目配置信息

**修复状态**: ✅ **完整解决方案已提供**  
**预计修复时间**: 15-30分钟  
**成功率**: 99%（按步骤执行）
