module.exports = {

"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/data/masks.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "masksByColor": ()=>masksByColor,
    "masksByRole": ()=>masksByRole,
    "operaMasks": ()=>operaMasks
});
const operaMasks = [
    {
        id: 'guanyu',
        name: '关羽脸谱',
        character: '关羽',
        roleCategory: '净',
        colorCategory: '红脸',
        mainColors: [
            '#DC143C',
            '#FFD700',
            '#000000'
        ],
        culturalBackground: {
            origin: '三国时期蜀汉名将，被尊为武圣',
            personality: '忠义勇武，刚正不阿，义薄云天',
            symbolism: '忠诚、正义、勇敢的象征',
            historicalPeriod: '东汉末年-三国时期'
        },
        colorMeaning: {
            '红色': '忠勇正义，赤胆忠心',
            '金色': '神圣威严，地位崇高',
            '黑色': '刚毅坚定，不可动摇'
        },
        relatedOperas: [
            {
                name: '单刀会',
                description: '关羽单刀赴会的故事',
                period: '三国'
            },
            {
                name: '华容道',
                description: '关羽义释曹操',
                period: '三国'
            },
            {
                name: '走麦城',
                description: '关羽败走麦城',
                period: '三国'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=关羽',
            fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=关羽脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹红色底色',
                duration: 1000,
                color: '#DC143C'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制浓眉',
                duration: 800,
                color: '#000000'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画眼部轮廓',
                duration: 1200,
                color: '#000000'
            },
            {
                id: 4,
                name: '鼻梁',
                description: '描绘鼻梁线条',
                duration: 600,
                color: '#000000'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加金色装饰',
                duration: 1000,
                color: '#FFD700'
            }
        ],
        difficulty: 'medium',
        popularity: 10,
        tags: [
            '三国',
            '武将',
            '忠义',
            '经典'
        ]
    },
    {
        id: 'baogong',
        name: '包拯脸谱',
        character: '包拯',
        roleCategory: '净',
        colorCategory: '黑脸',
        mainColors: [
            '#000000',
            '#FFFFFF',
            '#FFD700'
        ],
        culturalBackground: {
            origin: '北宋名臣，以清廉公正著称',
            personality: '铁面无私，执法如山，清正廉洁',
            symbolism: '公正执法，清廉为民的象征',
            historicalPeriod: '北宋时期'
        },
        colorMeaning: {
            '黑色': '公正严明，铁面无私',
            '白色': '清廉正直，一尘不染',
            '金色': '威严庄重，地位尊崇'
        },
        relatedOperas: [
            {
                name: '铡美案',
                description: '包拯铡驸马陈世美',
                period: '宋代'
            },
            {
                name: '打龙袍',
                description: '包拯怒斥宋仁宗',
                period: '宋代'
            },
            {
                name: '赤桑镇',
                description: '包拯审案的故事',
                period: '宋代'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/000000/FFFFFF?text=包拯',
            fullSize: 'https://via.placeholder.com/600x600/000000/FFFFFF?text=包拯脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹黑色底色',
                duration: 1000,
                color: '#000000'
            },
            {
                id: 2,
                name: '额头',
                description: '绘制白色月牙',
                duration: 800,
                color: '#FFFFFF'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画眼部轮廓',
                duration: 1000,
                color: '#FFFFFF'
            },
            {
                id: 4,
                name: '鼻翼',
                description: '描绘鼻翼线条',
                duration: 600,
                color: '#FFFFFF'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加金色细节',
                duration: 800,
                color: '#FFD700'
            }
        ],
        difficulty: 'easy',
        popularity: 9,
        tags: [
            '宋代',
            '清官',
            '正义',
            '经典'
        ]
    },
    {
        id: 'caocao',
        name: '曹操脸谱',
        character: '曹操',
        roleCategory: '净',
        colorCategory: '白脸',
        mainColors: [
            '#FFFFFF',
            '#000000',
            '#DC143C'
        ],
        culturalBackground: {
            origin: '东汉末年政治家、军事家、文学家',
            personality: '奸诈狡猾，野心勃勃，但才华横溢',
            symbolism: '奸诈、权谋、复杂人性的象征',
            historicalPeriod: '东汉末年-三国时期'
        },
        colorMeaning: {
            '白色': '奸诈狡猾，阴险毒辣',
            '黑色': '深沉城府，心机深重',
            '红色': '暴戾之气，杀伐果断'
        },
        relatedOperas: [
            {
                name: '捉放曹',
                description: '陈宫捉放曹操',
                period: '三国'
            },
            {
                name: '击鼓骂曹',
                description: '祢衡击鼓骂曹',
                period: '三国'
            },
            {
                name: '群英会',
                description: '曹操与群雄斗智',
                period: '三国'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/FFFFFF/000000?text=曹操',
            fullSize: 'https://via.placeholder.com/600x600/FFFFFF/000000?text=曹操脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹白色底色',
                duration: 1000,
                color: '#FFFFFF'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制黑色浓眉',
                duration: 800,
                color: '#000000'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画眼部轮廓',
                duration: 1200,
                color: '#000000'
            },
            {
                id: 4,
                name: '鼻梁',
                description: '描绘鼻梁阴影',
                duration: 600,
                color: '#000000'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加红色细节',
                duration: 1000,
                color: '#DC143C'
            }
        ],
        difficulty: 'medium',
        popularity: 8,
        tags: [
            '三国',
            '奸雄',
            '复杂',
            '经典'
        ]
    },
    {
        id: 'zhangfei',
        name: '张飞脸谱',
        character: '张飞',
        roleCategory: '净',
        colorCategory: '黑脸',
        mainColors: [
            '#000000',
            '#FFFFFF',
            '#DC143C'
        ],
        culturalBackground: {
            origin: '三国时期蜀汉名将，刘备义弟',
            personality: '勇猛粗犷，嫉恶如仇，忠义豪爽',
            symbolism: '勇猛、正直、豪爽的象征',
            historicalPeriod: '东汉末年-三国时期'
        },
        colorMeaning: {
            '黑色': '刚直勇猛，正气凛然',
            '白色': '纯真豪爽，心无城府',
            '红色': '热血沸腾，义气冲天'
        },
        relatedOperas: [
            {
                name: '长坂坡',
                description: '张飞大战长坂坡',
                period: '三国'
            },
            {
                name: '古城会',
                description: '张飞误会关羽',
                period: '三国'
            },
            {
                name: '芦花荡',
                description: '张飞智取芦花荡',
                period: '三国'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/000000/FFFFFF?text=张飞',
            fullSize: 'https://via.placeholder.com/600x600/000000/FFFFFF?text=张飞脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹黑色底色',
                duration: 1000,
                color: '#000000'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制白色粗眉',
                duration: 800,
                color: '#FFFFFF'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画眼部轮廓',
                duration: 1000,
                color: '#FFFFFF'
            },
            {
                id: 4,
                name: '鼻翼',
                description: '描绘鼻翼线条',
                duration: 600,
                color: '#FFFFFF'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加红色装饰',
                duration: 800,
                color: '#DC143C'
            }
        ],
        difficulty: 'easy',
        popularity: 8,
        tags: [
            '三国',
            '武将',
            '勇猛',
            '豪爽'
        ]
    },
    {
        id: 'doulujin',
        name: '窦尔敦脸谱',
        character: '窦尔敦',
        roleCategory: '净',
        colorCategory: '蓝脸',
        mainColors: [
            '#0066CC',
            '#FFFFFF',
            '#FFD700'
        ],
        culturalBackground: {
            origin: '清代绿林好汉，盗侠传奇人物',
            personality: '刚强勇猛，侠肝义胆，不畏强权',
            symbolism: '刚强、勇敢、反抗精神的象征',
            historicalPeriod: '清代'
        },
        colorMeaning: {
            '蓝色': '刚强勇猛，桀骜不驯',
            '白色': '正直豪爽，光明磊落',
            '金色': '英雄气概，不凡身份'
        },
        relatedOperas: [
            {
                name: '盗御马',
                description: '窦尔敦盗取御马',
                period: '清代'
            },
            {
                name: '连环套',
                description: '窦尔敦中计被擒',
                period: '清代'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/0066CC/FFFFFF?text=窦尔敦',
            fullSize: 'https://via.placeholder.com/600x600/0066CC/FFFFFF?text=窦尔敦脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹蓝色底色',
                duration: 1000,
                color: '#0066CC'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制白色眉毛',
                duration: 800,
                color: '#FFFFFF'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画眼部轮廓',
                duration: 1000,
                color: '#FFFFFF'
            },
            {
                id: 4,
                name: '鼻梁',
                description: '描绘鼻梁线条',
                duration: 600,
                color: '#FFFFFF'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加金色装饰',
                duration: 800,
                color: '#FFD700'
            }
        ],
        difficulty: 'medium',
        popularity: 7,
        tags: [
            '清代',
            '绿林',
            '侠客',
            '勇猛'
        ]
    },
    {
        id: 'yangqilang',
        name: '杨七郎脸谱',
        character: '杨七郎',
        roleCategory: '生',
        colorCategory: '红脸',
        mainColors: [
            '#DC143C',
            '#FFD700',
            '#000000'
        ],
        culturalBackground: {
            origin: '北宋杨家将中的七子杨延嗣',
            personality: '英勇善战，忠君爱国，血性男儿',
            symbolism: '忠勇、牺牲、家国情怀的象征',
            historicalPeriod: '北宋时期'
        },
        colorMeaning: {
            '红色': '忠勇热血，为国捐躯',
            '金色': '英雄本色，光耀门第',
            '黑色': '刚毅果敢，义无反顾'
        },
        relatedOperas: [
            {
                name: '杨家将',
                description: '杨家将抗辽的故事',
                period: '宋代'
            },
            {
                name: '四郎探母',
                description: '杨四郎探望母亲',
                period: '宋代'
            },
            {
                name: '穆桂英挂帅',
                description: '穆桂英率军出征',
                period: '宋代'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=杨七郎',
            fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=杨七郎脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹红色底色',
                duration: 1000,
                color: '#DC143C'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制黑色剑眉',
                duration: 800,
                color: '#000000'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画眼部轮廓',
                duration: 1000,
                color: '#000000'
            },
            {
                id: 4,
                name: '鼻梁',
                description: '描绘鼻梁线条',
                duration: 600,
                color: '#000000'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加金色装饰',
                duration: 800,
                color: '#FFD700'
            }
        ],
        difficulty: 'medium',
        popularity: 7,
        tags: [
            '宋代',
            '杨家将',
            '忠勇',
            '英雄'
        ]
    },
    {
        id: 'yangguifei',
        name: '杨贵妃脸谱',
        character: '杨贵妃',
        roleCategory: '旦',
        colorCategory: '红脸',
        mainColors: [
            '#FFB6C1',
            '#FFD700',
            '#DC143C'
        ],
        culturalBackground: {
            origin: '唐代著名美女，唐玄宗宠妃',
            personality: '美丽动人，聪慧机敏，但也任性娇纵',
            symbolism: '美丽、爱情、悲剧的象征',
            historicalPeriod: '唐代'
        },
        colorMeaning: {
            '粉红色': '娇美动人，温柔如水',
            '金色': '富贵荣华，地位尊贵',
            '红色': '热情如火，爱情炽烈'
        },
        relatedOperas: [
            {
                name: '贵妃醉酒',
                description: '杨贵妃醉酒的故事',
                period: '唐代'
            },
            {
                name: '长生殿',
                description: '唐玄宗与杨贵妃的爱情',
                period: '唐代'
            },
            {
                name: '马嵬坡',
                description: '杨贵妃马嵬坡之死',
                period: '唐代'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/FFB6C1/FFFFFF?text=杨贵妃',
            fullSize: 'https://via.placeholder.com/600x600/FFB6C1/FFFFFF?text=杨贵妃脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹粉色底色',
                duration: 1000,
                color: '#FFB6C1'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制柳叶眉',
                duration: 800,
                color: '#000000'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画凤眼轮廓',
                duration: 1200,
                color: '#000000'
            },
            {
                id: 4,
                name: '唇部',
                description: '描绘樱桃小口',
                duration: 600,
                color: '#DC143C'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加金色花钿',
                duration: 1000,
                color: '#FFD700'
            }
        ],
        difficulty: 'hard',
        popularity: 9,
        tags: [
            '唐代',
            '美女',
            '爱情',
            '悲剧'
        ]
    },
    {
        id: 'wusong',
        name: '武松脸谱',
        character: '武松',
        roleCategory: '净',
        colorCategory: '红脸',
        mainColors: [
            '#DC143C',
            '#000000',
            '#FFD700'
        ],
        culturalBackground: {
            origin: '水浒传中的英雄好汉，行者武松',
            personality: '勇猛无畏，嫉恶如仇，义薄云天',
            symbolism: '正义、勇敢、反抗精神的象征',
            historicalPeriod: '北宋时期'
        },
        colorMeaning: {
            '红色': '正义凛然，热血沸腾',
            '黑色': '刚毅果敢，不屈不挠',
            '金色': '英雄本色，光明磊落'
        },
        relatedOperas: [
            {
                name: '武松打虎',
                description: '武松景阳冈打虎',
                period: '宋代'
            },
            {
                name: '狮子楼',
                description: '武松杀西门庆',
                period: '宋代'
            },
            {
                name: '十字坡',
                description: '武松遇孙二娘',
                period: '宋代'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=武松',
            fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=武松脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹红色底色',
                duration: 1000,
                color: '#DC143C'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制浓黑剑眉',
                duration: 800,
                color: '#000000'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画虎目轮廓',
                duration: 1000,
                color: '#000000'
            },
            {
                id: 4,
                name: '鼻梁',
                description: '描绘挺直鼻梁',
                duration: 600,
                color: '#000000'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加金色装饰',
                duration: 800,
                color: '#FFD700'
            }
        ],
        difficulty: 'medium',
        popularity: 9,
        tags: [
            '水浒',
            '英雄',
            '正义',
            '勇猛'
        ]
    },
    {
        id: 'jianggan',
        name: '蒋干脸谱',
        character: '蒋干',
        roleCategory: '丑',
        colorCategory: '白脸',
        mainColors: [
            '#FFFFFF',
            '#000000',
            '#808080'
        ],
        culturalBackground: {
            origin: '三国时期人物，曹操谋士',
            personality: '自作聪明，好事多磨，常弄巧成拙',
            symbolism: '愚蠢、自负、滑稽的象征',
            historicalPeriod: '三国时期'
        },
        colorMeaning: {
            '白色': '愚蠢无知，自以为是',
            '黑色': '心机不深，容易上当',
            '灰色': '平庸无能，不值一提'
        },
        relatedOperas: [
            {
                name: '群英会',
                description: '蒋干中计盗书',
                period: '三国'
            },
            {
                name: '借东风',
                description: '诸葛亮借东风',
                period: '三国'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/FFFFFF/000000?text=蒋干',
            fullSize: 'https://via.placeholder.com/600x600/FFFFFF/000000?text=蒋干脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹白色底色',
                duration: 1000,
                color: '#FFFFFF'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制细眉',
                duration: 600,
                color: '#000000'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画小眼轮廓',
                duration: 800,
                color: '#000000'
            },
            {
                id: 4,
                name: '鼻部',
                description: '描绘尖鼻',
                duration: 400,
                color: '#000000'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加滑稽装饰',
                duration: 600,
                color: '#808080'
            }
        ],
        difficulty: 'easy',
        popularity: 6,
        tags: [
            '三国',
            '丑角',
            '滑稽',
            '愚蠢'
        ]
    },
    {
        id: 'liubei',
        name: '刘备脸谱',
        character: '刘备',
        roleCategory: '生',
        colorCategory: '红脸',
        mainColors: [
            '#DC143C',
            '#FFD700',
            '#000000'
        ],
        culturalBackground: {
            origin: '三国时期蜀汉开国皇帝',
            personality: '仁德宽厚，礼贤下士，志向远大',
            symbolism: '仁德、理想、领袖风范的象征',
            historicalPeriod: '东汉末年-三国时期'
        },
        colorMeaning: {
            '红色': '仁德之心，爱民如子',
            '金色': '帝王之相，天命所归',
            '黑色': '深沉稳重，胸怀大志'
        },
        relatedOperas: [
            {
                name: '三顾茅庐',
                description: '刘备三顾茅庐请诸葛亮',
                period: '三国'
            },
            {
                name: '甘露寺',
                description: '刘备招亲',
                period: '三国'
            },
            {
                name: '白帝城',
                description: '刘备托孤',
                period: '三国'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=刘备',
            fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=刘备脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹红色底色',
                duration: 1000,
                color: '#DC143C'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制慈眉',
                duration: 800,
                color: '#000000'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画慈目轮廓',
                duration: 1000,
                color: '#000000'
            },
            {
                id: 4,
                name: '胡须',
                description: '描绘长须',
                duration: 1200,
                color: '#000000'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加金色装饰',
                duration: 800,
                color: '#FFD700'
            }
        ],
        difficulty: 'medium',
        popularity: 8,
        tags: [
            '三国',
            '帝王',
            '仁德',
            '领袖'
        ]
    },
    {
        id: 'huangzhong',
        name: '黄忠脸谱',
        character: '黄忠',
        roleCategory: '净',
        colorCategory: '黄脸',
        mainColors: [
            '#FFD700',
            '#000000',
            '#DC143C'
        ],
        culturalBackground: {
            origin: '三国时期蜀汉五虎上将之一',
            personality: '老当益壮，勇猛善射，忠心耿耿',
            symbolism: '老骥伏枥、壮心不已的象征',
            historicalPeriod: '东汉末年-三国时期'
        },
        colorMeaning: {
            '黄色': '老成持重，经验丰富',
            '黑色': '刚毅坚定，不服老迈',
            '红色': '壮心不已，热血依然'
        },
        relatedOperas: [
            {
                name: '定军山',
                description: '黄忠定军山斩夏侯渊',
                period: '三国'
            },
            {
                name: '战长沙',
                description: '黄忠战关羽',
                period: '三国'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/FFD700/000000?text=黄忠',
            fullSize: 'https://via.placeholder.com/600x600/FFD700/000000?text=黄忠脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹黄色底色',
                duration: 1000,
                color: '#FFD700'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制白眉',
                duration: 800,
                color: '#FFFFFF'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画老目轮廓',
                duration: 1000,
                color: '#000000'
            },
            {
                id: 4,
                name: '胡须',
                description: '描绘白须',
                duration: 1200,
                color: '#FFFFFF'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加红色装饰',
                duration: 800,
                color: '#DC143C'
            }
        ],
        difficulty: 'medium',
        popularity: 7,
        tags: [
            '三国',
            '老将',
            '勇猛',
            '忠诚'
        ]
    },
    {
        id: 'machao',
        name: '马超脸谱',
        character: '马超',
        roleCategory: '净',
        colorCategory: '银脸',
        mainColors: [
            '#C0C0C0',
            '#000000',
            '#DC143C'
        ],
        culturalBackground: {
            origin: '三国时期蜀汉五虎上将之一，西凉马腾之子',
            personality: '英勇善战，威风凛凛，有万夫不当之勇',
            symbolism: '英勇、威武、西北豪杰的象征',
            historicalPeriod: '东汉末年-三国时期'
        },
        colorMeaning: {
            '银色': '英武不凡，光芒四射',
            '黑色': '刚毅果敢，威风凛凛',
            '红色': '热血沸腾，勇猛无敌'
        },
        relatedOperas: [
            {
                name: '战渭南',
                description: '马超大战曹操',
                period: '三国'
            },
            {
                name: '取成都',
                description: '马超助刘备取成都',
                period: '三国'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/C0C0C0/000000?text=马超',
            fullSize: 'https://via.placeholder.com/600x600/C0C0C0/000000?text=马超脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹银色底色',
                duration: 1000,
                color: '#C0C0C0'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制黑色剑眉',
                duration: 800,
                color: '#000000'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画鹰目轮廓',
                duration: 1000,
                color: '#000000'
            },
            {
                id: 4,
                name: '鼻梁',
                description: '描绘挺直鼻梁',
                duration: 600,
                color: '#000000'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加红色装饰',
                duration: 800,
                color: '#DC143C'
            }
        ],
        difficulty: 'hard',
        popularity: 7,
        tags: [
            '三国',
            '西凉',
            '英武',
            '威猛'
        ]
    },
    {
        id: 'zhaoyun',
        name: '赵云脸谱',
        character: '赵云',
        roleCategory: '生',
        colorCategory: '白脸',
        mainColors: [
            '#FFFFFF',
            '#000000',
            '#4169E1'
        ],
        culturalBackground: {
            origin: '三国时期蜀汉五虎上将之一，常山赵子龙',
            personality: '英勇善战，忠心耿耿，智勇双全',
            symbolism: '忠诚、勇敢、完美武将的象征',
            historicalPeriod: '东汉末年-三国时期'
        },
        colorMeaning: {
            '白色': '纯洁忠诚，品格高尚',
            '黑色': '刚毅果敢，意志坚定',
            '蓝色': '冷静睿智，深谋远虑'
        },
        relatedOperas: [
            {
                name: '长坂坡',
                description: '赵云长坂坡救阿斗',
                period: '三国'
            },
            {
                name: '截江夺斗',
                description: '赵云截江救阿斗',
                period: '三国'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/FFFFFF/000000?text=赵云',
            fullSize: 'https://via.placeholder.com/600x600/FFFFFF/000000?text=赵云脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹白色底色',
                duration: 1000,
                color: '#FFFFFF'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制黑色剑眉',
                duration: 800,
                color: '#000000'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画英目轮廓',
                duration: 1000,
                color: '#000000'
            },
            {
                id: 4,
                name: '鼻梁',
                description: '描绘挺直鼻梁',
                duration: 600,
                color: '#000000'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加蓝色装饰',
                duration: 800,
                color: '#4169E1'
            }
        ],
        difficulty: 'medium',
        popularity: 9,
        tags: [
            '三国',
            '完美',
            '忠诚',
            '英勇'
        ]
    },
    {
        id: 'sunwukong',
        name: '孙悟空脸谱',
        character: '孙悟空',
        roleCategory: '净',
        colorCategory: '金脸',
        mainColors: [
            '#FFD700',
            '#DC143C',
            '#000000'
        ],
        culturalBackground: {
            origin: '西游记中的齐天大圣，花果山美猴王',
            personality: '机智勇敢，神通广大，桀骜不驯',
            symbolism: '反抗精神、智慧勇敢的象征',
            historicalPeriod: '神话传说'
        },
        colorMeaning: {
            '金色': '神通广大，法力无边',
            '红色': '火眼金睛，热情如火',
            '黑色': '桀骜不驯，不畏权威'
        },
        relatedOperas: [
            {
                name: '大闹天宫',
                description: '孙悟空大闹天宫',
                period: '神话'
            },
            {
                name: '三打白骨精',
                description: '孙悟空三打白骨精',
                period: '神话'
            },
            {
                name: '真假美猴王',
                description: '真假美猴王大战',
                period: '神话'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/FFD700/000000?text=孙悟空',
            fullSize: 'https://via.placeholder.com/600x600/FFD700/000000?text=孙悟空脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹金色底色',
                duration: 1000,
                color: '#FFD700'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制火焰眉',
                duration: 1000,
                color: '#DC143C'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画火眼金睛',
                duration: 1200,
                color: '#DC143C'
            },
            {
                id: 4,
                name: '鼻部',
                description: '描绘猴鼻',
                duration: 600,
                color: '#000000'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加神话装饰',
                duration: 1000,
                color: '#000000'
            }
        ],
        difficulty: 'hard',
        popularity: 10,
        tags: [
            '西游记',
            '神话',
            '反抗',
            '智慧'
        ]
    }
];
const masksByRole = {
    '生': operaMasks.filter((mask)=>mask.roleCategory === '生'),
    '旦': operaMasks.filter((mask)=>mask.roleCategory === '旦'),
    '净': operaMasks.filter((mask)=>mask.roleCategory === '净'),
    '丑': operaMasks.filter((mask)=>mask.roleCategory === '丑')
};
const masksByColor = {
    '红脸': operaMasks.filter((mask)=>mask.colorCategory === '红脸'),
    '黑脸': operaMasks.filter((mask)=>mask.colorCategory === '黑脸'),
    '白脸': operaMasks.filter((mask)=>mask.colorCategory === '白脸'),
    '蓝脸': operaMasks.filter((mask)=>mask.colorCategory === '蓝脸'),
    '绿脸': operaMasks.filter((mask)=>mask.colorCategory === '绿脸'),
    '黄脸': operaMasks.filter((mask)=>mask.colorCategory === '黄脸'),
    '金脸': operaMasks.filter((mask)=>mask.colorCategory === '金脸'),
    '银脸': operaMasks.filter((mask)=>mask.colorCategory === '银脸')
};
}),
"[project]/src/hooks/useAppState.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "useAppState": ()=>useAppState
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
const STORAGE_KEY = 'beijing-opera-masks-state';
const defaultState = {
    filter: {},
    recentlyViewed: [],
    favorites: [],
    searchHistory: []
};
function useAppState() {
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(defaultState);
    // 从localStorage加载状态
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }, []);
    // 保存状态到localStorage
    const saveState = (newState)=>{
        const updatedState = {
            ...state,
            ...newState
        };
        setState(updatedState);
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    };
    // 更新筛选条件
    const updateFilter = (filter)=>{
        saveState({
            filter
        });
    };
    // 添加到最近查看
    const addToRecentlyViewed = (maskId)=>{
        const updated = [
            maskId,
            ...state.recentlyViewed.filter((id)=>id !== maskId)
        ].slice(0, 10);
        saveState({
            recentlyViewed: updated
        });
    };
    // 切换收藏状态
    const toggleFavorite = (maskId)=>{
        const updated = state.favorites.includes(maskId) ? state.favorites.filter((id)=>id !== maskId) : [
            ...state.favorites,
            maskId
        ];
        saveState({
            favorites: updated
        });
    };
    // 添加搜索历史
    const addToSearchHistory = (searchTerm)=>{
        if (!searchTerm.trim()) return;
        const updated = [
            searchTerm,
            ...state.searchHistory.filter((term)=>term !== searchTerm)
        ].slice(0, 10);
        saveState({
            searchHistory: updated
        });
    };
    // 清除搜索历史
    const clearSearchHistory = ()=>{
        saveState({
            searchHistory: []
        });
    };
    // 检查是否收藏
    const isFavorite = (maskId)=>{
        return state.favorites.includes(maskId);
    };
    // 获取最近查看的脸谱
    const getRecentlyViewedMasks = (masks)=>{
        return state.recentlyViewed.map((id)=>masks.find((mask)=>mask.id === id)).filter(Boolean);
    };
    // 获取收藏的脸谱
    const getFavoriteMasks = (masks)=>{
        return state.favorites.map((id)=>masks.find((mask)=>mask.id === id)).filter(Boolean);
    };
    return {
        // 状态
        filter: state.filter,
        recentlyViewed: state.recentlyViewed,
        favorites: state.favorites,
        searchHistory: state.searchHistory,
        // 操作
        updateFilter,
        addToRecentlyViewed,
        toggleFavorite,
        addToSearchHistory,
        clearSearchHistory,
        // 辅助函数
        isFavorite,
        getRecentlyViewedMasks,
        getFavoriteMasks
    };
}
}),
"[project]/src/utils/maskUtils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "filterMasks": ()=>filterMasks,
    "getAllColorCategories": ()=>getAllColorCategories,
    "getAllRoleCategories": ()=>getAllRoleCategories,
    "getMaskById": ()=>getMaskById,
    "getMaskStatistics": ()=>getMaskStatistics,
    "getRandomMasks": ()=>getRandomMasks,
    "getRelatedMasks": ()=>getRelatedMasks,
    "groupMasksByColor": ()=>groupMasksByColor,
    "groupMasksByRole": ()=>groupMasksByRole,
    "sortMasksByName": ()=>sortMasksByName,
    "sortMasksByPopularity": ()=>sortMasksByPopularity
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$masks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/masks.ts [app-ssr] (ecmascript)");
;
function filterMasks(masks, filter) {
    return masks.filter((mask)=>{
        // 按行当筛选
        if (filter.roleCategory && mask.roleCategory !== filter.roleCategory) {
            return false;
        }
        // 按颜色筛选
        if (filter.colorCategory && mask.colorCategory !== filter.colorCategory) {
            return false;
        }
        // 按难度筛选
        if (filter.difficulty && mask.difficulty !== filter.difficulty) {
            return false;
        }
        // 按搜索词筛选
        if (filter.searchTerm) {
            const searchTerm = filter.searchTerm.toLowerCase();
            const searchableText = [
                mask.name,
                mask.character,
                mask.culturalBackground.origin,
                mask.culturalBackground.personality,
                ...mask.tags
            ].join(' ').toLowerCase();
            if (!searchableText.includes(searchTerm)) {
                return false;
            }
        }
        return true;
    });
}
function sortMasksByPopularity(masks, ascending = false) {
    return [
        ...masks
    ].sort((a, b)=>{
        return ascending ? a.popularity - b.popularity : b.popularity - a.popularity;
    });
}
function sortMasksByName(masks, ascending = true) {
    return [
        ...masks
    ].sort((a, b)=>{
        return ascending ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name);
    });
}
function getAllRoleCategories() {
    return [
        '生',
        '旦',
        '净',
        '丑'
    ];
}
function getAllColorCategories() {
    return [
        '红脸',
        '黑脸',
        '白脸',
        '蓝脸',
        '绿脸',
        '黄脸',
        '金脸',
        '银脸'
    ];
}
function groupMasksByRole(masks) {
    const groups = {
        '生': [],
        '旦': [],
        '净': [],
        '丑': []
    };
    masks.forEach((mask)=>{
        groups[mask.roleCategory].push(mask);
    });
    return groups;
}
function groupMasksByColor(masks) {
    const groups = {
        '红脸': [],
        '黑脸': [],
        '白脸': [],
        '蓝脸': [],
        '绿脸': [],
        '黄脸': [],
        '金脸': [],
        '银脸': []
    };
    masks.forEach((mask)=>{
        groups[mask.colorCategory].push(mask);
    });
    return groups;
}
function getRandomMasks(count, excludeIds = []) {
    const availableMasks = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$masks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["operaMasks"].filter((mask)=>!excludeIds.includes(mask.id));
    const shuffled = [
        ...availableMasks
    ].sort(()=>Math.random() - 0.5);
    return shuffled.slice(0, count);
}
function getMaskById(id) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$masks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["operaMasks"].find((mask)=>mask.id === id);
}
function getRelatedMasks(maskId, count = 3) {
    const currentMask = getMaskById(maskId);
    if (!currentMask) return [];
    const otherMasks = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$masks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["operaMasks"].filter((mask)=>mask.id !== maskId);
    // 计算标签相似度
    const masksWithScore = otherMasks.map((mask)=>{
        const commonTags = mask.tags.filter((tag)=>currentMask.tags.includes(tag));
        const score = commonTags.length;
        return {
            mask,
            score
        };
    });
    // 按相似度排序并返回前几个
    return masksWithScore.sort((a, b)=>b.score - a.score).slice(0, count).map((item)=>item.mask);
}
function getMaskStatistics() {
    const roleStats = groupMasksByRole(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$masks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["operaMasks"]);
    const colorStats = groupMasksByColor(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$masks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["operaMasks"]);
    return {
        total: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$masks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["operaMasks"].length,
        byRole: Object.entries(roleStats).map(([role, masks])=>({
                category: role,
                count: masks.length
            })),
        byColor: Object.entries(colorStats).map(([color, masks])=>({
                category: color,
                count: masks.length
            })),
        averagePopularity: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$masks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["operaMasks"].reduce((sum, mask)=>sum + mask.popularity, 0) / __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$masks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["operaMasks"].length,
        difficultyDistribution: {
            easy: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$masks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["operaMasks"].filter((m)=>m.difficulty === 'easy').length,
            medium: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$masks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["operaMasks"].filter((m)=>m.difficulty === 'medium').length,
            hard: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$masks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["operaMasks"].filter((m)=>m.difficulty === 'hard').length
        }
    };
}
}),
"[project]/src/utils/maskImageGenerator.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// 京剧脸谱图片生成器
// 使用Canvas API生成高质量的脸谱图片
__turbopack_context__.s({
    "MaskImageGenerator": ()=>MaskImageGenerator,
    "maskConfigs": ()=>maskConfigs
});
class MaskImageGenerator {
    canvas;
    ctx;
    constructor(width = 300, height = 300){
        this.canvas = document.createElement('canvas');
        this.canvas.width = width;
        this.canvas.height = height;
        this.ctx = this.canvas.getContext('2d');
    }
    generateMaskImage(config) {
        const { ctx, canvas } = this;
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        // 设置高质量渲染
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        // 设置统一的背景
        const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, Math.max(canvas.width, canvas.height) / 2);
        gradient.addColorStop(0, '#FFFFFF');
        gradient.addColorStop(1, '#F8F9FA');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        // 绘制脸部轮廓
        this.drawFaceShape(config, centerX, centerY);
        // 绘制五官（按正确顺序）
        this.drawEyebrows(config, centerX, centerY);
        this.drawEyes(config, centerX, centerY);
        this.drawNose(config, centerX, centerY);
        this.drawMouth(config, centerX, centerY);
        // 绘制装饰
        this.drawDecorations(config, centerX, centerY);
        // 移除角色名称绘制，让脸谱图片更加纯净
        // this.drawCharacterName(config, centerX, centerY);
        return canvas.toDataURL('image/png', 0.95);
    }
    drawFaceShape(config, centerX, centerY) {
        const { ctx } = this;
        // 创建更精美的渐变效果
        const gradient = ctx.createRadialGradient(centerX, centerY - 40, 0, centerX, centerY, 130);
        gradient.addColorStop(0, config.mainColors[0]);
        gradient.addColorStop(0.7, config.mainColors[1] || config.mainColors[0]);
        gradient.addColorStop(1, this.darkenColor(config.mainColors[1] || config.mainColors[0], 0.2));
        ctx.fillStyle = gradient;
        ctx.strokeStyle = '#2D3748';
        ctx.lineWidth = 4;
        ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        ctx.shadowBlur = 8;
        ctx.shadowOffsetX = 2;
        ctx.shadowOffsetY = 2;
        ctx.beginPath();
        if (config.faceShape === 'oval') {
            ctx.ellipse(centerX, centerY - 10, 105, 125, 0, 0, 2 * Math.PI);
        } else if (config.faceShape === 'round') {
            ctx.arc(centerX, centerY - 5, 115, 0, 2 * Math.PI);
        } else {
            // angular - 更精细的棱角脸型
            ctx.moveTo(centerX - 95, centerY - 110);
            ctx.lineTo(centerX + 95, centerY - 110);
            ctx.lineTo(centerX + 115, centerY - 10);
            ctx.lineTo(centerX + 95, centerY + 125);
            ctx.lineTo(centerX - 95, centerY + 125);
            ctx.lineTo(centerX - 115, centerY - 10);
            ctx.closePath();
        }
        ctx.fill();
        ctx.stroke();
        // 重置阴影
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
    }
    // 辅助函数：加深颜色
    darkenColor(color, factor) {
        if (color.startsWith('#')) {
            const hex = color.slice(1);
            const r = parseInt(hex.substr(0, 2), 16);
            const g = parseInt(hex.substr(2, 2), 16);
            const b = parseInt(hex.substr(4, 2), 16);
            const newR = Math.floor(r * (1 - factor));
            const newG = Math.floor(g * (1 - factor));
            const newB = Math.floor(b * (1 - factor));
            return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
        }
        return color;
    }
    drawEyes(config, centerX, centerY) {
        const { ctx } = this;
        const eyeY = centerY - 20;
        const eyeWidth = config.eyeStyle === 'fierce' ? 25 : 20;
        const eyeHeight = config.eyeStyle === 'gentle' ? 12 : 15;
        // 左眼
        ctx.fillStyle = 'white';
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.ellipse(centerX - 35, eyeY, eyeWidth, eyeHeight, 0, 0, 2 * Math.PI);
        ctx.fill();
        ctx.stroke();
        // 右眼
        ctx.beginPath();
        ctx.ellipse(centerX + 35, eyeY, eyeWidth, eyeHeight, 0, 0, 2 * Math.PI);
        ctx.fill();
        ctx.stroke();
        // 眼珠
        ctx.fillStyle = '#000';
        ctx.beginPath();
        ctx.arc(centerX - 35, eyeY, 8, 0, 2 * Math.PI);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(centerX + 35, eyeY, 8, 0, 2 * Math.PI);
        ctx.fill();
    }
    drawEyebrows(config, centerX, centerY) {
        const { ctx } = this;
        const browY = centerY - 50;
        ctx.strokeStyle = '#000';
        ctx.lineWidth = config.eyeStyle === 'fierce' ? 8 : 5;
        ctx.lineCap = 'round';
        // 左眉
        ctx.beginPath();
        if (config.eyeStyle === 'fierce') {
            ctx.moveTo(centerX - 60, browY);
            ctx.quadraticCurveTo(centerX - 35, browY - 15, centerX - 10, browY);
        } else {
            ctx.moveTo(centerX - 55, browY);
            ctx.quadraticCurveTo(centerX - 35, browY - 10, centerX - 15, browY);
        }
        ctx.stroke();
        // 右眉
        ctx.beginPath();
        if (config.eyeStyle === 'fierce') {
            ctx.moveTo(centerX + 10, browY);
            ctx.quadraticCurveTo(centerX + 35, browY - 15, centerX + 60, browY);
        } else {
            ctx.moveTo(centerX + 15, browY);
            ctx.quadraticCurveTo(centerX + 35, browY - 10, centerX + 55, browY);
        }
        ctx.stroke();
    }
    drawNose(config, centerX, centerY) {
        const { ctx } = this;
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 3;
        ctx.lineCap = 'round';
        ctx.beginPath();
        ctx.moveTo(centerX, centerY + 10);
        ctx.lineTo(centerX, centerY + 40);
        ctx.moveTo(centerX - 8, centerY + 35);
        ctx.lineTo(centerX + 8, centerY + 35);
        ctx.stroke();
    }
    drawMouth(config, centerX, centerY) {
        const { ctx } = this;
        const mouthY = centerY + 60;
        ctx.fillStyle = config.mainColors[0] === '#FFFFFF' ? '#FF0000' : '#8B0000';
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.ellipse(centerX, mouthY, 15, 8, 0, 0, 2 * Math.PI);
        ctx.fill();
        ctx.stroke();
    }
    drawDecorations(config, centerX, centerY) {
        const { ctx } = this;
        // 根据角色添加特殊装饰
        if (config.decorations.includes('forehead-mark')) {
            ctx.fillStyle = '#FFD700';
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(centerX, centerY - 80);
            ctx.lineTo(centerX + 10, centerY - 60);
            ctx.lineTo(centerX - 10, centerY - 60);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
        }
        if (config.decorations.includes('cheek-patterns')) {
            ctx.strokeStyle = config.mainColors[1] || '#000';
            ctx.lineWidth = 2;
            // 左脸装饰
            ctx.beginPath();
            ctx.arc(centerX - 70, centerY + 20, 15, 0, 2 * Math.PI);
            ctx.stroke();
            // 右脸装饰
            ctx.beginPath();
            ctx.arc(centerX + 70, centerY + 20, 15, 0, 2 * Math.PI);
            ctx.stroke();
        }
        if (config.decorations.includes('beard')) {
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 4;
            ctx.lineCap = 'round';
            // 胡须线条
            for(let i = 0; i < 3; i++){
                ctx.beginPath();
                ctx.moveTo(centerX - 60 + i * 20, centerY + 80 + i * 5);
                ctx.quadraticCurveTo(centerX, centerY + 100 + i * 5, centerX + 60 - i * 20, centerY + 80 + i * 5);
                ctx.stroke();
            }
        }
    }
}
const maskConfigs = {
    guanyu: {
        id: 'guanyu',
        name: '关羽',
        character: '关羽',
        mainColors: [
            '#DC2626',
            '#991B1B'
        ],
        faceShape: 'oval',
        eyeStyle: 'fierce',
        decorations: [
            'forehead-mark',
            'beard'
        ]
    },
    caocao: {
        id: 'caocao',
        name: '曹操',
        character: '曹操',
        mainColors: [
            '#FFFFFF',
            '#F3F4F6'
        ],
        faceShape: 'angular',
        eyeStyle: 'fierce',
        decorations: [
            'cheek-patterns'
        ]
    },
    zhangfei: {
        id: 'zhangfei',
        name: '张飞',
        character: '张飞',
        mainColors: [
            '#1F2937',
            '#000000'
        ],
        faceShape: 'round',
        eyeStyle: 'fierce',
        decorations: [
            'beard'
        ]
    },
    huangzhong: {
        id: 'huangzhong',
        name: '黄忠',
        character: '黄忠',
        mainColors: [
            '#FFD700',
            '#F59E0B'
        ],
        faceShape: 'oval',
        eyeStyle: 'normal',
        decorations: [
            'forehead-mark',
            'beard'
        ]
    },
    diaochan: {
        id: 'diaochan',
        name: '貂蝉',
        character: '貂蝉',
        mainColors: [
            '#FFC0CB',
            '#F8BBD9'
        ],
        faceShape: 'oval',
        eyeStyle: 'gentle',
        decorations: [
            'forehead-mark'
        ]
    },
    baozhen: {
        id: 'baozhen',
        name: '包拯',
        character: '包拯',
        mainColors: [
            '#000000',
            '#1F2937'
        ],
        faceShape: 'round',
        eyeStyle: 'fierce',
        decorations: [
            'forehead-mark'
        ]
    },
    douerdun: {
        id: 'douerdun',
        name: '窦尔敦',
        character: '窦尔敦',
        mainColors: [
            '#1E40AF',
            '#3B82F6'
        ],
        faceShape: 'angular',
        eyeStyle: 'fierce',
        decorations: [
            'cheek-patterns'
        ]
    },
    dianwei: {
        id: 'dianwei',
        name: '典韦',
        character: '典韦',
        mainColors: [
            '#7C2D12',
            '#DC2626'
        ],
        faceShape: 'round',
        eyeStyle: 'fierce',
        decorations: [
            'beard'
        ]
    },
    likui: {
        id: 'likui',
        name: '李逵',
        character: '李逵',
        mainColors: [
            '#1F2937',
            '#000000'
        ],
        faceShape: 'round',
        eyeStyle: 'fierce',
        decorations: [
            'beard'
        ]
    },
    sunwukong: {
        id: 'sunwukong',
        name: '孙悟空',
        character: '孙悟空',
        mainColors: [
            '#F59E0B',
            '#FFD700'
        ],
        faceShape: 'oval',
        eyeStyle: 'fierce',
        decorations: [
            'forehead-mark',
            'cheek-patterns'
        ]
    },
    zhubaijie: {
        id: 'zhubaijie',
        name: '猪八戒',
        character: '猪八戒',
        mainColors: [
            '#EC4899',
            '#F472B6'
        ],
        faceShape: 'round',
        eyeStyle: 'normal',
        decorations: [
            'cheek-patterns'
        ]
    },
    baigu: {
        id: 'baigu',
        name: '白骨精',
        character: '白骨精',
        mainColors: [
            '#F3F4F6',
            '#E5E7EB'
        ],
        faceShape: 'angular',
        eyeStyle: 'fierce',
        decorations: [
            'forehead-mark'
        ]
    },
    huajiangjun: {
        id: 'huajiangjun',
        name: '花脸将军',
        character: '花脸将军',
        mainColors: [
            '#7C3AED',
            '#A855F7'
        ],
        faceShape: 'oval',
        eyeStyle: 'fierce',
        decorations: [
            'forehead-mark',
            'cheek-patterns'
        ]
    },
    qingyi: {
        id: 'qingyi',
        name: '青衣花旦',
        character: '青衣',
        mainColors: [
            '#10B981',
            '#34D399'
        ],
        faceShape: 'oval',
        eyeStyle: 'gentle',
        decorations: [
            'forehead-mark'
        ]
    },
    xiaosheng: {
        id: 'xiaosheng',
        name: '小生角色',
        character: '小生',
        mainColors: [
            '#3B82F6',
            '#60A5FA'
        ],
        faceShape: 'oval',
        eyeStyle: 'gentle',
        decorations: []
    }
};
}),
"[project]/src/components/mask/MaskImage.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "MaskImage": ()=>MaskImage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$maskImageGenerator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/maskImageGenerator.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
// 真实脸谱图片URL映射
// 为特定角色使用真实的京剧脸谱图片
const maskImageUrls = {
    // 关羽使用真实的京剧脸谱图片 - 使用本地存储的高质量关羽红脸脸谱
    'guanyu': '/images/masks/guanyu-mask.svg',
    // 其他角色暂时保持placeholder，后续可以逐步替换
    'caocao': '',
    'zhangfei': '',
    'huangzhong': '',
    'diaochan': '',
    'baozhen': '',
    'douerdun': '',
    'dianwei': '',
    'likui': '',
    'sunwukong': '',
    'zhubaijie': '',
    'baigu': '',
    'huajiangjun': '',
    'qingyi': '',
    'xiaosheng': '' // 小生角色 - 使用Canvas生成
};
function MaskImage({ mask, width = 300, height = 300, className }) {
    const [canvasImage, setCanvasImage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [realImage, setRealImage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [imageError, setImageError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    // 获取真实脸谱图片URL
    const realImageUrl = maskImageUrls[mask.id];
    // 生成Canvas图片作为后备方案
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }, [
        mask.id,
        mask.name,
        mask.character,
        mask.mainColors,
        width,
        height
    ]);
    // 如果有真实图片URL，尝试加载真实图片
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (realImageUrl) {
            const img = new Image();
            img.crossOrigin = 'anonymous';
            // 设置加载超时
            const timeout = setTimeout(()=>{
                setImageError(true);
                setRealImage(null);
                setIsLoading(false);
                console.warn(`图片加载超时: ${realImageUrl}`);
            }, 5000); // 5秒超时
            img.onload = ()=>{
                clearTimeout(timeout);
                setRealImage(realImageUrl);
                setImageError(false);
                setIsLoading(false);
                console.log(`成功加载真实脸谱图片: ${mask.name}`);
            };
            img.onerror = ()=>{
                clearTimeout(timeout);
                setImageError(true);
                setRealImage(null);
                setIsLoading(false);
                console.warn(`真实脸谱图片加载失败，回退到Canvas生成: ${mask.name}`);
            };
            img.src = realImageUrl;
            // 清理函数
            return ()=>{
                clearTimeout(timeout);
            };
        } else {
            // 没有真实图片URL，直接使用Canvas生成的图片
            setIsLoading(false);
        }
    }, [
        realImageUrl,
        mask.name
    ]);
    // 如果正在加载，显示加载状态
    if (isLoading) {
        return renderLoadingState();
    }
    // 优先使用真实图片，如果加载失败或没有真实图片则使用Canvas生成的图片
    if (realImage && !imageError) {
        return renderRealImage();
    } else if (canvasImage) {
        return renderCanvasImage();
    } else {
        return renderLoadingState();
    }
    //TURBOPACK unreachable
    ;
    // 渲染加载状态
    function renderLoadingState() {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: className,
            style: {
                width,
                height,
                position: 'relative',
                overflow: 'hidden',
                borderRadius: '0.5rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#F3F4F6',
                color: '#9CA3AF'
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    textAlign: 'center'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            fontSize: '2rem',
                            marginBottom: '0.5rem'
                        },
                        children: "🎭"
                    }, void 0, false, {
                        fileName: "[project]/src/components/mask/MaskImage.tsx",
                        lineNumber: 146,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            fontSize: '0.875rem'
                        },
                        children: "生成中..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/mask/MaskImage.tsx",
                        lineNumber: 147,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/mask/MaskImage.tsx",
                lineNumber: 145,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/mask/MaskImage.tsx",
            lineNumber: 130,
            columnNumber: 7
        }, this);
    }
    // 真实图片渲染
    function renderRealImage() {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: className,
            style: {
                width,
                height,
                position: 'relative',
                overflow: 'hidden',
                borderRadius: '0.5rem',
                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease'
            },
            title: `${mask.name} - 真实脸谱图片`,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                    src: realImage,
                    alt: `${mask.name} - ${mask.character}`,
                    style: {
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                        display: 'block'
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 169,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        position: 'absolute',
                        top: '8px',
                        right: '8px',
                        background: 'rgba(34, 197, 94, 0.8)',
                        color: 'white',
                        fontSize: '10px',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        fontWeight: 'bold'
                    },
                    children: "真实脸谱"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 181,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        position: 'absolute',
                        inset: 0,
                        background: 'transparent',
                        transition: 'background 0.3s ease'
                    },
                    onMouseEnter: (e)=>{
                        e.currentTarget.style.background = 'rgba(0, 0, 0, 0.1)';
                        e.currentTarget.parentElement.style.transform = 'scale(1.02)';
                        e.currentTarget.parentElement.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.2)';
                    },
                    onMouseLeave: (e)=>{
                        e.currentTarget.style.background = 'transparent';
                        e.currentTarget.parentElement.style.transform = 'scale(1)';
                        e.currentTarget.parentElement.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 198,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/mask/MaskImage.tsx",
            lineNumber: 156,
            columnNumber: 7
        }, this);
    }
    // Canvas生成的图片渲染
    function renderCanvasImage() {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: className,
            style: {
                width,
                height,
                position: 'relative',
                overflow: 'hidden',
                borderRadius: '0.5rem',
                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease'
            },
            title: `${mask.name} - Canvas生成脸谱`,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                    src: canvasImage,
                    alt: `${mask.name} - ${mask.character}`,
                    style: {
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                        display: 'block'
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 236,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        position: 'absolute',
                        top: '8px',
                        right: '8px',
                        background: 'rgba(59, 130, 246, 0.8)',
                        color: 'white',
                        fontSize: '10px',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        fontWeight: 'bold'
                    },
                    children: "AI生成"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 248,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        position: 'absolute',
                        inset: 0,
                        background: 'transparent',
                        transition: 'background 0.3s ease'
                    },
                    onMouseEnter: (e)=>{
                        e.currentTarget.style.background = 'rgba(0, 0, 0, 0.1)';
                        e.currentTarget.parentElement.style.transform = 'scale(1.02)';
                        e.currentTarget.parentElement.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.2)';
                    },
                    onMouseLeave: (e)=>{
                        e.currentTarget.style.background = 'transparent';
                        e.currentTarget.parentElement.style.transform = 'scale(1)';
                        e.currentTarget.parentElement.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 265,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/mask/MaskImage.tsx",
            lineNumber: 223,
            columnNumber: 7
        }, this);
    }
    // SVG后备方案
    function generateFallbackSVG() {
        const baseProps = {
            width,
            height,
            viewBox: "0 0 300 300",
            className,
            style: {
                borderRadius: '0.5rem'
            }
        };
        // 简化的SVG后备方案
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
            ...baseProps,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("radialGradient", {
                        id: `gradient-${mask.id}`,
                        cx: "50%",
                        cy: "40%",
                        r: "60%",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                offset: "0%",
                                stopColor: mask.mainColors[0]
                            }, void 0, false, {
                                fileName: "[project]/src/components/mask/MaskImage.tsx",
                                lineNumber: 302,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                offset: "100%",
                                stopColor: mask.mainColors[1] || mask.mainColors[0]
                            }, void 0, false, {
                                fileName: "[project]/src/components/mask/MaskImage.tsx",
                                lineNumber: 303,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/mask/MaskImage.tsx",
                        lineNumber: 301,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 300,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                    cx: "150",
                    cy: "150",
                    rx: "120",
                    ry: "140",
                    fill: `url(#gradient-${mask.id})`,
                    stroke: "#333",
                    strokeWidth: "3"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 308,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M 90 110 Q 120 90 150 110",
                    stroke: "#000",
                    strokeWidth: "4",
                    fill: "none"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 319,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M 150 110 Q 180 90 210 110",
                    stroke: "#000",
                    strokeWidth: "4",
                    fill: "none"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 320,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                    cx: "120",
                    cy: "140",
                    rx: "15",
                    ry: "10",
                    fill: "white",
                    stroke: "#000",
                    strokeWidth: "2"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 321,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                    cx: "180",
                    cy: "140",
                    rx: "15",
                    ry: "10",
                    fill: "white",
                    stroke: "#000",
                    strokeWidth: "2"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 322,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                    cx: "120",
                    cy: "140",
                    r: "6",
                    fill: "#000"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 323,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                    cx: "180",
                    cy: "140",
                    r: "6",
                    fill: "#000"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 324,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M 150 160 L 150 180",
                    stroke: "#000",
                    strokeWidth: "3"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 325,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                    cx: "150",
                    cy: "200",
                    rx: "10",
                    ry: "6",
                    fill: "#000"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 326,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("text", {
                    x: "150",
                    y: "260",
                    textAnchor: "middle",
                    fill: "white",
                    fontSize: "24",
                    fontWeight: "bold",
                    fontFamily: "serif",
                    stroke: "#000",
                    strokeWidth: "1",
                    children: mask.character
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 329,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/mask/MaskImage.tsx",
            lineNumber: 299,
            columnNumber: 7
        }, this);
    }
}
}),
"[project]/src/components/ui/Typography.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "BodyText": ()=>BodyText,
    "CaptionText": ()=>CaptionText,
    "EmphasisText": ()=>EmphasisText,
    "MainTitle": ()=>MainTitle,
    "PageTitle": ()=>PageTitle,
    "SectionTitle": ()=>SectionTitle,
    "Subtitle": ()=>Subtitle,
    "Typography": ()=>Typography,
    "fontStyles": ()=>fontStyles
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
'use client';
;
const fontStyles = {
    // 主标题 - 使用马善政字体（传统书法风格）
    mainTitle: {
        fontFamily: 'var(--font-ma-shan-zheng), "Ma Shan Zheng", "KaiTi", "楷体", cursive',
        fontSize: '2.5rem',
        fontWeight: '400',
        lineHeight: '1.2',
        color: '#B91C1C',
        textShadow: '2px 2px 4px rgba(0,0,0,0.1)'
    },
    // 页面标题 - 使用思源宋体
    pageTitle: {
        fontFamily: 'var(--font-noto-serif-sc), "Noto Serif SC", "SimSun", "宋体", serif',
        fontSize: '2rem',
        fontWeight: '700',
        lineHeight: '1.3',
        color: '#1F2937'
    },
    // 章节标题
    sectionTitle: {
        fontFamily: 'var(--font-noto-serif-sc), "Noto Serif SC", "SimSun", "宋体", serif',
        fontSize: '1.5rem',
        fontWeight: '600',
        lineHeight: '1.4',
        color: '#374151'
    },
    // 子标题
    subtitle: {
        fontFamily: 'var(--font-noto-serif-sc), "Noto Serif SC", "SimSun", "宋体", serif',
        fontSize: '1.25rem',
        fontWeight: '500',
        lineHeight: '1.5',
        color: '#4B5563'
    },
    // 正文 - 使用思源黑体
    body: {
        fontFamily: 'var(--font-noto-sans-sc), "Noto Sans SC", "Microsoft YaHei", "微软雅黑", sans-serif',
        fontSize: '1rem',
        fontWeight: '400',
        lineHeight: '1.6',
        color: '#6B7280'
    },
    // 重要文本
    emphasis: {
        fontFamily: 'var(--font-noto-serif-sc), "Noto Serif SC", "SimSun", "宋体", serif',
        fontSize: '1rem',
        fontWeight: '600',
        lineHeight: '1.6',
        color: '#374151'
    },
    // 小字说明
    caption: {
        fontFamily: 'var(--font-noto-sans-sc), "Noto Sans SC", "Microsoft YaHei", "微软雅黑", sans-serif',
        fontSize: '0.875rem',
        fontWeight: '400',
        lineHeight: '1.5',
        color: '#9CA3AF'
    },
    // 按钮文字
    button: {
        fontFamily: 'var(--font-noto-sans-sc), "Noto Sans SC", "Microsoft YaHei", "微软雅黑", sans-serif',
        fontSize: '0.875rem',
        fontWeight: '500',
        lineHeight: '1.4'
    }
};
function Typography({ variant, children, className, style, as = 'div' }) {
    const Component = as;
    const variantStyle = fontStyles[variant];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
        className: className,
        style: {
            ...variantStyle,
            ...style
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Typography.tsx",
        lineNumber: 100,
        columnNumber: 5
    }, this);
}
function MainTitle({ children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Typography, {
        variant: "mainTitle",
        as: "h1",
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Typography.tsx",
        lineNumber: 114,
        columnNumber: 10
    }, this);
}
function PageTitle({ children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Typography, {
        variant: "pageTitle",
        as: "h1",
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Typography.tsx",
        lineNumber: 118,
        columnNumber: 10
    }, this);
}
function SectionTitle({ children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Typography, {
        variant: "sectionTitle",
        as: "h2",
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Typography.tsx",
        lineNumber: 122,
        columnNumber: 10
    }, this);
}
function Subtitle({ children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Typography, {
        variant: "subtitle",
        as: "h3",
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Typography.tsx",
        lineNumber: 126,
        columnNumber: 10
    }, this);
}
function BodyText({ children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Typography, {
        variant: "body",
        as: "p",
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Typography.tsx",
        lineNumber: 130,
        columnNumber: 10
    }, this);
}
function EmphasisText({ children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Typography, {
        variant: "emphasis",
        as: "span",
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Typography.tsx",
        lineNumber: 134,
        columnNumber: 10
    }, this);
}
function CaptionText({ children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Typography, {
        variant: "caption",
        as: "span",
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Typography.tsx",
        lineNumber: 138,
        columnNumber: 10
    }, this);
}
}),
"[project]/src/components/ui/ThemeToggle.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ThemeToggle": ()=>ThemeToggle,
    "ThemeToggleWithText": ()=>ThemeToggleWithText
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$ThemeProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/providers/ThemeProvider.tsx [app-ssr] (ecmascript)");
'use client';
;
;
function ThemeToggle({ className, style }) {
    const { theme, toggleTheme, colors } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$ThemeProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTheme"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        onClick: toggleTheme,
        className: className,
        style: {
            backgroundColor: 'transparent',
            border: `2px solid ${colors.border}`,
            borderRadius: '0.5rem',
            padding: '0.5rem',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transition: 'all 0.3s ease',
            color: colors.textPrimary,
            ...style
        },
        onMouseEnter: (e)=>{
            e.currentTarget.style.borderColor = colors.primary;
            e.currentTarget.style.backgroundColor = colors.backgroundTertiary;
        },
        onMouseLeave: (e)=>{
            e.currentTarget.style.borderColor = colors.border;
            e.currentTarget.style.backgroundColor = 'transparent';
        },
        title: theme === 'light' ? '切换到深色模式' : '切换到浅色模式',
        children: theme === 'light' ? // 月亮图标 (深色模式)
        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
            width: "20",
            height: "20",
            viewBox: "0 0 24 24",
            fill: "none",
            stroke: "currentColor",
            strokeWidth: "2",
            strokeLinecap: "round",
            strokeLinejoin: "round",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/ThemeToggle.tsx",
                lineNumber: 53,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ui/ThemeToggle.tsx",
            lineNumber: 43,
            columnNumber: 9
        }, this) : // 太阳图标 (浅色模式)
        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
            width: "20",
            height: "20",
            viewBox: "0 0 24 24",
            fill: "none",
            stroke: "currentColor",
            strokeWidth: "2",
            strokeLinecap: "round",
            strokeLinejoin: "round",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                    cx: "12",
                    cy: "12",
                    r: "5"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/ThemeToggle.tsx",
                    lineNumber: 67,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/ThemeToggle.tsx",
                    lineNumber: 68,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/ThemeToggle.tsx",
            lineNumber: 57,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/ThemeToggle.tsx",
        lineNumber: 15,
        columnNumber: 5
    }, this);
}
function ThemeToggleWithText({ className, style }) {
    const { theme, toggleTheme, colors } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$ThemeProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTheme"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        onClick: toggleTheme,
        className: className,
        style: {
            backgroundColor: 'transparent',
            border: `2px solid ${colors.primary}`,
            borderRadius: '0.5rem',
            padding: '0.5rem 1rem',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            transition: 'all 0.3s ease',
            color: colors.primary,
            fontSize: '0.875rem',
            fontWeight: '500',
            ...style
        },
        onMouseEnter: (e)=>{
            e.currentTarget.style.backgroundColor = colors.primary;
            e.currentTarget.style.color = colors.background;
        },
        onMouseLeave: (e)=>{
            e.currentTarget.style.backgroundColor = 'transparent';
            e.currentTarget.style.color = colors.primary;
        },
        children: theme === 'light' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    width: "16",
                    height: "16",
                    viewBox: "0 0 24 24",
                    fill: "none",
                    stroke: "currentColor",
                    strokeWidth: "2",
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/ThemeToggle.tsx",
                        lineNumber: 119,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/ThemeToggle.tsx",
                    lineNumber: 109,
                    columnNumber: 11
                }, this),
                "深色模式"
            ]
        }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    width: "16",
                    height: "16",
                    viewBox: "0 0 24 24",
                    fill: "none",
                    stroke: "currentColor",
                    strokeWidth: "2",
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                            cx: "12",
                            cy: "12",
                            r: "5"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/ThemeToggle.tsx",
                            lineNumber: 135,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            d: "M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/ThemeToggle.tsx",
                            lineNumber: 136,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/ThemeToggle.tsx",
                    lineNumber: 125,
                    columnNumber: 11
                }, this),
                "浅色模式"
            ]
        }, void 0, true)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/ThemeToggle.tsx",
        lineNumber: 80,
        columnNumber: 5
    }, this);
}
}),
"[project]/src/components/decorations/TraditionalDecorations.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AnimatedDecoration": ()=>AnimatedDecoration,
    "CloudPattern": ()=>CloudPattern,
    "FloatingDecoration": ()=>FloatingDecoration,
    "FloralPattern": ()=>FloralPattern,
    "GeometricPattern": ()=>GeometricPattern,
    "InkWashBackground": ()=>InkWashBackground,
    "TraditionalBorder": ()=>TraditionalBorder
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/styled-jsx/style.js [app-ssr] (ecmascript)");
'use client';
;
;
function CloudPattern({ width = 100, height = 50, color = '#F59E0B', opacity = 0.3, className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: width,
        height: height,
        viewBox: "0 0 100 50",
        className: className,
        style: {
            opacity
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M10,25 Q20,10 35,25 Q50,40 65,25 Q80,10 90,25",
                stroke: color,
                strokeWidth: "2",
                fill: "none",
                strokeLinecap: "round"
            }, void 0, false, {
                fileName: "[project]/src/components/decorations/TraditionalDecorations.tsx",
                lineNumber: 27,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M15,35 Q25,20 40,35 Q55,50 70,35 Q85,20 95,35",
                stroke: color,
                strokeWidth: "1.5",
                fill: "none",
                strokeLinecap: "round",
                opacity: "0.7"
            }, void 0, false, {
                fileName: "[project]/src/components/decorations/TraditionalDecorations.tsx",
                lineNumber: 34,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/decorations/TraditionalDecorations.tsx",
        lineNumber: 20,
        columnNumber: 5
    }, this);
}
function GeometricPattern({ size = 60, color = '#B91C1C', opacity = 0.2, className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: size,
        height: size,
        viewBox: "0 0 60 60",
        className: className,
        style: {
            opacity
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                x: "10",
                y: "10",
                width: "40",
                height: "40",
                stroke: color,
                strokeWidth: "2",
                fill: "none"
            }, void 0, false, {
                fileName: "[project]/src/components/decorations/TraditionalDecorations.tsx",
                lineNumber: 66,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                x: "20",
                y: "20",
                width: "20",
                height: "20",
                stroke: color,
                strokeWidth: "1.5",
                fill: "none"
            }, void 0, false, {
                fileName: "[project]/src/components/decorations/TraditionalDecorations.tsx",
                lineNumber: 67,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                cx: "30",
                cy: "30",
                r: "5",
                stroke: color,
                strokeWidth: "1",
                fill: "none"
            }, void 0, false, {
                fileName: "[project]/src/components/decorations/TraditionalDecorations.tsx",
                lineNumber: 68,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M15,15 L45,45 M45,15 L15,45",
                stroke: color,
                strokeWidth: "0.5",
                opacity: "0.5"
            }, void 0, false, {
                fileName: "[project]/src/components/decorations/TraditionalDecorations.tsx",
                lineNumber: 69,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/decorations/TraditionalDecorations.tsx",
        lineNumber: 59,
        columnNumber: 5
    }, this);
}
function FloralPattern({ size = 80, color = '#DC2626', opacity = 0.25, className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: size,
        height: size,
        viewBox: "0 0 80 80",
        className: className,
        style: {
            opacity
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
            transform: "translate(40,40)",
            children: [
                [
                    0,
                    60,
                    120,
                    180,
                    240,
                    300
                ].map((angle, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                        transform: `rotate(${angle})`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                            cx: "0",
                            cy: "-20",
                            rx: "8",
                            ry: "15",
                            fill: color,
                            opacity: "0.6"
                        }, void 0, false, {
                            fileName: "[project]/src/components/decorations/TraditionalDecorations.tsx",
                            lineNumber: 98,
                            columnNumber: 13
                        }, this)
                    }, index, false, {
                        fileName: "[project]/src/components/decorations/TraditionalDecorations.tsx",
                        lineNumber: 97,
                        columnNumber: 11
                    }, this)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                    cx: "0",
                    cy: "0",
                    r: "6",
                    fill: color,
                    opacity: "0.8"
                }, void 0, false, {
                    fileName: "[project]/src/components/decorations/TraditionalDecorations.tsx",
                    lineNumber: 109,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/decorations/TraditionalDecorations.tsx",
            lineNumber: 94,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/decorations/TraditionalDecorations.tsx",
        lineNumber: 87,
        columnNumber: 5
    }, this);
}
function TraditionalBorder({ width = '100%', height = 4, color = '#F59E0B', className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        style: {
            width,
            height: `${height}px`,
            background: `linear-gradient(90deg, transparent 0%, ${color} 20%, ${color} 80%, transparent 100%)`,
            position: 'relative'
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            style: {
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                width: '60px',
                height: '8px',
                background: color,
                borderRadius: '4px'
            }
        }, void 0, false, {
            fileName: "[project]/src/components/decorations/TraditionalDecorations.tsx",
            lineNumber: 137,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/decorations/TraditionalDecorations.tsx",
        lineNumber: 128,
        columnNumber: 5
    }, this);
}
function InkWashBackground({ className, opacity = 0.05 }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        style: {
            position: 'absolute',
            inset: 0,
            opacity,
            pointerEvents: 'none',
            background: `
          radial-gradient(circle at 20% 30%, rgba(185, 28, 28, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 80% 70%, rgba(245, 158, 11, 0.2) 0%, transparent 50%),
          radial-gradient(circle at 60% 20%, rgba(31, 41, 55, 0.1) 0%, transparent 40%)
        `
        }
    }, void 0, false, {
        fileName: "[project]/src/components/decorations/TraditionalDecorations.tsx",
        lineNumber: 162,
        columnNumber: 5
    }, this);
}
function AnimatedDecoration({ children, delay = 0, duration = 2000, className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        style: {
            animation: `fadeInUp ${duration}ms ease-out ${delay}ms both`
        },
        className: "jsx-7f6e919a1a66d054" + " " + (className || ""),
        children: [
            children,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                id: "7f6e919a1a66d054",
                children: "@keyframes fadeInUp{0%{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}"
            }, void 0, false, void 0, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/decorations/TraditionalDecorations.tsx",
        lineNumber: 192,
        columnNumber: 5
    }, this);
}
function FloatingDecoration({ children, duration = 3000, className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        style: {
            animation: `float ${duration}ms ease-in-out infinite`
        },
        className: "jsx-8b0457587b5e27b1" + " " + (className || ""),
        children: [
            children,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                id: "8b0457587b5e27b1",
                children: "@keyframes float{0%,to{transform:translateY(0)}50%{transform:translateY(-10px)}}"
            }, void 0, false, void 0, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/decorations/TraditionalDecorations.tsx",
        lineNumber: 226,
        columnNumber: 5
    }, this);
}
}),
"[project]/src/app/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>Home
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/styled-jsx/style.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$masks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/masks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAppState$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useAppState.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$maskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/maskUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$mask$2f$MaskImage$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/mask/MaskImage.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Typography.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ThemeToggle$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/ThemeToggle.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$ThemeProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/providers/ThemeProvider.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$decorations$2f$TraditionalDecorations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/decorations/TraditionalDecorations.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
function Home() {
    const [selectedMask, setSelectedMask] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const { colors, styles } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$ThemeProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTheme"])();
    const { filter, updateFilter, addToRecentlyViewed, toggleFavorite, isFavorite, getRecentlyViewedMasks } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAppState$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppState"])();
    // 过滤脸谱
    const filteredMasks = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$maskUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["filterMasks"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$masks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["operaMasks"], filter);
    }, [
        filter
    ]);
    // 最近查看的脸谱
    const recentlyViewedMasks = getRecentlyViewedMasks(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$masks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["operaMasks"]);
    const handleMaskClick = (mask)=>{
        setSelectedMask(mask);
    };
    const handleViewDetails = (mask)=>{
        addToRecentlyViewed(mask.id);
        router.push(`/mask/${mask.id}`);
    };
    const handleFilterChange = (newFilter)=>{
        updateFilter(newFilter);
    };
    const handleCloseModal = ()=>{
        setSelectedMask(null);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        style: {
            minHeight: '100vh',
            backgroundColor: colors.background,
            color: colors.textPrimary,
            fontFamily: '"Noto Sans SC", sans-serif',
            position: 'relative',
            overflow: 'hidden'
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$decorations$2f$TraditionalDecorations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InkWashBackground"], {}, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 71,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    position: 'absolute',
                    top: '5rem',
                    left: '2rem',
                    zIndex: 0
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$decorations$2f$TraditionalDecorations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FloatingDecoration"], {
                    duration: 4000,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$decorations$2f$TraditionalDecorations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CloudPattern"], {
                        width: 120,
                        height: 60,
                        color: colors.secondary,
                        opacity: 0.2
                    }, void 0, false, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 76,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 75,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 74,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    position: 'absolute',
                    top: '10rem',
                    right: '5rem',
                    zIndex: 0
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$decorations$2f$TraditionalDecorations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FloatingDecoration"], {
                    duration: 5000,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$decorations$2f$TraditionalDecorations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GeometricPattern"], {
                        size: 80,
                        color: colors.primary,
                        opacity: 0.15
                    }, void 0, false, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 82,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 81,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 80,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    position: 'absolute',
                    bottom: '10rem',
                    left: '25%',
                    zIndex: 0
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$decorations$2f$TraditionalDecorations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FloatingDecoration"], {
                    duration: 6000,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$decorations$2f$TraditionalDecorations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FloralPattern"], {
                        size: 100,
                        color: colors.secondary,
                        opacity: 0.2
                    }, void 0, false, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 88,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 87,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 86,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                style: {
                    ...styles.navigation,
                    position: 'sticky',
                    top: 0,
                    zIndex: 40
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        maxWidth: '1200px',
                        margin: '0 auto',
                        padding: '1rem 1.5rem',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            style: {
                                ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fontStyles"].pageTitle,
                                color: colors.textPrimary
                            },
                            children: "京剧脸谱文化展示平台"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 106,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ThemeToggle$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ThemeToggle"], {}, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 112,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 98,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 92,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                style: {
                    padding: '2rem 1.5rem',
                    backgroundColor: colors.background,
                    minHeight: '100vh'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$decorations$2f$TraditionalDecorations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatedDecoration"], {
                        delay: 300,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                            style: {
                                textAlign: 'center',
                                padding: '3rem 2rem',
                                background: `linear-gradient(135deg, ${colors.primary}10 0%, transparent 50%, ${colors.secondary}10 100%)`,
                                borderRadius: '0.75rem',
                                marginBottom: '3rem',
                                border: `2px solid ${colors.border}`,
                                position: 'relative',
                                zIndex: 10,
                                boxShadow: `0 8px 32px ${colors.shadow}`
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        marginBottom: '2rem'
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$decorations$2f$TraditionalDecorations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TraditionalBorder"], {
                                        color: colors.secondary,
                                        height: 6
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 137,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 136,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$decorations$2f$TraditionalDecorations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatedDecoration"], {
                                    delay: 600,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MainTitle"], {
                                        style: {
                                            marginBottom: '1rem',
                                            textShadow: `2px 2px 4px ${colors.shadow}`
                                        },
                                        children: "探索京剧脸谱的艺术魅力"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 141,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 140,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$decorations$2f$TraditionalDecorations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatedDecoration"], {
                                    delay: 900,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            style: {
                                                maxWidth: '50rem',
                                                margin: '0 auto 2rem',
                                                padding: '0 1rem'
                                            },
                                            className: "jsx-f89d4213c2f7eb11" + " " + "hero-description",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BodyText"], {
                                                style: {
                                                    fontSize: '1.125rem',
                                                    lineHeight: '1.6',
                                                    textAlign: 'center',
                                                    display: 'block'
                                                },
                                                children: "深入了解中国传统戏曲文化，感受脸谱艺术的独特魅力与深厚内涵"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 158,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 150,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            id: "f89d4213c2f7eb11",
                                            children: ".hero-description.jsx-f89d4213c2f7eb11{white-space:nowrap;text-overflow:ellipsis;overflow:hidden}@media (width<=768px){.hero-description.jsx-f89d4213c2f7eb11{white-space:normal;text-overflow:unset;overflow:visible}}@media (width<=480px){.hero-description.jsx-f89d4213c2f7eb11{padding:0 .5rem}}"
                                        }, void 0, false, void 0, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 149,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        marginTop: '2rem'
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$decorations$2f$TraditionalDecorations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TraditionalBorder"], {
                                        color: colors.primary,
                                        height: 4
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 194,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 193,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        position: 'absolute',
                                        top: '1rem',
                                        left: '1rem'
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$decorations$2f$TraditionalDecorations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GeometricPattern"], {
                                        size: 40,
                                        color: colors.secondary,
                                        opacity: 0.3
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 199,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 198,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        position: 'absolute',
                                        top: '1rem',
                                        right: '1rem'
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$decorations$2f$TraditionalDecorations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GeometricPattern"], {
                                        size: 40,
                                        color: colors.secondary,
                                        opacity: 0.3
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 202,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 201,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 124,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 123,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$decorations$2f$TraditionalDecorations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatedDecoration"], {
                        delay: 1200,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                display: 'grid',
                                gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
                                gap: '2rem',
                                maxWidth: '1200px',
                                margin: '0 auto',
                                position: 'relative',
                                zIndex: 10
                            },
                            children: filteredMasks.map((mask, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$decorations$2f$TraditionalDecorations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatedDecoration"], {
                                    delay: 1500 + index * 100,
                                    duration: 800,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        onClick: ()=>handleMaskClick(mask),
                                        style: {
                                            ...styles.card,
                                            borderRadius: '1rem',
                                            border: `2px solid ${colors.secondary}`,
                                            overflow: 'hidden',
                                            cursor: 'pointer',
                                            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                            position: 'relative',
                                            boxShadow: `0 4px 6px -1px ${colors.shadow}, 0 2px 4px -1px ${colors.shadow}`
                                        },
                                        onMouseEnter: (e)=>{
                                            e.currentTarget.style.transform = 'translateY(-4px) scale(1.02)';
                                            e.currentTarget.style.boxShadow = `0 20px 25px -5px ${colors.shadowHover}, 0 10px 10px -5px ${colors.shadowHover}`;
                                            e.currentTarget.style.borderColor = colors.primary;
                                        },
                                        onMouseLeave: (e)=>{
                                            e.currentTarget.style.transform = 'translateY(0) scale(1)';
                                            e.currentTarget.style.boxShadow = `0 4px 6px -1px ${colors.shadow}, 0 2px 4px -1px ${colors.shadow}`;
                                            e.currentTarget.style.borderColor = colors.secondary;
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    aspectRatio: '1',
                                                    position: 'relative',
                                                    backgroundColor: '#F8F9FA',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    padding: '1rem'
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$mask$2f$MaskImage$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MaskImage"], {
                                                        mask: mask,
                                                        width: 260,
                                                        height: 260
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 257,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            position: 'absolute',
                                                            top: '0.75rem',
                                                            left: '0.75rem',
                                                            backgroundColor: 'rgba(0,0,0,0.85)',
                                                            color: 'white',
                                                            padding: '0.375rem 0.75rem',
                                                            borderRadius: '1rem',
                                                            fontSize: '0.75rem',
                                                            fontWeight: '600',
                                                            backdropFilter: 'blur(4px)',
                                                            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                                                        },
                                                        children: mask.roleCategory
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 264,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            position: 'absolute',
                                                            top: '0.75rem',
                                                            right: '0.75rem',
                                                            backgroundColor: 'rgba(0,0,0,0.85)',
                                                            color: 'white',
                                                            padding: '0.375rem 0.75rem',
                                                            borderRadius: '1rem',
                                                            fontSize: '0.75rem',
                                                            fontWeight: '600',
                                                            backdropFilter: 'blur(4px)',
                                                            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                                                        },
                                                        children: mask.colorCategory
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 280,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 248,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    padding: '1.25rem',
                                                    backgroundColor: colors.background,
                                                    borderTop: `1px solid ${colors.border}`
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            textAlign: 'center',
                                                            marginBottom: '1rem',
                                                            paddingBottom: '0.75rem',
                                                            borderBottom: `1px solid ${colors.border}`
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                                style: {
                                                                    fontSize: '1.5rem',
                                                                    fontWeight: '700',
                                                                    color: colors.textPrimary,
                                                                    marginBottom: '0.25rem',
                                                                    fontFamily: '"Noto Serif SC", serif'
                                                                },
                                                                children: mask.character
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 310,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                style: {
                                                                    fontSize: '1rem',
                                                                    fontWeight: '500',
                                                                    color: colors.textSecondary,
                                                                    fontFamily: '"Noto Serif SC", serif'
                                                                },
                                                                children: mask.name
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 319,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 304,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        style: {
                                                            fontSize: '0.875rem',
                                                            color: colors.textSecondary,
                                                            marginBottom: '1rem',
                                                            lineHeight: '1.5',
                                                            display: '-webkit-box',
                                                            WebkitLineClamp: 2,
                                                            WebkitBoxOrient: 'vertical',
                                                            overflow: 'hidden'
                                                        },
                                                        children: mask.culturalBackground.personality
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 330,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            gap: '0.75rem',
                                                            marginBottom: '1rem'
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                style: {
                                                                    fontSize: '0.875rem',
                                                                    color: colors.textSecondary,
                                                                    fontWeight: '500'
                                                                },
                                                                children: "主要色彩:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 350,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                style: {
                                                                    display: 'flex',
                                                                    gap: '0.375rem'
                                                                },
                                                                children: mask.mainColors.slice(0, 3).map((color, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        style: {
                                                                            width: '1.25rem',
                                                                            height: '1.25rem',
                                                                            borderRadius: '50%',
                                                                            backgroundColor: color,
                                                                            border: `2px solid ${colors.border}`,
                                                                            boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                                                                        },
                                                                        title: color
                                                                    }, index, false, {
                                                                        fileName: "[project]/src/app/page.tsx",
                                                                        lineNumber: 359,
                                                                        columnNumber: 23
                                                                    }, this))
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 357,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 344,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            display: 'flex',
                                                            flexWrap: 'wrap',
                                                            gap: '0.375rem'
                                                        },
                                                        children: mask.tags.slice(0, 3).map((tag, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                style: {
                                                                    padding: '0.375rem 0.75rem',
                                                                    fontSize: '0.75rem',
                                                                    fontWeight: '500',
                                                                    backgroundColor: colors.backgroundTertiary,
                                                                    color: colors.textTertiary,
                                                                    borderRadius: '1rem',
                                                                    border: `1px solid ${colors.border}`,
                                                                    transition: 'all 0.2s ease'
                                                                },
                                                                children: tag
                                                            }, index, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 378,
                                                                columnNumber: 21
                                                            }, this))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 376,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 298,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 224,
                                        columnNumber: 17
                                    }, this)
                                }, mask.id, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 219,
                                    columnNumber: 15
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 209,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 208,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 117,
                columnNumber: 7
            }, this),
            selectedMask && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    position: 'fixed',
                    inset: 0,
                    zIndex: 50,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    backdropFilter: 'blur(4px)'
                },
                onClick: handleCloseModal,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        backgroundColor: 'white',
                        borderRadius: '0.75rem',
                        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
                        border: '2px solid #F59E0B',
                        maxWidth: '90vw',
                        maxHeight: '90vh',
                        overflow: 'auto',
                        position: 'relative'
                    },
                    onClick: (e)=>e.stopPropagation(),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: handleCloseModal,
                            style: {
                                position: 'absolute',
                                top: '1rem',
                                right: '1rem',
                                backgroundColor: 'transparent',
                                border: 'none',
                                fontSize: '1.5rem',
                                cursor: 'pointer',
                                zIndex: 10,
                                color: '#6B7280'
                            },
                            children: "×"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 432,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                display: 'grid',
                                gridTemplateColumns: '1fr 1fr',
                                gap: '1.5rem',
                                padding: '1.5rem'
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            style: {
                                                aspectRatio: '1',
                                                background: `linear-gradient(135deg, ${selectedMask.mainColors[0]} 0%, ${selectedMask.mainColors[1] || selectedMask.mainColors[0]} 100%)`,
                                                borderRadius: '0.5rem',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                color: 'white',
                                                fontSize: '3rem',
                                                fontWeight: 'bold',
                                                marginBottom: '1rem'
                                            },
                                            children: selectedMask.character
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 457,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            style: {
                                                display: 'flex',
                                                gap: '0.5rem'
                                            },
                                            children: selectedMask.mainColors.map((color, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    style: {
                                                        width: '2rem',
                                                        height: '2rem',
                                                        borderRadius: '50%',
                                                        backgroundColor: color,
                                                        border: '2px solid #D1D5DB'
                                                    },
                                                    title: color
                                                }, index, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 473,
                                                    columnNumber: 21
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 471,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 456,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        display: 'flex',
                                        flexDirection: 'column',
                                        gap: '1.5rem'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                    style: {
                                                        fontSize: '1.5rem',
                                                        fontWeight: 'bold',
                                                        color: '#1F2937',
                                                        marginBottom: '0.5rem',
                                                        fontFamily: '"Noto Serif SC", serif'
                                                    },
                                                    children: selectedMask.name
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 491,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    style: {
                                                        color: '#6B7280',
                                                        fontWeight: '500'
                                                    },
                                                    children: selectedMask.character
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 500,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 490,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                    style: {
                                                        fontSize: '1.125rem',
                                                        fontWeight: '600',
                                                        color: '#1F2937',
                                                        marginBottom: '0.5rem'
                                                    },
                                                    children: "基本信息"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 506,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    style: {
                                                        fontSize: '0.875rem',
                                                        display: 'flex',
                                                        flexDirection: 'column',
                                                        gap: '0.5rem'
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    style: {
                                                                        fontWeight: '500'
                                                                    },
                                                                    children: "行当:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 515,
                                                                    columnNumber: 24
                                                                }, this),
                                                                " ",
                                                                selectedMask.roleCategory
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 515,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    style: {
                                                                        fontWeight: '500'
                                                                    },
                                                                    children: "颜色分类:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 516,
                                                                    columnNumber: 24
                                                                }, this),
                                                                " ",
                                                                selectedMask.colorCategory
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 516,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    style: {
                                                                        fontWeight: '500'
                                                                    },
                                                                    children: "绘制难度:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 517,
                                                                    columnNumber: 24
                                                                }, this),
                                                                " ",
                                                                selectedMask.difficulty
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 517,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 514,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 505,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                    style: {
                                                        fontSize: '1.125rem',
                                                        fontWeight: '600',
                                                        color: '#1F2937',
                                                        marginBottom: '0.5rem'
                                                    },
                                                    children: "文化背景"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 522,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    style: {
                                                        fontSize: '0.875rem',
                                                        color: '#6B7280',
                                                        display: 'flex',
                                                        flexDirection: 'column',
                                                        gap: '0.5rem'
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    style: {
                                                                        fontWeight: '500'
                                                                    },
                                                                    children: "历史起源:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 531,
                                                                    columnNumber: 24
                                                                }, this),
                                                                " ",
                                                                selectedMask.culturalBackground.origin
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 531,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    style: {
                                                                        fontWeight: '500'
                                                                    },
                                                                    children: "性格特点:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 532,
                                                                    columnNumber: 24
                                                                }, this),
                                                                " ",
                                                                selectedMask.culturalBackground.personality
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 532,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    style: {
                                                                        fontWeight: '500'
                                                                    },
                                                                    children: "象征意义:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 533,
                                                                    columnNumber: 24
                                                                }, this),
                                                                " ",
                                                                selectedMask.culturalBackground.symbolism
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 533,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 530,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 521,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                    style: {
                                                        fontSize: '1.125rem',
                                                        fontWeight: '600',
                                                        color: '#1F2937',
                                                        marginBottom: '0.5rem'
                                                    },
                                                    children: "相关剧目"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 538,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    style: {
                                                        fontSize: '0.875rem',
                                                        display: 'flex',
                                                        flexDirection: 'column',
                                                        gap: '0.5rem'
                                                    },
                                                    children: selectedMask.relatedOperas.map((opera, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    style: {
                                                                        fontWeight: '500'
                                                                    },
                                                                    children: opera.name
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 549,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    style: {
                                                                        color: '#6B7280'
                                                                    },
                                                                    children: opera.description
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 550,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, index, true, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 548,
                                                            columnNumber: 23
                                                        }, this))
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 546,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 537,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            style: {
                                                display: 'flex',
                                                flexWrap: 'wrap',
                                                gap: '0.5rem',
                                                marginBottom: '1.5rem'
                                            },
                                            children: selectedMask.tags.map((tag, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    style: {
                                                        padding: '0.5rem 0.75rem',
                                                        fontSize: '0.75rem',
                                                        backgroundColor: 'rgba(245, 158, 11, 0.2)',
                                                        color: '#F59E0B',
                                                        border: '1px solid #F59E0B',
                                                        borderRadius: '9999px'
                                                    },
                                                    children: tag
                                                }, index, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 558,
                                                    columnNumber: 21
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 556,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>handleViewDetails(selectedMask),
                                            style: {
                                                width: '100%',
                                                backgroundColor: '#B91C1C',
                                                color: 'white',
                                                padding: '0.75rem 1.5rem',
                                                borderRadius: '0.5rem',
                                                border: 'none',
                                                cursor: 'pointer',
                                                fontSize: '1rem',
                                                fontWeight: '600',
                                                transition: 'all 0.2s ease'
                                            },
                                            onMouseEnter: (e)=>{
                                                e.currentTarget.style.backgroundColor = '#991B1B';
                                            },
                                            onMouseLeave: (e)=>{
                                                e.currentTarget.style.backgroundColor = '#B91C1C';
                                            },
                                            children: "查看完整详情 →"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 575,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 489,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 449,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 418,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 405,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 62,
        columnNumber: 5
    }, this);
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__6d73f969._.js.map