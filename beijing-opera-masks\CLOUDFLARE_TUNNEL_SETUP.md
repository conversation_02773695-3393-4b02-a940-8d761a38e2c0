# Cloudflare Tunnel 配置指南
## 企业级免费公网访问解决方案

### 🌐 方案优势
- ✅ **完全免费** - 无流量限制
- ✅ **全球CDN** - 世界各地快速访问
- ✅ **HTTPS安全** - 自动SSL证书
- ✅ **稳定可靠** - 企业级基础设施
- ✅ **自定义域名** - 专业的访问地址
- ✅ **DDoS防护** - 内置安全防护

---

## 📋 配置步骤

### 步骤1: 下载 Cloudflared (2分钟)

#### Windows x64 版本下载
1. 访问 **https://github.com/cloudflare/cloudflared/releases**
2. 下载最新版本的 `cloudflared-windows-amd64.exe`
3. 重命名为 `cloudflared.exe`
4. 放置到项目目录或系统PATH中

#### 快速下载命令 (PowerShell)
```powershell
# 下载到当前目录
Invoke-WebRequest -Uri "https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-windows-amd64.exe" -OutFile "cloudflared.exe"
```

### 步骤2: 登录 Cloudflare (3分钟)

#### 2.1 注册 Cloudflare 账户
1. 访问 **https://dash.cloudflare.com/sign-up**
2. 注册免费账户
3. 验证邮箱

#### 2.2 登录 Cloudflared
```bash
# 在命令提示符中运行
cloudflared tunnel login
```

这会：
- 打开浏览器登录页面
- 选择要使用的域名（如果有）
- 下载认证证书到本地

### 步骤3: 创建隧道 (2分钟)

```bash
# 创建名为 beijing-opera-masks 的隧道
cloudflared tunnel create beijing-opera-masks
```

成功后会显示：
```
Tunnel credentials written to: C:\Users\<USER>\.cloudflared\[tunnel-id].json
Created tunnel beijing-opera-masks with id [tunnel-id]
```

### 步骤4: 配置隧道 (3分钟)

#### 4.1 创建配置文件
在用户目录下创建 `.cloudflared/config.yml`：

**Windows路径**: `C:\Users\<USER>\.cloudflared\config.yml`

```yaml
tunnel: beijing-opera-masks
credentials-file: C:\Users\<USER>\.cloudflared\[tunnel-id].json

ingress:
  - hostname: beijing-opera-masks.your-domain.com
    service: http://localhost:3002
  - service: http_status:404
```

#### 4.2 如果没有自定义域名
使用 Cloudflare 提供的免费域名：
```yaml
tunnel: beijing-opera-masks
credentials-file: C:\Users\<USER>\.cloudflared\[tunnel-id].json

ingress:
  - service: http://localhost:3002
```

### 步骤5: 启动隧道 (1分钟)

```bash
# 启动隧道
cloudflared tunnel run beijing-opera-masks
```

成功启动后会显示：
```
2025-07-22T10:30:00Z INF Starting tunnel tunnelID=[tunnel-id]
2025-07-22T10:30:00Z INF Version 2024.x.x
2025-07-22T10:30:00Z INF GOOS: windows, GOARCH: amd64, built: 2024-xx-xx
2025-07-22T10:30:00Z INF Generated Connector ID: [connector-id]
2025-07-22T10:30:00Z INF cloudflared will not automatically update when run from the shell
2025-07-22T10:30:00Z INF Initial protocol quic
2025-07-22T10:30:00Z INF Starting metrics server on 127.0.0.1:60123/metrics
2025-07-22T10:30:01Z INF Connection [connection-id] registered connIndex=0 ip=xxx.xxx.xxx.xxx location=NRT
2025-07-22T10:30:02Z INF Connection [connection-id] registered connIndex=1 ip=xxx.xxx.xxx.xxx location=NRT
```

---

## 🌐 获取访问地址

### 方法1: 使用临时域名 (立即可用)
```bash
# 快速启动临时隧道
cloudflared tunnel --url http://localhost:3002
```

会显示类似：
```
2025-07-22T10:30:00Z INF Thank you for trying Cloudflare Tunnel. Doing so, without a Cloudflare account, is a quick way to experiment and try it out. However, be aware that these account-less Tunnels have no uptime guarantee. If you intend to use Tunnels in production you should use a pre-created named tunnel by following: https://developers.cloudflare.com/cloudflare-one/connections/connect-apps
2025-07-22T10:30:01Z INF Requesting new quick Tunnel on trycloudflare.com...
2025-07-22T10:30:02Z INF +--------------------------------------------------------------------------------------------+
2025-07-22T10:30:02Z INF |  Your quick Tunnel has been created! Visit it at (it may take some time to be reachable):  |
2025-07-22T10:30:02Z INF |  https://abc123.trycloudflare.com                                                          |
2025-07-22T10:30:02Z INF +--------------------------------------------------------------------------------------------+
```

**🎉 立即可用的公网地址**: `https://abc123.trycloudflare.com`

### 方法2: 使用自定义域名
如果您有域名，可以在 Cloudflare Dashboard 中配置 DNS 记录。

---

## 🚀 一键启动脚本

让我创建一个自动化脚本：
