'use client';

import React, { useState, useEffect } from 'react';
import { useTheme } from '@/components/providers/ThemeProvider';

interface DrawingStep {
  title: string;
  description: string;
  duration: number;
}

interface SimpleDrawingAnimationProps {
  maskName: string;
  steps?: DrawingStep[];
}

export function SimpleDrawingAnimation({ maskName, steps }: SimpleDrawingAnimationProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const { colors } = useTheme();

  // 默认绘制步骤
  const defaultSteps: DrawingStep[] = [
    {
      title: '绘制脸部轮廓',
      description: '首先用淡色勾勒出脸部的基本轮廓，确定五官位置',
      duration: 3000
    },
    {
      title: '涂抹底色',
      description: '用主色调涂抹整个脸部，这是脸谱的基础色彩',
      duration: 4000
    },
    {
      title: '描绘眉毛和眼部',
      description: '用深色勾勒眉毛和眼部轮廓，突出角色的神态',
      duration: 3500
    },
    {
      title: '绘制特色纹样',
      description: '添加角色特有的纹样和装饰，体现角色性格',
      duration: 3000
    },
    {
      title: '添加装饰细节',
      description: '在适当位置添加金色或其他装饰，增加华贵感',
      duration: 2500
    },
    {
      title: '完善细节',
      description: '最后完善各种细节，确保脸谱的完整性和美观性',
      duration: 4000
    }
  ];

  const drawingSteps = steps || defaultSteps;

  useEffect(() => {
    if (!isPlaying) return;

    const timer = setTimeout(() => {
      if (currentStep < drawingSteps.length - 1) {
        setCurrentStep(prev => prev + 1);
      } else {
        setIsPlaying(false);
        setCurrentStep(0);
      }
    }, drawingSteps[currentStep].duration);

    return () => clearTimeout(timer);
  }, [currentStep, isPlaying, drawingSteps]);

  const startAnimation = () => {
    setCurrentStep(0);
    setIsPlaying(true);
  };

  const stopAnimation = () => {
    setIsPlaying(false);
    setCurrentStep(0);
  };

  return (
    <div style={{
      backgroundColor: colors.backgroundSecondary,
      borderRadius: '12px',
      padding: '2rem',
      marginTop: '2rem'
    }}>
      <h3 style={{
        fontSize: '1.25rem',
        fontWeight: 'bold',
        marginBottom: '1.5rem',
        color: colors.textPrimary,
        textAlign: 'center'
      }}>
        🎨 {maskName} 绘制过程
      </h3>

      {/* 动画控制按钮 */}
      <div style={{
        display: 'flex',
        gap: '1rem',
        justifyContent: 'center',
        marginBottom: '2rem'
      }}>
        <button
          onClick={startAnimation}
          disabled={isPlaying}
          style={{
            backgroundColor: isPlaying ? colors.border : colors.primary,
            color: 'white',
            padding: '0.75rem 1.5rem',
            borderRadius: '0.5rem',
            border: 'none',
            cursor: isPlaying ? 'not-allowed' : 'pointer',
            fontSize: '1rem'
          }}
        >
          {isPlaying ? '播放中...' : '开始播放'}
        </button>
        
        <button
          onClick={stopAnimation}
          disabled={!isPlaying}
          style={{
            backgroundColor: !isPlaying ? colors.border : colors.textSecondary,
            color: 'white',
            padding: '0.75rem 1.5rem',
            borderRadius: '0.5rem',
            border: 'none',
            cursor: !isPlaying ? 'not-allowed' : 'pointer',
            fontSize: '1rem'
          }}
        >
          停止
        </button>
      </div>

      {/* 步骤显示 */}
      <div style={{
        backgroundColor: colors.background,
        borderRadius: '8px',
        padding: '1.5rem',
        marginBottom: '1rem'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          marginBottom: '1rem'
        }}>
          <div style={{
            backgroundColor: colors.primary,
            color: 'white',
            borderRadius: '50%',
            width: '30px',
            height: '30px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '0.875rem',
            fontWeight: 'bold',
            marginRight: '1rem'
          }}>
            {currentStep + 1}
          </div>
          <h4 style={{
            fontSize: '1.125rem',
            fontWeight: 'bold',
            color: colors.textPrimary,
            margin: 0
          }}>
            {drawingSteps[currentStep].title}
          </h4>
        </div>
        
        <p style={{
          color: colors.textSecondary,
          lineHeight: '1.6',
          margin: 0
        }}>
          {drawingSteps[currentStep].description}
        </p>
      </div>

      {/* 进度条 */}
      <div style={{
        backgroundColor: colors.border,
        borderRadius: '10px',
        height: '8px',
        overflow: 'hidden',
        marginBottom: '1rem'
      }}>
        <div style={{
          backgroundColor: colors.primary,
          height: '100%',
          width: `${((currentStep + 1) / drawingSteps.length) * 100}%`,
          transition: 'width 0.3s ease',
          borderRadius: '10px'
        }} />
      </div>

      {/* 步骤列表 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '0.5rem'
      }}>
        {drawingSteps.map((step, index) => (
          <div
            key={index}
            style={{
              padding: '0.5rem',
              borderRadius: '6px',
              backgroundColor: index <= currentStep ? colors.primary + '20' : colors.border + '50',
              border: index === currentStep ? `2px solid ${colors.primary}` : '1px solid transparent',
              transition: 'all 0.3s ease'
            }}
          >
            <div style={{
              fontSize: '0.875rem',
              fontWeight: 'bold',
              color: index <= currentStep ? colors.primary : colors.textSecondary,
              marginBottom: '0.25rem'
            }}>
              步骤 {index + 1}
            </div>
            <div style={{
              fontSize: '0.75rem',
              color: colors.textSecondary
            }}>
              {step.title}
            </div>
          </div>
        ))}
      </div>

      {isPlaying && (
        <div style={{
          textAlign: 'center',
          marginTop: '1rem',
          color: colors.textSecondary,
          fontSize: '0.875rem'
        }}>
          预计完成时间: {Math.ceil(drawingSteps[currentStep].duration / 1000)} 秒
        </div>
      )}
    </div>
  );
}
