/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C2-AIprogram%5C%5C3-Augment%5C%5C1-test1%5C%5Cbeijing-opera-masks%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CPerformanceMonitor.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C2-AIprogram%5C%5C3-Augment%5C%5C1-test1%5C%5Cbeijing-opera-masks%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=false!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C2-AIprogram%5C%5C3-Augment%5C%5C1-test1%5C%5Cbeijing-opera-masks%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CPerformanceMonitor.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C2-AIprogram%5C%5C3-Augment%5C%5C1-test1%5C%5Cbeijing-opera-masks%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=false! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/performance/PerformanceMonitor.tsx */ \"(app-pages-browser)/./src/components/performance/PerformanceMonitor.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ThemeProvider.tsx */ \"(app-pages-browser)/./src/components/providers/ThemeProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1QzItQUlwcm9ncmFtJTVDJTVDMy1BdWdtZW50JTVDJTVDMS10ZXN0MSU1QyU1Q2JlaWppbmctb3BlcmEtbWFza3MlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDcGVyZm9ybWFuY2UlNUMlNUNQZXJmb3JtYW5jZU1vbml0b3IudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyUGVyZm9ybWFuY2VNb25pdG9yJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMyLUFJcHJvZ3JhbSU1QyU1QzMtQXVnbWVudCU1QyU1QzEtdGVzdDElNUMlNUNiZWlqaW5nLW9wZXJhLW1hc2tzJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3Byb3ZpZGVycyU1QyU1Q1RoZW1lUHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVGhlbWVQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLGdPQUEyTDtBQUMzTDtBQUNBLGtOQUErSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUGVyZm9ybWFuY2VNb25pdG9yXCJdICovIFwiRTpcXFxcMi1BSXByb2dyYW1cXFxcMy1BdWdtZW50XFxcXDEtdGVzdDFcXFxcYmVpamluZy1vcGVyYS1tYXNrc1xcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxwZXJmb3JtYW5jZVxcXFxQZXJmb3JtYW5jZU1vbml0b3IudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUaGVtZVByb3ZpZGVyXCJdICovIFwiRTpcXFxcMi1BSXByb2dyYW1cXFxcMy1BdWdtZW50XFxcXDEtdGVzdDFcXFxcYmVpamluZy1vcGVyYS1tYXNrc1xcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxwcm92aWRlcnNcXFxcVGhlbWVQcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C2-AIprogram%5C%5C3-Augment%5C%5C1-test1%5C%5Cbeijing-opera-masks%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CPerformanceMonitor.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C2-AIprogram%5C%5C3-Augment%5C%5C1-test1%5C%5Cbeijing-opera-masks%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkU6XFwyLUFJcHJvZ3JhbVxcMy1BdWdtZW50XFwxLXRlc3QxXFxiZWlqaW5nLW9wZXJhLW1hc2tzXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/performance/PerformanceMonitor.tsx":
/*!***********************************************************!*\
  !*** ./src/components/performance/PerformanceMonitor.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerformanceMonitor: () => (/* binding */ PerformanceMonitor),\n/* harmony export */   performanceUtils: () => (/* binding */ performanceUtils)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_useEffect_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=useEffect!=!react */ \"(app-pages-browser)/__barrel_optimize__?names=useEffect!=!./node_modules/next/dist/compiled/react/index.js\");\n/* __next_internal_client_entry_do_not_use__ PerformanceMonitor,performanceUtils auto */ var _s = $RefreshSig$();\n\nfunction PerformanceMonitor() {\n    _s();\n    (0,_barrel_optimize_names_useEffect_react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"PerformanceMonitor.useEffect\": ()=>{\n            // 只在生产环境中启用性能监控\n            if (true) {\n                return;\n            }\n            const metrics = {};\n            // 监控 First Contentful Paint\n            const observeFCP = {\n                \"PerformanceMonitor.useEffect.observeFCP\": ()=>{\n                    const observer = new PerformanceObserver({\n                        \"PerformanceMonitor.useEffect.observeFCP\": (list)=>{\n                            const entries = list.getEntries();\n                            entries.forEach({\n                                \"PerformanceMonitor.useEffect.observeFCP\": (entry)=>{\n                                    if (entry.name === 'first-contentful-paint') {\n                                        metrics.fcp = entry.startTime;\n                                        console.log('FCP:', entry.startTime);\n                                    }\n                                }\n                            }[\"PerformanceMonitor.useEffect.observeFCP\"]);\n                        }\n                    }[\"PerformanceMonitor.useEffect.observeFCP\"]);\n                    observer.observe({\n                        entryTypes: [\n                            'paint'\n                        ]\n                    });\n                }\n            }[\"PerformanceMonitor.useEffect.observeFCP\"];\n            // 监控 Largest Contentful Paint\n            const observeLCP = {\n                \"PerformanceMonitor.useEffect.observeLCP\": ()=>{\n                    const observer = new PerformanceObserver({\n                        \"PerformanceMonitor.useEffect.observeLCP\": (list)=>{\n                            const entries = list.getEntries();\n                            const lastEntry = entries[entries.length - 1];\n                            metrics.lcp = lastEntry.startTime;\n                            console.log('LCP:', lastEntry.startTime);\n                        }\n                    }[\"PerformanceMonitor.useEffect.observeLCP\"]);\n                    observer.observe({\n                        entryTypes: [\n                            'largest-contentful-paint'\n                        ]\n                    });\n                }\n            }[\"PerformanceMonitor.useEffect.observeLCP\"];\n            // 监控 First Input Delay\n            const observeFID = {\n                \"PerformanceMonitor.useEffect.observeFID\": ()=>{\n                    const observer = new PerformanceObserver({\n                        \"PerformanceMonitor.useEffect.observeFID\": (list)=>{\n                            const entries = list.getEntries();\n                            entries.forEach({\n                                \"PerformanceMonitor.useEffect.observeFID\": (entry)=>{\n                                    metrics.fid = entry.processingStart - entry.startTime;\n                                    console.log('FID:', metrics.fid);\n                                }\n                            }[\"PerformanceMonitor.useEffect.observeFID\"]);\n                        }\n                    }[\"PerformanceMonitor.useEffect.observeFID\"]);\n                    observer.observe({\n                        entryTypes: [\n                            'first-input'\n                        ]\n                    });\n                }\n            }[\"PerformanceMonitor.useEffect.observeFID\"];\n            // 监控 Cumulative Layout Shift\n            const observeCLS = {\n                \"PerformanceMonitor.useEffect.observeCLS\": ()=>{\n                    let clsValue = 0;\n                    const observer = new PerformanceObserver({\n                        \"PerformanceMonitor.useEffect.observeCLS\": (list)=>{\n                            const entries = list.getEntries();\n                            entries.forEach({\n                                \"PerformanceMonitor.useEffect.observeCLS\": (entry)=>{\n                                    if (!entry.hadRecentInput) {\n                                        clsValue += entry.value;\n                                    }\n                                }\n                            }[\"PerformanceMonitor.useEffect.observeCLS\"]);\n                            metrics.cls = clsValue;\n                            console.log('CLS:', clsValue);\n                        }\n                    }[\"PerformanceMonitor.useEffect.observeCLS\"]);\n                    observer.observe({\n                        entryTypes: [\n                            'layout-shift'\n                        ]\n                    });\n                }\n            }[\"PerformanceMonitor.useEffect.observeCLS\"];\n            // 监控 Time to First Byte\n            const observeTTFB = {\n                \"PerformanceMonitor.useEffect.observeTTFB\": ()=>{\n                    const navigationEntry = performance.getEntriesByType('navigation')[0];\n                    if (navigationEntry) {\n                        metrics.ttfb = navigationEntry.responseStart - navigationEntry.requestStart;\n                        console.log('TTFB:', metrics.ttfb);\n                    }\n                }\n            }[\"PerformanceMonitor.useEffect.observeTTFB\"];\n            // 检查浏览器支持\n            if ('PerformanceObserver' in window) {\n                observeFCP();\n                observeLCP();\n                observeFID();\n                observeCLS();\n                observeTTFB();\n            }\n            // 页面卸载时发送性能数据\n            const sendMetrics = {\n                \"PerformanceMonitor.useEffect.sendMetrics\": ()=>{\n                    // 这里可以发送到分析服务\n                    console.log('Performance Metrics:', metrics);\n                // 示例：发送到Google Analytics或其他分析服务\n                // if (window.gtag) {\n                //   window.gtag('event', 'web_vitals', {\n                //     custom_map: {\n                //       metric_fcp: 'fcp',\n                //       metric_lcp: 'lcp',\n                //       metric_fid: 'fid',\n                //       metric_cls: 'cls',\n                //       metric_ttfb: 'ttfb'\n                //     },\n                //     fcp: metrics.fcp,\n                //     lcp: metrics.lcp,\n                //     fid: metrics.fid,\n                //     cls: metrics.cls,\n                //     ttfb: metrics.ttfb\n                //   });\n                // }\n                }\n            }[\"PerformanceMonitor.useEffect.sendMetrics\"];\n            // 监听页面卸载事件\n            window.addEventListener('beforeunload', sendMetrics);\n            // 监听页面可见性变化\n            document.addEventListener('visibilitychange', {\n                \"PerformanceMonitor.useEffect\": ()=>{\n                    if (document.visibilityState === 'hidden') {\n                        sendMetrics();\n                    }\n                }\n            }[\"PerformanceMonitor.useEffect\"]);\n            return ({\n                \"PerformanceMonitor.useEffect\": ()=>{\n                    window.removeEventListener('beforeunload', sendMetrics);\n                    document.removeEventListener('visibilitychange', sendMetrics);\n                }\n            })[\"PerformanceMonitor.useEffect\"];\n        }\n    }[\"PerformanceMonitor.useEffect\"], []);\n    return null; // 这个组件不渲染任何内容\n}\n_s(PerformanceMonitor, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = PerformanceMonitor;\n// 性能优化工具函数\nconst performanceUtils = {\n    // 预加载关键资源\n    preloadResource: (href, as)=>{\n        const link = document.createElement('link');\n        link.rel = 'preload';\n        link.href = href;\n        link.as = as;\n        document.head.appendChild(link);\n    },\n    // 预连接到外部域名\n    preconnect: (href)=>{\n        const link = document.createElement('link');\n        link.rel = 'preconnect';\n        link.href = href;\n        document.head.appendChild(link);\n    },\n    // DNS预解析\n    dnsPrefetch: (href)=>{\n        const link = document.createElement('link');\n        link.rel = 'dns-prefetch';\n        link.href = href;\n        document.head.appendChild(link);\n    },\n    // 延迟执行非关键代码\n    defer: function(callback) {\n        let delay = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        if ('requestIdleCallback' in window) {\n            requestIdleCallback(callback);\n        } else {\n            setTimeout(callback, delay);\n        }\n    },\n    // 检查网络连接质量\n    getNetworkInfo: ()=>{\n        if ('connection' in navigator) {\n            const connection = navigator.connection;\n            return {\n                effectiveType: connection.effectiveType,\n                downlink: connection.downlink,\n                rtt: connection.rtt,\n                saveData: connection.saveData\n            };\n        }\n        return null;\n    }\n};\nvar _c;\n$RefreshReg$(_c, \"PerformanceMonitor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/performance/PerformanceMonitor.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers/ThemeProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/ThemeProvider.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   themeColors: () => (/* binding */ themeColors),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ themeColors,useTheme,ThemeProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n// 主题颜色配置\nconst themeColors = {\n    light: {\n        background: '#FFFFFF',\n        backgroundSecondary: '#F9FAFB',\n        backgroundTertiary: '#F3F4F6',\n        textPrimary: '#1F2937',\n        textSecondary: '#374151',\n        textTertiary: '#6B7280',\n        textMuted: '#9CA3AF',\n        border: '#E5E7EB',\n        borderSecondary: '#D1D5DB',\n        primary: '#B91C1C',\n        primaryHover: '#991B1B',\n        secondary: '#F59E0B',\n        secondaryHover: '#D97706',\n        success: '#10B981',\n        warning: '#F59E0B',\n        error: '#EF4444',\n        shadow: 'rgba(0, 0, 0, 0.1)',\n        shadowHover: 'rgba(0, 0, 0, 0.2)'\n    },\n    dark: {\n        background: '#0F1419',\n        backgroundSecondary: '#1A1F2E',\n        backgroundTertiary: '#252A3A',\n        textPrimary: '#F9FAFB',\n        textSecondary: '#E5E7EB',\n        textTertiary: '#D1D5DB',\n        textMuted: '#9CA3AF',\n        border: '#374151',\n        borderSecondary: '#4B5563',\n        primary: '#DC2626',\n        primaryHover: '#EF4444',\n        secondary: '#FBBF24',\n        secondaryHover: '#FCD34D',\n        success: '#34D399',\n        warning: '#FBBF24',\n        error: '#F87171',\n        shadow: 'rgba(0, 0, 0, 0.3)',\n        shadowHover: 'rgba(0, 0, 0, 0.5)'\n    }\n};\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useTheme() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\n_s(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction ThemeProvider(param) {\n    let { children } = param;\n    _s1();\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    // 从localStorage加载主题\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (true) {\n                const savedTheme = localStorage.getItem('beijing-opera-theme');\n                if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {\n                    setThemeState(savedTheme);\n                } else {\n                    // 检测系统主题偏好\n                    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n                    setThemeState(prefersDark ? 'dark' : 'light');\n                }\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // 设置主题\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n        if (true) {\n            localStorage.setItem('beijing-opera-theme', newTheme);\n            document.documentElement.setAttribute('data-theme', newTheme);\n        }\n    };\n    // 切换主题\n    const toggleTheme = ()=>{\n        const newTheme = theme === 'light' ? 'dark' : 'light';\n        setTheme(newTheme);\n    };\n    // 获取当前主题的颜色\n    const colors = themeColors[theme];\n    // 生成样式\n    const styles = {\n        card: {\n            backgroundColor: colors.backgroundSecondary,\n            border: \"1px solid \".concat(colors.border),\n            boxShadow: \"0 4px 6px -1px \".concat(colors.shadow),\n            transition: 'all 0.3s ease'\n        },\n        navigation: {\n            backgroundColor: colors.backgroundSecondary,\n            borderBottom: \"2px solid \".concat(colors.secondary),\n            boxShadow: \"0 4px 6px -1px \".concat(colors.shadow)\n        },\n        button: {\n            primary: {\n                backgroundColor: colors.primary,\n                color: colors.background,\n                border: \"2px solid \".concat(colors.primary)\n            },\n            secondary: {\n                backgroundColor: 'transparent',\n                color: colors.primary,\n                border: \"2px solid \".concat(colors.primary)\n            }\n        }\n    };\n    // 应用主题到body\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (true) {\n                document.body.style.backgroundColor = colors.background;\n                document.body.style.color = colors.textPrimary;\n                document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';\n                document.documentElement.setAttribute('data-theme', theme);\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme,\n        colors\n    ]);\n    const contextValue = {\n        theme,\n        toggleTheme,\n        setTheme,\n        colors,\n        styles\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\components\\\\providers\\\\ThemeProvider.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n_s1(ThemeProvider, \"Jg7kX3tl44AHRN9Wplaj1WmEy+o=\");\n_c = ThemeProvider;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/ThemeProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/__barrel_optimize__?names=useEffect!=!./node_modules/next/dist/compiled/react/index.js":
/*!**********************************************************************************************!*\
  !*** __barrel_optimize__?names=useEffect!=!./node_modules/next/dist/compiled/react/index.js ***!
  \**********************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var E_2_AIprogram_3_Augment_1_test1_beijing_opera_masks_node_modules_next_dist_compiled_react_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/react/index.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var E_2_AIprogram_3_Augment_1_test1_beijing_opera_masks_node_modules_next_dist_compiled_react_index_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(E_2_AIprogram_3_Augment_1_test1_beijing_opera_masks_node_modules_next_dist_compiled_react_index_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in E_2_AIprogram_3_Augment_1_test1_beijing_opera_masks_node_modules_next_dist_compiled_react_index_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => E_2_AIprogram_3_Augment_1_test1_beijing_opera_masks_node_modules_next_dist_compiled_react_index_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/__barrel_optimize__?names=useEffect!=!./node_modules/next/dist/compiled/react/index.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C2-AIprogram%5C%5C3-Augment%5C%5C1-test1%5C%5Cbeijing-opera-masks%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CPerformanceMonitor.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C2-AIprogram%5C%5C3-Augment%5C%5C1-test1%5C%5Cbeijing-opera-masks%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);