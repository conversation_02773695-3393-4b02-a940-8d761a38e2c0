-- 清理脚本：删除所有现有的表和类型
-- 在Supabase SQL编辑器中执行此脚本来清理数据库

-- 删除所有触发器
DROP TRIGGER IF EXISTS update_masks_updated_at ON masks;
DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
DROP TRIGGER IF EXISTS update_likes_count_trigger ON user_likes;
DROP TRIGGER IF EXISTS update_contribution_stats_trigger ON masks;

-- 删除所有函数
DROP FUNCTION IF EXISTS update_updated_at_column();
DROP FUNCTION IF EXISTS update_mask_likes_count();
DROP FUNCTION IF EXISTS update_user_contribution_stats();

-- 删除所有表（按依赖关系顺序）
DROP TABLE IF EXISTS user_favorites CASCADE;
DROP TABLE IF EXISTS user_likes CASCADE;
DROP TABLE IF EXISTS drawing_steps CASCADE;
DROP TABLE IF EXISTS user_profiles CASCADE;
DROP TABLE IF EXISTS masks CASCADE;

-- 删除枚举类型
DROP TYPE IF EXISTS role_type CASCADE;

-- 显示清理完成信息
SELECT 'Database cleanup completed. You can now run the schema creation script.' as status;
