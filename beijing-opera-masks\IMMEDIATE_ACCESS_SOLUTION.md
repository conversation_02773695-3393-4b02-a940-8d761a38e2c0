# 立即访问解决方案
## 让外部用户立即访问京剧脸谱平台

### 🚀 方案1: 使用Ngrok (推荐 - 5分钟内解决)

#### 步骤1: 下载Ngrok
1. 访问 https://ngrok.com/
2. 注册免费账户
3. 下载适合您系统的版本

#### 步骤2: 安装和配置
```bash
# Windows
# 1. 解压下载的文件
# 2. 将ngrok.exe放到系统PATH中，或直接在解压目录运行

# 获取认证token (注册后在dashboard中获取)
ngrok authtoken YOUR_AUTH_TOKEN
```

#### 步骤3: 启动服务
```bash
# 终端1: 启动本地应用
cd beijing-opera-masks
npm start

# 终端2: 启动ngrok
ngrok http 3002
```

#### 步骤4: 获取公网URL
Ngrok会显示类似这样的信息：
```
Session Status                online
Account                       <EMAIL>
Version                       3.x.x
Region                        United States (us)
Latency                       45ms
Web Interface                 http://127.0.0.1:4040
Forwarding                    https://abc123.ngrok.io -> http://localhost:3002
```

**您的公网访问地址**: `https://abc123.ngrok.io`

---

### 🌐 方案2: 使用免费动态DNS服务

#### DuckDNS (推荐)
1. **注册**: 访问 https://www.duckdns.org/
2. **选择域名**: 例如 `beijing-opera.duckdns.org`
3. **获取token**: 记录您的token
4. **设置IP**: 
   ```bash
   curl "https://www.duckdns.org/update?domains=beijing-opera&token=YOUR_TOKEN&ip=YOUR_SERVER_IP"
   ```

#### No-IP
1. **注册**: 访问 https://www.noip.com/
2. **选择域名**: 例如 `beijing-opera.ddns.net`
3. **下载客户端**: 自动更新IP地址

---

### 💻 方案3: 本地网络访问

#### 获取本地IP地址
```bash
# Windows
ipconfig | findstr "IPv4"

# 结果示例: *************
```

#### 配置防火墙
确保端口3002对局域网开放：
```bash
# Windows防火墙
# 控制面板 > 系统和安全 > Windows Defender防火墙 > 高级设置
# 入站规则 > 新建规则 > 端口 > TCP > 3002
```

#### 访问地址
局域网内用户可以通过以下地址访问：
- `http://您的IP地址:3002`
- 例如: `http://*************:3002`

---

### 🔧 快速部署脚本

让我创建一个一键启动脚本：
