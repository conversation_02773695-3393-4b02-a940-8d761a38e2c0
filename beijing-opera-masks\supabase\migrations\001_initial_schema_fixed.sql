-- 修正版数据库架构脚本
-- 使用字符串ID以兼容现有数据结构

-- 创建角色类型枚举
CREATE TYPE role_type AS ENUM ('sheng', 'dan', 'jing', 'chou');

-- 创建脸谱表（使用字符串ID）
CREATE TABLE masks (
  id TEXT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  character VARCHAR(100) NOT NULL,
  role_type role_type NOT NULL,
  color_theme TEXT[] NOT NULL DEFAULT '{}',
  image_url TEXT NOT NULL,
  thumbnail_url TEXT,
  cultural_background TEXT NOT NULL,
  personality_traits TEXT[] NOT NULL DEFAULT '{}',
  story_description TEXT,
  dynasty VARCHAR(50),
  is_official BOOLEAN DEFAULT false,
  is_approved BOOLEAN DEFAULT false,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  likes_count INTEGER DEFAULT 0,
  views_count INTEGER DEFAULT 0
);

-- 创建绘制步骤表
CREATE TABLE drawing_steps (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  mask_id TEXT REFERENCES masks(id) ON DELETE CASCADE,
  step_number INTEGER NOT NULL,
  title VARCHAR(100) NOT NULL,
  description TEXT NOT NULL,
  duration INTEGER NOT NULL DEFAULT 3000,
  svg_path TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(mask_id, step_number)
);

-- 创建用户资料表
CREATE TABLE user_profiles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  username VARCHAR(50) NOT NULL UNIQUE,
  display_name VARCHAR(100),
  avatar_url TEXT,
  bio TEXT,
  contribution_points INTEGER DEFAULT 0,
  masks_contributed INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建用户点赞表
CREATE TABLE user_likes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  mask_id TEXT REFERENCES masks(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, mask_id)
);

-- 创建用户收藏表
CREATE TABLE user_favorites (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  mask_id TEXT REFERENCES masks(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, mask_id)
);

-- 创建索引
CREATE INDEX idx_masks_role_type ON masks(role_type);
CREATE INDEX idx_masks_is_official ON masks(is_official);
CREATE INDEX idx_masks_is_approved ON masks(is_approved);
CREATE INDEX idx_masks_created_by ON masks(created_by);
CREATE INDEX idx_masks_created_at ON masks(created_at);
CREATE INDEX idx_drawing_steps_mask_id ON drawing_steps(mask_id);
CREATE INDEX idx_user_likes_user_id ON user_likes(user_id);
CREATE INDEX idx_user_likes_mask_id ON user_likes(mask_id);
CREATE INDEX idx_user_favorites_user_id ON user_favorites(user_id);
CREATE INDEX idx_user_favorites_mask_id ON user_favorites(mask_id);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- 为masks表创建更新时间触发器
CREATE TRIGGER update_masks_updated_at 
  BEFORE UPDATE ON masks 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- 为user_profiles表创建更新时间触发器
CREATE TRIGGER update_user_profiles_updated_at 
  BEFORE UPDATE ON user_profiles 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- 创建点赞计数更新函数
CREATE OR REPLACE FUNCTION update_mask_likes_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE masks SET likes_count = likes_count + 1 WHERE id = NEW.mask_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE masks SET likes_count = likes_count - 1 WHERE id = OLD.mask_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ language 'plpgsql';

-- 创建点赞计数触发器
CREATE TRIGGER update_likes_count_trigger
  AFTER INSERT OR DELETE ON user_likes
  FOR EACH ROW
  EXECUTE FUNCTION update_mask_likes_count();

-- 创建用户贡献统计更新函数
CREATE OR REPLACE FUNCTION update_user_contribution_stats()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' AND NEW.created_by IS NOT NULL THEN
    UPDATE user_profiles 
    SET masks_contributed = masks_contributed + 1,
        contribution_points = contribution_points + 10
    WHERE user_id = NEW.created_by;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' AND OLD.created_by IS NOT NULL THEN
    UPDATE user_profiles 
    SET masks_contributed = GREATEST(masks_contributed - 1, 0),
        contribution_points = GREATEST(contribution_points - 10, 0)
    WHERE user_id = OLD.created_by;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ language 'plpgsql';

-- 创建用户贡献统计触发器
CREATE TRIGGER update_contribution_stats_trigger
  AFTER INSERT OR DELETE ON masks
  FOR EACH ROW
  EXECUTE FUNCTION update_user_contribution_stats();

-- 启用行级安全策略
ALTER TABLE masks ENABLE ROW LEVEL SECURITY;
ALTER TABLE drawing_steps ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_favorites ENABLE ROW LEVEL SECURITY;

-- 创建安全策略
-- 脸谱表策略
CREATE POLICY "公开脸谱可被所有人查看" ON masks
  FOR SELECT USING (is_approved = true OR is_official = true);

CREATE POLICY "用户可以查看自己创建的脸谱" ON masks
  FOR SELECT USING (auth.uid() = created_by);

CREATE POLICY "认证用户可以创建脸谱" ON masks
  FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "用户可以更新自己创建的脸谱" ON masks
  FOR UPDATE USING (auth.uid() = created_by);

CREATE POLICY "用户可以删除自己创建的脸谱" ON masks
  FOR DELETE USING (auth.uid() = created_by);

-- 绘制步骤表策略
CREATE POLICY "绘制步骤跟随脸谱权限" ON drawing_steps
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM masks 
      WHERE masks.id = drawing_steps.mask_id 
      AND (masks.is_approved = true OR masks.is_official = true OR masks.created_by = auth.uid())
    )
  );

CREATE POLICY "用户可以为自己的脸谱添加绘制步骤" ON drawing_steps
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM masks 
      WHERE masks.id = drawing_steps.mask_id 
      AND masks.created_by = auth.uid()
    )
  );

-- 用户资料表策略
CREATE POLICY "用户资料公开可见" ON user_profiles
  FOR SELECT USING (true);

CREATE POLICY "用户可以创建自己的资料" ON user_profiles
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "用户可以更新自己的资料" ON user_profiles
  FOR UPDATE USING (auth.uid() = user_id);

-- 用户点赞表策略
CREATE POLICY "用户可以查看所有点赞" ON user_likes
  FOR SELECT USING (true);

CREATE POLICY "用户可以点赞" ON user_likes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "用户可以取消自己的点赞" ON user_likes
  FOR DELETE USING (auth.uid() = user_id);

-- 用户收藏表策略
CREATE POLICY "用户可以查看自己的收藏" ON user_favorites
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "用户可以添加收藏" ON user_favorites
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "用户可以删除自己的收藏" ON user_favorites
  FOR DELETE USING (auth.uid() = user_id);

-- 显示创建完成信息
SELECT 'Database schema created successfully with string IDs for masks.' as status;
