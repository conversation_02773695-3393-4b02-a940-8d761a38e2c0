"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_masks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/masks */ \"(app-pages-browser)/./src/data/masks.ts\");\n/* harmony import */ var _services_maskService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/maskService */ \"(app-pages-browser)/./src/services/maskService.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers/ThemeProvider */ \"(app-pages-browser)/./src/components/providers/ThemeProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Home() {\n    var _selectedMask_images, _selectedMask_culturalBackground;\n    _s();\n    const [masks, setMasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_data_masks__WEBPACK_IMPORTED_MODULE_2__.operaMasks);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedMask, setSelectedMask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { colors } = (0,_components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    // 加载脸谱数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const loadMasks = {\n                \"Home.useEffect.loadMasks\": async ()=>{\n                    if (!(0,_lib_supabase__WEBPACK_IMPORTED_MODULE_4__.isSupabaseConfigured)()) {\n                        console.log('Using static mask data');\n                        return;\n                    }\n                    setLoading(true);\n                    try {\n                        const maskData = await _services_maskService__WEBPACK_IMPORTED_MODULE_3__.MaskService.getAllApprovedMasks();\n                        setMasks(maskData);\n                    } catch (error) {\n                        console.error('Error loading masks:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"Home.useEffect.loadMasks\"];\n            loadMasks();\n        }\n    }[\"Home.useEffect\"], []);\n    const handleMaskClick = (mask)=>{\n        setSelectedMask(mask);\n    };\n    const closeModal = ()=>{\n        setSelectedMask(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            backgroundColor: colors.background,\n            color: colors.text,\n            padding: '2rem'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center',\n                    marginBottom: '3rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            fontSize: '2.5rem',\n                            fontWeight: 'bold',\n                            marginBottom: '1rem',\n                            background: 'linear-gradient(135deg, #DC2626, #B91C1C)',\n                            WebkitBackgroundClip: 'text',\n                            WebkitTextFillColor: 'transparent',\n                            fontFamily: '\"Ma Shan Zheng\", cursive'\n                        },\n                        children: \"\\uD83C\\uDFAD 京剧脸谱文化展示\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            fontSize: '1.125rem',\n                            color: colors.textSecondary,\n                            maxWidth: '600px',\n                            margin: '0 auto'\n                        },\n                        children: \"探索中国传统京剧脸谱艺术的魅力，了解每个角色背后的文化内涵\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center',\n                    padding: '2rem',\n                    color: colors.textSecondary\n                },\n                children: \"正在加载脸谱数据...\"\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                    gap: '2rem',\n                    maxWidth: '1200px',\n                    margin: '0 auto'\n                },\n                children: masks.map((mask)=>{\n                    var _mask_images, _mask_images1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: ()=>handleMaskClick(mask),\n                        style: {\n                            backgroundColor: colors.cardBackground,\n                            borderRadius: '12px',\n                            padding: '1.5rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.3s ease',\n                            border: \"1px solid \".concat(colors.border),\n                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: '100%',\n                                    height: '200px',\n                                    borderRadius: '8px',\n                                    overflow: 'hidden',\n                                    marginBottom: '1rem'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: ((_mask_images = mask.images) === null || _mask_images === void 0 ? void 0 : _mask_images.fullSize) || mask.imageUrl || ((_mask_images1 = mask.images) === null || _mask_images1 === void 0 ? void 0 : _mask_images1.thumbnail),\n                                    alt: mask.name,\n                                    style: {\n                                        width: '100%',\n                                        height: '100%',\n                                        objectFit: 'cover'\n                                    },\n                                    onError: (e)=>{\n                                        // 图片加载失败时的备用处理\n                                        const target = e.target;\n                                        target.src = \"https://via.placeholder.com/300x300/DC143C/FFFFFF?text=\".concat(encodeURIComponent(mask.name));\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    fontSize: '1.25rem',\n                                    fontWeight: 'bold',\n                                    marginBottom: '0.5rem',\n                                    color: colors.text\n                                },\n                                children: mask.name\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: '0.875rem',\n                                    color: colors.textSecondary,\n                                    marginBottom: '1rem'\n                                },\n                                children: [\n                                    \"角色: \",\n                                    mask.character\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this),\n                            (mask.colorTheme || mask.mainColors) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '0.5rem',\n                                    marginBottom: '1rem'\n                                },\n                                children: (mask.colorTheme || mask.mainColors || []).slice(0, 3).map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: '20px',\n                                            height: '20px',\n                                            borderRadius: '50%',\n                                            backgroundColor: color,\n                                            border: '1px solid rgba(0,0,0,0.1)'\n                                        },\n                                        title: color\n                                    }, index, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 15\n                            }, this),\n                            (mask.personalityTraits || mask.tags) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexWrap: 'wrap',\n                                    gap: '0.5rem'\n                                },\n                                children: (mask.personalityTraits || mask.tags || []).slice(0, 3).map((trait, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: '0.75rem',\n                                            padding: '0.25rem 0.5rem',\n                                            backgroundColor: colors.primary + '20',\n                                            color: colors.primary,\n                                            borderRadius: '12px'\n                                        },\n                                        children: trait\n                                    }, index, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, mask.id, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            selectedMask && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    zIndex: 1000,\n                    padding: '1rem'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: colors.background,\n                        borderRadius: '12px',\n                        padding: '2rem',\n                        maxWidth: '600px',\n                        width: '100%',\n                        maxHeight: '80vh',\n                        overflow: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center',\n                                marginBottom: '1.5rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        fontSize: '1.5rem',\n                                        fontWeight: 'bold',\n                                        color: colors.text\n                                    },\n                                    children: selectedMask.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: closeModal,\n                                    style: {\n                                        background: 'none',\n                                        border: 'none',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer',\n                                        color: colors.textSecondary\n                                    },\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: selectedMask.imageUrl || ((_selectedMask_images = selectedMask.images) === null || _selectedMask_images === void 0 ? void 0 : _selectedMask_images.fullSize),\n                            alt: selectedMask.name,\n                            style: {\n                                width: '100%',\n                                height: '300px',\n                                objectFit: 'cover',\n                                borderRadius: '8px',\n                                marginBottom: '1.5rem'\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '0.875rem',\n                                color: colors.textSecondary,\n                                lineHeight: '1.6'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"角色:\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 18\n                                        }, this),\n                                        \" \",\n                                        selectedMask.character\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"文化背景:\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 18\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        marginLeft: '1rem',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: typeof selectedMask.culturalBackground === 'string' ? selectedMask.culturalBackground : ((_selectedMask_culturalBackground = selectedMask.culturalBackground) === null || _selectedMask_culturalBackground === void 0 ? void 0 : _selectedMask_culturalBackground.origin) || '传统京剧脸谱艺术'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, this),\n                                (selectedMask.personalityTraits || selectedMask.tags) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"特征:\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 22\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                flexWrap: 'wrap',\n                                                gap: '0.5rem',\n                                                marginLeft: '1rem'\n                                            },\n                                            children: (selectedMask.personalityTraits || selectedMask.tags || []).map((trait, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.75rem',\n                                                        padding: '0.25rem 0.5rem',\n                                                        backgroundColor: colors.primary + '20',\n                                                        color: colors.primary,\n                                                        borderRadius: '12px'\n                                                    },\n                                                    children: trait\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"Vfoqi3Tz0LvR925DWBUyKoanCxA=\", false, function() {\n    return [\n        _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_5__.useTheme\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});