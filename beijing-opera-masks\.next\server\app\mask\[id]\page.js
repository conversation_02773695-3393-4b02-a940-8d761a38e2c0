(()=>{var a={};a.id=183,a.ids=[183],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},465:(a,b,c)=>{Promise.resolve().then(c.bind(c,9935))},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},1137:(a,b,c)=>{Promise.resolve().then(c.bind(c,7061))},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:a=>{"use strict";a.exports=require("path")},5032:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["mask",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,7061)),"E:\\2-AIprogram\\3-Augment\\1-test1\\beijing-opera-masks\\src\\app\\mask\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,4431)),"E:\\2-AIprogram\\3-Augment\\1-test1\\beijing-opera-masks\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["E:\\2-AIprogram\\3-Augment\\1-test1\\beijing-opera-masks\\src\\app\\mask\\[id]\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/mask/[id]/page",pathname:"/mask/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/mask/[id]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},7061:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\2-AIprogram\\3-Augment\\1-test1\\beijing-opera-masks\\src\\app\\mask\\[id]\\page.tsx","default")},8354:a=>{"use strict";a.exports=require("util")},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9935:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>k});var d=c(687),e=c(3210),f=c(6189),g=c(883);function h({mask:a,isPlaying:b,onPlayStateChange:c,speed:f=1,className:g}){let[h,i]=(0,e.useState)(0),[j,k]=(0,e.useState)(0),l=(0,e.useRef)(null),m=(0,e.useRef)(null);return(0,d.jsxs)("div",{className:g,style:{position:"relative"},children:[(0,d.jsx)("div",{style:{aspectRatio:"1",border:"3px solid #F59E0B",borderRadius:"1rem",overflow:"hidden",backgroundColor:"white"},children:(()=>{let b=a.drawingSteps.slice(0,h+1),c=a.drawingSteps[h];return(0,d.jsxs)("svg",{width:"100%",height:"100%",viewBox:"0 0 300 300",style:{background:"white",borderRadius:"1rem"},children:[(0,d.jsx)("ellipse",{cx:"150",cy:"150",rx:"120",ry:"140",fill:"#FFF8DC",stroke:"#D4A574",strokeWidth:"2"}),b.map((a,b)=>{let c=b===h,e=c?`${100*j} 100`:void 0;return(0,d.jsx)("g",{opacity:c?j:1,children:((a,b)=>{switch(a.name){case"底色":return(0,d.jsx)("ellipse",{cx:"150",cy:"150",rx:"115",ry:"135",fill:a.color,fillOpacity:"0.8"});case"眉毛":return(0,d.jsxs)("g",{children:[(0,d.jsx)("path",{d:"M 100 120 Q 130 110 160 120",stroke:a.color,strokeWidth:a.strokeWidth||4,fill:"none",strokeLinecap:"round",strokeDasharray:b}),(0,d.jsx)("path",{d:"M 140 120 Q 170 110 200 120",stroke:a.color,strokeWidth:a.strokeWidth||4,fill:"none",strokeLinecap:"round",strokeDasharray:b})]});case"眼部":return(0,d.jsxs)("g",{children:[(0,d.jsx)("ellipse",{cx:"120",cy:"140",rx:"15",ry:"10",stroke:a.color,strokeWidth:a.strokeWidth||3,fill:"white",strokeDasharray:b}),(0,d.jsx)("ellipse",{cx:"180",cy:"140",rx:"15",ry:"10",stroke:a.color,strokeWidth:a.strokeWidth||3,fill:"white",strokeDasharray:b}),(0,d.jsx)("circle",{cx:"120",cy:"140",r:"5",fill:a.color}),(0,d.jsx)("circle",{cx:"180",cy:"140",r:"5",fill:a.color})]});case"鼻梁":case"鼻部":return(0,d.jsx)("path",{d:"M 150 160 L 150 180 M 140 185 L 160 185",stroke:a.color,strokeWidth:a.strokeWidth||3,strokeLinecap:"round",strokeDasharray:b});case"装饰":return(0,d.jsxs)("g",{children:[(0,d.jsx)("path",{d:"M 150 100 L 155 110 L 145 110 Z",fill:a.color,stroke:a.color,strokeWidth:"1"}),(0,d.jsx)("circle",{cx:"120",cy:"180",r:"3",fill:a.color}),(0,d.jsx)("circle",{cx:"180",cy:"180",r:"3",fill:a.color})]});case"胡须":return(0,d.jsxs)("g",{children:[(0,d.jsx)("path",{d:"M 120 200 Q 150 220 180 200",stroke:a.color,strokeWidth:a.strokeWidth||2,fill:"none",strokeDasharray:b}),(0,d.jsx)("path",{d:"M 110 210 Q 150 230 190 210",stroke:a.color,strokeWidth:a.strokeWidth||2,fill:"none",strokeDasharray:b})]});case"唇部":return(0,d.jsx)("ellipse",{cx:"150",cy:"200",rx:"12",ry:"6",fill:a.color,stroke:a.color,strokeWidth:"1"});default:return(0,d.jsx)("circle",{cx:"150",cy:"150",r:"5",fill:a.color,stroke:a.color})}})(a,e)},a.id)}),(0,d.jsx)("text",{x:"150",y:"280",textAnchor:"middle",fontSize:"14",fill:"#1F2937",fontWeight:"600",children:c?.name||"完成"})]})})()}),(0,d.jsxs)("div",{style:{marginTop:"1rem",padding:"1rem",backgroundColor:"white",borderRadius:"0.5rem",border:"2px solid #F59E0B",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)"},children:[(0,d.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"1rem"},children:[(0,d.jsx)("h3",{style:{fontSize:"1.125rem",fontWeight:"600",color:"#1F2937",fontFamily:'"Noto Serif SC", serif'},children:"绘制过程演示"}),(0,d.jsxs)("span",{style:{fontSize:"0.875rem",color:"#6B7280"},children:["步骤 ",h+1," / ",a.drawingSteps.length]})]}),(0,d.jsx)("div",{style:{width:"100%",height:"6px",backgroundColor:"#E5E7EB",borderRadius:"3px",marginBottom:"1rem",overflow:"hidden"},children:(0,d.jsx)("div",{style:{height:"100%",backgroundColor:"#F59E0B",borderRadius:"3px",transition:"width 0.1s ease",width:`${(h+j)/a.drawingSteps.length*100}%`}})}),(0,d.jsxs)("div",{style:{display:"flex",gap:"0.5rem",justifyContent:"center"},children:[(0,d.jsx)("button",{onClick:()=>c(!b),style:{backgroundColor:b?"#EF4444":"#10B981",color:"white",border:"none",padding:"0.5rem 1rem",borderRadius:"0.5rem",cursor:"pointer",fontSize:"0.875rem",fontWeight:"500"},children:b?"暂停":"播放"}),(0,d.jsx)("button",{onClick:()=>{i(0),k(0),l.current&&(clearInterval(l.current),l.current=null),m.current&&(cancelAnimationFrame(m.current),m.current=null)},style:{backgroundColor:"#6B7280",color:"white",border:"none",padding:"0.5rem 1rem",borderRadius:"0.5rem",cursor:"pointer",fontSize:"0.875rem",fontWeight:"500"},children:"重置"})]}),(0,d.jsx)("div",{style:{marginTop:"1rem",padding:"0.75rem",backgroundColor:"#F9FAFB",borderRadius:"0.5rem",borderLeft:"4px solid #F59E0B"},children:(0,d.jsxs)("p",{style:{fontSize:"0.875rem",color:"#374151",margin:0},children:[(0,d.jsxs)("strong",{children:[a.drawingSteps[h]?.name,"："]}),a.drawingSteps[h]?.description]})})]})]})}function i({items:a,className:b}){let c=(0,f.useRouter)();return(0,d.jsx)("nav",{"aria-label":"面包屑导航",className:b,style:{padding:"1rem 0",borderBottom:"1px solid #E5E7EB"},children:(0,d.jsx)("ol",{style:{display:"flex",alignItems:"center",gap:"0.5rem",fontSize:"0.875rem",color:"#6B7280"},children:a.map((a,b)=>(0,d.jsxs)("li",{style:{display:"flex",alignItems:"center"},children:[b>0&&(0,d.jsx)("span",{style:{margin:"0 0.5rem",color:"#D1D5DB"},children:"/"}),a.current?(0,d.jsx)("span",{style:{color:"#1F2937",fontWeight:"500",fontFamily:'"Noto Serif SC", serif'},children:a.label}):(0,d.jsx)("button",{onClick:()=>{var b;(b=a.href)&&c.push(b)},style:{background:"none",border:"none",color:"#6B7280",cursor:"pointer",textDecoration:"none",fontSize:"inherit",padding:0,transition:"color 0.2s ease"},onMouseEnter:a=>{a.currentTarget.style.color="#B91C1C"},onMouseLeave:a=>{a.currentTarget.style.color="#6B7280"},children:a.label})]},b))})})}var j=c(7225);function k(){let a=(0,f.useParams)(),b=(0,f.useRouter)(),c=a.id,[k,l]=(0,e.useState)(!1),[m,n]=(0,e.useState)(!1),{addToRecentlyViewed:o,toggleFavorite:p,isFavorite:q}=(0,j.A)(),r=g.hu.find(a=>a.id===c);return r?(0,d.jsxs)("div",{style:{minHeight:"100vh",backgroundColor:"#F9FAFB",fontFamily:'"Noto Sans SC", sans-serif'},children:[(0,d.jsx)("header",{style:{backgroundColor:"white",borderBottom:"2px solid #F59E0B",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)",position:"sticky",top:0,zIndex:40},children:(0,d.jsxs)("div",{style:{maxWidth:"1200px",margin:"0 auto",padding:"1rem 1.5rem",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,d.jsx)("button",{onClick:()=>b.push("/"),style:{backgroundColor:"transparent",border:"2px solid #B91C1C",color:"#B91C1C",padding:"0.5rem 1rem",borderRadius:"0.5rem",cursor:"pointer",fontSize:"0.875rem",fontWeight:"500"},children:"← 返回首页"}),(0,d.jsx)("h1",{style:{fontSize:"1.5rem",fontWeight:"bold",color:"#1F2937",fontFamily:'"Noto Serif SC", serif'},children:r.name}),(0,d.jsxs)("div",{style:{display:"flex",gap:"0.5rem"},children:[(0,d.jsxs)("button",{onClick:()=>p(r.id),style:{backgroundColor:q(r.id)?"#EF4444":"#6B7280",color:"white",border:"none",padding:"0.5rem 1rem",borderRadius:"0.5rem",cursor:"pointer",fontSize:"0.875rem",fontWeight:"500",display:"flex",alignItems:"center",gap:"0.25rem"},children:[q(r.id)?"❤️":"\uD83E\uDD0D",q(r.id)?"已收藏":"收藏"]}),(0,d.jsx)("button",{onClick:()=>l(!k),style:{backgroundColor:k?"#EF4444":"#10B981",color:"white",border:"none",padding:"0.5rem 1rem",borderRadius:"0.5rem",cursor:"pointer",fontSize:"0.875rem",fontWeight:"500"},children:k?"关闭动画":"绘制动画"})]})]})}),(0,d.jsxs)("main",{style:{padding:"2rem 1.5rem"},children:[(0,d.jsx)("div",{style:{maxWidth:"1200px",margin:"0 auto"},children:(0,d.jsx)(i,{items:[{label:"首页",href:"/"},{label:"脸谱详情",current:!0}]})}),(0,d.jsxs)("div",{style:{maxWidth:"1200px",margin:"0 auto",display:"grid",gridTemplateColumns:"1fr 1fr",gap:"3rem",alignItems:"start"},children:[(0,d.jsxs)("div",{style:{position:"sticky",top:"120px"},children:[k?(0,d.jsx)(h,{mask:r,isPlaying:m,onPlayStateChange:n,speed:1,style:{marginBottom:"2rem"}}):(0,d.jsxs)("div",{style:{aspectRatio:"1",background:`linear-gradient(135deg, ${r.mainColors[0]} 0%, ${r.mainColors[1]||r.mainColors[0]} 100%)`,borderRadius:"1rem",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"4rem",fontWeight:"bold",marginBottom:"2rem",position:"relative",boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1)",border:"3px solid #F59E0B"},children:[(0,d.jsx)("div",{style:{position:"absolute",inset:0,background:"linear-gradient(to top, rgba(0,0,0,0.3) 0%, transparent 100%)",borderRadius:"1rem"}}),(0,d.jsx)("span",{style:{position:"relative",zIndex:10},children:r.character}),(0,d.jsx)("div",{style:{position:"absolute",top:"1rem",left:"1rem",backgroundColor:"rgba(0,0,0,0.8)",color:"white",padding:"0.5rem 1rem",borderRadius:"9999px",fontSize:"0.875rem",fontWeight:"600"},children:r.roleCategory}),(0,d.jsx)("div",{style:{position:"absolute",top:"1rem",right:"1rem",backgroundColor:"rgba(0,0,0,0.8)",color:"white",padding:"0.5rem 1rem",borderRadius:"9999px",fontSize:"0.875rem",fontWeight:"600"},children:r.colorCategory}),(0,d.jsx)("div",{style:{position:"absolute",bottom:"1rem",right:"1rem",display:"flex",gap:"0.25rem"},children:Array.from({length:5}).map((a,b)=>(0,d.jsx)("span",{style:{color:b<Math.floor(r.popularity/2)?"#F59E0B":"rgba(255,255,255,0.5)",fontSize:"1.25rem"},children:"★"},b))})]}),(0,d.jsxs)("div",{style:{backgroundColor:"white",padding:"1.5rem",borderRadius:"1rem",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1)",border:"2px solid #F59E0B"},children:[(0,d.jsx)("h3",{style:{fontSize:"1.25rem",fontWeight:"600",color:"#1F2937",marginBottom:"1rem",fontFamily:'"Noto Serif SC", serif'},children:"主要色彩"}),(0,d.jsx)("div",{style:{display:"flex",gap:"1rem",alignItems:"center"},children:r.mainColors.map((a,b)=>(0,d.jsxs)("div",{style:{textAlign:"center"},children:[(0,d.jsx)("div",{style:{width:"3rem",height:"3rem",borderRadius:"50%",backgroundColor:a,border:"3px solid #D1D5DB",marginBottom:"0.5rem",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)"}}),(0,d.jsx)("span",{style:{fontSize:"0.75rem",color:"#6B7280",fontFamily:"monospace"},children:a})]},b))})]})]}),(0,d.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"2rem"},children:[(0,d.jsxs)("div",{style:{backgroundColor:"white",padding:"2rem",borderRadius:"1rem",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1)",border:"2px solid #F59E0B"},children:[(0,d.jsx)("h2",{style:{fontSize:"1.5rem",fontWeight:"bold",color:"#1F2937",marginBottom:"1.5rem",fontFamily:'"Noto Serif SC", serif'},children:"基本信息"}),(0,d.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"1rem"},children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{style:{fontWeight:"600",color:"#374151"},children:"角色名称："}),(0,d.jsx)("span",{style:{color:"#6B7280"},children:r.character})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{style:{fontWeight:"600",color:"#374151"},children:"行当分类："}),(0,d.jsx)("span",{style:{color:"#6B7280"},children:r.roleCategory})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{style:{fontWeight:"600",color:"#374151"},children:"颜色分类："}),(0,d.jsx)("span",{style:{color:"#6B7280"},children:r.colorCategory})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{style:{fontWeight:"600",color:"#374151"},children:"绘制难度："}),(0,d.jsx)("span",{style:{color:"#6B7280"},children:"easy"===r.difficulty?"简单":"medium"===r.difficulty?"中等":"困难"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{style:{fontWeight:"600",color:"#374151"},children:"受欢迎程度："}),(0,d.jsxs)("span",{style:{color:"#6B7280"},children:[r.popularity,"/10"]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{style:{fontWeight:"600",color:"#374151"},children:"历史时期："}),(0,d.jsx)("span",{style:{color:"#6B7280"},children:r.culturalBackground.historicalPeriod})]})]})]}),(0,d.jsxs)("div",{style:{backgroundColor:"white",padding:"2rem",borderRadius:"1rem",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1)",border:"2px solid #F59E0B"},children:[(0,d.jsx)("h2",{style:{fontSize:"1.5rem",fontWeight:"bold",color:"#1F2937",marginBottom:"1.5rem",fontFamily:'"Noto Serif SC", serif'},children:"文化背景"}),(0,d.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"1.5rem"},children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{style:{fontWeight:"600",color:"#374151",marginBottom:"0.5rem"},children:"历史起源"}),(0,d.jsx)("p",{style:{color:"#6B7280",lineHeight:"1.6"},children:r.culturalBackground.origin})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{style:{fontWeight:"600",color:"#374151",marginBottom:"0.5rem"},children:"性格特点"}),(0,d.jsx)("p",{style:{color:"#6B7280",lineHeight:"1.6"},children:r.culturalBackground.personality})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{style:{fontWeight:"600",color:"#374151",marginBottom:"0.5rem"},children:"象征意义"}),(0,d.jsx)("p",{style:{color:"#6B7280",lineHeight:"1.6"},children:r.culturalBackground.symbolism})]})]})]}),(0,d.jsxs)("div",{style:{backgroundColor:"white",padding:"2rem",borderRadius:"1rem",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1)",border:"2px solid #F59E0B"},children:[(0,d.jsx)("h2",{style:{fontSize:"1.5rem",fontWeight:"bold",color:"#1F2937",marginBottom:"1.5rem",fontFamily:'"Noto Serif SC", serif'},children:"色彩寓意"}),(0,d.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:"1rem"},children:Object.entries(r.colorMeaning).map(([a,b])=>(0,d.jsxs)("div",{style:{padding:"1rem",backgroundColor:"#F9FAFB",borderRadius:"0.5rem",borderLeft:"4px solid #F59E0B"},children:[(0,d.jsxs)("span",{style:{fontWeight:"600",color:"#374151"},children:[a,"："]}),(0,d.jsx)("span",{style:{color:"#6B7280"},children:b})]},a))})]}),(0,d.jsxs)("div",{style:{backgroundColor:"white",padding:"2rem",borderRadius:"1rem",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1)",border:"2px solid #F59E0B"},children:[(0,d.jsx)("h2",{style:{fontSize:"1.5rem",fontWeight:"bold",color:"#1F2937",marginBottom:"1.5rem",fontFamily:'"Noto Serif SC", serif'},children:"相关剧目"}),(0,d.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:"1.5rem"},children:r.relatedOperas.map((a,b)=>(0,d.jsxs)("div",{style:{padding:"1.5rem",backgroundColor:"#F9FAFB",borderRadius:"0.75rem",border:"1px solid #E5E7EB"},children:[(0,d.jsx)("h3",{style:{fontSize:"1.125rem",fontWeight:"600",color:"#1F2937",marginBottom:"0.5rem",fontFamily:'"Noto Serif SC", serif'},children:a.name}),(0,d.jsx)("p",{style:{color:"#6B7280",marginBottom:"0.5rem",lineHeight:"1.6"},children:a.description}),(0,d.jsx)("span",{style:{fontSize:"0.875rem",color:"#F59E0B",fontWeight:"500"},children:a.period})]},b))})]}),(0,d.jsxs)("div",{style:{backgroundColor:"white",padding:"2rem",borderRadius:"1rem",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1)",border:"2px solid #F59E0B"},children:[(0,d.jsx)("h2",{style:{fontSize:"1.5rem",fontWeight:"bold",color:"#1F2937",marginBottom:"1.5rem",fontFamily:'"Noto Serif SC", serif'},children:"相关标签"}),(0,d.jsx)("div",{style:{display:"flex",flexWrap:"wrap",gap:"0.75rem"},children:r.tags.map((a,b)=>(0,d.jsx)("span",{style:{padding:"0.75rem 1rem",fontSize:"0.875rem",backgroundColor:"rgba(245, 158, 11, 0.1)",color:"#F59E0B",border:"2px solid #F59E0B",borderRadius:"9999px",fontWeight:"500"},children:a},b))})]})]})]})]})]}):(0,d.jsx)("div",{style:{minHeight:"100vh",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"#F9FAFB",fontFamily:'"Noto Sans SC", sans-serif'},children:(0,d.jsxs)("div",{style:{textAlign:"center"},children:[(0,d.jsx)("h1",{style:{fontSize:"2rem",color:"#1F2937",marginBottom:"1rem"},children:"脸谱未找到"}),(0,d.jsx)("p",{style:{color:"#6B7280",marginBottom:"2rem"},children:"抱歉，您访问的脸谱不存在。"}),(0,d.jsx)("button",{onClick:()=>b.push("/"),style:{backgroundColor:"#B91C1C",color:"white",padding:"0.75rem 1.5rem",borderRadius:"0.5rem",border:"none",cursor:"pointer",fontSize:"1rem"},children:"返回首页"})]})})}}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,400,12],()=>b(b.s=5032));module.exports=c})();