<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关羽脸谱图片加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .image-test {
            display: flex;
            align-items: center;
            gap: 20px;
            margin: 20px 0;
        }
        .image-container {
            width: 260px;
            height: 260px;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
        }
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .loading { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎭 关羽脸谱图片加载测试</h1>
        <p>测试关羽脸谱图片是否能够正常加载显示</p>
        
        <div class="image-test">
            <div class="image-container">
                <img id="testImage" alt="关羽脸谱测试">
            </div>
            <div>
                <h3>图片信息</h3>
                <p><strong>URL:</strong> https://d.bmcx.com/lianpu/d/0072.jpg</p>
                <p><strong>角色:</strong> 关羽</p>
                <p><strong>特征:</strong> 红脸，象征忠诚勇猛</p>
                <div id="status" class="status loading">正在加载图片...</div>
            </div>
        </div>
        
        <div id="details" style="margin-top: 20px;">
            <h3>加载详情</h3>
            <ul id="logList"></ul>
        </div>
    </div>

    <script>
        const imageUrl = 'https://d.bmcx.com/lianpu/d/0072.jpg';
        const testImage = document.getElementById('testImage');
        const status = document.getElementById('status');
        const logList = document.getElementById('logList');
        
        function addLog(message, type = 'info') {
            const li = document.createElement('li');
            li.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            li.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#6c757d';
            logList.appendChild(li);
        }
        
        addLog('开始测试图片加载...');
        
        testImage.onload = function() {
            status.className = 'status success';
            status.textContent = '✅ 图片加载成功！';
            addLog('图片加载成功', 'success');
            addLog(`图片尺寸: ${this.naturalWidth} x ${this.naturalHeight}`, 'success');
        };
        
        testImage.onerror = function() {
            status.className = 'status error';
            status.textContent = '❌ 图片加载失败';
            addLog('图片加载失败', 'error');
        };
        
        // 设置超时
        setTimeout(() => {
            if (!testImage.complete) {
                status.className = 'status error';
                status.textContent = '⏰ 图片加载超时';
                addLog('图片加载超时（5秒）', 'error');
            }
        }, 5000);
        
        addLog(`设置图片源: ${imageUrl}`);
        testImage.src = imageUrl;
    </script>
</body>
</html>
