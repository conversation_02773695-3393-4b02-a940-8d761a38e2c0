"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/mask/[id]/page",{

/***/ "(app-pages-browser)/./src/app/mask/[id]/page.tsx":
/*!************************************!*\
  !*** ./src/app/mask/[id]/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MaskDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _data_masks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/masks */ \"(app-pages-browser)/./src/data/masks.ts\");\n/* harmony import */ var _services_maskService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/maskService */ \"(app-pages-browser)/./src/services/maskService.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/providers/ThemeProvider */ \"(app-pages-browser)/./src/components/providers/ThemeProvider.tsx\");\n/* harmony import */ var _components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/navigation/SimpleNavbar */ \"(app-pages-browser)/./src/components/navigation/SimpleNavbar.tsx\");\n/* harmony import */ var _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useLocalStorage */ \"(app-pages-browser)/./src/hooks/useLocalStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction MaskDetailPage() {\n    var _mask_images, _mask_images1;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const maskId = params.id;\n    const [mask, setMask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAnimation, setShowAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { colors } = (0,_components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const { toggleFavorite, isFavorite } = (0,_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useFavorites)();\n    const { addToRecentlyViewed } = (0,_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useRecentlyViewed)();\n    // 加载脸谱数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MaskDetailPage.useEffect\": ()=>{\n            const loadMask = {\n                \"MaskDetailPage.useEffect.loadMask\": async ()=>{\n                    try {\n                        let maskData = _data_masks__WEBPACK_IMPORTED_MODULE_3__.operaMasks; // 默认使用静态数据\n                        if ((0,_lib_supabase__WEBPACK_IMPORTED_MODULE_5__.isSupabaseConfigured)()) {\n                            try {\n                                maskData = await _services_maskService__WEBPACK_IMPORTED_MODULE_4__.MaskService.getAllApprovedMasks();\n                            } catch (error) {\n                                console.error('Error loading masks from database:', error);\n                            }\n                        }\n                        const foundMask = maskData.find({\n                            \"MaskDetailPage.useEffect.loadMask.foundMask\": (m)=>m.id === maskId\n                        }[\"MaskDetailPage.useEffect.loadMask.foundMask\"]);\n                        setMask(foundMask || null);\n                        // 添加到最近浏览记录\n                        if (foundMask) {\n                            addToRecentlyViewed(foundMask.id);\n                        }\n                    } catch (error) {\n                        console.error('Error loading mask:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"MaskDetailPage.useEffect.loadMask\"];\n            loadMask();\n        }\n    }[\"MaskDetailPage.useEffect\"], [\n        maskId,\n        addToRecentlyViewed\n    ]);\n    // 加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                backgroundColor: colors.background,\n                color: colors.textPrimary\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__.SimpleNavbar, {\n                    showBackButton: true,\n                    title: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        minHeight: 'calc(100vh - 80px)'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '3rem',\n                                    marginBottom: '1rem',\n                                    animation: 'pulse 2s infinite'\n                                },\n                                children: \"\\uD83C\\uDFAD\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"正在加载脸谱信息...\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, this);\n    }\n    // 脸谱未找到\n    if (!mask) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                backgroundColor: colors.background,\n                color: colors.textPrimary\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__.SimpleNavbar, {\n                    showBackButton: true,\n                    title: \"脸谱未找到\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        minHeight: 'calc(100vh - 80px)'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontSize: '2rem',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"脸谱未找到\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    color: colors.textSecondary,\n                                    marginBottom: '2rem'\n                                },\n                                children: \"抱歉，您访问的脸谱不存在。\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/'),\n                                style: {\n                                    backgroundColor: colors.primary,\n                                    color: 'white',\n                                    padding: '0.75rem 1.5rem',\n                                    borderRadius: '0.5rem',\n                                    border: 'none',\n                                    cursor: 'pointer',\n                                    fontSize: '1rem'\n                                },\n                                children: \"返回首页\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            backgroundColor: colors.background,\n            color: colors.textPrimary\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__.SimpleNavbar, {\n                showBackButton: true,\n                title: mask.name\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '1200px',\n                    margin: '0 auto',\n                    padding: '2rem'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'grid',\n                        gridTemplateColumns: '1fr 1fr',\n                        gap: '3rem',\n                        '@media (max-width: 768px)': {\n                            gridTemplateColumns: '1fr'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: colors.backgroundSecondary,\n                                    borderRadius: '12px',\n                                    padding: '2rem',\n                                    textAlign: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: ((_mask_images = mask.images) === null || _mask_images === void 0 ? void 0 : _mask_images.fullSize) || mask.imageUrl || ((_mask_images1 = mask.images) === null || _mask_images1 === void 0 ? void 0 : _mask_images1.thumbnail),\n                                        alt: mask.name,\n                                        style: {\n                                            width: '100%',\n                                            maxWidth: '400px',\n                                            height: 'auto',\n                                            borderRadius: '8px',\n                                            marginBottom: '1rem'\n                                        },\n                                        onError: (e)=>{\n                                            const target = e.target;\n                                            target.src = \"https://via.placeholder.com/400x400/DC143C/FFFFFF?text=\".concat(encodeURIComponent(mask.name));\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '1rem',\n                                            marginTop: '1rem',\n                                            justifyContent: 'center'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowAnimation(!showAnimation),\n                                                style: {\n                                                    backgroundColor: colors.primary,\n                                                    color: 'white',\n                                                    padding: '0.75rem 1.5rem',\n                                                    borderRadius: '0.5rem',\n                                                    border: 'none',\n                                                    cursor: 'pointer',\n                                                    fontSize: '1rem'\n                                                },\n                                                children: showAnimation ? '隐藏绘制过程' : '观看绘制过程'\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleFavorite(mask.id),\n                                                style: {\n                                                    backgroundColor: isFavorite(mask.id) ? colors.primary : 'transparent',\n                                                    color: isFavorite(mask.id) ? 'white' : colors.primary,\n                                                    border: \"2px solid \".concat(colors.primary),\n                                                    padding: '0.75rem 1.5rem',\n                                                    borderRadius: '0.5rem',\n                                                    cursor: 'pointer',\n                                                    fontSize: '1rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '0.5rem'\n                                                },\n                                                children: isFavorite(mask.id) ? '❤️ 已收藏' : '🤍 收藏'\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: colors.backgroundSecondary,\n                                        borderRadius: '12px',\n                                        padding: '2rem',\n                                        marginBottom: '2rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            style: {\n                                                fontSize: '1.5rem',\n                                                fontWeight: 'bold',\n                                                marginBottom: '1rem',\n                                                color: colors.textPrimary\n                                            },\n                                            children: \"基本信息\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                marginBottom: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"角色：\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" \",\n                                                mask.character\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                marginBottom: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"行当：\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" \",\n                                                mask.roleCategory\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                marginBottom: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"色彩分类：\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" \",\n                                                mask.colorCategory\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        mask.mainColors && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                marginBottom: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"主要色彩：\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        gap: '0.5rem',\n                                                        marginTop: '0.5rem'\n                                                    },\n                                                    children: mask.mainColors.map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                width: '30px',\n                                                                height: '30px',\n                                                                borderRadius: '50%',\n                                                                backgroundColor: color,\n                                                                border: '2px solid rgba(0,0,0,0.1)'\n                                                            },\n                                                            title: color\n                                                        }, index, false, {\n                                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: colors.backgroundSecondary,\n                                        borderRadius: '12px',\n                                        padding: '2rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            style: {\n                                                fontSize: '1.5rem',\n                                                fontWeight: 'bold',\n                                                marginBottom: '1rem',\n                                                color: colors.textPrimary\n                                            },\n                                            children: \"文化背景\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                lineHeight: '1.6'\n                                            },\n                                            children: typeof mask.culturalBackground === 'string' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: mask.culturalBackground\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '1rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"历史起源：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                style: {\n                                                                    marginTop: '0.5rem'\n                                                                },\n                                                                children: mask.culturalBackground.origin\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '1rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"性格特点：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                style: {\n                                                                    marginTop: '0.5rem'\n                                                                },\n                                                                children: mask.culturalBackground.personality\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '1rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"象征意义：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                style: {\n                                                                    marginTop: '0.5rem'\n                                                                },\n                                                                children: mask.culturalBackground.symbolism\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, this);\n}\n_s(MaskDetailPage, \"1OydzzeE+F7DM5O1RwNKSjUnD/s=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__.useTheme,\n        _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useFavorites,\n        _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useRecentlyViewed\n    ];\n});\n_c = MaskDetailPage;\nvar _c;\n$RefreshReg$(_c, \"MaskDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/mask/[id]/page.tsx\n"));

/***/ })

});