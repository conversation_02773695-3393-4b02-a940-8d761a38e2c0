"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_masks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/masks */ \"(app-pages-browser)/./src/data/masks.ts\");\n/* harmony import */ var _services_maskService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/maskService */ \"(app-pages-browser)/./src/services/maskService.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers/ThemeProvider */ \"(app-pages-browser)/./src/components/providers/ThemeProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Home() {\n    var _selectedMask_images, _selectedMask_culturalBackground;\n    _s();\n    const [masks, setMasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_data_masks__WEBPACK_IMPORTED_MODULE_2__.operaMasks);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedMask, setSelectedMask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { colors } = (0,_components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    // 加载脸谱数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const loadMasks = {\n                \"Home.useEffect.loadMasks\": async ()=>{\n                    if (!(0,_lib_supabase__WEBPACK_IMPORTED_MODULE_4__.isSupabaseConfigured)()) {\n                        console.log('Using static mask data');\n                        return;\n                    }\n                    setLoading(true);\n                    try {\n                        const maskData = await _services_maskService__WEBPACK_IMPORTED_MODULE_3__.MaskService.getAllApprovedMasks();\n                        setMasks(maskData);\n                    } catch (error) {\n                        console.error('Error loading masks:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"Home.useEffect.loadMasks\"];\n            loadMasks();\n        }\n    }[\"Home.useEffect\"], []);\n    const handleMaskClick = (mask)=>{\n        setSelectedMask(mask);\n    };\n    const closeModal = ()=>{\n        setSelectedMask(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            backgroundColor: colors.background,\n            color: colors.text,\n            padding: '2rem'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center',\n                    marginBottom: '3rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            fontSize: '2.5rem',\n                            fontWeight: 'bold',\n                            marginBottom: '1rem',\n                            background: 'linear-gradient(135deg, #DC2626, #B91C1C)',\n                            WebkitBackgroundClip: 'text',\n                            WebkitTextFillColor: 'transparent',\n                            fontFamily: '\"Ma Shan Zheng\", cursive'\n                        },\n                        children: \"\\uD83C\\uDFAD 京剧脸谱文化展示\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            fontSize: '1.125rem',\n                            color: colors.textSecondary,\n                            maxWidth: '600px',\n                            margin: '0 auto'\n                        },\n                        children: \"探索中国传统京剧脸谱艺术的魅力，了解每个角色背后的文化内涵\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center',\n                    padding: '2rem',\n                    color: colors.textSecondary\n                },\n                children: \"正在加载脸谱数据...\"\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                    gap: '2rem',\n                    maxWidth: '1200px',\n                    margin: '0 auto'\n                },\n                children: masks.map((mask)=>{\n                    var _mask_images, _mask_images1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: ()=>handleMaskClick(mask),\n                        style: {\n                            backgroundColor: colors.cardBackground,\n                            borderRadius: '12px',\n                            padding: '1.5rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.3s ease',\n                            border: \"1px solid \".concat(colors.border),\n                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: '100%',\n                                    height: '200px',\n                                    borderRadius: '8px',\n                                    overflow: 'hidden',\n                                    marginBottom: '1rem'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: ((_mask_images = mask.images) === null || _mask_images === void 0 ? void 0 : _mask_images.fullSize) || mask.imageUrl || ((_mask_images1 = mask.images) === null || _mask_images1 === void 0 ? void 0 : _mask_images1.thumbnail),\n                                    alt: mask.name,\n                                    style: {\n                                        width: '100%',\n                                        height: '100%',\n                                        objectFit: 'cover'\n                                    },\n                                    onError: (e)=>{\n                                        // 图片加载失败时的备用处理\n                                        const target = e.target;\n                                        target.src = \"https://via.placeholder.com/300x300/DC143C/FFFFFF?text=\".concat(encodeURIComponent(mask.name));\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    fontSize: '1.25rem',\n                                    fontWeight: 'bold',\n                                    marginBottom: '0.5rem',\n                                    color: colors.textPrimary\n                                },\n                                children: mask.name\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: '0.875rem',\n                                    color: colors.textSecondary,\n                                    marginBottom: '1rem'\n                                },\n                                children: [\n                                    \"角色: \",\n                                    mask.character\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this),\n                            (mask.colorTheme || mask.mainColors) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '0.5rem',\n                                    marginBottom: '1rem'\n                                },\n                                children: (mask.colorTheme || mask.mainColors || []).slice(0, 3).map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: '20px',\n                                            height: '20px',\n                                            borderRadius: '50%',\n                                            backgroundColor: color,\n                                            border: '1px solid rgba(0,0,0,0.1)'\n                                        },\n                                        title: color\n                                    }, index, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 15\n                            }, this),\n                            (mask.personalityTraits || mask.tags) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexWrap: 'wrap',\n                                    gap: '0.5rem'\n                                },\n                                children: (mask.personalityTraits || mask.tags || []).slice(0, 3).map((trait, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: '0.75rem',\n                                            padding: '0.25rem 0.5rem',\n                                            backgroundColor: colors.primary + '20',\n                                            color: colors.primary,\n                                            borderRadius: '12px'\n                                        },\n                                        children: trait\n                                    }, index, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, mask.id, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            selectedMask && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    zIndex: 1000,\n                    padding: '1rem'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: colors.background,\n                        borderRadius: '12px',\n                        padding: '2rem',\n                        maxWidth: '600px',\n                        width: '100%',\n                        maxHeight: '80vh',\n                        overflow: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center',\n                                marginBottom: '1.5rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        fontSize: '1.5rem',\n                                        fontWeight: 'bold',\n                                        color: colors.text\n                                    },\n                                    children: selectedMask.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: closeModal,\n                                    style: {\n                                        background: 'none',\n                                        border: 'none',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer',\n                                        color: colors.textSecondary\n                                    },\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: selectedMask.imageUrl || ((_selectedMask_images = selectedMask.images) === null || _selectedMask_images === void 0 ? void 0 : _selectedMask_images.fullSize),\n                            alt: selectedMask.name,\n                            style: {\n                                width: '100%',\n                                height: '300px',\n                                objectFit: 'cover',\n                                borderRadius: '8px',\n                                marginBottom: '1.5rem'\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '0.875rem',\n                                color: colors.textSecondary,\n                                lineHeight: '1.6'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"角色:\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 18\n                                        }, this),\n                                        \" \",\n                                        selectedMask.character\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"文化背景:\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 18\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        marginLeft: '1rem',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: typeof selectedMask.culturalBackground === 'string' ? selectedMask.culturalBackground : ((_selectedMask_culturalBackground = selectedMask.culturalBackground) === null || _selectedMask_culturalBackground === void 0 ? void 0 : _selectedMask_culturalBackground.origin) || '传统京剧脸谱艺术'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, this),\n                                (selectedMask.personalityTraits || selectedMask.tags) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"特征:\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 22\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                flexWrap: 'wrap',\n                                                gap: '0.5rem',\n                                                marginLeft: '1rem'\n                                            },\n                                            children: (selectedMask.personalityTraits || selectedMask.tags || []).map((trait, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.75rem',\n                                                        padding: '0.25rem 0.5rem',\n                                                        backgroundColor: colors.primary + '20',\n                                                        color: colors.primary,\n                                                        borderRadius: '12px'\n                                                    },\n                                                    children: trait\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"Vfoqi3Tz0LvR925DWBUyKoanCxA=\", false, function() {\n    return [\n        _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_5__.useTheme\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});