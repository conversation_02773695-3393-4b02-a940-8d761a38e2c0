@echo off
echo 🎭 京剧脸谱文化展示平台 - 网络访问配置
echo =============================================

REM 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ⚠️  此脚本需要管理员权限来配置防火墙
    echo 请右键点击此文件，选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ 管理员权限确认
echo.

echo 📋 当前网络配置信息:
echo.

REM 显示网络配置
echo 🌐 IP地址信息:
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr "IPv4"') do (
    echo   本地IP: %%a
)

echo.
echo 🔧 配置Windows防火墙...

REM 删除可能存在的旧规则
netsh advfirewall firewall delete rule name="Beijing Opera Masks - Port 3002" >nul 2>&1

REM 添加新的防火墙规则
netsh advfirewall firewall add rule name="Beijing Opera Masks - Port 3002" dir=in action=allow protocol=TCP localport=3002

if %errorlevel% equ 0 (
    echo ✅ 防火墙规则添加成功 - 端口3002已开放
) else (
    echo ❌ 防火墙规则添加失败
)

echo.
echo 🚀 启动应用程序服务器...
echo.

REM 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js
    pause
    exit /b 1
)

echo ✅ Node.js 版本: 
node --version

echo.
echo 📦 检查依赖包...
if not exist "node_modules" (
    echo 安装依赖包...
    npm install
)

echo.
echo 🎭 启动京剧脸谱文化展示平台...
echo.

REM 获取本机IP地址
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr "IPv4" ^| findstr "192.168"') do (
    set LOCAL_IP=%%a
    set LOCAL_IP=!LOCAL_IP: =!
)

echo 📍 访问地址信息:
echo.
echo   本地访问: http://localhost:3002
if defined LOCAL_IP (
    echo   局域网访问: http://%LOCAL_IP%:3002
)
echo.
echo 🌐 获取公网访问地址 (推荐):
echo.
echo   方案1 - Ngrok (推荐):
echo     1. 下载: https://ngrok.com/
echo     2. 注册并获取token
echo     3. 运行: ngrok http 3002
echo     4. 获得: https://xxx.ngrok.io
echo.
echo   方案2 - Cloudflare Tunnel:
echo     1. 下载: cloudflared
echo     2. 运行: cloudflared tunnel --url http://localhost:3002
echo.
echo   方案3 - 路由器端口转发:
echo     1. 登录路由器: http://***********
echo     2. 配置端口转发: 3002 → %LOCAL_IP%:3002
echo     3. 公网访问: http://您的公网IP:3002
echo.

echo 🎉 配置完成！应用程序启动中...
echo.
echo 📱 功能特色:
echo   • 9个真实京剧脸谱角色
echo   • 响应式设计 (手机/平板/电脑)
echo   • 明暗主题切换
echo   • 角色详情和文化介绍
echo   • 模态对话框交互
echo   • 外部图片加载 (d.bmcx.com, baidu.com)
echo.
echo ⚠️  按 Ctrl+C 停止服务器
echo.

REM 启动服务器，绑定到所有网络接口
npx next dev -H 0.0.0.0 -p 3002
