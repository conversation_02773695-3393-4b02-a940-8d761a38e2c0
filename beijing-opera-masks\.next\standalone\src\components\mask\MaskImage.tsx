'use client';

import React, { useState, useEffect } from 'react';
import { OperaMask } from '@/types/mask';
import { MaskImageGenerator, maskConfigs } from '@/utils/maskImageGenerator';

interface MaskImageProps {
  mask: OperaMask;
  width?: number;
  height?: number;
  className?: string;
}

// 真实脸谱图片URL映射
// 使用一些可访问的京剧脸谱图片资源
const maskImageUrls: Record<string, string> = {
  'guanyu': 'https://via.placeholder.com/300x300/DC2626/FFFFFF?text=关羽', // 关羽红脸
  'caocao': 'https://via.placeholder.com/300x300/FFFFFF/000000?text=曹操', // 曹操白脸
  'zhangfei': 'https://via.placeholder.com/300x300/000000/FFFFFF?text=张飞', // 张飞黑脸
  'huangzhong': 'https://via.placeholder.com/300x300/FFD700/000000?text=黄忠', // 黄忠黄脸
  'diaochan': 'https://via.placeholder.com/300x300/FFC0CB/000000?text=貂蝉', // 貂蝉
  'baozhen': 'https://via.placeholder.com/300x300/000000/FFFFFF?text=包拯', // 包拯黑脸
  'douerdun': 'https://via.placeholder.com/300x300/1E40AF/FFFFFF?text=窦尔敦', // 窦尔敦蓝脸
  'dianwei': 'https://via.placeholder.com/300x300/7C2D12/FFFFFF?text=典韦', // 典韦
  'likui': 'https://via.placeholder.com/300x300/1F2937/FFFFFF?text=李逵', // 李逵
  'sunwukong': 'https://via.placeholder.com/300x300/F59E0B/000000?text=孙悟空', // 孙悟空
  'zhubaijie': 'https://via.placeholder.com/300x300/EC4899/FFFFFF?text=猪八戒', // 猪八戒
  'baigu': 'https://via.placeholder.com/300x300/F3F4F6/000000?text=白骨精', // 白骨精
  'huajiangjun': 'https://via.placeholder.com/300x300/7C3AED/FFFFFF?text=花脸将军', // 花脸将军
  'qingyi': 'https://via.placeholder.com/300x300/10B981/FFFFFF?text=青衣', // 青衣花旦
  'xiaosheng': 'https://via.placeholder.com/300x300/3B82F6/FFFFFF?text=小生' // 小生角色
};

export function MaskImage({ mask, width = 300, height = 300, className }: MaskImageProps) {
  const [canvasImage, setCanvasImage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 统一使用Canvas生成脸谱图片，确保风格一致
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const config = maskConfigs[mask.id];
      if (config) {
        const generator = new MaskImageGenerator(width, height);
        const generatedImage = generator.generateMaskImage(config);
        setCanvasImage(generatedImage);
        setIsLoading(false);
      } else {
        // 如果没有配置，使用默认配置
        const defaultConfig = {
          id: mask.id,
          name: mask.name,
          character: mask.character,
          mainColors: mask.mainColors,
          faceShape: 'oval' as const,
          eyeStyle: 'normal' as const,
          decorations: ['forehead-mark']
        };
        const generator = new MaskImageGenerator(width, height);
        const generatedImage = generator.generateMaskImage(defaultConfig);
        setCanvasImage(generatedImage);
        setIsLoading(false);
      }
    }
  }, [mask.id, mask.name, mask.character, mask.mainColors, width, height]);

  // 如果正在加载，显示加载状态
  if (isLoading || !canvasImage) {
    return renderLoadingState();
  }

  // 渲染Canvas生成的图片
  return renderCanvasImage();

  // 渲染加载状态
  function renderLoadingState() {
    return (
      <div
        className={className}
        style={{
          width,
          height,
          position: 'relative',
          overflow: 'hidden',
          borderRadius: '0.5rem',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#F3F4F6',
          color: '#9CA3AF'
        }}
      >
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🎭</div>
          <div style={{ fontSize: '0.875rem' }}>生成中...</div>
        </div>
      </div>
    );
  }

  // Canvas生成的图片渲染
  function renderCanvasImage() {
    return (
      <div
        className={className}
        style={{
          width,
          height,
          position: 'relative',
          overflow: 'hidden',
          borderRadius: '0.5rem',
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
          transition: 'transform 0.3s ease, box-shadow 0.3s ease'
        }}
      >
        <img
          src={canvasImage!}
          alt={`${mask.name} - ${mask.character}`}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            display: 'block'
          }}
        />

        {/* 统一的悬停效果 */}
        <div
          style={{
            position: 'absolute',
            inset: 0,
            background: 'transparent',
            transition: 'background 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = 'rgba(0, 0, 0, 0.1)';
            e.currentTarget.parentElement!.style.transform = 'scale(1.02)';
            e.currentTarget.parentElement!.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.2)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'transparent';
            e.currentTarget.parentElement!.style.transform = 'scale(1)';
            e.currentTarget.parentElement!.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
          }}
        />
      </div>
    );
  }

  // SVG后备方案
  function generateFallbackSVG() {
    const baseProps = {
      width,
      height,
      viewBox: "0 0 300 300",
      className,
      style: { borderRadius: '0.5rem' }
    };

    // 简化的SVG后备方案
    return (
      <svg {...baseProps}>
        <defs>
          <radialGradient id={`gradient-${mask.id}`} cx="50%" cy="40%" r="60%">
            <stop offset="0%" stopColor={mask.mainColors[0]} />
            <stop offset="100%" stopColor={mask.mainColors[1] || mask.mainColors[0]} />
          </radialGradient>
        </defs>

        {/* 脸部轮廓 */}
        <ellipse
          cx="150"
          cy="150"
          rx="120"
          ry="140"
          fill={`url(#gradient-${mask.id})`}
          stroke="#333"
          strokeWidth="3"
        />

        {/* 基础五官 */}
        <path d="M 90 110 Q 120 90 150 110" stroke="#000" strokeWidth="4" fill="none"/>
        <path d="M 150 110 Q 180 90 210 110" stroke="#000" strokeWidth="4" fill="none"/>
        <ellipse cx="120" cy="140" rx="15" ry="10" fill="white" stroke="#000" strokeWidth="2"/>
        <ellipse cx="180" cy="140" rx="15" ry="10" fill="white" stroke="#000" strokeWidth="2"/>
        <circle cx="120" cy="140" r="6" fill="#000"/>
        <circle cx="180" cy="140" r="6" fill="#000"/>
        <path d="M 150 160 L 150 180" stroke="#000" strokeWidth="3"/>
        <ellipse cx="150" cy="200" rx="10" ry="6" fill="#000"/>

        {/* 角色名称 */}
        <text
          x="150"
          y="260"
          textAnchor="middle"
          fill="white"
          fontSize="24"
          fontWeight="bold"
          fontFamily="serif"
          stroke="#000"
          strokeWidth="1"
        >
          {mask.character}
        </text>
      </svg>
    );
  }
}
