#!/usr/bin/env node

/**
 * Beijing Opera Masks Platform - Deployment Verification Script
 * Target Domain: milei7.dpdns.org
 */

const https = require('https');
const http = require('http');

const config = {
  targetDomain: 'milei7.dpdns.org',
  testUrls: [
    'https://milei7.dpdns.org',
    'https://milei7.dpdns.org/mask/guanyu',
    'https://milei7.dpdns.org/mask/baogong',
    'https://milei7.dpdns.org/mask/caocao',
    'https://milei7.dpdns.org/sitemap.xml',
    'https://milei7.dpdns.org/robots.txt'
  ],
  imageUrls: [
    'https://d.bmcx.com/lianpu/d/0072.jpg', // 关羽
    'https://d.bmcx.com/lianpu/d/0018.jpg', // 包拯
    'https://d.bmcx.com/lianpu/d/0028.jpg', // 曹操
    'https://d.bmcx.com/lianpu/d/0324.jpg', // 张飞
    'https://d.bmcx.com/lianpu/d/0056.jpg', // 窦尔敦
    'https://d.bmcx.com/lianpu/d/0117.jpg', // 蒋干
    'https://d.bmcx.com/lianpu/d/0234.jpg', // 孙悟空
    'https://img1.baidu.com/it/u=348325659,1868481632&fm=253&fmt=auto&app=138&f=JPEG?w=351&h=441', // 杨七郎
    'https://img1.baidu.com/it/u=93431987,3113680563&fm=253&fmt=auto&app=138&f=JPEG?w=412&h=502' // 刘备
  ]
};

console.log('🎭 Beijing Opera Masks Platform - Deployment Verification');
console.log('========================================================');
console.log(`Target Domain: ${config.targetDomain}`);
console.log('');

function makeRequest(url) {
  return new Promise((resolve) => {
    const client = url.startsWith('https:') ? https : http;
    const startTime = Date.now();
    
    const req = client.get(url, (res) => {
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      resolve({
        url,
        statusCode: res.statusCode,
        responseTime,
        headers: res.headers,
        success: res.statusCode >= 200 && res.statusCode < 400
      });
    });
    
    req.on('error', (error) => {
      resolve({
        url,
        statusCode: 0,
        responseTime: 0,
        error: error.message,
        success: false
      });
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      resolve({
        url,
        statusCode: 0,
        responseTime: 0,
        error: 'Timeout',
        success: false
      });
    });
  });
}

async function testUrls(urls, title) {
  console.log(`🔍 Testing ${title}:`);
  console.log('─'.repeat(50));
  
  let successCount = 0;
  
  for (const url of urls) {
    const result = await makeRequest(url);
    
    if (result.success) {
      console.log(`✅ ${result.statusCode} - ${url} (${result.responseTime}ms)`);
      successCount++;
    } else {
      console.log(`❌ ${result.statusCode || 'ERR'} - ${url} ${result.error ? `(${result.error})` : ''}`);
    }
  }
  
  console.log('');
  console.log(`📊 Results: ${successCount}/${urls.length} successful`);
  console.log('');
  
  return successCount === urls.length;
}

async function checkDNS() {
  console.log('🌐 DNS Resolution Check:');
  console.log('─'.repeat(50));
  
  try {
    const dns = require('dns').promises;
    const addresses = await dns.resolve4(config.targetDomain);
    console.log(`✅ DNS resolved: ${config.targetDomain} → ${addresses.join(', ')}`);
    return true;
  } catch (error) {
    console.log(`❌ DNS resolution failed: ${error.message}`);
    return false;
  }
}

async function main() {
  try {
    // Check DNS resolution
    const dnsOk = await checkDNS();
    console.log('');
    
    // Test main application URLs
    const appOk = await testUrls(config.testUrls, 'Application URLs');
    
    // Test external image URLs
    const imagesOk = await testUrls(config.imageUrls, 'External Mask Images');
    
    // Summary
    console.log('📋 Deployment Verification Summary:');
    console.log('═'.repeat(50));
    console.log(`DNS Resolution: ${dnsOk ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Application URLs: ${appOk ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`External Images: ${imagesOk ? '✅ PASS' : '❌ FAIL'}`);
    console.log('');
    
    if (dnsOk && appOk && imagesOk) {
      console.log('🎉 All tests passed! Deployment is successful.');
      console.log('');
      console.log('✅ Features verified:');
      console.log('   • Domain resolution working');
      console.log('   • Application pages loading');
      console.log('   • Character detail pages accessible');
      console.log('   • SEO files (sitemap, robots) available');
      console.log('   • All 9 real mask images accessible');
      console.log('');
      console.log('🎭 Ready for production use!');
      process.exit(0);
    } else {
      console.log('⚠️  Some tests failed. Please check the issues above.');
      console.log('');
      console.log('🔧 Troubleshooting tips:');
      if (!dnsOk) console.log('   • Check DNS records configuration');
      if (!appOk) console.log('   • Verify server deployment and SSL certificate');
      if (!imagesOk) console.log('   • Check external image source availability');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    process.exit(1);
  }
}

// Run verification
main();
