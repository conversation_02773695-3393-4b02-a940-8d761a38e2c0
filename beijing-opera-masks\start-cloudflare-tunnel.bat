@echo off
echo 🌐 京剧脸谱文化展示平台 - Cloudflare Tunnel 启动
echo ================================================

echo.
echo 📋 检查系统环境...

REM 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js
    pause
    exit /b 1
)

echo ✅ Node.js 已安装: 
node --version

REM 检查cloudflared
cloudflared --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ⚠️  Cloudflared 未找到，正在下载...
    echo.
    
    REM 下载cloudflared
    powershell -Command "try { Invoke-WebRequest -Uri 'https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-windows-amd64.exe' -OutFile 'cloudflared.exe'; Write-Host '✅ Cloudflared 下载完成' } catch { Write-Host '❌ 下载失败，请手动下载'; exit 1 }"
    
    if %errorlevel% neq 0 (
        echo.
        echo 📥 请手动下载 Cloudflared:
        echo 1. 访问: https://github.com/cloudflare/cloudflared/releases
        echo 2. 下载: cloudflared-windows-amd64.exe
        echo 3. 重命名为: cloudflared.exe
        echo 4. 放置在此目录中
        echo.
        pause
        exit /b 1
    )
) else (
    echo ✅ Cloudflared 已安装: 
    cloudflared --version
)

echo.
echo 🎭 启动京剧脸谱应用程序...

REM 检查应用程序是否已在运行
netstat -an | findstr ":3002" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 应用程序已在端口3002运行
) else (
    echo 📦 启动应用程序...
    start "Beijing Opera Masks App" cmd /c "npx next dev -H 0.0.0.0 -p 3002"
    
    REM 等待应用程序启动
    echo 等待应用程序启动...
    timeout /t 10 /nobreak >nul
    
    REM 再次检查
    netstat -an | findstr ":3002" >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ 应用程序启动失败，请检查错误信息
        pause
        exit /b 1
    )
    echo ✅ 应用程序启动成功
)

echo.
echo 🌐 启动 Cloudflare Tunnel...
echo.

echo 📍 选择启动方式:
echo.
echo   1. 快速启动 (临时域名，立即可用)
echo   2. 命名隧道 (需要Cloudflare账户，永久域名)
echo.

set /p choice="请选择 (1 或 2): "

if "%choice%"=="1" (
    echo.
    echo 🚀 启动快速隧道...
    echo.
    echo 📝 注意事项:
    echo   • 会生成临时的 .trycloudflare.com 域名
    echo   • 无需注册账户，立即可用
    echo   • 隧道关闭后域名失效
    echo   • 适合测试和演示使用
    echo.
    echo ⏳ 正在建立隧道连接...
    echo.
    
    cloudflared tunnel --url http://localhost:3002
    
) else if "%choice%"=="2" (
    echo.
    echo 🔐 启动命名隧道...
    echo.
    echo 📝 首次使用需要:
    echo   1. 注册 Cloudflare 账户: https://dash.cloudflare.com/sign-up
    echo   2. 运行: cloudflared tunnel login
    echo   3. 创建隧道: cloudflared tunnel create beijing-opera-masks
    echo   4. 配置隧道 (参考 CLOUDFLARE_TUNNEL_SETUP.md)
    echo.
    
    REM 检查是否已登录
    if not exist "%USERPROFILE%\.cloudflared\cert.pem" (
        echo ⚠️  未检测到 Cloudflare 认证，请先登录:
        echo.
        echo cloudflare tunnel login
        echo.
        pause
        exit /b 1
    )
    
    echo 🔍 检查现有隧道...
    cloudflared tunnel list
    
    echo.
    echo 🚀 启动隧道 beijing-opera-masks...
    cloudflared tunnel run beijing-opera-masks
    
) else (
    echo ❌ 无效选择，请重新运行脚本
    pause
    exit /b 1
)

echo.
echo 🎉 隧道已启动！
echo.
echo 📱 功能特色:
echo   • 9个真实京剧脸谱角色
echo   • 响应式设计 (手机/平板/电脑)
echo   • 明暗主题切换
echo   • 角色详情和文化介绍
echo   • 模态对话框交互
echo   • 全球CDN加速访问
echo.
echo ⚠️  按 Ctrl+C 停止隧道
echo.

pause
