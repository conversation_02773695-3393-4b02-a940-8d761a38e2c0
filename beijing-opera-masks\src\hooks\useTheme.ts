'use client';

import { useState, useEffect, createContext, useContext } from 'react';

export type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// 主题颜色配置
export const themeColors = {
  light: {
    // 背景色
    background: '#FFFFFF',
    backgroundSecondary: '#F9FAFB',
    backgroundTertiary: '#F3F4F6',
    
    // 文字色
    textPrimary: '#1F2937',
    textSecondary: '#374151',
    textTertiary: '#6B7280',
    textMuted: '#9CA3AF',
    
    // 边框色
    border: '#E5E7EB',
    borderSecondary: '#D1D5DB',
    
    // 品牌色
    primary: '#B91C1C',
    primaryHover: '#991B1B',
    secondary: '#F59E0B',
    secondaryHover: '#D97706',
    
    // 状态色
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    
    // 阴影
    shadow: 'rgba(0, 0, 0, 0.1)',
    shadowHover: 'rgba(0, 0, 0, 0.2)',
  },
  dark: {
    // 背景色 - 使用深色但保持中国传统色彩感
    background: '#0F1419', // 深墨色
    backgroundSecondary: '#1A1F2E', // 深蓝墨色
    backgroundTertiary: '#252A3A', // 中等深色
    
    // 文字色 - 保持良好对比度
    textPrimary: '#F9FAFB',
    textSecondary: '#E5E7EB',
    textTertiary: '#D1D5DB',
    textMuted: '#9CA3AF',
    
    // 边框色
    border: '#374151',
    borderSecondary: '#4B5563',
    
    // 品牌色 - 在深色背景下调整亮度
    primary: '#DC2626', // 更亮的红色
    primaryHover: '#EF4444',
    secondary: '#FBBF24', // 更亮的金色
    secondaryHover: '#FCD34D',
    
    // 状态色
    success: '#34D399',
    warning: '#FBBF24',
    error: '#F87171',
    
    // 阴影
    shadow: 'rgba(0, 0, 0, 0.3)',
    shadowHover: 'rgba(0, 0, 0, 0.5)',
  }
};

// 主题样式生成器
export function getThemeStyles(theme: Theme) {
  const colors = themeColors[theme];
  
  return {
    // 全局样式
    global: {
      backgroundColor: colors.background,
      color: colors.textPrimary,
      transition: 'background-color 0.3s ease, color 0.3s ease'
    },
    
    // 卡片样式
    card: {
      backgroundColor: colors.backgroundSecondary,
      border: `1px solid ${colors.border}`,
      boxShadow: `0 4px 6px -1px ${colors.shadow}`,
      transition: 'all 0.3s ease'
    },
    
    // 按钮样式
    button: {
      primary: {
        backgroundColor: colors.primary,
        color: colors.background,
        border: `2px solid ${colors.primary}`,
        ':hover': {
          backgroundColor: colors.primaryHover,
          borderColor: colors.primaryHover
        }
      },
      secondary: {
        backgroundColor: 'transparent',
        color: colors.primary,
        border: `2px solid ${colors.primary}`,
        ':hover': {
          backgroundColor: colors.primary,
          color: colors.background
        }
      }
    },
    
    // 输入框样式
    input: {
      backgroundColor: colors.backgroundTertiary,
      border: `1px solid ${colors.border}`,
      color: colors.textPrimary,
      ':focus': {
        borderColor: colors.primary,
        boxShadow: `0 0 0 3px ${colors.primary}20`
      }
    },
    
    // 导航样式
    navigation: {
      backgroundColor: colors.backgroundSecondary,
      borderBottom: `2px solid ${colors.secondary}`,
      boxShadow: `0 4px 6px -1px ${colors.shadow}`
    }
  };
}

// 自定义Hook
export function useThemeManager() {
  const [theme, setThemeState] = useState<Theme>('light');
  const [isClient, setIsClient] = useState(false);

  // 标记客户端已挂载
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 从localStorage加载主题
  useEffect(() => {
    if (isClient) {
      const savedTheme = localStorage.getItem('beijing-opera-theme') as Theme;
      if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {
        setThemeState(savedTheme);
      } else {
        // 检测系统主题偏好
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        setThemeState(prefersDark ? 'dark' : 'light');
      }
    }
  }, [isClient]);
  
  // 保存主题到localStorage
  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
    if (isClient) {
      localStorage.setItem('beijing-opera-theme', newTheme);
      // 更新HTML根元素的data-theme属性
      document.documentElement.setAttribute('data-theme', newTheme);
    }
  };
  
  // 切换主题
  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };
  
  // 应用主题到HTML根元素
  useEffect(() => {
    if (isClient) {
      document.documentElement.setAttribute('data-theme', theme);
      // 更新body样式
      const styles = getThemeStyles(theme);
      Object.assign(document.body.style, styles.global);
    }
  }, [theme, isClient]);
  
  return {
    theme,
    setTheme,
    toggleTheme,
    colors: themeColors[theme],
    styles: getThemeStyles(theme)
  };
}
