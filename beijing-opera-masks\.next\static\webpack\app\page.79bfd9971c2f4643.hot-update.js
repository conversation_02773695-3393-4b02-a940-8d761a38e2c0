"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _data_masks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/masks */ \"(app-pages-browser)/./src/data/masks.ts\");\n/* harmony import */ var _services_maskService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/maskService */ \"(app-pages-browser)/./src/services/maskService.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/providers/ThemeProvider */ \"(app-pages-browser)/./src/components/providers/ThemeProvider.tsx\");\n/* harmony import */ var _components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/navigation/SimpleNavbar */ \"(app-pages-browser)/./src/components/navigation/SimpleNavbar.tsx\");\n/* harmony import */ var _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useLocalStorage */ \"(app-pages-browser)/./src/hooks/useLocalStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [masks, setMasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_data_masks__WEBPACK_IMPORTED_MODULE_3__.operaMasks);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedRole, setSelectedRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [showFavoritesOnly, setShowFavoritesOnly] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { colors } = (0,_components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const { favorites, toggleFavorite, isFavorite } = (0,_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useFavorites)();\n    const { recentlyViewed } = (0,_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useRecentlyViewed)();\n    // 加载脸谱数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const loadMasks = {\n                \"Home.useEffect.loadMasks\": async ()=>{\n                    if (!(0,_lib_supabase__WEBPACK_IMPORTED_MODULE_5__.isSupabaseConfigured)()) {\n                        console.log('Using static mask data');\n                        return;\n                    }\n                    setLoading(true);\n                    try {\n                        const maskData = await _services_maskService__WEBPACK_IMPORTED_MODULE_4__.MaskService.getAllApprovedMasks();\n                        setMasks(maskData);\n                    } catch (error) {\n                        console.error('Error loading masks:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"Home.useEffect.loadMasks\"];\n            loadMasks();\n        }\n    }[\"Home.useEffect\"], []);\n    // 筛选脸谱\n    const filteredMasks = masks.filter((mask)=>{\n        // 搜索筛选\n        if (searchTerm) {\n            const searchLower = searchTerm.toLowerCase();\n            const matchesName = mask.name.toLowerCase().includes(searchLower);\n            const matchesCharacter = mask.character.toLowerCase().includes(searchLower);\n            if (!matchesName && !matchesCharacter) return false;\n        }\n        // 角色筛选\n        if (selectedRole !== 'all') {\n            if (mask.roleCategory !== selectedRole) return false;\n        }\n        // 收藏筛选\n        if (showFavoritesOnly) {\n            if (!isFavorite(mask.id)) return false;\n        }\n        return true;\n    });\n    const handleMaskClick = (mask)=>{\n        // 导航到详情页面\n        router.push(\"/mask/\".concat(mask.id));\n    };\n    const handleFavoriteClick = (e, maskId)=>{\n        e.stopPropagation(); // 防止触发卡片点击\n        toggleFavorite(maskId);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            backgroundColor: colors.background,\n            color: colors.textPrimary\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__.SimpleNavbar, {}, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '2rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            marginBottom: '3rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontSize: '2.5rem',\n                                    fontWeight: 'bold',\n                                    marginBottom: '1rem',\n                                    background: 'linear-gradient(135deg, #DC2626, #B91C1C)',\n                                    WebkitBackgroundClip: 'text',\n                                    WebkitTextFillColor: 'transparent',\n                                    fontFamily: '\"Ma Shan Zheng\", cursive'\n                                },\n                                children: \"\\uD83C\\uDFAD 京剧脸谱文化展示\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: '1.125rem',\n                                    color: colors.textSecondary,\n                                    maxWidth: '600px',\n                                    margin: '0 auto'\n                                },\n                                children: \"探索中国传统京剧脸谱艺术的魅力，了解每个角色背后的文化内涵\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            maxWidth: '1200px',\n                            margin: '0 auto 3rem auto',\n                            padding: '0 2rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '1rem',\n                                    flexWrap: 'wrap',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    marginBottom: '2rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"搜索脸谱名称或角色...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        style: {\n                                            padding: '0.75rem 1rem',\n                                            borderRadius: '0.5rem',\n                                            border: \"1px solid \".concat(colors.border),\n                                            backgroundColor: colors.backgroundSecondary,\n                                            color: colors.textPrimary,\n                                            fontSize: '1rem',\n                                            minWidth: '250px'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedRole,\n                                        onChange: (e)=>setSelectedRole(e.target.value),\n                                        style: {\n                                            padding: '0.75rem 1rem',\n                                            borderRadius: '0.5rem',\n                                            border: \"1px solid \".concat(colors.border),\n                                            backgroundColor: colors.backgroundSecondary,\n                                            color: colors.textPrimary,\n                                            fontSize: '1rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"所有角色\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"生\",\n                                                children: \"生角\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"旦\",\n                                                children: \"旦角\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"净\",\n                                                children: \"净角\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"丑\",\n                                                children: \"丑角\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.5rem',\n                                            cursor: 'pointer',\n                                            fontSize: '1rem',\n                                            color: colors.textPrimary\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: showFavoritesOnly,\n                                                onChange: (e)=>setShowFavoritesOnly(e.target.checked),\n                                                style: {\n                                                    width: '18px',\n                                                    height: '18px',\n                                                    cursor: 'pointer'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 13\n                                            }, this),\n                                            \"仅显示收藏 (\",\n                                            favorites.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center',\n                                    color: colors.textSecondary,\n                                    marginBottom: '1rem'\n                                },\n                                children: [\n                                    \"找到 \",\n                                    filteredMasks.length,\n                                    \" 个脸谱\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 9\n                            }, this),\n                            recentlyViewed.length > 0 && !showFavoritesOnly && !searchTerm && selectedRole === 'all' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: colors.backgroundSecondary,\n                                    borderRadius: '12px',\n                                    padding: '1.5rem',\n                                    marginBottom: '2rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            fontSize: '1.125rem',\n                                            fontWeight: 'bold',\n                                            color: colors.textPrimary,\n                                            marginBottom: '1rem',\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.5rem'\n                                        },\n                                        children: \"\\uD83D\\uDD52 最近浏览\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '1rem',\n                                            overflowX: 'auto',\n                                            paddingBottom: '0.5rem'\n                                        },\n                                        children: recentlyViewed.slice(0, 5).map((maskId)=>{\n                                            var _recentMask_images, _recentMask_images1;\n                                            const recentMask = masks.find((m)=>m.id === maskId);\n                                            if (!recentMask) return null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                onClick: ()=>handleMaskClick(recentMask),\n                                                style: {\n                                                    minWidth: '120px',\n                                                    cursor: 'pointer',\n                                                    textAlign: 'center',\n                                                    transition: 'transform 0.2s ease'\n                                                },\n                                                onMouseEnter: (e)=>{\n                                                    e.currentTarget.style.transform = 'scale(1.05)';\n                                                },\n                                                onMouseLeave: (e)=>{\n                                                    e.currentTarget.style.transform = 'scale(1)';\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: ((_recentMask_images = recentMask.images) === null || _recentMask_images === void 0 ? void 0 : _recentMask_images.fullSize) || ((_recentMask_images1 = recentMask.images) === null || _recentMask_images1 === void 0 ? void 0 : _recentMask_images1.thumbnail),\n                                                        alt: recentMask.name,\n                                                        style: {\n                                                            width: '80px',\n                                                            height: '80px',\n                                                            borderRadius: '50%',\n                                                            objectFit: 'cover',\n                                                            marginBottom: '0.5rem',\n                                                            border: \"2px solid \".concat(colors.border)\n                                                        },\n                                                        onError: (e)=>{\n                                                            const target = e.target;\n                                                            target.src = \"https://via.placeholder.com/80x80/DC143C/FFFFFF?text=\".concat(encodeURIComponent(recentMask.name.charAt(0)));\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: '0.75rem',\n                                                            color: colors.textSecondary,\n                                                            whiteSpace: 'nowrap',\n                                                            overflow: 'hidden',\n                                                            textOverflow: 'ellipsis'\n                                                        },\n                                                        children: recentMask.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, maskId, true, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 7\n                    }, this),\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            padding: '2rem',\n                            color: colors.textSecondary\n                        },\n                        children: \"正在加载脸谱数据...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'grid',\n                            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                            gap: '2rem',\n                            maxWidth: '1200px',\n                            margin: '0 auto'\n                        },\n                        children: filteredMasks.map((mask)=>{\n                            var _mask_images, _mask_images1;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>handleMaskClick(mask),\n                                style: {\n                                    backgroundColor: colors.backgroundSecondary,\n                                    borderRadius: '12px',\n                                    padding: '1.5rem',\n                                    cursor: 'pointer',\n                                    transition: 'all 0.3s ease',\n                                    border: \"1px solid \".concat(colors.border),\n                                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n                                    position: 'relative'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>handleFavoriteClick(e, mask.id),\n                                        style: {\n                                            position: 'absolute',\n                                            top: '1rem',\n                                            right: '1rem',\n                                            backgroundColor: 'transparent',\n                                            border: 'none',\n                                            fontSize: '1.5rem',\n                                            cursor: 'pointer',\n                                            padding: '0.25rem',\n                                            borderRadius: '50%',\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            justifyContent: 'center',\n                                            transition: 'all 0.2s ease'\n                                        },\n                                        title: isFavorite(mask.id) ? '取消收藏' : '添加收藏',\n                                        children: isFavorite(mask.id) ? '❤️' : '🤍'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: '100%',\n                                            height: '200px',\n                                            borderRadius: '8px',\n                                            overflow: 'hidden',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: ((_mask_images = mask.images) === null || _mask_images === void 0 ? void 0 : _mask_images.fullSize) || mask.imageUrl || ((_mask_images1 = mask.images) === null || _mask_images1 === void 0 ? void 0 : _mask_images1.thumbnail),\n                                            alt: mask.name,\n                                            style: {\n                                                width: '100%',\n                                                height: '100%',\n                                                objectFit: 'cover'\n                                            },\n                                            onError: (e)=>{\n                                                // 图片加载失败时的备用处理\n                                                const target = e.target;\n                                                target.src = \"https://via.placeholder.com/300x300/DC143C/FFFFFF?text=\".concat(encodeURIComponent(mask.name));\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            fontSize: '1.25rem',\n                                            fontWeight: 'bold',\n                                            marginBottom: '0.5rem',\n                                            color: colors.textPrimary\n                                        },\n                                        children: mask.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            fontSize: '0.875rem',\n                                            color: colors.textSecondary,\n                                            marginBottom: '1rem'\n                                        },\n                                        children: [\n                                            \"角色: \",\n                                            mask.character\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 13\n                                    }, this),\n                                    (mask.colorTheme || mask.mainColors) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '0.5rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: (mask.colorTheme || mask.mainColors || []).slice(0, 3).map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: '20px',\n                                                    height: '20px',\n                                                    borderRadius: '50%',\n                                                    backgroundColor: color,\n                                                    border: '1px solid rgba(0,0,0,0.1)'\n                                                },\n                                                title: color\n                                            }, index, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this),\n                                    (mask.personalityTraits || mask.tags) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            flexWrap: 'wrap',\n                                            gap: '0.5rem'\n                                        },\n                                        children: (mask.personalityTraits || mask.tags || []).slice(0, 3).map((trait, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.75rem',\n                                                    padding: '0.25rem 0.5rem',\n                                                    backgroundColor: colors.primary + '20',\n                                                    color: colors.primary,\n                                                    borderRadius: '12px'\n                                                },\n                                                children: trait\n                                            }, index, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, mask.id, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"4D2COSusSC3T/zyaM3JfwzW8TAo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__.useTheme,\n        _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useFavorites,\n        _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useRecentlyViewed\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});