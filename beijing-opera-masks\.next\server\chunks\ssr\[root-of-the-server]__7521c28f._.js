module.exports = {

"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/data/masks.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "masksByColor": ()=>masksByColor,
    "masksByRole": ()=>masksByRole,
    "operaMasks": ()=>operaMasks
});
const operaMasks = [
    {
        id: 'guanyu',
        name: '关羽脸谱',
        character: '关羽',
        roleCategory: '净',
        colorCategory: '红脸',
        mainColors: [
            '#DC143C',
            '#FFD700',
            '#000000'
        ],
        culturalBackground: {
            origin: '三国时期蜀汉名将，被尊为武圣',
            personality: '忠义勇武，刚正不阿，义薄云天',
            symbolism: '忠诚、正义、勇敢的象征',
            historicalPeriod: '东汉末年-三国时期'
        },
        colorMeaning: {
            '红色': '忠勇正义，赤胆忠心',
            '金色': '神圣威严，地位崇高',
            '黑色': '刚毅坚定，不可动摇'
        },
        relatedOperas: [
            {
                name: '单刀会',
                description: '关羽单刀赴会的故事',
                period: '三国'
            },
            {
                name: '华容道',
                description: '关羽义释曹操',
                period: '三国'
            },
            {
                name: '走麦城',
                description: '关羽败走麦城',
                period: '三国'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=关羽',
            fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=关羽脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹红色底色',
                duration: 1000,
                color: '#DC143C'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制浓眉',
                duration: 800,
                color: '#000000'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画眼部轮廓',
                duration: 1200,
                color: '#000000'
            },
            {
                id: 4,
                name: '鼻梁',
                description: '描绘鼻梁线条',
                duration: 600,
                color: '#000000'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加金色装饰',
                duration: 1000,
                color: '#FFD700'
            }
        ],
        difficulty: 'medium',
        popularity: 10,
        tags: [
            '三国',
            '武将',
            '忠义',
            '经典'
        ]
    },
    {
        id: 'baogong',
        name: '包拯脸谱',
        character: '包拯',
        roleCategory: '净',
        colorCategory: '黑脸',
        mainColors: [
            '#000000',
            '#FFFFFF',
            '#FFD700'
        ],
        culturalBackground: {
            origin: '北宋名臣，以清廉公正著称',
            personality: '铁面无私，执法如山，清正廉洁',
            symbolism: '公正执法，清廉为民的象征',
            historicalPeriod: '北宋时期'
        },
        colorMeaning: {
            '黑色': '公正严明，铁面无私',
            '白色': '清廉正直，一尘不染',
            '金色': '威严庄重，地位尊崇'
        },
        relatedOperas: [
            {
                name: '铡美案',
                description: '包拯铡驸马陈世美',
                period: '宋代'
            },
            {
                name: '打龙袍',
                description: '包拯怒斥宋仁宗',
                period: '宋代'
            },
            {
                name: '赤桑镇',
                description: '包拯审案的故事',
                period: '宋代'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/000000/FFFFFF?text=包拯',
            fullSize: 'https://via.placeholder.com/600x600/000000/FFFFFF?text=包拯脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹黑色底色',
                duration: 1000,
                color: '#000000'
            },
            {
                id: 2,
                name: '额头',
                description: '绘制白色月牙',
                duration: 800,
                color: '#FFFFFF'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画眼部轮廓',
                duration: 1000,
                color: '#FFFFFF'
            },
            {
                id: 4,
                name: '鼻翼',
                description: '描绘鼻翼线条',
                duration: 600,
                color: '#FFFFFF'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加金色细节',
                duration: 800,
                color: '#FFD700'
            }
        ],
        difficulty: 'easy',
        popularity: 9,
        tags: [
            '宋代',
            '清官',
            '正义',
            '经典'
        ]
    },
    {
        id: 'caocao',
        name: '曹操脸谱',
        character: '曹操',
        roleCategory: '净',
        colorCategory: '白脸',
        mainColors: [
            '#FFFFFF',
            '#000000',
            '#DC143C'
        ],
        culturalBackground: {
            origin: '东汉末年政治家、军事家、文学家',
            personality: '奸诈狡猾，野心勃勃，但才华横溢',
            symbolism: '奸诈、权谋、复杂人性的象征',
            historicalPeriod: '东汉末年-三国时期'
        },
        colorMeaning: {
            '白色': '奸诈狡猾，阴险毒辣',
            '黑色': '深沉城府，心机深重',
            '红色': '暴戾之气，杀伐果断'
        },
        relatedOperas: [
            {
                name: '捉放曹',
                description: '陈宫捉放曹操',
                period: '三国'
            },
            {
                name: '击鼓骂曹',
                description: '祢衡击鼓骂曹',
                period: '三国'
            },
            {
                name: '群英会',
                description: '曹操与群雄斗智',
                period: '三国'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/FFFFFF/000000?text=曹操',
            fullSize: 'https://via.placeholder.com/600x600/FFFFFF/000000?text=曹操脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹白色底色',
                duration: 1000,
                color: '#FFFFFF'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制黑色浓眉',
                duration: 800,
                color: '#000000'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画眼部轮廓',
                duration: 1200,
                color: '#000000'
            },
            {
                id: 4,
                name: '鼻梁',
                description: '描绘鼻梁阴影',
                duration: 600,
                color: '#000000'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加红色细节',
                duration: 1000,
                color: '#DC143C'
            }
        ],
        difficulty: 'medium',
        popularity: 8,
        tags: [
            '三国',
            '奸雄',
            '复杂',
            '经典'
        ]
    },
    {
        id: 'zhangfei',
        name: '张飞脸谱',
        character: '张飞',
        roleCategory: '净',
        colorCategory: '黑脸',
        mainColors: [
            '#000000',
            '#FFFFFF',
            '#DC143C'
        ],
        culturalBackground: {
            origin: '三国时期蜀汉名将，刘备义弟',
            personality: '勇猛粗犷，嫉恶如仇，忠义豪爽',
            symbolism: '勇猛、正直、豪爽的象征',
            historicalPeriod: '东汉末年-三国时期'
        },
        colorMeaning: {
            '黑色': '刚直勇猛，正气凛然',
            '白色': '纯真豪爽，心无城府',
            '红色': '热血沸腾，义气冲天'
        },
        relatedOperas: [
            {
                name: '长坂坡',
                description: '张飞大战长坂坡',
                period: '三国'
            },
            {
                name: '古城会',
                description: '张飞误会关羽',
                period: '三国'
            },
            {
                name: '芦花荡',
                description: '张飞智取芦花荡',
                period: '三国'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/000000/FFFFFF?text=张飞',
            fullSize: 'https://via.placeholder.com/600x600/000000/FFFFFF?text=张飞脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹黑色底色',
                duration: 1000,
                color: '#000000'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制白色粗眉',
                duration: 800,
                color: '#FFFFFF'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画眼部轮廓',
                duration: 1000,
                color: '#FFFFFF'
            },
            {
                id: 4,
                name: '鼻翼',
                description: '描绘鼻翼线条',
                duration: 600,
                color: '#FFFFFF'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加红色装饰',
                duration: 800,
                color: '#DC143C'
            }
        ],
        difficulty: 'easy',
        popularity: 8,
        tags: [
            '三国',
            '武将',
            '勇猛',
            '豪爽'
        ]
    },
    {
        id: 'doulujin',
        name: '窦尔敦脸谱',
        character: '窦尔敦',
        roleCategory: '净',
        colorCategory: '蓝脸',
        mainColors: [
            '#0066CC',
            '#FFFFFF',
            '#FFD700'
        ],
        culturalBackground: {
            origin: '清代绿林好汉，盗侠传奇人物',
            personality: '刚强勇猛，侠肝义胆，不畏强权',
            symbolism: '刚强、勇敢、反抗精神的象征',
            historicalPeriod: '清代'
        },
        colorMeaning: {
            '蓝色': '刚强勇猛，桀骜不驯',
            '白色': '正直豪爽，光明磊落',
            '金色': '英雄气概，不凡身份'
        },
        relatedOperas: [
            {
                name: '盗御马',
                description: '窦尔敦盗取御马',
                period: '清代'
            },
            {
                name: '连环套',
                description: '窦尔敦中计被擒',
                period: '清代'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/0066CC/FFFFFF?text=窦尔敦',
            fullSize: 'https://via.placeholder.com/600x600/0066CC/FFFFFF?text=窦尔敦脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹蓝色底色',
                duration: 1000,
                color: '#0066CC'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制白色眉毛',
                duration: 800,
                color: '#FFFFFF'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画眼部轮廓',
                duration: 1000,
                color: '#FFFFFF'
            },
            {
                id: 4,
                name: '鼻梁',
                description: '描绘鼻梁线条',
                duration: 600,
                color: '#FFFFFF'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加金色装饰',
                duration: 800,
                color: '#FFD700'
            }
        ],
        difficulty: 'medium',
        popularity: 7,
        tags: [
            '清代',
            '绿林',
            '侠客',
            '勇猛'
        ]
    },
    {
        id: 'yangqilang',
        name: '杨七郎脸谱',
        character: '杨七郎',
        roleCategory: '生',
        colorCategory: '红脸',
        mainColors: [
            '#DC143C',
            '#FFD700',
            '#000000'
        ],
        culturalBackground: {
            origin: '北宋杨家将中的七子杨延嗣',
            personality: '英勇善战，忠君爱国，血性男儿',
            symbolism: '忠勇、牺牲、家国情怀的象征',
            historicalPeriod: '北宋时期'
        },
        colorMeaning: {
            '红色': '忠勇热血，为国捐躯',
            '金色': '英雄本色，光耀门第',
            '黑色': '刚毅果敢，义无反顾'
        },
        relatedOperas: [
            {
                name: '杨家将',
                description: '杨家将抗辽的故事',
                period: '宋代'
            },
            {
                name: '四郎探母',
                description: '杨四郎探望母亲',
                period: '宋代'
            },
            {
                name: '穆桂英挂帅',
                description: '穆桂英率军出征',
                period: '宋代'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=杨七郎',
            fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=杨七郎脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹红色底色',
                duration: 1000,
                color: '#DC143C'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制黑色剑眉',
                duration: 800,
                color: '#000000'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画眼部轮廓',
                duration: 1000,
                color: '#000000'
            },
            {
                id: 4,
                name: '鼻梁',
                description: '描绘鼻梁线条',
                duration: 600,
                color: '#000000'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加金色装饰',
                duration: 800,
                color: '#FFD700'
            }
        ],
        difficulty: 'medium',
        popularity: 7,
        tags: [
            '宋代',
            '杨家将',
            '忠勇',
            '英雄'
        ]
    },
    {
        id: 'yangguifei',
        name: '杨贵妃脸谱',
        character: '杨贵妃',
        roleCategory: '旦',
        colorCategory: '红脸',
        mainColors: [
            '#FFB6C1',
            '#FFD700',
            '#DC143C'
        ],
        culturalBackground: {
            origin: '唐代著名美女，唐玄宗宠妃',
            personality: '美丽动人，聪慧机敏，但也任性娇纵',
            symbolism: '美丽、爱情、悲剧的象征',
            historicalPeriod: '唐代'
        },
        colorMeaning: {
            '粉红色': '娇美动人，温柔如水',
            '金色': '富贵荣华，地位尊贵',
            '红色': '热情如火，爱情炽烈'
        },
        relatedOperas: [
            {
                name: '贵妃醉酒',
                description: '杨贵妃醉酒的故事',
                period: '唐代'
            },
            {
                name: '长生殿',
                description: '唐玄宗与杨贵妃的爱情',
                period: '唐代'
            },
            {
                name: '马嵬坡',
                description: '杨贵妃马嵬坡之死',
                period: '唐代'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/FFB6C1/FFFFFF?text=杨贵妃',
            fullSize: 'https://via.placeholder.com/600x600/FFB6C1/FFFFFF?text=杨贵妃脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹粉色底色',
                duration: 1000,
                color: '#FFB6C1'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制柳叶眉',
                duration: 800,
                color: '#000000'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画凤眼轮廓',
                duration: 1200,
                color: '#000000'
            },
            {
                id: 4,
                name: '唇部',
                description: '描绘樱桃小口',
                duration: 600,
                color: '#DC143C'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加金色花钿',
                duration: 1000,
                color: '#FFD700'
            }
        ],
        difficulty: 'hard',
        popularity: 9,
        tags: [
            '唐代',
            '美女',
            '爱情',
            '悲剧'
        ]
    },
    {
        id: 'wusong',
        name: '武松脸谱',
        character: '武松',
        roleCategory: '净',
        colorCategory: '红脸',
        mainColors: [
            '#DC143C',
            '#000000',
            '#FFD700'
        ],
        culturalBackground: {
            origin: '水浒传中的英雄好汉，行者武松',
            personality: '勇猛无畏，嫉恶如仇，义薄云天',
            symbolism: '正义、勇敢、反抗精神的象征',
            historicalPeriod: '北宋时期'
        },
        colorMeaning: {
            '红色': '正义凛然，热血沸腾',
            '黑色': '刚毅果敢，不屈不挠',
            '金色': '英雄本色，光明磊落'
        },
        relatedOperas: [
            {
                name: '武松打虎',
                description: '武松景阳冈打虎',
                period: '宋代'
            },
            {
                name: '狮子楼',
                description: '武松杀西门庆',
                period: '宋代'
            },
            {
                name: '十字坡',
                description: '武松遇孙二娘',
                period: '宋代'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=武松',
            fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=武松脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹红色底色',
                duration: 1000,
                color: '#DC143C'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制浓黑剑眉',
                duration: 800,
                color: '#000000'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画虎目轮廓',
                duration: 1000,
                color: '#000000'
            },
            {
                id: 4,
                name: '鼻梁',
                description: '描绘挺直鼻梁',
                duration: 600,
                color: '#000000'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加金色装饰',
                duration: 800,
                color: '#FFD700'
            }
        ],
        difficulty: 'medium',
        popularity: 9,
        tags: [
            '水浒',
            '英雄',
            '正义',
            '勇猛'
        ]
    },
    {
        id: 'jianggan',
        name: '蒋干脸谱',
        character: '蒋干',
        roleCategory: '丑',
        colorCategory: '白脸',
        mainColors: [
            '#FFFFFF',
            '#000000',
            '#808080'
        ],
        culturalBackground: {
            origin: '三国时期人物，曹操谋士',
            personality: '自作聪明，好事多磨，常弄巧成拙',
            symbolism: '愚蠢、自负、滑稽的象征',
            historicalPeriod: '三国时期'
        },
        colorMeaning: {
            '白色': '愚蠢无知，自以为是',
            '黑色': '心机不深，容易上当',
            '灰色': '平庸无能，不值一提'
        },
        relatedOperas: [
            {
                name: '群英会',
                description: '蒋干中计盗书',
                period: '三国'
            },
            {
                name: '借东风',
                description: '诸葛亮借东风',
                period: '三国'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/FFFFFF/000000?text=蒋干',
            fullSize: 'https://via.placeholder.com/600x600/FFFFFF/000000?text=蒋干脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹白色底色',
                duration: 1000,
                color: '#FFFFFF'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制细眉',
                duration: 600,
                color: '#000000'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画小眼轮廓',
                duration: 800,
                color: '#000000'
            },
            {
                id: 4,
                name: '鼻部',
                description: '描绘尖鼻',
                duration: 400,
                color: '#000000'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加滑稽装饰',
                duration: 600,
                color: '#808080'
            }
        ],
        difficulty: 'easy',
        popularity: 6,
        tags: [
            '三国',
            '丑角',
            '滑稽',
            '愚蠢'
        ]
    },
    {
        id: 'liubei',
        name: '刘备脸谱',
        character: '刘备',
        roleCategory: '生',
        colorCategory: '红脸',
        mainColors: [
            '#DC143C',
            '#FFD700',
            '#000000'
        ],
        culturalBackground: {
            origin: '三国时期蜀汉开国皇帝',
            personality: '仁德宽厚，礼贤下士，志向远大',
            symbolism: '仁德、理想、领袖风范的象征',
            historicalPeriod: '东汉末年-三国时期'
        },
        colorMeaning: {
            '红色': '仁德之心，爱民如子',
            '金色': '帝王之相，天命所归',
            '黑色': '深沉稳重，胸怀大志'
        },
        relatedOperas: [
            {
                name: '三顾茅庐',
                description: '刘备三顾茅庐请诸葛亮',
                period: '三国'
            },
            {
                name: '甘露寺',
                description: '刘备招亲',
                period: '三国'
            },
            {
                name: '白帝城',
                description: '刘备托孤',
                period: '三国'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=刘备',
            fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=刘备脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹红色底色',
                duration: 1000,
                color: '#DC143C'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制慈眉',
                duration: 800,
                color: '#000000'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画慈目轮廓',
                duration: 1000,
                color: '#000000'
            },
            {
                id: 4,
                name: '胡须',
                description: '描绘长须',
                duration: 1200,
                color: '#000000'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加金色装饰',
                duration: 800,
                color: '#FFD700'
            }
        ],
        difficulty: 'medium',
        popularity: 8,
        tags: [
            '三国',
            '帝王',
            '仁德',
            '领袖'
        ]
    },
    {
        id: 'huangzhong',
        name: '黄忠脸谱',
        character: '黄忠',
        roleCategory: '净',
        colorCategory: '黄脸',
        mainColors: [
            '#FFD700',
            '#000000',
            '#DC143C'
        ],
        culturalBackground: {
            origin: '三国时期蜀汉五虎上将之一',
            personality: '老当益壮，勇猛善射，忠心耿耿',
            symbolism: '老骥伏枥、壮心不已的象征',
            historicalPeriod: '东汉末年-三国时期'
        },
        colorMeaning: {
            '黄色': '老成持重，经验丰富',
            '黑色': '刚毅坚定，不服老迈',
            '红色': '壮心不已，热血依然'
        },
        relatedOperas: [
            {
                name: '定军山',
                description: '黄忠定军山斩夏侯渊',
                period: '三国'
            },
            {
                name: '战长沙',
                description: '黄忠战关羽',
                period: '三国'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/FFD700/000000?text=黄忠',
            fullSize: 'https://via.placeholder.com/600x600/FFD700/000000?text=黄忠脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹黄色底色',
                duration: 1000,
                color: '#FFD700'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制白眉',
                duration: 800,
                color: '#FFFFFF'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画老目轮廓',
                duration: 1000,
                color: '#000000'
            },
            {
                id: 4,
                name: '胡须',
                description: '描绘白须',
                duration: 1200,
                color: '#FFFFFF'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加红色装饰',
                duration: 800,
                color: '#DC143C'
            }
        ],
        difficulty: 'medium',
        popularity: 7,
        tags: [
            '三国',
            '老将',
            '勇猛',
            '忠诚'
        ]
    },
    {
        id: 'machao',
        name: '马超脸谱',
        character: '马超',
        roleCategory: '净',
        colorCategory: '银脸',
        mainColors: [
            '#C0C0C0',
            '#000000',
            '#DC143C'
        ],
        culturalBackground: {
            origin: '三国时期蜀汉五虎上将之一，西凉马腾之子',
            personality: '英勇善战，威风凛凛，有万夫不当之勇',
            symbolism: '英勇、威武、西北豪杰的象征',
            historicalPeriod: '东汉末年-三国时期'
        },
        colorMeaning: {
            '银色': '英武不凡，光芒四射',
            '黑色': '刚毅果敢，威风凛凛',
            '红色': '热血沸腾，勇猛无敌'
        },
        relatedOperas: [
            {
                name: '战渭南',
                description: '马超大战曹操',
                period: '三国'
            },
            {
                name: '取成都',
                description: '马超助刘备取成都',
                period: '三国'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/C0C0C0/000000?text=马超',
            fullSize: 'https://via.placeholder.com/600x600/C0C0C0/000000?text=马超脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹银色底色',
                duration: 1000,
                color: '#C0C0C0'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制黑色剑眉',
                duration: 800,
                color: '#000000'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画鹰目轮廓',
                duration: 1000,
                color: '#000000'
            },
            {
                id: 4,
                name: '鼻梁',
                description: '描绘挺直鼻梁',
                duration: 600,
                color: '#000000'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加红色装饰',
                duration: 800,
                color: '#DC143C'
            }
        ],
        difficulty: 'hard',
        popularity: 7,
        tags: [
            '三国',
            '西凉',
            '英武',
            '威猛'
        ]
    },
    {
        id: 'zhaoyun',
        name: '赵云脸谱',
        character: '赵云',
        roleCategory: '生',
        colorCategory: '白脸',
        mainColors: [
            '#FFFFFF',
            '#000000',
            '#4169E1'
        ],
        culturalBackground: {
            origin: '三国时期蜀汉五虎上将之一，常山赵子龙',
            personality: '英勇善战，忠心耿耿，智勇双全',
            symbolism: '忠诚、勇敢、完美武将的象征',
            historicalPeriod: '东汉末年-三国时期'
        },
        colorMeaning: {
            '白色': '纯洁忠诚，品格高尚',
            '黑色': '刚毅果敢，意志坚定',
            '蓝色': '冷静睿智，深谋远虑'
        },
        relatedOperas: [
            {
                name: '长坂坡',
                description: '赵云长坂坡救阿斗',
                period: '三国'
            },
            {
                name: '截江夺斗',
                description: '赵云截江救阿斗',
                period: '三国'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/FFFFFF/000000?text=赵云',
            fullSize: 'https://via.placeholder.com/600x600/FFFFFF/000000?text=赵云脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹白色底色',
                duration: 1000,
                color: '#FFFFFF'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制黑色剑眉',
                duration: 800,
                color: '#000000'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画英目轮廓',
                duration: 1000,
                color: '#000000'
            },
            {
                id: 4,
                name: '鼻梁',
                description: '描绘挺直鼻梁',
                duration: 600,
                color: '#000000'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加蓝色装饰',
                duration: 800,
                color: '#4169E1'
            }
        ],
        difficulty: 'medium',
        popularity: 9,
        tags: [
            '三国',
            '完美',
            '忠诚',
            '英勇'
        ]
    },
    {
        id: 'sunwukong',
        name: '孙悟空脸谱',
        character: '孙悟空',
        roleCategory: '净',
        colorCategory: '金脸',
        mainColors: [
            '#FFD700',
            '#DC143C',
            '#000000'
        ],
        culturalBackground: {
            origin: '西游记中的齐天大圣，花果山美猴王',
            personality: '机智勇敢，神通广大，桀骜不驯',
            symbolism: '反抗精神、智慧勇敢的象征',
            historicalPeriod: '神话传说'
        },
        colorMeaning: {
            '金色': '神通广大，法力无边',
            '红色': '火眼金睛，热情如火',
            '黑色': '桀骜不驯，不畏权威'
        },
        relatedOperas: [
            {
                name: '大闹天宫',
                description: '孙悟空大闹天宫',
                period: '神话'
            },
            {
                name: '三打白骨精',
                description: '孙悟空三打白骨精',
                period: '神话'
            },
            {
                name: '真假美猴王',
                description: '真假美猴王大战',
                period: '神话'
            }
        ],
        images: {
            thumbnail: 'https://via.placeholder.com/300x300/FFD700/000000?text=孙悟空',
            fullSize: 'https://via.placeholder.com/600x600/FFD700/000000?text=孙悟空脸谱'
        },
        drawingSteps: [
            {
                id: 1,
                name: '底色',
                description: '涂抹金色底色',
                duration: 1000,
                color: '#FFD700'
            },
            {
                id: 2,
                name: '眉毛',
                description: '绘制火焰眉',
                duration: 1000,
                color: '#DC143C'
            },
            {
                id: 3,
                name: '眼部',
                description: '勾画火眼金睛',
                duration: 1200,
                color: '#DC143C'
            },
            {
                id: 4,
                name: '鼻部',
                description: '描绘猴鼻',
                duration: 600,
                color: '#000000'
            },
            {
                id: 5,
                name: '装饰',
                description: '添加神话装饰',
                duration: 1000,
                color: '#000000'
            }
        ],
        difficulty: 'hard',
        popularity: 10,
        tags: [
            '西游记',
            '神话',
            '反抗',
            '智慧'
        ]
    }
];
const masksByRole = {
    '生': operaMasks.filter((mask)=>mask.roleCategory === '生'),
    '旦': operaMasks.filter((mask)=>mask.roleCategory === '旦'),
    '净': operaMasks.filter((mask)=>mask.roleCategory === '净'),
    '丑': operaMasks.filter((mask)=>mask.roleCategory === '丑')
};
const masksByColor = {
    '红脸': operaMasks.filter((mask)=>mask.colorCategory === '红脸'),
    '黑脸': operaMasks.filter((mask)=>mask.colorCategory === '黑脸'),
    '白脸': operaMasks.filter((mask)=>mask.colorCategory === '白脸'),
    '蓝脸': operaMasks.filter((mask)=>mask.colorCategory === '蓝脸'),
    '绿脸': operaMasks.filter((mask)=>mask.colorCategory === '绿脸'),
    '黄脸': operaMasks.filter((mask)=>mask.colorCategory === '黄脸'),
    '金脸': operaMasks.filter((mask)=>mask.colorCategory === '金脸'),
    '银脸': operaMasks.filter((mask)=>mask.colorCategory === '银脸')
};
}),
"[project]/src/components/animation/MaskDrawingAnimation.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "MaskDrawingAnimation": ()=>MaskDrawingAnimation
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
function MaskDrawingAnimation({ mask, isPlaying, onPlayStateChange, speed = 1, className }) {
    const [currentStep, setCurrentStep] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [progress, setProgress] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const intervalRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const animationRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // 重置动画
    const resetAnimation = ()=>{
        setCurrentStep(0);
        setProgress(0);
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
        }
        if (animationRef.current) {
            cancelAnimationFrame(animationRef.current);
            animationRef.current = null;
        }
    };
    // 播放动画
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isPlaying && currentStep < mask.drawingSteps.length) {
            const currentStepData = mask.drawingSteps[currentStep];
            const stepDuration = currentStepData.duration / speed;
            const frameRate = 60; // 60 FPS
            const totalFrames = stepDuration / 1000 * frameRate;
            let frame = 0;
            const animate = ()=>{
                frame++;
                const stepProgress = Math.min(frame / totalFrames, 1);
                setProgress(stepProgress);
                if (stepProgress < 1) {
                    animationRef.current = requestAnimationFrame(animate);
                } else {
                    // 当前步骤完成，移动到下一步
                    setTimeout(()=>{
                        if (currentStep < mask.drawingSteps.length - 1) {
                            setCurrentStep((prev)=>prev + 1);
                            setProgress(0);
                        } else {
                            // 动画完成
                            onPlayStateChange(false);
                        }
                    }, 200); // 短暂停顿
                }
            };
            animationRef.current = requestAnimationFrame(animate);
        }
        return ()=>{
            if (animationRef.current) {
                cancelAnimationFrame(animationRef.current);
            }
        };
    }, [
        isPlaying,
        currentStep,
        speed,
        mask.drawingSteps,
        onPlayStateChange
    ]);
    // 暂停时清理动画
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isPlaying && animationRef.current) {
            cancelAnimationFrame(animationRef.current);
            animationRef.current = null;
        }
    }, [
        isPlaying
    ]);
    // 生成SVG路径
    const generateMaskSVG = ()=>{
        const steps = mask.drawingSteps.slice(0, currentStep + 1);
        const currentStepData = mask.drawingSteps[currentStep];
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
            width: "100%",
            height: "100%",
            viewBox: "0 0 300 300",
            style: {
                background: 'white',
                borderRadius: '1rem'
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                    cx: "150",
                    cy: "150",
                    rx: "120",
                    ry: "140",
                    fill: "#FFF8DC",
                    stroke: "#D4A574",
                    strokeWidth: "2"
                }, void 0, false, {
                    fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                    lineNumber: 101,
                    columnNumber: 9
                }, this),
                steps.map((step, index)=>{
                    const isCurrentStep = index === currentStep;
                    const opacity = isCurrentStep ? progress : 1;
                    const strokeDasharray = isCurrentStep ? `${progress * 100} 100` : undefined;
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                        opacity: opacity,
                        children: renderStepElement(step, strokeDasharray)
                    }, step.id, false, {
                        fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                        lineNumber: 118,
                        columnNumber: 13
                    }, this);
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("text", {
                    x: "150",
                    y: "280",
                    textAnchor: "middle",
                    fontSize: "14",
                    fill: "#1F2937",
                    fontWeight: "600",
                    children: currentStepData?.name || '完成'
                }, void 0, false, {
                    fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                    lineNumber: 125,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
            lineNumber: 94,
            columnNumber: 7
        }, this);
    };
    // 根据脸谱角色渲染真实的绘制步骤
    const renderStepElement = (step, strokeDasharray)=>{
        // 根据脸谱角色选择不同的绘制方式
        const maskId = mask.id;
        switch(step.name){
            case '底色':
                if (maskId === 'guanyu') {
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                        cx: "150",
                        cy: "150",
                        rx: "115",
                        ry: "135",
                        fill: "#FF4444",
                        fillOpacity: "0.9"
                    }, void 0, false, {
                        fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                        lineNumber: 148,
                        columnNumber: 13
                    }, this);
                } else if (maskId === 'caocao') {
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                        cx: "150",
                        cy: "150",
                        rx: "115",
                        ry: "135",
                        fill: "#FFFFFF",
                        fillOpacity: "0.9"
                    }, void 0, false, {
                        fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                        lineNumber: 159,
                        columnNumber: 13
                    }, this);
                } else if (maskId === 'zhangfei') {
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                        cx: "150",
                        cy: "150",
                        rx: "115",
                        ry: "135",
                        fill: "#333333",
                        fillOpacity: "0.9"
                    }, void 0, false, {
                        fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                        lineNumber: 170,
                        columnNumber: 13
                    }, this);
                }
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                    cx: "150",
                    cy: "150",
                    rx: "115",
                    ry: "135",
                    fill: step.color,
                    fillOpacity: "0.8"
                }, void 0, false, {
                    fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                    lineNumber: 181,
                    columnNumber: 11
                }, this);
            case '眉毛':
                if (maskId === 'guanyu') {
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                d: "M 80 100 Q 120 80 160 100",
                                stroke: "#000",
                                strokeWidth: "8",
                                fill: "none",
                                strokeLinecap: "round",
                                strokeDasharray: strokeDasharray
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 195,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                d: "M 140 100 Q 180 80 220 100",
                                stroke: "#000",
                                strokeWidth: "8",
                                fill: "none",
                                strokeLinecap: "round",
                                strokeDasharray: strokeDasharray
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 203,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                        lineNumber: 194,
                        columnNumber: 13
                    }, this);
                } else if (maskId === 'caocao') {
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                d: "M 70 90 Q 120 70 170 90",
                                stroke: "#000",
                                strokeWidth: "10",
                                fill: "none",
                                strokeDasharray: strokeDasharray
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 216,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                d: "M 130 90 Q 180 70 230 90",
                                stroke: "#000",
                                strokeWidth: "10",
                                fill: "none",
                                strokeDasharray: strokeDasharray
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 223,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                        lineNumber: 215,
                        columnNumber: 13
                    }, this);
                } else if (maskId === 'zhangfei') {
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                d: "M 80 100 Q 120 80 160 100",
                                stroke: "white",
                                strokeWidth: "6",
                                fill: "none",
                                strokeDasharray: strokeDasharray
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 235,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                d: "M 140 100 Q 180 80 220 100",
                                stroke: "white",
                                strokeWidth: "6",
                                fill: "none",
                                strokeDasharray: strokeDasharray
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 242,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                        lineNumber: 234,
                        columnNumber: 13
                    }, this);
                }
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            d: "M 100 120 Q 130 110 160 120",
                            stroke: step.color,
                            strokeWidth: step.strokeWidth || 4,
                            fill: "none",
                            strokeLinecap: "round",
                            strokeDasharray: strokeDasharray
                        }, void 0, false, {
                            fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                            lineNumber: 254,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            d: "M 140 120 Q 170 110 200 120",
                            stroke: step.color,
                            strokeWidth: step.strokeWidth || 4,
                            fill: "none",
                            strokeLinecap: "round",
                            strokeDasharray: strokeDasharray
                        }, void 0, false, {
                            fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                            lineNumber: 262,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                    lineNumber: 253,
                    columnNumber: 11
                }, this);
            case '眼部':
                if (maskId === 'guanyu') {
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                                cx: "110",
                                cy: "130",
                                rx: "20",
                                ry: "15",
                                fill: "white",
                                stroke: "#000",
                                strokeWidth: "2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 277,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                                cx: "190",
                                cy: "130",
                                rx: "20",
                                ry: "15",
                                fill: "white",
                                stroke: "#000",
                                strokeWidth: "2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 278,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                cx: "110",
                                cy: "130",
                                r: "8",
                                fill: "#000"
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 279,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                cx: "190",
                                cy: "130",
                                r: "8",
                                fill: "#000"
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 280,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                        lineNumber: 276,
                        columnNumber: 13
                    }, this);
                } else if (maskId === 'caocao') {
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                                cx: "110",
                                cy: "130",
                                rx: "18",
                                ry: "12",
                                fill: "#000"
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 286,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                                cx: "190",
                                cy: "130",
                                rx: "18",
                                ry: "12",
                                fill: "#000"
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 287,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                cx: "110",
                                cy: "130",
                                r: "6",
                                fill: "white"
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 288,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                cx: "190",
                                cy: "130",
                                r: "6",
                                fill: "white"
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 289,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                        lineNumber: 285,
                        columnNumber: 13
                    }, this);
                } else if (maskId === 'zhangfei') {
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                                cx: "110",
                                cy: "130",
                                rx: "20",
                                ry: "15",
                                fill: "white"
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 295,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                                cx: "190",
                                cy: "130",
                                rx: "20",
                                ry: "15",
                                fill: "white"
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 296,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                cx: "110",
                                cy: "130",
                                r: "8",
                                fill: "#000"
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 297,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                cx: "190",
                                cy: "130",
                                r: "8",
                                fill: "#000"
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 298,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                        lineNumber: 294,
                        columnNumber: 13
                    }, this);
                }
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                            cx: "120",
                            cy: "140",
                            rx: "15",
                            ry: "10",
                            stroke: step.color,
                            strokeWidth: step.strokeWidth || 3,
                            fill: "white",
                            strokeDasharray: strokeDasharray
                        }, void 0, false, {
                            fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                            lineNumber: 304,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                            cx: "180",
                            cy: "140",
                            rx: "15",
                            ry: "10",
                            stroke: step.color,
                            strokeWidth: step.strokeWidth || 3,
                            fill: "white",
                            strokeDasharray: strokeDasharray
                        }, void 0, false, {
                            fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                            lineNumber: 314,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                            cx: "120",
                            cy: "140",
                            r: "5",
                            fill: step.color
                        }, void 0, false, {
                            fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                            lineNumber: 324,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                            cx: "180",
                            cy: "140",
                            r: "5",
                            fill: step.color
                        }, void 0, false, {
                            fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                            lineNumber: 325,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                    lineNumber: 303,
                    columnNumber: 11
                }, this);
            case '鼻梁':
            case '鼻部':
                const noseColor = maskId === 'zhangfei' ? 'white' : maskId === 'caocao' ? '#000' : '#000';
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M 150 150 L 150 180 M 140 185 L 160 185",
                    stroke: noseColor,
                    strokeWidth: step.strokeWidth || 4,
                    strokeLinecap: "round",
                    strokeDasharray: strokeDasharray
                }, void 0, false, {
                    fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                    lineNumber: 333,
                    columnNumber: 11
                }, this);
            case '装饰':
                if (maskId === 'guanyu') {
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            d: "M 150 80 L 160 100 L 140 100 Z",
                            fill: "#FFD700",
                            stroke: "#000",
                            strokeWidth: "2"
                        }, void 0, false, {
                            fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                            lineNumber: 346,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                        lineNumber: 345,
                        columnNumber: 13
                    }, this);
                } else if (maskId === 'zhangfei') {
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                        cx: "150",
                        cy: "90",
                        r: "8",
                        fill: "white"
                    }, void 0, false, {
                        fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                        lineNumber: 356,
                        columnNumber: 13
                    }, this);
                }
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            d: "M 150 100 L 155 110 L 145 110 Z",
                            fill: step.color,
                            stroke: step.color,
                            strokeWidth: "1"
                        }, void 0, false, {
                            fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                            lineNumber: 361,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                            cx: "120",
                            cy: "180",
                            r: "3",
                            fill: step.color
                        }, void 0, false, {
                            fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                            lineNumber: 367,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                            cx: "180",
                            cy: "180",
                            r: "3",
                            fill: step.color
                        }, void 0, false, {
                            fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                            lineNumber: 368,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                    lineNumber: 360,
                    columnNumber: 11
                }, this);
            case '胡须':
                const beardColor = maskId === 'zhangfei' ? 'white' : '#000';
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            d: "M 100 220 Q 150 240 200 220",
                            stroke: beardColor,
                            strokeWidth: maskId === 'zhangfei' ? 4 : 3,
                            fill: "none",
                            strokeDasharray: strokeDasharray
                        }, void 0, false, {
                            fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                            lineNumber: 376,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            d: "M 90 235 Q 150 255 210 235",
                            stroke: beardColor,
                            strokeWidth: maskId === 'zhangfei' ? 4 : 3,
                            fill: "none",
                            strokeDasharray: strokeDasharray
                        }, void 0, false, {
                            fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                            lineNumber: 383,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                    lineNumber: 375,
                    columnNumber: 11
                }, this);
            case '唇部':
                const lipColor = maskId === 'guanyu' ? '#8B0000' : maskId === 'zhangfei' ? '#FF0000' : step.color;
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                    cx: "150",
                    cy: maskId === 'zhangfei' ? 210 : 200,
                    rx: maskId === 'zhangfei' ? 18 : 12,
                    ry: maskId === 'zhangfei' ? 10 : 6,
                    fill: lipColor,
                    stroke: maskId === 'zhangfei' ? 'white' : step.color,
                    strokeWidth: "1"
                }, void 0, false, {
                    fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                    lineNumber: 396,
                    columnNumber: 11
                }, this);
            default:
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                    cx: "150",
                    cy: "150",
                    r: "5",
                    fill: step.color,
                    stroke: step.color
                }, void 0, false, {
                    fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                    lineNumber: 409,
                    columnNumber: 11
                }, this);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        style: {
            position: 'relative'
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    aspectRatio: '1',
                    border: '3px solid #F59E0B',
                    borderRadius: '1rem',
                    overflow: 'hidden',
                    backgroundColor: 'white'
                },
                children: generateMaskSVG()
            }, void 0, false, {
                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                lineNumber: 423,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    marginTop: '1rem',
                    padding: '1rem',
                    backgroundColor: 'white',
                    borderRadius: '0.5rem',
                    border: '2px solid #F59E0B',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            marginBottom: '1rem'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                style: {
                                    fontSize: '1.125rem',
                                    fontWeight: '600',
                                    color: '#1F2937',
                                    fontFamily: '"Noto Serif SC", serif'
                                },
                                children: "绘制过程演示"
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 448,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                style: {
                                    fontSize: '0.875rem',
                                    color: '#6B7280'
                                },
                                children: [
                                    "步骤 ",
                                    currentStep + 1,
                                    " / ",
                                    mask.drawingSteps.length
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 456,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                        lineNumber: 442,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            width: '100%',
                            height: '6px',
                            backgroundColor: '#E5E7EB',
                            borderRadius: '3px',
                            marginBottom: '1rem',
                            overflow: 'hidden'
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                height: '100%',
                                backgroundColor: '#F59E0B',
                                borderRadius: '3px',
                                transition: 'width 0.1s ease',
                                width: `${(currentStep + progress) / mask.drawingSteps.length * 100}%`
                            }
                        }, void 0, false, {
                            fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                            lineNumber: 473,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                        lineNumber: 465,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            display: 'flex',
                            gap: '0.5rem',
                            justifyContent: 'center'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>onPlayStateChange(!isPlaying),
                                style: {
                                    backgroundColor: isPlaying ? '#EF4444' : '#10B981',
                                    color: 'white',
                                    border: 'none',
                                    padding: '0.5rem 1rem',
                                    borderRadius: '0.5rem',
                                    cursor: 'pointer',
                                    fontSize: '0.875rem',
                                    fontWeight: '500'
                                },
                                children: isPlaying ? '暂停' : '播放'
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 490,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: resetAnimation,
                                style: {
                                    backgroundColor: '#6B7280',
                                    color: 'white',
                                    border: 'none',
                                    padding: '0.5rem 1rem',
                                    borderRadius: '0.5rem',
                                    cursor: 'pointer',
                                    fontSize: '0.875rem',
                                    fontWeight: '500'
                                },
                                children: "重置"
                            }, void 0, false, {
                                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                lineNumber: 506,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                        lineNumber: 485,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            marginTop: '1rem',
                            padding: '0.75rem',
                            backgroundColor: '#F9FAFB',
                            borderRadius: '0.5rem',
                            borderLeft: '4px solid #F59E0B'
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            style: {
                                fontSize: '0.875rem',
                                color: '#374151',
                                margin: 0
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                    children: [
                                        mask.drawingSteps[currentStep]?.name,
                                        "："
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                                    lineNumber: 536,
                                    columnNumber: 13
                                }, this),
                                mask.drawingSteps[currentStep]?.description
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                            lineNumber: 531,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                        lineNumber: 524,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
                lineNumber: 434,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/animation/MaskDrawingAnimation.tsx",
        lineNumber: 421,
        columnNumber: 5
    }, this);
}
}),
"[project]/src/utils/maskImageGenerator.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// 京剧脸谱图片生成器
// 使用Canvas API生成高质量的脸谱图片
__turbopack_context__.s({
    "MaskImageGenerator": ()=>MaskImageGenerator,
    "maskConfigs": ()=>maskConfigs
});
class MaskImageGenerator {
    canvas;
    ctx;
    constructor(width = 300, height = 300){
        this.canvas = document.createElement('canvas');
        this.canvas.width = width;
        this.canvas.height = height;
        this.ctx = this.canvas.getContext('2d');
    }
    generateMaskImage(config) {
        const { ctx, canvas } = this;
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        // 设置高质量渲染
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        // 设置统一的背景
        const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, Math.max(canvas.width, canvas.height) / 2);
        gradient.addColorStop(0, '#FFFFFF');
        gradient.addColorStop(1, '#F8F9FA');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        // 绘制脸部轮廓
        this.drawFaceShape(config, centerX, centerY);
        // 绘制五官（按正确顺序）
        this.drawEyebrows(config, centerX, centerY);
        this.drawEyes(config, centerX, centerY);
        this.drawNose(config, centerX, centerY);
        this.drawMouth(config, centerX, centerY);
        // 绘制装饰
        this.drawDecorations(config, centerX, centerY);
        // 移除角色名称绘制，让脸谱图片更加纯净
        // this.drawCharacterName(config, centerX, centerY);
        return canvas.toDataURL('image/png', 0.95);
    }
    drawFaceShape(config, centerX, centerY) {
        const { ctx } = this;
        // 创建更精美的渐变效果
        const gradient = ctx.createRadialGradient(centerX, centerY - 40, 0, centerX, centerY, 130);
        gradient.addColorStop(0, config.mainColors[0]);
        gradient.addColorStop(0.7, config.mainColors[1] || config.mainColors[0]);
        gradient.addColorStop(1, this.darkenColor(config.mainColors[1] || config.mainColors[0], 0.2));
        ctx.fillStyle = gradient;
        ctx.strokeStyle = '#2D3748';
        ctx.lineWidth = 4;
        ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        ctx.shadowBlur = 8;
        ctx.shadowOffsetX = 2;
        ctx.shadowOffsetY = 2;
        ctx.beginPath();
        if (config.faceShape === 'oval') {
            ctx.ellipse(centerX, centerY - 10, 105, 125, 0, 0, 2 * Math.PI);
        } else if (config.faceShape === 'round') {
            ctx.arc(centerX, centerY - 5, 115, 0, 2 * Math.PI);
        } else {
            // angular - 更精细的棱角脸型
            ctx.moveTo(centerX - 95, centerY - 110);
            ctx.lineTo(centerX + 95, centerY - 110);
            ctx.lineTo(centerX + 115, centerY - 10);
            ctx.lineTo(centerX + 95, centerY + 125);
            ctx.lineTo(centerX - 95, centerY + 125);
            ctx.lineTo(centerX - 115, centerY - 10);
            ctx.closePath();
        }
        ctx.fill();
        ctx.stroke();
        // 重置阴影
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
    }
    // 辅助函数：加深颜色
    darkenColor(color, factor) {
        if (color.startsWith('#')) {
            const hex = color.slice(1);
            const r = parseInt(hex.substr(0, 2), 16);
            const g = parseInt(hex.substr(2, 2), 16);
            const b = parseInt(hex.substr(4, 2), 16);
            const newR = Math.floor(r * (1 - factor));
            const newG = Math.floor(g * (1 - factor));
            const newB = Math.floor(b * (1 - factor));
            return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
        }
        return color;
    }
    drawEyes(config, centerX, centerY) {
        const { ctx } = this;
        const eyeY = centerY - 20;
        const eyeWidth = config.eyeStyle === 'fierce' ? 25 : 20;
        const eyeHeight = config.eyeStyle === 'gentle' ? 12 : 15;
        // 左眼
        ctx.fillStyle = 'white';
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.ellipse(centerX - 35, eyeY, eyeWidth, eyeHeight, 0, 0, 2 * Math.PI);
        ctx.fill();
        ctx.stroke();
        // 右眼
        ctx.beginPath();
        ctx.ellipse(centerX + 35, eyeY, eyeWidth, eyeHeight, 0, 0, 2 * Math.PI);
        ctx.fill();
        ctx.stroke();
        // 眼珠
        ctx.fillStyle = '#000';
        ctx.beginPath();
        ctx.arc(centerX - 35, eyeY, 8, 0, 2 * Math.PI);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(centerX + 35, eyeY, 8, 0, 2 * Math.PI);
        ctx.fill();
    }
    drawEyebrows(config, centerX, centerY) {
        const { ctx } = this;
        const browY = centerY - 50;
        ctx.strokeStyle = '#000';
        ctx.lineWidth = config.eyeStyle === 'fierce' ? 8 : 5;
        ctx.lineCap = 'round';
        // 左眉
        ctx.beginPath();
        if (config.eyeStyle === 'fierce') {
            ctx.moveTo(centerX - 60, browY);
            ctx.quadraticCurveTo(centerX - 35, browY - 15, centerX - 10, browY);
        } else {
            ctx.moveTo(centerX - 55, browY);
            ctx.quadraticCurveTo(centerX - 35, browY - 10, centerX - 15, browY);
        }
        ctx.stroke();
        // 右眉
        ctx.beginPath();
        if (config.eyeStyle === 'fierce') {
            ctx.moveTo(centerX + 10, browY);
            ctx.quadraticCurveTo(centerX + 35, browY - 15, centerX + 60, browY);
        } else {
            ctx.moveTo(centerX + 15, browY);
            ctx.quadraticCurveTo(centerX + 35, browY - 10, centerX + 55, browY);
        }
        ctx.stroke();
    }
    drawNose(config, centerX, centerY) {
        const { ctx } = this;
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 3;
        ctx.lineCap = 'round';
        ctx.beginPath();
        ctx.moveTo(centerX, centerY + 10);
        ctx.lineTo(centerX, centerY + 40);
        ctx.moveTo(centerX - 8, centerY + 35);
        ctx.lineTo(centerX + 8, centerY + 35);
        ctx.stroke();
    }
    drawMouth(config, centerX, centerY) {
        const { ctx } = this;
        const mouthY = centerY + 60;
        ctx.fillStyle = config.mainColors[0] === '#FFFFFF' ? '#FF0000' : '#8B0000';
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.ellipse(centerX, mouthY, 15, 8, 0, 0, 2 * Math.PI);
        ctx.fill();
        ctx.stroke();
    }
    drawDecorations(config, centerX, centerY) {
        const { ctx } = this;
        // 根据角色添加特殊装饰
        if (config.decorations.includes('forehead-mark')) {
            ctx.fillStyle = '#FFD700';
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(centerX, centerY - 80);
            ctx.lineTo(centerX + 10, centerY - 60);
            ctx.lineTo(centerX - 10, centerY - 60);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
        }
        if (config.decorations.includes('cheek-patterns')) {
            ctx.strokeStyle = config.mainColors[1] || '#000';
            ctx.lineWidth = 2;
            // 左脸装饰
            ctx.beginPath();
            ctx.arc(centerX - 70, centerY + 20, 15, 0, 2 * Math.PI);
            ctx.stroke();
            // 右脸装饰
            ctx.beginPath();
            ctx.arc(centerX + 70, centerY + 20, 15, 0, 2 * Math.PI);
            ctx.stroke();
        }
        if (config.decorations.includes('beard')) {
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 4;
            ctx.lineCap = 'round';
            // 胡须线条
            for(let i = 0; i < 3; i++){
                ctx.beginPath();
                ctx.moveTo(centerX - 60 + i * 20, centerY + 80 + i * 5);
                ctx.quadraticCurveTo(centerX, centerY + 100 + i * 5, centerX + 60 - i * 20, centerY + 80 + i * 5);
                ctx.stroke();
            }
        }
    }
}
const maskConfigs = {
    guanyu: {
        id: 'guanyu',
        name: '关羽',
        character: '关羽',
        mainColors: [
            '#DC2626',
            '#991B1B'
        ],
        faceShape: 'oval',
        eyeStyle: 'fierce',
        decorations: [
            'forehead-mark',
            'beard'
        ]
    },
    caocao: {
        id: 'caocao',
        name: '曹操',
        character: '曹操',
        mainColors: [
            '#FFFFFF',
            '#F3F4F6'
        ],
        faceShape: 'angular',
        eyeStyle: 'fierce',
        decorations: [
            'cheek-patterns'
        ]
    },
    zhangfei: {
        id: 'zhangfei',
        name: '张飞',
        character: '张飞',
        mainColors: [
            '#1F2937',
            '#000000'
        ],
        faceShape: 'round',
        eyeStyle: 'fierce',
        decorations: [
            'beard'
        ]
    },
    huangzhong: {
        id: 'huangzhong',
        name: '黄忠',
        character: '黄忠',
        mainColors: [
            '#FFD700',
            '#F59E0B'
        ],
        faceShape: 'oval',
        eyeStyle: 'normal',
        decorations: [
            'forehead-mark',
            'beard'
        ]
    },
    diaochan: {
        id: 'diaochan',
        name: '貂蝉',
        character: '貂蝉',
        mainColors: [
            '#FFC0CB',
            '#F8BBD9'
        ],
        faceShape: 'oval',
        eyeStyle: 'gentle',
        decorations: [
            'forehead-mark'
        ]
    },
    baozhen: {
        id: 'baozhen',
        name: '包拯',
        character: '包拯',
        mainColors: [
            '#000000',
            '#1F2937'
        ],
        faceShape: 'round',
        eyeStyle: 'fierce',
        decorations: [
            'forehead-mark'
        ]
    },
    douerdun: {
        id: 'douerdun',
        name: '窦尔敦',
        character: '窦尔敦',
        mainColors: [
            '#1E40AF',
            '#3B82F6'
        ],
        faceShape: 'angular',
        eyeStyle: 'fierce',
        decorations: [
            'cheek-patterns'
        ]
    },
    dianwei: {
        id: 'dianwei',
        name: '典韦',
        character: '典韦',
        mainColors: [
            '#7C2D12',
            '#DC2626'
        ],
        faceShape: 'round',
        eyeStyle: 'fierce',
        decorations: [
            'beard'
        ]
    },
    likui: {
        id: 'likui',
        name: '李逵',
        character: '李逵',
        mainColors: [
            '#1F2937',
            '#000000'
        ],
        faceShape: 'round',
        eyeStyle: 'fierce',
        decorations: [
            'beard'
        ]
    },
    sunwukong: {
        id: 'sunwukong',
        name: '孙悟空',
        character: '孙悟空',
        mainColors: [
            '#F59E0B',
            '#FFD700'
        ],
        faceShape: 'oval',
        eyeStyle: 'fierce',
        decorations: [
            'forehead-mark',
            'cheek-patterns'
        ]
    },
    zhubaijie: {
        id: 'zhubaijie',
        name: '猪八戒',
        character: '猪八戒',
        mainColors: [
            '#EC4899',
            '#F472B6'
        ],
        faceShape: 'round',
        eyeStyle: 'normal',
        decorations: [
            'cheek-patterns'
        ]
    },
    baigu: {
        id: 'baigu',
        name: '白骨精',
        character: '白骨精',
        mainColors: [
            '#F3F4F6',
            '#E5E7EB'
        ],
        faceShape: 'angular',
        eyeStyle: 'fierce',
        decorations: [
            'forehead-mark'
        ]
    },
    huajiangjun: {
        id: 'huajiangjun',
        name: '花脸将军',
        character: '花脸将军',
        mainColors: [
            '#7C3AED',
            '#A855F7'
        ],
        faceShape: 'oval',
        eyeStyle: 'fierce',
        decorations: [
            'forehead-mark',
            'cheek-patterns'
        ]
    },
    qingyi: {
        id: 'qingyi',
        name: '青衣花旦',
        character: '青衣',
        mainColors: [
            '#10B981',
            '#34D399'
        ],
        faceShape: 'oval',
        eyeStyle: 'gentle',
        decorations: [
            'forehead-mark'
        ]
    },
    xiaosheng: {
        id: 'xiaosheng',
        name: '小生角色',
        character: '小生',
        mainColors: [
            '#3B82F6',
            '#60A5FA'
        ],
        faceShape: 'oval',
        eyeStyle: 'gentle',
        decorations: []
    }
};
}),
"[project]/src/components/mask/MaskImage.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "MaskImage": ()=>MaskImage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$maskImageGenerator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/maskImageGenerator.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
// 真实脸谱图片URL映射
// 为特定角色使用真实的京剧脸谱图片
const maskImageUrls = {
    // 已添加真实脸谱图片的角色
    'guanyu': 'https://d.bmcx.com/lianpu/d/0072.jpg',
    'baogong': 'https://d.bmcx.com/lianpu/d/0018.jpg',
    'caocao': 'https://d.bmcx.com/lianpu/d/0028.jpg',
    'zhangfei': 'https://d.bmcx.com/lianpu/d/0324.jpg',
    'doulujin': 'https://d.bmcx.com/lianpu/d/0056.jpg',
    'yangqilang': 'https://img1.baidu.com/it/u=348325659,1868481632&fm=253&fmt=auto&app=138&f=JPEG?w=351&h=441',
    'jianggan': 'https://d.bmcx.com/lianpu/d/0117.jpg',
    'liubei': 'https://img1.baidu.com/it/u=93431987,3113680563&fm=253&fmt=auto&app=138&f=JPEG?w=412&h=502',
    'sunwukong': 'https://d.bmcx.com/lianpu/d/0234.jpg',
    // 其他角色暂时使用Canvas生成，后续可以逐步替换
    'huangzhong': '',
    'diaochan': '',
    'dianwei': '',
    'likui': '',
    'zhubaijie': '',
    'baigu': '',
    'huajiangjun': '',
    'qingyi': '',
    'xiaosheng': '',
    'yangguifei': '',
    'machao': '' // 马超 - 使用Canvas生成
};
function MaskImage({ mask, width = 300, height = 300, className }) {
    const [canvasImage, setCanvasImage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [realImage, setRealImage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [imageError, setImageError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    // 获取真实脸谱图片URL
    const realImageUrl = maskImageUrls[mask.id];
    // 生成Canvas图片作为后备方案
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const config = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$maskImageGenerator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["maskConfigs"][mask.id];
        if (config) {
            const generator = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$maskImageGenerator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MaskImageGenerator"](width, height);
            const generatedImage = generator.generateMaskImage(config);
            setCanvasImage(generatedImage);
        } else {
            // 如果没有配置，使用默认配置
            const defaultConfig = {
                id: mask.id,
                name: mask.name,
                character: mask.character,
                mainColors: mask.mainColors,
                faceShape: 'oval',
                eyeStyle: 'normal',
                decorations: [
                    'forehead-mark'
                ]
            };
            const generator = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$maskImageGenerator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MaskImageGenerator"](width, height);
            const generatedImage = generator.generateMaskImage(defaultConfig);
            setCanvasImage(generatedImage);
        }
    }, [
        mask.id,
        mask.name,
        mask.character,
        mask.mainColors,
        width,
        height
    ]);
    // 如果有真实图片URL，尝试加载真实图片
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (realImageUrl) {
            const img = new Image();
            // 对于外部图片，尝试不设置crossOrigin以避免CORS问题
            if (realImageUrl.startsWith('http')) {
            // 外部图片不设置crossOrigin
            } else {
                img.crossOrigin = 'anonymous';
            }
            // 设置加载超时
            const timeout = setTimeout(()=>{
                setImageError(true);
                setRealImage(null);
                setIsLoading(false);
                console.warn(`图片加载超时: ${realImageUrl}`);
            }, 5000); // 5秒超时
            img.onload = ()=>{
                clearTimeout(timeout);
                setRealImage(realImageUrl);
                setImageError(false);
                setIsLoading(false);
                console.log(`✅ 成功加载真实脸谱图片: ${mask.name} - ${realImageUrl}`);
            };
            img.onerror = (error)=>{
                clearTimeout(timeout);
                setImageError(true);
                setRealImage(null);
                setIsLoading(false);
                console.warn(`❌ 真实脸谱图片加载失败，回退到Canvas生成: ${mask.name} - ${realImageUrl}`, error);
            };
            console.log(`🔄 开始加载真实脸谱图片: ${mask.name} - ${realImageUrl}`);
            img.src = realImageUrl;
            // 清理函数
            return ()=>{
                clearTimeout(timeout);
            };
        } else {
            // 没有真实图片URL，直接使用Canvas生成的图片
            setIsLoading(false);
        }
    }, [
        realImageUrl,
        mask.name
    ]);
    // 如果正在加载，显示加载状态
    if (isLoading) {
        return renderLoadingState();
    }
    // 优先使用真实图片，如果加载失败或没有真实图片则使用Canvas生成的图片
    if (realImage && !imageError) {
        return renderRealImage();
    } else if (canvasImage) {
        return renderCanvasImage();
    } else {
        return renderLoadingState();
    }
    //TURBOPACK unreachable
    ;
    // 渲染加载状态
    function renderLoadingState() {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: className,
            style: {
                width,
                height,
                position: 'relative',
                overflow: 'hidden',
                borderRadius: '0.5rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#F3F4F6',
                color: '#9CA3AF'
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    textAlign: 'center'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            fontSize: '2rem',
                            marginBottom: '0.5rem'
                        },
                        children: "🎭"
                    }, void 0, false, {
                        fileName: "[project]/src/components/mask/MaskImage.tsx",
                        lineNumber: 156,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            fontSize: '0.875rem'
                        },
                        children: "生成中..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/mask/MaskImage.tsx",
                        lineNumber: 157,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/mask/MaskImage.tsx",
                lineNumber: 155,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/mask/MaskImage.tsx",
            lineNumber: 140,
            columnNumber: 7
        }, this);
    }
    // 真实图片渲染
    function renderRealImage() {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: className,
            style: {
                width,
                height,
                position: 'relative',
                overflow: 'hidden',
                borderRadius: '0.5rem',
                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease'
            },
            title: `${mask.name} - 真实脸谱图片`,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                    src: realImage,
                    alt: `${mask.name} - ${mask.character}`,
                    style: {
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                        display: 'block'
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 179,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        position: 'absolute',
                        top: '8px',
                        right: '8px',
                        background: 'rgba(34, 197, 94, 0.8)',
                        color: 'white',
                        fontSize: '10px',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        fontWeight: 'bold'
                    },
                    children: "真实脸谱"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 191,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        position: 'absolute',
                        inset: 0,
                        background: 'transparent',
                        transition: 'background 0.3s ease'
                    },
                    onMouseEnter: (e)=>{
                        e.currentTarget.style.background = 'rgba(0, 0, 0, 0.1)';
                        e.currentTarget.parentElement.style.transform = 'scale(1.02)';
                        e.currentTarget.parentElement.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.2)';
                    },
                    onMouseLeave: (e)=>{
                        e.currentTarget.style.background = 'transparent';
                        e.currentTarget.parentElement.style.transform = 'scale(1)';
                        e.currentTarget.parentElement.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 208,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/mask/MaskImage.tsx",
            lineNumber: 166,
            columnNumber: 7
        }, this);
    }
    // Canvas生成的图片渲染
    function renderCanvasImage() {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: className,
            style: {
                width,
                height,
                position: 'relative',
                overflow: 'hidden',
                borderRadius: '0.5rem',
                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease'
            },
            title: `${mask.name} - Canvas生成脸谱`,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                    src: canvasImage,
                    alt: `${mask.name} - ${mask.character}`,
                    style: {
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                        display: 'block'
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 246,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        position: 'absolute',
                        top: '8px',
                        right: '8px',
                        background: 'rgba(59, 130, 246, 0.8)',
                        color: 'white',
                        fontSize: '10px',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        fontWeight: 'bold'
                    },
                    children: "AI生成"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 258,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        position: 'absolute',
                        inset: 0,
                        background: 'transparent',
                        transition: 'background 0.3s ease'
                    },
                    onMouseEnter: (e)=>{
                        e.currentTarget.style.background = 'rgba(0, 0, 0, 0.1)';
                        e.currentTarget.parentElement.style.transform = 'scale(1.02)';
                        e.currentTarget.parentElement.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.2)';
                    },
                    onMouseLeave: (e)=>{
                        e.currentTarget.style.background = 'transparent';
                        e.currentTarget.parentElement.style.transform = 'scale(1)';
                        e.currentTarget.parentElement.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 275,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/mask/MaskImage.tsx",
            lineNumber: 233,
            columnNumber: 7
        }, this);
    }
    // SVG后备方案
    function generateFallbackSVG() {
        const baseProps = {
            width,
            height,
            viewBox: "0 0 300 300",
            className,
            style: {
                borderRadius: '0.5rem'
            }
        };
        // 简化的SVG后备方案
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
            ...baseProps,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("radialGradient", {
                        id: `gradient-${mask.id}`,
                        cx: "50%",
                        cy: "40%",
                        r: "60%",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                offset: "0%",
                                stopColor: mask.mainColors[0]
                            }, void 0, false, {
                                fileName: "[project]/src/components/mask/MaskImage.tsx",
                                lineNumber: 312,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                offset: "100%",
                                stopColor: mask.mainColors[1] || mask.mainColors[0]
                            }, void 0, false, {
                                fileName: "[project]/src/components/mask/MaskImage.tsx",
                                lineNumber: 313,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/mask/MaskImage.tsx",
                        lineNumber: 311,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 310,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                    cx: "150",
                    cy: "150",
                    rx: "120",
                    ry: "140",
                    fill: `url(#gradient-${mask.id})`,
                    stroke: "#333",
                    strokeWidth: "3"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 318,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M 90 110 Q 120 90 150 110",
                    stroke: "#000",
                    strokeWidth: "4",
                    fill: "none"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 329,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M 150 110 Q 180 90 210 110",
                    stroke: "#000",
                    strokeWidth: "4",
                    fill: "none"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 330,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                    cx: "120",
                    cy: "140",
                    rx: "15",
                    ry: "10",
                    fill: "white",
                    stroke: "#000",
                    strokeWidth: "2"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 331,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                    cx: "180",
                    cy: "140",
                    rx: "15",
                    ry: "10",
                    fill: "white",
                    stroke: "#000",
                    strokeWidth: "2"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 332,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                    cx: "120",
                    cy: "140",
                    r: "6",
                    fill: "#000"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 333,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                    cx: "180",
                    cy: "140",
                    r: "6",
                    fill: "#000"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 334,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M 150 160 L 150 180",
                    stroke: "#000",
                    strokeWidth: "3"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 335,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ellipse", {
                    cx: "150",
                    cy: "200",
                    rx: "10",
                    ry: "6",
                    fill: "#000"
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 336,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("text", {
                    x: "150",
                    y: "260",
                    textAnchor: "middle",
                    fill: "white",
                    fontSize: "24",
                    fontWeight: "bold",
                    fontFamily: "serif",
                    stroke: "#000",
                    strokeWidth: "1",
                    children: mask.character
                }, void 0, false, {
                    fileName: "[project]/src/components/mask/MaskImage.tsx",
                    lineNumber: 339,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/mask/MaskImage.tsx",
            lineNumber: 309,
            columnNumber: 7
        }, this);
    }
}
}),
"[project]/src/components/navigation/Breadcrumb.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "Breadcrumb": ()=>Breadcrumb
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
'use client';
;
;
function Breadcrumb({ items, className }) {
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const handleClick = (href, event)=>{
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }
        if (href) {
            console.log('面包屑导航点击:', href);
            console.log('Router对象:', router);
            try {
                router.push(href);
                console.log('面包屑路由跳转已执行');
            } catch (error) {
                console.error('面包屑路由跳转失败，使用window.location作为后备:', error);
                window.location.href = href;
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
        "aria-label": "面包屑导航",
        className: className,
        style: {
            padding: '1rem 0',
            borderBottom: '1px solid #E5E7EB'
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ol", {
            style: {
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                fontSize: '0.875rem',
                color: '#6B7280'
            },
            children: items.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    style: {
                        display: 'flex',
                        alignItems: 'center'
                    },
                    children: [
                        index > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            style: {
                                margin: '0 0.5rem',
                                color: '#D1D5DB'
                            },
                            children: "/"
                        }, void 0, false, {
                            fileName: "[project]/src/components/navigation/Breadcrumb.tsx",
                            lineNumber: 57,
                            columnNumber: 15
                        }, this),
                        item.current ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            style: {
                                color: '#1F2937',
                                fontWeight: '500',
                                fontFamily: '"Noto Serif SC", serif'
                            },
                            children: item.label
                        }, void 0, false, {
                            fileName: "[project]/src/components/navigation/Breadcrumb.tsx",
                            lineNumber: 62,
                            columnNumber: 15
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: (e)=>handleClick(item.href, e),
                            style: {
                                background: 'none',
                                border: 'none',
                                color: '#6B7280',
                                cursor: 'pointer',
                                textDecoration: 'none',
                                fontSize: 'inherit',
                                padding: 0,
                                transition: 'color 0.2s ease'
                            },
                            onMouseEnter: (e)=>{
                                e.currentTarget.style.color = '#B91C1C';
                            },
                            onMouseLeave: (e)=>{
                                e.currentTarget.style.color = '#6B7280';
                            },
                            children: item.label
                        }, void 0, false, {
                            fileName: "[project]/src/components/navigation/Breadcrumb.tsx",
                            lineNumber: 70,
                            columnNumber: 15
                        }, this)
                    ]
                }, index, true, {
                    fileName: "[project]/src/components/navigation/Breadcrumb.tsx",
                    lineNumber: 55,
                    columnNumber: 11
                }, this))
        }, void 0, false, {
            fileName: "[project]/src/components/navigation/Breadcrumb.tsx",
            lineNumber: 47,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/navigation/Breadcrumb.tsx",
        lineNumber: 39,
        columnNumber: 5
    }, this);
}
}),
"[project]/src/hooks/useAppState.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "useAppState": ()=>useAppState
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
const STORAGE_KEY = 'beijing-opera-masks-state';
const defaultState = {
    filter: {},
    recentlyViewed: [],
    favorites: [],
    searchHistory: []
};
function useAppState() {
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(defaultState);
    // 从localStorage加载状态
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }, []);
    // 保存状态到localStorage
    const saveState = (newState)=>{
        const updatedState = {
            ...state,
            ...newState
        };
        setState(updatedState);
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    };
    // 更新筛选条件
    const updateFilter = (filter)=>{
        saveState({
            filter
        });
    };
    // 添加到最近查看
    const addToRecentlyViewed = (maskId)=>{
        const updated = [
            maskId,
            ...state.recentlyViewed.filter((id)=>id !== maskId)
        ].slice(0, 10);
        saveState({
            recentlyViewed: updated
        });
    };
    // 切换收藏状态
    const toggleFavorite = (maskId)=>{
        const updated = state.favorites.includes(maskId) ? state.favorites.filter((id)=>id !== maskId) : [
            ...state.favorites,
            maskId
        ];
        saveState({
            favorites: updated
        });
    };
    // 添加搜索历史
    const addToSearchHistory = (searchTerm)=>{
        if (!searchTerm.trim()) return;
        const updated = [
            searchTerm,
            ...state.searchHistory.filter((term)=>term !== searchTerm)
        ].slice(0, 10);
        saveState({
            searchHistory: updated
        });
    };
    // 清除搜索历史
    const clearSearchHistory = ()=>{
        saveState({
            searchHistory: []
        });
    };
    // 检查是否收藏
    const isFavorite = (maskId)=>{
        return state.favorites.includes(maskId);
    };
    // 获取最近查看的脸谱
    const getRecentlyViewedMasks = (masks)=>{
        return state.recentlyViewed.map((id)=>masks.find((mask)=>mask.id === id)).filter(Boolean);
    };
    // 获取收藏的脸谱
    const getFavoriteMasks = (masks)=>{
        return state.favorites.map((id)=>masks.find((mask)=>mask.id === id)).filter(Boolean);
    };
    return {
        // 状态
        filter: state.filter,
        recentlyViewed: state.recentlyViewed,
        favorites: state.favorites,
        searchHistory: state.searchHistory,
        // 操作
        updateFilter,
        addToRecentlyViewed,
        toggleFavorite,
        addToSearchHistory,
        clearSearchHistory,
        // 辅助函数
        isFavorite,
        getRecentlyViewedMasks,
        getFavoriteMasks
    };
}
}),
"[project]/src/components/ui/Typography.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "BodyText": ()=>BodyText,
    "CaptionText": ()=>CaptionText,
    "EmphasisText": ()=>EmphasisText,
    "MainTitle": ()=>MainTitle,
    "PageTitle": ()=>PageTitle,
    "SectionTitle": ()=>SectionTitle,
    "Subtitle": ()=>Subtitle,
    "Typography": ()=>Typography,
    "fontStyles": ()=>fontStyles
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
'use client';
;
const fontStyles = {
    // 主标题 - 使用马善政字体（传统书法风格）
    mainTitle: {
        fontFamily: 'var(--font-ma-shan-zheng), "Ma Shan Zheng", "KaiTi", "楷体", cursive',
        fontSize: '2.5rem',
        fontWeight: '400',
        lineHeight: '1.2',
        color: '#B91C1C',
        textShadow: '2px 2px 4px rgba(0,0,0,0.1)'
    },
    // 页面标题 - 使用思源宋体
    pageTitle: {
        fontFamily: 'var(--font-noto-serif-sc), "Noto Serif SC", "SimSun", "宋体", serif',
        fontSize: '2rem',
        fontWeight: '700',
        lineHeight: '1.3',
        color: '#1F2937'
    },
    // 章节标题
    sectionTitle: {
        fontFamily: 'var(--font-noto-serif-sc), "Noto Serif SC", "SimSun", "宋体", serif',
        fontSize: '1.5rem',
        fontWeight: '600',
        lineHeight: '1.4',
        color: '#374151'
    },
    // 子标题
    subtitle: {
        fontFamily: 'var(--font-noto-serif-sc), "Noto Serif SC", "SimSun", "宋体", serif',
        fontSize: '1.25rem',
        fontWeight: '500',
        lineHeight: '1.5',
        color: '#4B5563'
    },
    // 正文 - 使用思源黑体
    body: {
        fontFamily: 'var(--font-noto-sans-sc), "Noto Sans SC", "Microsoft YaHei", "微软雅黑", sans-serif',
        fontSize: '1rem',
        fontWeight: '400',
        lineHeight: '1.6',
        color: '#6B7280'
    },
    // 重要文本
    emphasis: {
        fontFamily: 'var(--font-noto-serif-sc), "Noto Serif SC", "SimSun", "宋体", serif',
        fontSize: '1rem',
        fontWeight: '600',
        lineHeight: '1.6',
        color: '#374151'
    },
    // 小字说明
    caption: {
        fontFamily: 'var(--font-noto-sans-sc), "Noto Sans SC", "Microsoft YaHei", "微软雅黑", sans-serif',
        fontSize: '0.875rem',
        fontWeight: '400',
        lineHeight: '1.5',
        color: '#9CA3AF'
    },
    // 按钮文字
    button: {
        fontFamily: 'var(--font-noto-sans-sc), "Noto Sans SC", "Microsoft YaHei", "微软雅黑", sans-serif',
        fontSize: '0.875rem',
        fontWeight: '500',
        lineHeight: '1.4'
    }
};
function Typography({ variant, children, className, style, as = 'div' }) {
    const Component = as;
    const variantStyle = fontStyles[variant];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
        className: className,
        style: {
            ...variantStyle,
            ...style
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Typography.tsx",
        lineNumber: 100,
        columnNumber: 5
    }, this);
}
function MainTitle({ children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Typography, {
        variant: "mainTitle",
        as: "h1",
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Typography.tsx",
        lineNumber: 114,
        columnNumber: 10
    }, this);
}
function PageTitle({ children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Typography, {
        variant: "pageTitle",
        as: "h1",
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Typography.tsx",
        lineNumber: 118,
        columnNumber: 10
    }, this);
}
function SectionTitle({ children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Typography, {
        variant: "sectionTitle",
        as: "h2",
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Typography.tsx",
        lineNumber: 122,
        columnNumber: 10
    }, this);
}
function Subtitle({ children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Typography, {
        variant: "subtitle",
        as: "h3",
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Typography.tsx",
        lineNumber: 126,
        columnNumber: 10
    }, this);
}
function BodyText({ children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Typography, {
        variant: "body",
        as: "p",
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Typography.tsx",
        lineNumber: 130,
        columnNumber: 10
    }, this);
}
function EmphasisText({ children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Typography, {
        variant: "emphasis",
        as: "span",
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Typography.tsx",
        lineNumber: 134,
        columnNumber: 10
    }, this);
}
function CaptionText({ children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Typography, {
        variant: "caption",
        as: "span",
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Typography.tsx",
        lineNumber: 138,
        columnNumber: 10
    }, this);
}
}),
"[project]/src/app/mask/[id]/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>MaskDetailPage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$masks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/masks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$animation$2f$MaskDrawingAnimation$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/animation/MaskDrawingAnimation.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$mask$2f$MaskImage$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/mask/MaskImage.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$navigation$2f$Breadcrumb$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/navigation/Breadcrumb.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAppState$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useAppState.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Typography.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$ThemeProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/providers/ThemeProvider.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
function MaskDetailPage() {
    const params = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useParams"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const maskId = params.id;
    const [showAnimation, setShowAnimation] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isAnimationPlaying, setIsAnimationPlaying] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const { addToRecentlyViewed, toggleFavorite, isFavorite } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAppState$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppState"])();
    const { colors, styles } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$ThemeProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTheme"])();
    const mask = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$masks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["operaMasks"].find((m)=>m.id === maskId);
    // 记录访问
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (mask) {
            addToRecentlyViewed(mask.id);
        }
    }, [
        mask,
        addToRecentlyViewed
    ]);
    if (!mask) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            style: {
                minHeight: '100vh',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#F9FAFB',
                fontFamily: '"Noto Sans SC", sans-serif'
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    textAlign: 'center'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        style: {
                            fontSize: '2rem',
                            color: '#1F2937',
                            marginBottom: '1rem'
                        },
                        children: "脸谱未找到"
                    }, void 0, false, {
                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                        lineNumber: 44,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        style: {
                            color: '#6B7280',
                            marginBottom: '2rem'
                        },
                        children: "抱歉，您访问的脸谱不存在。"
                    }, void 0, false, {
                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                        lineNumber: 47,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>router.push('/'),
                        style: {
                            backgroundColor: '#B91C1C',
                            color: 'white',
                            padding: '0.75rem 1.5rem',
                            borderRadius: '0.5rem',
                            border: 'none',
                            cursor: 'pointer',
                            fontSize: '1rem'
                        },
                        children: "返回首页"
                    }, void 0, false, {
                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                        lineNumber: 50,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/mask/[id]/page.tsx",
                lineNumber: 43,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/mask/[id]/page.tsx",
            lineNumber: 35,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        style: {
            minHeight: '100vh',
            backgroundColor: '#F9FAFB',
            fontFamily: '"Noto Sans SC", sans-serif'
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                style: {
                    backgroundColor: 'white',
                    borderBottom: '2px solid #F59E0B',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                    position: 'sticky',
                    top: 0,
                    zIndex: 40
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        maxWidth: '1200px',
                        margin: '0 auto',
                        padding: '1rem 1.5rem',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: (e)=>{
                                e.preventDefault();
                                e.stopPropagation();
                                console.log('返回首页按钮被点击');
                                console.log('Router对象:', router);
                                try {
                                    router.push('/');
                                    console.log('路由跳转已执行');
                                } catch (error) {
                                    console.error('路由跳转失败，使用window.location作为后备:', error);
                                    window.location.href = '/';
                                }
                            },
                            style: {
                                backgroundColor: 'transparent',
                                border: '2px solid #B91C1C',
                                color: '#B91C1C',
                                padding: '0.5rem 1rem',
                                borderRadius: '0.5rem',
                                cursor: 'pointer',
                                fontSize: '0.875rem',
                                fontWeight: '500',
                                transition: 'all 0.2s ease'
                            },
                            onMouseEnter: (e)=>{
                                e.currentTarget.style.backgroundColor = '#B91C1C';
                                e.currentTarget.style.color = 'white';
                            },
                            onMouseLeave: (e)=>{
                                e.currentTarget.style.backgroundColor = 'transparent';
                                e.currentTarget.style.color = '#B91C1C';
                            },
                            children: "← 返回首页"
                        }, void 0, false, {
                            fileName: "[project]/src/app/mask/[id]/page.tsx",
                            lineNumber: 92,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PageTitle"], {
                            children: mask.name
                        }, void 0, false, {
                            fileName: "[project]/src/app/mask/[id]/page.tsx",
                            lineNumber: 128,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                display: 'flex',
                                gap: '0.5rem'
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>toggleFavorite(mask.id),
                                    style: {
                                        backgroundColor: isFavorite(mask.id) ? '#EF4444' : '#6B7280',
                                        color: 'white',
                                        border: 'none',
                                        padding: '0.5rem 1rem',
                                        borderRadius: '0.5rem',
                                        cursor: 'pointer',
                                        fontSize: '0.875rem',
                                        fontWeight: '500',
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '0.25rem'
                                    },
                                    children: [
                                        isFavorite(mask.id) ? '❤️' : '🤍',
                                        isFavorite(mask.id) ? '已收藏' : '收藏'
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/mask/[id]/page.tsx",
                                    lineNumber: 132,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>setShowAnimation(!showAnimation),
                                    style: {
                                        backgroundColor: showAnimation ? '#EF4444' : '#10B981',
                                        color: 'white',
                                        border: 'none',
                                        padding: '0.5rem 1rem',
                                        borderRadius: '0.5rem',
                                        cursor: 'pointer',
                                        fontSize: '0.875rem',
                                        fontWeight: '500'
                                    },
                                    children: showAnimation ? '关闭动画' : '绘制动画'
                                }, void 0, false, {
                                    fileName: "[project]/src/app/mask/[id]/page.tsx",
                                    lineNumber: 151,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/mask/[id]/page.tsx",
                            lineNumber: 131,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/mask/[id]/page.tsx",
                    lineNumber: 84,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/mask/[id]/page.tsx",
                lineNumber: 76,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                style: {
                    padding: '2rem 1.5rem'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            maxWidth: '1200px',
                            margin: '0 auto'
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$navigation$2f$Breadcrumb$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Breadcrumb"], {
                            items: [
                                {
                                    label: '首页',
                                    href: '/'
                                },
                                {
                                    label: '脸谱详情',
                                    current: true
                                }
                            ]
                        }, void 0, false, {
                            fileName: "[project]/src/app/mask/[id]/page.tsx",
                            lineNumber: 174,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                        lineNumber: 172,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            maxWidth: '1200px',
                            margin: '0 auto',
                            display: 'grid',
                            gridTemplateColumns: '1fr 1fr',
                            gap: '3rem',
                            alignItems: 'start'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    position: 'sticky',
                                    top: '120px'
                                },
                                children: [
                                    showAnimation ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            marginBottom: '2rem'
                                        },
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$animation$2f$MaskDrawingAnimation$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MaskDrawingAnimation"], {
                                            mask: mask,
                                            isPlaying: isAnimationPlaying,
                                            onPlayStateChange: setIsAnimationPlaying,
                                            speed: 1
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/mask/[id]/page.tsx",
                                            lineNumber: 195,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                        lineNumber: 194,
                                        columnNumber: 15
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            aspectRatio: '1',
                                            marginBottom: '2rem',
                                            position: 'relative',
                                            borderRadius: '1rem',
                                            overflow: 'hidden',
                                            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
                                            border: '3px solid #F59E0B'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$mask$2f$MaskImage$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MaskImage"], {
                                                mask: mask,
                                                width: 400,
                                                height: 400,
                                                className: "detail-mask-image"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                lineNumber: 213,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    position: 'absolute',
                                                    top: '1rem',
                                                    left: '1rem',
                                                    backgroundColor: 'rgba(0,0,0,0.8)',
                                                    color: 'white',
                                                    padding: '0.5rem 1rem',
                                                    borderRadius: '9999px',
                                                    fontSize: '0.875rem',
                                                    fontWeight: '600',
                                                    zIndex: 20
                                                },
                                                children: mask.roleCategory
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                lineNumber: 221,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    position: 'absolute',
                                                    top: '1rem',
                                                    right: '1rem',
                                                    backgroundColor: 'rgba(0,0,0,0.8)',
                                                    color: 'white',
                                                    padding: '0.5rem 1rem',
                                                    borderRadius: '9999px',
                                                    fontSize: '0.875rem',
                                                    fontWeight: '600',
                                                    zIndex: 20
                                                },
                                                children: mask.colorCategory
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                lineNumber: 236,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    position: 'absolute',
                                                    bottom: '1rem',
                                                    right: '1rem',
                                                    display: 'flex',
                                                    gap: '0.25rem',
                                                    zIndex: 20
                                                },
                                                children: Array.from({
                                                    length: 5
                                                }).map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        style: {
                                                            color: i < Math.floor(mask.popularity / 2) ? '#F59E0B' : 'rgba(255,255,255,0.5)',
                                                            fontSize: '1.25rem',
                                                            textShadow: '0 1px 2px rgba(0,0,0,0.5)'
                                                        },
                                                        children: "★"
                                                    }, i, false, {
                                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                        lineNumber: 261,
                                                        columnNumber: 21
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                lineNumber: 252,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                        lineNumber: 203,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            backgroundColor: 'white',
                                            padding: '1.5rem',
                                            borderRadius: '1rem',
                                            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                                            border: '2px solid #F59E0B'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                style: {
                                                    fontSize: '1.25rem',
                                                    fontWeight: '600',
                                                    color: '#1F2937',
                                                    marginBottom: '1rem',
                                                    fontFamily: '"Noto Serif SC", serif'
                                                },
                                                children: "主要色彩"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                lineNumber: 284,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    display: 'flex',
                                                    gap: '1rem',
                                                    alignItems: 'center'
                                                },
                                                children: mask.mainColors.map((color, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            textAlign: 'center'
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                style: {
                                                                    width: '3rem',
                                                                    height: '3rem',
                                                                    borderRadius: '50%',
                                                                    backgroundColor: color,
                                                                    border: '3px solid #D1D5DB',
                                                                    marginBottom: '0.5rem',
                                                                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 296,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                style: {
                                                                    fontSize: '0.75rem',
                                                                    color: '#6B7280',
                                                                    fontFamily: 'monospace'
                                                                },
                                                                children: color
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 307,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, index, true, {
                                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                        lineNumber: 295,
                                                        columnNumber: 19
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                lineNumber: 293,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                        lineNumber: 277,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                lineNumber: 191,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    display: 'flex',
                                    flexDirection: 'column',
                                    gap: '2rem'
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            backgroundColor: 'white',
                                            padding: '2rem',
                                            borderRadius: '1rem',
                                            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                                            border: '2px solid #F59E0B'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                style: {
                                                    fontSize: '1.5rem',
                                                    fontWeight: 'bold',
                                                    color: '#1F2937',
                                                    marginBottom: '1.5rem',
                                                    fontFamily: '"Noto Serif SC", serif'
                                                },
                                                children: "基本信息"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                lineNumber: 330,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    display: 'grid',
                                                    gridTemplateColumns: '1fr 1fr',
                                                    gap: '1rem'
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                style: {
                                                                    fontWeight: '600',
                                                                    color: '#374151'
                                                                },
                                                                children: "角色名称："
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 341,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                style: {
                                                                    color: '#6B7280'
                                                                },
                                                                children: mask.character
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 342,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                        lineNumber: 340,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                style: {
                                                                    fontWeight: '600',
                                                                    color: '#374151'
                                                                },
                                                                children: "行当分类："
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 345,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                style: {
                                                                    color: '#6B7280'
                                                                },
                                                                children: mask.roleCategory
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 346,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                        lineNumber: 344,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                style: {
                                                                    fontWeight: '600',
                                                                    color: '#374151'
                                                                },
                                                                children: "颜色分类："
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 349,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                style: {
                                                                    color: '#6B7280'
                                                                },
                                                                children: mask.colorCategory
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 350,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                        lineNumber: 348,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                style: {
                                                                    fontWeight: '600',
                                                                    color: '#374151'
                                                                },
                                                                children: "绘制难度："
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 353,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                style: {
                                                                    color: '#6B7280'
                                                                },
                                                                children: mask.difficulty === 'easy' ? '简单' : mask.difficulty === 'medium' ? '中等' : '困难'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 354,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                        lineNumber: 352,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                style: {
                                                                    fontWeight: '600',
                                                                    color: '#374151'
                                                                },
                                                                children: "受欢迎程度："
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 359,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                style: {
                                                                    color: '#6B7280'
                                                                },
                                                                children: [
                                                                    mask.popularity,
                                                                    "/10"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 360,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                        lineNumber: 358,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                style: {
                                                                    fontWeight: '600',
                                                                    color: '#374151'
                                                                },
                                                                children: "历史时期："
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 363,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                style: {
                                                                    color: '#6B7280'
                                                                },
                                                                children: mask.culturalBackground.historicalPeriod
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 364,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                        lineNumber: 362,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                lineNumber: 339,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                        lineNumber: 323,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            backgroundColor: 'white',
                                            padding: '2rem',
                                            borderRadius: '1rem',
                                            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                                            border: '2px solid #F59E0B'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                style: {
                                                    fontSize: '1.5rem',
                                                    fontWeight: 'bold',
                                                    color: '#1F2937',
                                                    marginBottom: '1.5rem',
                                                    fontFamily: '"Noto Serif SC", serif'
                                                },
                                                children: "文化背景"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                lineNumber: 377,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    gap: '1.5rem'
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                style: {
                                                                    fontWeight: '600',
                                                                    color: '#374151',
                                                                    marginBottom: '0.5rem'
                                                                },
                                                                children: "历史起源"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 388,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                style: {
                                                                    color: '#6B7280',
                                                                    lineHeight: '1.6'
                                                                },
                                                                children: mask.culturalBackground.origin
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 391,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                        lineNumber: 387,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                style: {
                                                                    fontWeight: '600',
                                                                    color: '#374151',
                                                                    marginBottom: '0.5rem'
                                                                },
                                                                children: "性格特点"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 396,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                style: {
                                                                    color: '#6B7280',
                                                                    lineHeight: '1.6'
                                                                },
                                                                children: mask.culturalBackground.personality
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 399,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                        lineNumber: 395,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                style: {
                                                                    fontWeight: '600',
                                                                    color: '#374151',
                                                                    marginBottom: '0.5rem'
                                                                },
                                                                children: "象征意义"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 404,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                style: {
                                                                    color: '#6B7280',
                                                                    lineHeight: '1.6'
                                                                },
                                                                children: mask.culturalBackground.symbolism
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 407,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                        lineNumber: 403,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                lineNumber: 386,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                        lineNumber: 370,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            backgroundColor: 'white',
                                            padding: '2rem',
                                            borderRadius: '1rem',
                                            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                                            border: '2px solid #F59E0B'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                style: {
                                                    fontSize: '1.5rem',
                                                    fontWeight: 'bold',
                                                    color: '#1F2937',
                                                    marginBottom: '1.5rem',
                                                    fontFamily: '"Noto Serif SC", serif'
                                                },
                                                children: "色彩寓意"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                lineNumber: 422,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    gap: '1rem'
                                                },
                                                children: Object.entries(mask.colorMeaning).map(([color, meaning])=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            padding: '1rem',
                                                            backgroundColor: '#F9FAFB',
                                                            borderRadius: '0.5rem',
                                                            borderLeft: '4px solid #F59E0B'
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                style: {
                                                                    fontWeight: '600',
                                                                    color: '#374151'
                                                                },
                                                                children: [
                                                                    color,
                                                                    "："
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 439,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                style: {
                                                                    color: '#6B7280'
                                                                },
                                                                children: meaning
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 440,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, color, true, {
                                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                        lineNumber: 433,
                                                        columnNumber: 19
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                lineNumber: 431,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                        lineNumber: 415,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            backgroundColor: 'white',
                                            padding: '2rem',
                                            borderRadius: '1rem',
                                            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                                            border: '2px solid #F59E0B'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                style: {
                                                    fontSize: '1.5rem',
                                                    fontWeight: 'bold',
                                                    color: '#1F2937',
                                                    marginBottom: '1.5rem',
                                                    fontFamily: '"Noto Serif SC", serif'
                                                },
                                                children: "相关剧目"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                lineNumber: 454,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    gap: '1.5rem'
                                                },
                                                children: mask.relatedOperas.map((opera, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            padding: '1.5rem',
                                                            backgroundColor: '#F9FAFB',
                                                            borderRadius: '0.75rem',
                                                            border: '1px solid #E5E7EB'
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                style: {
                                                                    fontSize: '1.125rem',
                                                                    fontWeight: '600',
                                                                    color: '#1F2937',
                                                                    marginBottom: '0.5rem',
                                                                    fontFamily: '"Noto Serif SC", serif'
                                                                },
                                                                children: opera.name
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 471,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                style: {
                                                                    color: '#6B7280',
                                                                    marginBottom: '0.5rem',
                                                                    lineHeight: '1.6'
                                                                },
                                                                children: opera.description
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 480,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                style: {
                                                                    fontSize: '0.875rem',
                                                                    color: '#F59E0B',
                                                                    fontWeight: '500'
                                                                },
                                                                children: opera.period
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                                lineNumber: 483,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, index, true, {
                                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                        lineNumber: 465,
                                                        columnNumber: 19
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                lineNumber: 463,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                        lineNumber: 447,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            backgroundColor: 'white',
                                            padding: '2rem',
                                            borderRadius: '1rem',
                                            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                                            border: '2px solid #F59E0B'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                style: {
                                                    fontSize: '1.5rem',
                                                    fontWeight: 'bold',
                                                    color: '#1F2937',
                                                    marginBottom: '1.5rem',
                                                    fontFamily: '"Noto Serif SC", serif'
                                                },
                                                children: "相关标签"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                lineNumber: 503,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    display: 'flex',
                                                    flexWrap: 'wrap',
                                                    gap: '0.75rem'
                                                },
                                                children: mask.tags.map((tag, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        style: {
                                                            padding: '0.75rem 1rem',
                                                            fontSize: '0.875rem',
                                                            backgroundColor: 'rgba(245, 158, 11, 0.1)',
                                                            color: '#F59E0B',
                                                            border: '2px solid #F59E0B',
                                                            borderRadius: '9999px',
                                                            fontWeight: '500'
                                                        },
                                                        children: tag
                                                    }, index, false, {
                                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                        lineNumber: 514,
                                                        columnNumber: 19
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                                lineNumber: 512,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                                        lineNumber: 496,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/mask/[id]/page.tsx",
                                lineNumber: 321,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/mask/[id]/page.tsx",
                        lineNumber: 182,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/mask/[id]/page.tsx",
                lineNumber: 171,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/mask/[id]/page.tsx",
        lineNumber: 70,
        columnNumber: 5
    }, this);
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__7521c28f._.js.map