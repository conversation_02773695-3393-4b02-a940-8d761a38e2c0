'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@/components/providers/ThemeProvider';

interface SimpleNavbarProps {
  showBackButton?: boolean;
  title?: string;
}

export function SimpleNavbar({ showBackButton = false, title }: SimpleNavbarProps) {
  const router = useRouter();
  const { colors, toggleTheme, theme } = useTheme();

  return (
    <nav style={{
      backgroundColor: colors.backgroundSecondary,
      borderBottom: `1px solid ${colors.border}`,
      padding: '1rem 2rem',
      position: 'sticky',
      top: 0,
      zIndex: 100,
      backdropFilter: 'blur(10px)'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        {/* 左侧：返回按钮或标题 */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          {showBackButton && (
            <button
              onClick={() => router.push('/')}
              style={{
                backgroundColor: 'transparent',
                border: `1px solid ${colors.border}`,
                color: colors.textPrimary,
                padding: '0.5rem 1rem',
                borderRadius: '0.5rem',
                cursor: 'pointer',
                fontSize: '0.875rem',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
            >
              ← 返回
            </button>
          )}
          
          {title && (
            <h1 style={{
              fontSize: '1.25rem',
              fontWeight: 'bold',
              color: colors.textPrimary,
              margin: 0
            }}>
              {title}
            </h1>
          )}
          
          {!title && !showBackButton && (
            <div style={{
              fontSize: '1.5rem',
              fontWeight: 'bold',
              color: colors.primary,
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              🎭 京剧脸谱
            </div>
          )}
        </div>

        {/* 右侧：主题切换按钮 */}
        <button
          onClick={toggleTheme}
          style={{
            backgroundColor: colors.backgroundSecondary,
            border: `1px solid ${colors.border}`,
            color: colors.textPrimary,
            padding: '0.5rem',
            borderRadius: '0.5rem',
            cursor: 'pointer',
            fontSize: '1.25rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '40px',
            height: '40px'
          }}
          title={theme === 'light' ? '切换到暗色主题' : '切换到亮色主题'}
        >
          {theme === 'light' ? '🌙' : '☀️'}
        </button>
      </div>
    </nav>
  );
}
