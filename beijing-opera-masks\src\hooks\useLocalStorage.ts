import { useState, useEffect } from 'react';

// 通用的localStorage hook
export function useLocalStorage<T>(key: string, initialValue: T) {
  // 添加客户端渲染状态
  const [isClient, setIsClient] = useState(false);

  // 获取初始值 - 服务器端始终返回初始值
  const [storedValue, setStoredValue] = useState<T>(initialValue);

  // 客户端挂载后读取localStorage
  useEffect(() => {
    setIsClient(true);

    try {
      const item = window.localStorage.getItem(key);
      if (item) {
        setStoredValue(JSON.parse(item));
      }
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
    }
  }, [key]);

  // 设置值的函数
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);

      if (isClient) {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue, isClient] as const;
}

// 收藏功能hook
export function useFavorites() {
  const [favorites, setFavorites, isClient] = useLocalStorage<string[]>('opera-mask-favorites', []);

  const toggleFavorite = (maskId: string) => {
    setFavorites(prev => {
      if (prev.includes(maskId)) {
        return prev.filter(id => id !== maskId);
      } else {
        return [...prev, maskId];
      }
    });
  };

  const isFavorite = (maskId: string) => {
    return favorites.includes(maskId);
  };

  return {
    favorites,
    toggleFavorite,
    isFavorite,
    isClient
  };
}

// 最近浏览功能hook
export function useRecentlyViewed() {
  const [recentlyViewed, setRecentlyViewed, isClient] = useLocalStorage<string[]>('opera-mask-recent', []);

  const addToRecentlyViewed = (maskId: string) => {
    setRecentlyViewed(prev => {
      // 移除已存在的项目
      const filtered = prev.filter(id => id !== maskId);
      // 添加到开头，限制最多10个
      return [maskId, ...filtered].slice(0, 10);
    });
  };

  return {
    recentlyViewed,
    addToRecentlyViewed,
    isClient
  };
}
