import { SupabaseClient } from '@supabase/supabase-js';
import { I18nVariables } from '@supabase/auth-ui-shared';
import { Appearance } from '../../../types';
declare function UpdatePassword({ supabaseClient, i18n, appearance, }: {
    supabaseClient: SupabaseClient;
    i18n?: I18nVariables;
    appearance?: Appearance;
}): import("react/jsx-runtime").JSX.Element;
export { UpdatePassword };
//# sourceMappingURL=UpdatePassword.d.ts.map