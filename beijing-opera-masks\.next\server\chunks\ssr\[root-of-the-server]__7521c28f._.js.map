{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/data/masks.ts"], "sourcesContent": ["import { OperaMask } from '@/types/mask';\n\nexport const operaMasks: OperaMask[] = [\n  {\n    id: 'guanyu',\n    name: '关羽脸谱',\n    character: '关羽',\n    roleCategory: '净',\n    colorCategory: '红脸',\n    mainColors: ['#DC143C', '#FFD700', '#000000'],\n    culturalBackground: {\n      origin: '三国时期蜀汉名将，被尊为武圣',\n      personality: '忠义勇武，刚正不阿，义薄云天',\n      symbolism: '忠诚、正义、勇敢的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '红色': '忠勇正义，赤胆忠心',\n      '金色': '神圣威严，地位崇高',\n      '黑色': '刚毅坚定，不可动摇'\n    },\n    relatedOperas: [\n      { name: '单刀会', description: '关羽单刀赴会的故事', period: '三国' },\n      { name: '华容道', description: '关羽义释曹操', period: '三国' },\n      { name: '走麦城', description: '关羽败走麦城', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=关羽',\n      fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=关羽脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹红色底色', duration: 1000, color: '#DC143C' },\n      { id: 2, name: '眉毛', description: '绘制浓眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1200, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘鼻梁线条', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加金色装饰', duration: 1000, color: '#FFD700' }\n    ],\n    difficulty: 'medium',\n    popularity: 10,\n    tags: ['三国', '武将', '忠义', '经典']\n  },\n  {\n    id: 'baogong',\n    name: '包拯脸谱',\n    character: '包拯',\n    roleCategory: '净',\n    colorCategory: '黑脸',\n    mainColors: ['#000000', '#FFFFFF', '#FFD700'],\n    culturalBackground: {\n      origin: '北宋名臣，以清廉公正著称',\n      personality: '铁面无私，执法如山，清正廉洁',\n      symbolism: '公正执法，清廉为民的象征',\n      historicalPeriod: '北宋时期'\n    },\n    colorMeaning: {\n      '黑色': '公正严明，铁面无私',\n      '白色': '清廉正直，一尘不染',\n      '金色': '威严庄重，地位尊崇'\n    },\n    relatedOperas: [\n      { name: '铡美案', description: '包拯铡驸马陈世美', period: '宋代' },\n      { name: '打龙袍', description: '包拯怒斥宋仁宗', period: '宋代' },\n      { name: '赤桑镇', description: '包拯审案的故事', period: '宋代' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/000000/FFFFFF?text=包拯',\n      fullSize: 'https://via.placeholder.com/600x600/000000/FFFFFF?text=包拯脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹黑色底色', duration: 1000, color: '#000000' },\n      { id: 2, name: '额头', description: '绘制白色月牙', duration: 800, color: '#FFFFFF' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1000, color: '#FFFFFF' },\n      { id: 4, name: '鼻翼', description: '描绘鼻翼线条', duration: 600, color: '#FFFFFF' },\n      { id: 5, name: '装饰', description: '添加金色细节', duration: 800, color: '#FFD700' }\n    ],\n    difficulty: 'easy',\n    popularity: 9,\n    tags: ['宋代', '清官', '正义', '经典']\n  },\n  {\n    id: 'caocao',\n    name: '曹操脸谱',\n    character: '曹操',\n    roleCategory: '净',\n    colorCategory: '白脸',\n    mainColors: ['#FFFFFF', '#000000', '#DC143C'],\n    culturalBackground: {\n      origin: '东汉末年政治家、军事家、文学家',\n      personality: '奸诈狡猾，野心勃勃，但才华横溢',\n      symbolism: '奸诈、权谋、复杂人性的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '白色': '奸诈狡猾，阴险毒辣',\n      '黑色': '深沉城府，心机深重',\n      '红色': '暴戾之气，杀伐果断'\n    },\n    relatedOperas: [\n      { name: '捉放曹', description: '陈宫捉放曹操', period: '三国' },\n      { name: '击鼓骂曹', description: '祢衡击鼓骂曹', period: '三国' },\n      { name: '群英会', description: '曹操与群雄斗智', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/FFFFFF/000000?text=曹操',\n      fullSize: 'https://via.placeholder.com/600x600/FFFFFF/000000?text=曹操脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹白色底色', duration: 1000, color: '#FFFFFF' },\n      { id: 2, name: '眉毛', description: '绘制黑色浓眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1200, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘鼻梁阴影', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加红色细节', duration: 1000, color: '#DC143C' }\n    ],\n    difficulty: 'medium',\n    popularity: 8,\n    tags: ['三国', '奸雄', '复杂', '经典']\n  },\n  {\n    id: 'zhangfei',\n    name: '张飞脸谱',\n    character: '张飞',\n    roleCategory: '净',\n    colorCategory: '黑脸',\n    mainColors: ['#000000', '#FFFFFF', '#DC143C'],\n    culturalBackground: {\n      origin: '三国时期蜀汉名将，刘备义弟',\n      personality: '勇猛粗犷，嫉恶如仇，忠义豪爽',\n      symbolism: '勇猛、正直、豪爽的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '黑色': '刚直勇猛，正气凛然',\n      '白色': '纯真豪爽，心无城府',\n      '红色': '热血沸腾，义气冲天'\n    },\n    relatedOperas: [\n      { name: '长坂坡', description: '张飞大战长坂坡', period: '三国' },\n      { name: '古城会', description: '张飞误会关羽', period: '三国' },\n      { name: '芦花荡', description: '张飞智取芦花荡', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/000000/FFFFFF?text=张飞',\n      fullSize: 'https://via.placeholder.com/600x600/000000/FFFFFF?text=张飞脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹黑色底色', duration: 1000, color: '#000000' },\n      { id: 2, name: '眉毛', description: '绘制白色粗眉', duration: 800, color: '#FFFFFF' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1000, color: '#FFFFFF' },\n      { id: 4, name: '鼻翼', description: '描绘鼻翼线条', duration: 600, color: '#FFFFFF' },\n      { id: 5, name: '装饰', description: '添加红色装饰', duration: 800, color: '#DC143C' }\n    ],\n    difficulty: 'easy',\n    popularity: 8,\n    tags: ['三国', '武将', '勇猛', '豪爽']\n  },\n  {\n    id: 'doulujin',\n    name: '窦尔敦脸谱',\n    character: '窦尔敦',\n    roleCategory: '净',\n    colorCategory: '蓝脸',\n    mainColors: ['#0066CC', '#FFFFFF', '#FFD700'],\n    culturalBackground: {\n      origin: '清代绿林好汉，盗侠传奇人物',\n      personality: '刚强勇猛，侠肝义胆，不畏强权',\n      symbolism: '刚强、勇敢、反抗精神的象征',\n      historicalPeriod: '清代'\n    },\n    colorMeaning: {\n      '蓝色': '刚强勇猛，桀骜不驯',\n      '白色': '正直豪爽，光明磊落',\n      '金色': '英雄气概，不凡身份'\n    },\n    relatedOperas: [\n      { name: '盗御马', description: '窦尔敦盗取御马', period: '清代' },\n      { name: '连环套', description: '窦尔敦中计被擒', period: '清代' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/0066CC/FFFFFF?text=窦尔敦',\n      fullSize: 'https://via.placeholder.com/600x600/0066CC/FFFFFF?text=窦尔敦脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹蓝色底色', duration: 1000, color: '#0066CC' },\n      { id: 2, name: '眉毛', description: '绘制白色眉毛', duration: 800, color: '#FFFFFF' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1000, color: '#FFFFFF' },\n      { id: 4, name: '鼻梁', description: '描绘鼻梁线条', duration: 600, color: '#FFFFFF' },\n      { id: 5, name: '装饰', description: '添加金色装饰', duration: 800, color: '#FFD700' }\n    ],\n    difficulty: 'medium',\n    popularity: 7,\n    tags: ['清代', '绿林', '侠客', '勇猛']\n  },\n  {\n    id: 'yangqilang',\n    name: '杨七郎脸谱',\n    character: '杨七郎',\n    roleCategory: '生',\n    colorCategory: '红脸',\n    mainColors: ['#DC143C', '#FFD700', '#000000'],\n    culturalBackground: {\n      origin: '北宋杨家将中的七子杨延嗣',\n      personality: '英勇善战，忠君爱国，血性男儿',\n      symbolism: '忠勇、牺牲、家国情怀的象征',\n      historicalPeriod: '北宋时期'\n    },\n    colorMeaning: {\n      '红色': '忠勇热血，为国捐躯',\n      '金色': '英雄本色，光耀门第',\n      '黑色': '刚毅果敢，义无反顾'\n    },\n    relatedOperas: [\n      { name: '杨家将', description: '杨家将抗辽的故事', period: '宋代' },\n      { name: '四郎探母', description: '杨四郎探望母亲', period: '宋代' },\n      { name: '穆桂英挂帅', description: '穆桂英率军出征', period: '宋代' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=杨七郎',\n      fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=杨七郎脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹红色底色', duration: 1000, color: '#DC143C' },\n      { id: 2, name: '眉毛', description: '绘制黑色剑眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘鼻梁线条', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加金色装饰', duration: 800, color: '#FFD700' }\n    ],\n    difficulty: 'medium',\n    popularity: 7,\n    tags: ['宋代', '杨家将', '忠勇', '英雄']\n  },\n  {\n    id: 'yangguifei',\n    name: '杨贵妃脸谱',\n    character: '杨贵妃',\n    roleCategory: '旦',\n    colorCategory: '红脸',\n    mainColors: ['#FFB6C1', '#FFD700', '#DC143C'],\n    culturalBackground: {\n      origin: '唐代著名美女，唐玄宗宠妃',\n      personality: '美丽动人，聪慧机敏，但也任性娇纵',\n      symbolism: '美丽、爱情、悲剧的象征',\n      historicalPeriod: '唐代'\n    },\n    colorMeaning: {\n      '粉红色': '娇美动人，温柔如水',\n      '金色': '富贵荣华，地位尊贵',\n      '红色': '热情如火，爱情炽烈'\n    },\n    relatedOperas: [\n      { name: '贵妃醉酒', description: '杨贵妃醉酒的故事', period: '唐代' },\n      { name: '长生殿', description: '唐玄宗与杨贵妃的爱情', period: '唐代' },\n      { name: '马嵬坡', description: '杨贵妃马嵬坡之死', period: '唐代' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/FFB6C1/FFFFFF?text=杨贵妃',\n      fullSize: 'https://via.placeholder.com/600x600/FFB6C1/FFFFFF?text=杨贵妃脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹粉色底色', duration: 1000, color: '#FFB6C1' },\n      { id: 2, name: '眉毛', description: '绘制柳叶眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画凤眼轮廓', duration: 1200, color: '#000000' },\n      { id: 4, name: '唇部', description: '描绘樱桃小口', duration: 600, color: '#DC143C' },\n      { id: 5, name: '装饰', description: '添加金色花钿', duration: 1000, color: '#FFD700' }\n    ],\n    difficulty: 'hard',\n    popularity: 9,\n    tags: ['唐代', '美女', '爱情', '悲剧']\n  },\n  {\n    id: 'wusong',\n    name: '武松脸谱',\n    character: '武松',\n    roleCategory: '净',\n    colorCategory: '红脸',\n    mainColors: ['#DC143C', '#000000', '#FFD700'],\n    culturalBackground: {\n      origin: '水浒传中的英雄好汉，行者武松',\n      personality: '勇猛无畏，嫉恶如仇，义薄云天',\n      symbolism: '正义、勇敢、反抗精神的象征',\n      historicalPeriod: '北宋时期'\n    },\n    colorMeaning: {\n      '红色': '正义凛然，热血沸腾',\n      '黑色': '刚毅果敢，不屈不挠',\n      '金色': '英雄本色，光明磊落'\n    },\n    relatedOperas: [\n      { name: '武松打虎', description: '武松景阳冈打虎', period: '宋代' },\n      { name: '狮子楼', description: '武松杀西门庆', period: '宋代' },\n      { name: '十字坡', description: '武松遇孙二娘', period: '宋代' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=武松',\n      fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=武松脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹红色底色', duration: 1000, color: '#DC143C' },\n      { id: 2, name: '眉毛', description: '绘制浓黑剑眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画虎目轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘挺直鼻梁', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加金色装饰', duration: 800, color: '#FFD700' }\n    ],\n    difficulty: 'medium',\n    popularity: 9,\n    tags: ['水浒', '英雄', '正义', '勇猛']\n  },\n  {\n    id: 'jianggan',\n    name: '蒋干脸谱',\n    character: '蒋干',\n    roleCategory: '丑',\n    colorCategory: '白脸',\n    mainColors: ['#FFFFFF', '#000000', '#808080'],\n    culturalBackground: {\n      origin: '三国时期人物，曹操谋士',\n      personality: '自作聪明，好事多磨，常弄巧成拙',\n      symbolism: '愚蠢、自负、滑稽的象征',\n      historicalPeriod: '三国时期'\n    },\n    colorMeaning: {\n      '白色': '愚蠢无知，自以为是',\n      '黑色': '心机不深，容易上当',\n      '灰色': '平庸无能，不值一提'\n    },\n    relatedOperas: [\n      { name: '群英会', description: '蒋干中计盗书', period: '三国' },\n      { name: '借东风', description: '诸葛亮借东风', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/FFFFFF/000000?text=蒋干',\n      fullSize: 'https://via.placeholder.com/600x600/FFFFFF/000000?text=蒋干脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹白色底色', duration: 1000, color: '#FFFFFF' },\n      { id: 2, name: '眉毛', description: '绘制细眉', duration: 600, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画小眼轮廓', duration: 800, color: '#000000' },\n      { id: 4, name: '鼻部', description: '描绘尖鼻', duration: 400, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加滑稽装饰', duration: 600, color: '#808080' }\n    ],\n    difficulty: 'easy',\n    popularity: 6,\n    tags: ['三国', '丑角', '滑稽', '愚蠢']\n  },\n  {\n    id: 'liubei',\n    name: '刘备脸谱',\n    character: '刘备',\n    roleCategory: '生',\n    colorCategory: '红脸',\n    mainColors: ['#DC143C', '#FFD700', '#000000'],\n    culturalBackground: {\n      origin: '三国时期蜀汉开国皇帝',\n      personality: '仁德宽厚，礼贤下士，志向远大',\n      symbolism: '仁德、理想、领袖风范的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '红色': '仁德之心，爱民如子',\n      '金色': '帝王之相，天命所归',\n      '黑色': '深沉稳重，胸怀大志'\n    },\n    relatedOperas: [\n      { name: '三顾茅庐', description: '刘备三顾茅庐请诸葛亮', period: '三国' },\n      { name: '甘露寺', description: '刘备招亲', period: '三国' },\n      { name: '白帝城', description: '刘备托孤', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=刘备',\n      fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=刘备脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹红色底色', duration: 1000, color: '#DC143C' },\n      { id: 2, name: '眉毛', description: '绘制慈眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画慈目轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '胡须', description: '描绘长须', duration: 1200, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加金色装饰', duration: 800, color: '#FFD700' }\n    ],\n    difficulty: 'medium',\n    popularity: 8,\n    tags: ['三国', '帝王', '仁德', '领袖']\n  },\n  {\n    id: 'huangzhong',\n    name: '黄忠脸谱',\n    character: '黄忠',\n    roleCategory: '净',\n    colorCategory: '黄脸',\n    mainColors: ['#FFD700', '#000000', '#DC143C'],\n    culturalBackground: {\n      origin: '三国时期蜀汉五虎上将之一',\n      personality: '老当益壮，勇猛善射，忠心耿耿',\n      symbolism: '老骥伏枥、壮心不已的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '黄色': '老成持重，经验丰富',\n      '黑色': '刚毅坚定，不服老迈',\n      '红色': '壮心不已，热血依然'\n    },\n    relatedOperas: [\n      { name: '定军山', description: '黄忠定军山斩夏侯渊', period: '三国' },\n      { name: '战长沙', description: '黄忠战关羽', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/FFD700/000000?text=黄忠',\n      fullSize: 'https://via.placeholder.com/600x600/FFD700/000000?text=黄忠脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹黄色底色', duration: 1000, color: '#FFD700' },\n      { id: 2, name: '眉毛', description: '绘制白眉', duration: 800, color: '#FFFFFF' },\n      { id: 3, name: '眼部', description: '勾画老目轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '胡须', description: '描绘白须', duration: 1200, color: '#FFFFFF' },\n      { id: 5, name: '装饰', description: '添加红色装饰', duration: 800, color: '#DC143C' }\n    ],\n    difficulty: 'medium',\n    popularity: 7,\n    tags: ['三国', '老将', '勇猛', '忠诚']\n  },\n  {\n    id: 'machao',\n    name: '马超脸谱',\n    character: '马超',\n    roleCategory: '净',\n    colorCategory: '银脸',\n    mainColors: ['#C0C0C0', '#000000', '#DC143C'],\n    culturalBackground: {\n      origin: '三国时期蜀汉五虎上将之一，西凉马腾之子',\n      personality: '英勇善战，威风凛凛，有万夫不当之勇',\n      symbolism: '英勇、威武、西北豪杰的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '银色': '英武不凡，光芒四射',\n      '黑色': '刚毅果敢，威风凛凛',\n      '红色': '热血沸腾，勇猛无敌'\n    },\n    relatedOperas: [\n      { name: '战渭南', description: '马超大战曹操', period: '三国' },\n      { name: '取成都', description: '马超助刘备取成都', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/C0C0C0/000000?text=马超',\n      fullSize: 'https://via.placeholder.com/600x600/C0C0C0/000000?text=马超脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹银色底色', duration: 1000, color: '#C0C0C0' },\n      { id: 2, name: '眉毛', description: '绘制黑色剑眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画鹰目轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘挺直鼻梁', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加红色装饰', duration: 800, color: '#DC143C' }\n    ],\n    difficulty: 'hard',\n    popularity: 7,\n    tags: ['三国', '西凉', '英武', '威猛']\n  },\n  {\n    id: 'zhaoyun',\n    name: '赵云脸谱',\n    character: '赵云',\n    roleCategory: '生',\n    colorCategory: '白脸',\n    mainColors: ['#FFFFFF', '#000000', '#4169E1'],\n    culturalBackground: {\n      origin: '三国时期蜀汉五虎上将之一，常山赵子龙',\n      personality: '英勇善战，忠心耿耿，智勇双全',\n      symbolism: '忠诚、勇敢、完美武将的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '白色': '纯洁忠诚，品格高尚',\n      '黑色': '刚毅果敢，意志坚定',\n      '蓝色': '冷静睿智，深谋远虑'\n    },\n    relatedOperas: [\n      { name: '长坂坡', description: '赵云长坂坡救阿斗', period: '三国' },\n      { name: '截江夺斗', description: '赵云截江救阿斗', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/FFFFFF/000000?text=赵云',\n      fullSize: 'https://via.placeholder.com/600x600/FFFFFF/000000?text=赵云脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹白色底色', duration: 1000, color: '#FFFFFF' },\n      { id: 2, name: '眉毛', description: '绘制黑色剑眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画英目轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘挺直鼻梁', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加蓝色装饰', duration: 800, color: '#4169E1' }\n    ],\n    difficulty: 'medium',\n    popularity: 9,\n    tags: ['三国', '完美', '忠诚', '英勇']\n  },\n  {\n    id: 'sunwukong',\n    name: '孙悟空脸谱',\n    character: '孙悟空',\n    roleCategory: '净',\n    colorCategory: '金脸',\n    mainColors: ['#FFD700', '#DC143C', '#000000'],\n    culturalBackground: {\n      origin: '西游记中的齐天大圣，花果山美猴王',\n      personality: '机智勇敢，神通广大，桀骜不驯',\n      symbolism: '反抗精神、智慧勇敢的象征',\n      historicalPeriod: '神话传说'\n    },\n    colorMeaning: {\n      '金色': '神通广大，法力无边',\n      '红色': '火眼金睛，热情如火',\n      '黑色': '桀骜不驯，不畏权威'\n    },\n    relatedOperas: [\n      { name: '大闹天宫', description: '孙悟空大闹天宫', period: '神话' },\n      { name: '三打白骨精', description: '孙悟空三打白骨精', period: '神话' },\n      { name: '真假美猴王', description: '真假美猴王大战', period: '神话' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/FFD700/000000?text=孙悟空',\n      fullSize: 'https://via.placeholder.com/600x600/FFD700/000000?text=孙悟空脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹金色底色', duration: 1000, color: '#FFD700' },\n      { id: 2, name: '眉毛', description: '绘制火焰眉', duration: 1000, color: '#DC143C' },\n      { id:3, name: '眼部', description: '勾画火眼金睛', duration: 1200, color: '#DC143C' },\n      { id: 4, name: '鼻部', description: '描绘猴鼻', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加神话装饰', duration: 1000, color: '#000000' }\n    ],\n    difficulty: 'hard',\n    popularity: 10,\n    tags: ['西游记', '神话', '反抗', '智慧']\n  }\n];\n\n// 按分类组织的脸谱数据\nexport const masksByRole = {\n  '生': operaMasks.filter(mask => mask.roleCategory === '生'),\n  '旦': operaMasks.filter(mask => mask.roleCategory === '旦'),\n  '净': operaMasks.filter(mask => mask.roleCategory === '净'),\n  '丑': operaMasks.filter(mask => mask.roleCategory === '丑')\n};\n\nexport const masksByColor = {\n  '红脸': operaMasks.filter(mask => mask.colorCategory === '红脸'),\n  '黑脸': operaMasks.filter(mask => mask.colorCategory === '黑脸'),\n  '白脸': operaMasks.filter(mask => mask.colorCategory === '白脸'),\n  '蓝脸': operaMasks.filter(mask => mask.colorCategory === '蓝脸'),\n  '绿脸': operaMasks.filter(mask => mask.colorCategory === '绿脸'),\n  '黄脸': operaMasks.filter(mask => mask.colorCategory === '黄脸'),\n  '金脸': operaMasks.filter(mask => mask.colorCategory === '金脸'),\n  '银脸': operaMasks.filter(mask => mask.colorCategory === '银脸')\n};\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,aAA0B;IACrC;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAa,QAAQ;YAAK;YACtD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;SACpD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;SAC9E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAY,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;YACpD;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;SACrD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAQ,aAAa;gBAAU,QAAQ;YAAK;YACpD;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;SACrD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;SAC9E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;YACpD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;SACrD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;YACpD;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;SACrD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAY,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAQ,aAAa;gBAAW,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAS,aAAa;gBAAW,QAAQ;YAAK;SACvD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAO;YAAM;SAAK;IACjC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,OAAO;YACP,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAQ,aAAa;gBAAY,QAAQ;YAAK;YACtD;gBAAE,MAAM;gBAAO,aAAa;gBAAc,QAAQ;YAAK;YACvD;gBAAE,MAAM;gBAAO,aAAa;gBAAY,QAAQ;YAAK;SACtD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAS,UAAU;gBAAK,OAAO;YAAU;YAC3E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;SAC9E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAQ,aAAa;gBAAW,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;SACpD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;SACpD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAQ,aAAa;gBAAc,QAAQ;YAAK;YACxD;gBAAE,MAAM;gBAAO,aAAa;gBAAQ,QAAQ;YAAK;YACjD;gBAAE,MAAM;gBAAO,aAAa;gBAAQ,QAAQ;YAAK;SAClD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAM,OAAO;YAAU;YAC3E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAa,QAAQ;YAAK;YACtD;gBAAE,MAAM;gBAAO,aAAa;gBAAS,QAAQ;YAAK;SACnD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAM,OAAO;YAAU;YAC3E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAO,aAAa;gBAAY,QAAQ;YAAK;SACtD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAY,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAQ,aAAa;gBAAW,QAAQ;YAAK;SACtD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAQ,aAAa;gBAAW,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAS,aAAa;gBAAY,QAAQ;YAAK;YACvD;gBAAE,MAAM;gBAAS,aAAa;gBAAW,QAAQ;YAAK;SACvD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAS,UAAU;gBAAM,OAAO;YAAU;YAC5E;gBAAE,IAAG;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;SAC9E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAO;YAAM;YAAM;SAAK;IACjC;CACD;AAGM,MAAM,cAAc;IACzB,KAAK,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,YAAY,KAAK;IACrD,KAAK,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,YAAY,KAAK;IACrD,KAAK,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,YAAY,KAAK;IACrD,KAAK,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,YAAY,KAAK;AACvD;AAEO,MAAM,eAAe;IAC1B,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;AACzD", "debugId": null}}, {"offset": {"line": 1277, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/animation/MaskDrawingAnimation.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { OperaMask, DrawingStep } from '@/types/mask';\n\ninterface MaskDrawingAnimationProps {\n  mask: OperaMask;\n  isPlaying: boolean;\n  onPlayStateChange: (isPlaying: boolean) => void;\n  speed?: number;\n  className?: string;\n}\n\nexport function MaskDrawingAnimation({\n  mask,\n  isPlaying,\n  onPlayStateChange,\n  speed = 1,\n  className\n}: MaskDrawingAnimationProps) {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [progress, setProgress] = useState(0);\n  const intervalRef = useRef<NodeJS.Timeout | null>(null);\n  const animationRef = useRef<number | null>(null);\n  \n  // 重置动画\n  const resetAnimation = () => {\n    setCurrentStep(0);\n    setProgress(0);\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n    if (animationRef.current) {\n      cancelAnimationFrame(animationRef.current);\n      animationRef.current = null;\n    }\n  };\n  \n  // 播放动画\n  useEffect(() => {\n    if (isPlaying && currentStep < mask.drawingSteps.length) {\n      const currentStepData = mask.drawingSteps[currentStep];\n      const stepDuration = currentStepData.duration / speed;\n      const frameRate = 60; // 60 FPS\n      const totalFrames = (stepDuration / 1000) * frameRate;\n      let frame = 0;\n      \n      const animate = () => {\n        frame++;\n        const stepProgress = Math.min(frame / totalFrames, 1);\n        setProgress(stepProgress);\n        \n        if (stepProgress < 1) {\n          animationRef.current = requestAnimationFrame(animate);\n        } else {\n          // 当前步骤完成，移动到下一步\n          setTimeout(() => {\n            if (currentStep < mask.drawingSteps.length - 1) {\n              setCurrentStep(prev => prev + 1);\n              setProgress(0);\n            } else {\n              // 动画完成\n              onPlayStateChange(false);\n            }\n          }, 200); // 短暂停顿\n        }\n      };\n      \n      animationRef.current = requestAnimationFrame(animate);\n    }\n    \n    return () => {\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, [isPlaying, currentStep, speed, mask.drawingSteps, onPlayStateChange]);\n  \n  // 暂停时清理动画\n  useEffect(() => {\n    if (!isPlaying && animationRef.current) {\n      cancelAnimationFrame(animationRef.current);\n      animationRef.current = null;\n    }\n  }, [isPlaying]);\n  \n  // 生成SVG路径\n  const generateMaskSVG = () => {\n    const steps = mask.drawingSteps.slice(0, currentStep + 1);\n    const currentStepData = mask.drawingSteps[currentStep];\n    \n    return (\n      <svg\n        width=\"100%\"\n        height=\"100%\"\n        viewBox=\"0 0 300 300\"\n        style={{ background: 'white', borderRadius: '1rem' }}\n      >\n        {/* 脸部轮廓 */}\n        <ellipse\n          cx=\"150\"\n          cy=\"150\"\n          rx=\"120\"\n          ry=\"140\"\n          fill=\"#FFF8DC\"\n          stroke=\"#D4A574\"\n          strokeWidth=\"2\"\n        />\n        \n        {/* 绘制步骤 */}\n        {steps.map((step, index) => {\n          const isCurrentStep = index === currentStep;\n          const opacity = isCurrentStep ? progress : 1;\n          const strokeDasharray = isCurrentStep ? `${progress * 100} 100` : undefined;\n          \n          return (\n            <g key={step.id} opacity={opacity}>\n              {renderStepElement(step, strokeDasharray)}\n            </g>\n          );\n        })}\n        \n        {/* 当前步骤标签 */}\n        <text\n          x=\"150\"\n          y=\"280\"\n          textAnchor=\"middle\"\n          fontSize=\"14\"\n          fill=\"#1F2937\"\n          fontWeight=\"600\"\n        >\n          {currentStepData?.name || '完成'}\n        </text>\n      </svg>\n    );\n  };\n  \n  // 根据脸谱角色渲染真实的绘制步骤\n  const renderStepElement = (step: DrawingStep, strokeDasharray?: string) => {\n    // 根据脸谱角色选择不同的绘制方式\n    const maskId = mask.id;\n\n    switch (step.name) {\n      case '底色':\n        if (maskId === 'guanyu') {\n          return (\n            <ellipse\n              cx=\"150\"\n              cy=\"150\"\n              rx=\"115\"\n              ry=\"135\"\n              fill=\"#FF4444\"\n              fillOpacity=\"0.9\"\n            />\n          );\n        } else if (maskId === 'caocao') {\n          return (\n            <ellipse\n              cx=\"150\"\n              cy=\"150\"\n              rx=\"115\"\n              ry=\"135\"\n              fill=\"#FFFFFF\"\n              fillOpacity=\"0.9\"\n            />\n          );\n        } else if (maskId === 'zhangfei') {\n          return (\n            <ellipse\n              cx=\"150\"\n              cy=\"150\"\n              rx=\"115\"\n              ry=\"135\"\n              fill=\"#333333\"\n              fillOpacity=\"0.9\"\n            />\n          );\n        }\n        return (\n          <ellipse\n            cx=\"150\"\n            cy=\"150\"\n            rx=\"115\"\n            ry=\"135\"\n            fill={step.color}\n            fillOpacity=\"0.8\"\n          />\n        );\n\n      case '眉毛':\n        if (maskId === 'guanyu') {\n          return (\n            <g>\n              <path\n                d=\"M 80 100 Q 120 80 160 100\"\n                stroke=\"#000\"\n                strokeWidth=\"8\"\n                fill=\"none\"\n                strokeLinecap=\"round\"\n                strokeDasharray={strokeDasharray}\n              />\n              <path\n                d=\"M 140 100 Q 180 80 220 100\"\n                stroke=\"#000\"\n                strokeWidth=\"8\"\n                fill=\"none\"\n                strokeLinecap=\"round\"\n                strokeDasharray={strokeDasharray}\n              />\n            </g>\n          );\n        } else if (maskId === 'caocao') {\n          return (\n            <g>\n              <path\n                d=\"M 70 90 Q 120 70 170 90\"\n                stroke=\"#000\"\n                strokeWidth=\"10\"\n                fill=\"none\"\n                strokeDasharray={strokeDasharray}\n              />\n              <path\n                d=\"M 130 90 Q 180 70 230 90\"\n                stroke=\"#000\"\n                strokeWidth=\"10\"\n                fill=\"none\"\n                strokeDasharray={strokeDasharray}\n              />\n            </g>\n          );\n        } else if (maskId === 'zhangfei') {\n          return (\n            <g>\n              <path\n                d=\"M 80 100 Q 120 80 160 100\"\n                stroke=\"white\"\n                strokeWidth=\"6\"\n                fill=\"none\"\n                strokeDasharray={strokeDasharray}\n              />\n              <path\n                d=\"M 140 100 Q 180 80 220 100\"\n                stroke=\"white\"\n                strokeWidth=\"6\"\n                fill=\"none\"\n                strokeDasharray={strokeDasharray}\n              />\n            </g>\n          );\n        }\n        return (\n          <g>\n            <path\n              d=\"M 100 120 Q 130 110 160 120\"\n              stroke={step.color}\n              strokeWidth={step.strokeWidth || 4}\n              fill=\"none\"\n              strokeLinecap=\"round\"\n              strokeDasharray={strokeDasharray}\n            />\n            <path\n              d=\"M 140 120 Q 170 110 200 120\"\n              stroke={step.color}\n              strokeWidth={step.strokeWidth || 4}\n              fill=\"none\"\n              strokeLinecap=\"round\"\n              strokeDasharray={strokeDasharray}\n            />\n          </g>\n        );\n\n      case '眼部':\n        if (maskId === 'guanyu') {\n          return (\n            <g>\n              <ellipse cx=\"110\" cy=\"130\" rx=\"20\" ry=\"15\" fill=\"white\" stroke=\"#000\" strokeWidth=\"2\"/>\n              <ellipse cx=\"190\" cy=\"130\" rx=\"20\" ry=\"15\" fill=\"white\" stroke=\"#000\" strokeWidth=\"2\"/>\n              <circle cx=\"110\" cy=\"130\" r=\"8\" fill=\"#000\"/>\n              <circle cx=\"190\" cy=\"130\" r=\"8\" fill=\"#000\"/>\n            </g>\n          );\n        } else if (maskId === 'caocao') {\n          return (\n            <g>\n              <ellipse cx=\"110\" cy=\"130\" rx=\"18\" ry=\"12\" fill=\"#000\"/>\n              <ellipse cx=\"190\" cy=\"130\" rx=\"18\" ry=\"12\" fill=\"#000\"/>\n              <circle cx=\"110\" cy=\"130\" r=\"6\" fill=\"white\"/>\n              <circle cx=\"190\" cy=\"130\" r=\"6\" fill=\"white\"/>\n            </g>\n          );\n        } else if (maskId === 'zhangfei') {\n          return (\n            <g>\n              <ellipse cx=\"110\" cy=\"130\" rx=\"20\" ry=\"15\" fill=\"white\"/>\n              <ellipse cx=\"190\" cy=\"130\" rx=\"20\" ry=\"15\" fill=\"white\"/>\n              <circle cx=\"110\" cy=\"130\" r=\"8\" fill=\"#000\"/>\n              <circle cx=\"190\" cy=\"130\" r=\"8\" fill=\"#000\"/>\n            </g>\n          );\n        }\n        return (\n          <g>\n            <ellipse\n              cx=\"120\"\n              cy=\"140\"\n              rx=\"15\"\n              ry=\"10\"\n              stroke={step.color}\n              strokeWidth={step.strokeWidth || 3}\n              fill=\"white\"\n              strokeDasharray={strokeDasharray}\n            />\n            <ellipse\n              cx=\"180\"\n              cy=\"140\"\n              rx=\"15\"\n              ry=\"10\"\n              stroke={step.color}\n              strokeWidth={step.strokeWidth || 3}\n              fill=\"white\"\n              strokeDasharray={strokeDasharray}\n            />\n            <circle cx=\"120\" cy=\"140\" r=\"5\" fill={step.color} />\n            <circle cx=\"180\" cy=\"140\" r=\"5\" fill={step.color} />\n          </g>\n        );\n\n      case '鼻梁':\n      case '鼻部':\n        const noseColor = maskId === 'zhangfei' ? 'white' : (maskId === 'caocao' ? '#000' : '#000');\n        return (\n          <path\n            d=\"M 150 150 L 150 180 M 140 185 L 160 185\"\n            stroke={noseColor}\n            strokeWidth={step.strokeWidth || 4}\n            strokeLinecap=\"round\"\n            strokeDasharray={strokeDasharray}\n          />\n        );\n\n      case '装饰':\n        if (maskId === 'guanyu') {\n          return (\n            <g>\n              <path\n                d=\"M 150 80 L 160 100 L 140 100 Z\"\n                fill=\"#FFD700\"\n                stroke=\"#000\"\n                strokeWidth=\"2\"\n              />\n            </g>\n          );\n        } else if (maskId === 'zhangfei') {\n          return (\n            <circle cx=\"150\" cy=\"90\" r=\"8\" fill=\"white\"/>\n          );\n        }\n        return (\n          <g>\n            <path\n              d=\"M 150 100 L 155 110 L 145 110 Z\"\n              fill={step.color}\n              stroke={step.color}\n              strokeWidth=\"1\"\n            />\n            <circle cx=\"120\" cy=\"180\" r=\"3\" fill={step.color} />\n            <circle cx=\"180\" cy=\"180\" r=\"3\" fill={step.color} />\n          </g>\n        );\n\n      case '胡须':\n        const beardColor = maskId === 'zhangfei' ? 'white' : '#000';\n        return (\n          <g>\n            <path\n              d=\"M 100 220 Q 150 240 200 220\"\n              stroke={beardColor}\n              strokeWidth={maskId === 'zhangfei' ? 4 : 3}\n              fill=\"none\"\n              strokeDasharray={strokeDasharray}\n            />\n            <path\n              d=\"M 90 235 Q 150 255 210 235\"\n              stroke={beardColor}\n              strokeWidth={maskId === 'zhangfei' ? 4 : 3}\n              fill=\"none\"\n              strokeDasharray={strokeDasharray}\n            />\n          </g>\n        );\n\n      case '唇部':\n        const lipColor = maskId === 'guanyu' ? '#8B0000' : (maskId === 'zhangfei' ? '#FF0000' : step.color);\n        return (\n          <ellipse\n            cx=\"150\"\n            cy={maskId === 'zhangfei' ? 210 : 200}\n            rx={maskId === 'zhangfei' ? 18 : 12}\n            ry={maskId === 'zhangfei' ? 10 : 6}\n            fill={lipColor}\n            stroke={maskId === 'zhangfei' ? 'white' : step.color}\n            strokeWidth=\"1\"\n          />\n        );\n\n      default:\n        return (\n          <circle\n            cx=\"150\"\n            cy=\"150\"\n            r=\"5\"\n            fill={step.color}\n            stroke={step.color}\n          />\n        );\n    }\n  };\n  \n  return (\n    <div className={className} style={{ position: 'relative' }}>\n      {/* SVG 动画区域 */}\n      <div style={{\n        aspectRatio: '1',\n        border: '3px solid #F59E0B',\n        borderRadius: '1rem',\n        overflow: 'hidden',\n        backgroundColor: 'white'\n      }}>\n        {generateMaskSVG()}\n      </div>\n      \n      {/* 控制面板 */}\n      <div style={{\n        marginTop: '1rem',\n        padding: '1rem',\n        backgroundColor: 'white',\n        borderRadius: '0.5rem',\n        border: '2px solid #F59E0B',\n        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n      }}>\n        <div style={{\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: '1rem'\n        }}>\n          <h3 style={{\n            fontSize: '1.125rem',\n            fontWeight: '600',\n            color: '#1F2937',\n            fontFamily: '\"Noto Serif SC\", serif'\n          }}>\n            绘制过程演示\n          </h3>\n          <span style={{\n            fontSize: '0.875rem',\n            color: '#6B7280'\n          }}>\n            步骤 {currentStep + 1} / {mask.drawingSteps.length}\n          </span>\n        </div>\n        \n        {/* 进度条 */}\n        <div style={{\n          width: '100%',\n          height: '6px',\n          backgroundColor: '#E5E7EB',\n          borderRadius: '3px',\n          marginBottom: '1rem',\n          overflow: 'hidden'\n        }}>\n          <div\n            style={{\n              height: '100%',\n              backgroundColor: '#F59E0B',\n              borderRadius: '3px',\n              transition: 'width 0.1s ease',\n              width: `${((currentStep + progress) / mask.drawingSteps.length) * 100}%`\n            }}\n          />\n        </div>\n        \n        {/* 控制按钮 */}\n        <div style={{\n          display: 'flex',\n          gap: '0.5rem',\n          justifyContent: 'center'\n        }}>\n          <button\n            onClick={() => onPlayStateChange(!isPlaying)}\n            style={{\n              backgroundColor: isPlaying ? '#EF4444' : '#10B981',\n              color: 'white',\n              border: 'none',\n              padding: '0.5rem 1rem',\n              borderRadius: '0.5rem',\n              cursor: 'pointer',\n              fontSize: '0.875rem',\n              fontWeight: '500'\n            }}\n          >\n            {isPlaying ? '暂停' : '播放'}\n          </button>\n          \n          <button\n            onClick={resetAnimation}\n            style={{\n              backgroundColor: '#6B7280',\n              color: 'white',\n              border: 'none',\n              padding: '0.5rem 1rem',\n              borderRadius: '0.5rem',\n              cursor: 'pointer',\n              fontSize: '0.875rem',\n              fontWeight: '500'\n            }}\n          >\n            重置\n          </button>\n        </div>\n        \n        {/* 当前步骤描述 */}\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.75rem',\n          backgroundColor: '#F9FAFB',\n          borderRadius: '0.5rem',\n          borderLeft: '4px solid #F59E0B'\n        }}>\n          <p style={{\n            fontSize: '0.875rem',\n            color: '#374151',\n            margin: 0\n          }}>\n            <strong>{mask.drawingSteps[currentStep]?.name}：</strong>\n            {mask.drawingSteps[currentStep]?.description}\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAaO,SAAS,qBAAqB,EACnC,IAAI,EACJ,SAAS,EACT,iBAAiB,EACjB,QAAQ,CAAC,EACT,SAAS,EACiB;IAC1B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAClD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IAE3C,OAAO;IACP,MAAM,iBAAiB;QACrB,eAAe;QACf,YAAY;QACZ,IAAI,YAAY,OAAO,EAAE;YACvB,cAAc,YAAY,OAAO;YACjC,YAAY,OAAO,GAAG;QACxB;QACA,IAAI,aAAa,OAAO,EAAE;YACxB,qBAAqB,aAAa,OAAO;YACzC,aAAa,OAAO,GAAG;QACzB;IACF;IAEA,OAAO;IACP,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,cAAc,KAAK,YAAY,CAAC,MAAM,EAAE;YACvD,MAAM,kBAAkB,KAAK,YAAY,CAAC,YAAY;YACtD,MAAM,eAAe,gBAAgB,QAAQ,GAAG;YAChD,MAAM,YAAY,IAAI,SAAS;YAC/B,MAAM,cAAc,AAAC,eAAe,OAAQ;YAC5C,IAAI,QAAQ;YAEZ,MAAM,UAAU;gBACd;gBACA,MAAM,eAAe,KAAK,GAAG,CAAC,QAAQ,aAAa;gBACnD,YAAY;gBAEZ,IAAI,eAAe,GAAG;oBACpB,aAAa,OAAO,GAAG,sBAAsB;gBAC/C,OAAO;oBACL,gBAAgB;oBAChB,WAAW;wBACT,IAAI,cAAc,KAAK,YAAY,CAAC,MAAM,GAAG,GAAG;4BAC9C,eAAe,CAAA,OAAQ,OAAO;4BAC9B,YAAY;wBACd,OAAO;4BACL,OAAO;4BACP,kBAAkB;wBACpB;oBACF,GAAG,MAAM,OAAO;gBAClB;YACF;YAEA,aAAa,OAAO,GAAG,sBAAsB;QAC/C;QAEA,OAAO;YACL,IAAI,aAAa,OAAO,EAAE;gBACxB,qBAAqB,aAAa,OAAO;YAC3C;QACF;IACF,GAAG;QAAC;QAAW;QAAa;QAAO,KAAK,YAAY;QAAE;KAAkB;IAExE,UAAU;IACV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,aAAa,OAAO,EAAE;YACtC,qBAAqB,aAAa,OAAO;YACzC,aAAa,OAAO,GAAG;QACzB;IACF,GAAG;QAAC;KAAU;IAEd,UAAU;IACV,MAAM,kBAAkB;QACtB,MAAM,QAAQ,KAAK,YAAY,CAAC,KAAK,CAAC,GAAG,cAAc;QACvD,MAAM,kBAAkB,KAAK,YAAY,CAAC,YAAY;QAEtD,qBACE,8OAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,OAAO;gBAAE,YAAY;gBAAS,cAAc;YAAO;;8BAGnD,8OAAC;oBACC,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,MAAK;oBACL,QAAO;oBACP,aAAY;;;;;;gBAIb,MAAM,GAAG,CAAC,CAAC,MAAM;oBAChB,MAAM,gBAAgB,UAAU;oBAChC,MAAM,UAAU,gBAAgB,WAAW;oBAC3C,MAAM,kBAAkB,gBAAgB,GAAG,WAAW,IAAI,IAAI,CAAC,GAAG;oBAElE,qBACE,8OAAC;wBAAgB,SAAS;kCACvB,kBAAkB,MAAM;uBADnB,KAAK,EAAE;;;;;gBAInB;8BAGA,8OAAC;oBACC,GAAE;oBACF,GAAE;oBACF,YAAW;oBACX,UAAS;oBACT,MAAK;oBACL,YAAW;8BAEV,iBAAiB,QAAQ;;;;;;;;;;;;IAIlC;IAEA,kBAAkB;IAClB,MAAM,oBAAoB,CAAC,MAAmB;QAC5C,kBAAkB;QAClB,MAAM,SAAS,KAAK,EAAE;QAEtB,OAAQ,KAAK,IAAI;YACf,KAAK;gBACH,IAAI,WAAW,UAAU;oBACvB,qBACE,8OAAC;wBACC,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,MAAK;wBACL,aAAY;;;;;;gBAGlB,OAAO,IAAI,WAAW,UAAU;oBAC9B,qBACE,8OAAC;wBACC,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,MAAK;wBACL,aAAY;;;;;;gBAGlB,OAAO,IAAI,WAAW,YAAY;oBAChC,qBACE,8OAAC;wBACC,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,MAAK;wBACL,aAAY;;;;;;gBAGlB;gBACA,qBACE,8OAAC;oBACC,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,MAAM,KAAK,KAAK;oBAChB,aAAY;;;;;;YAIlB,KAAK;gBACH,IAAI,WAAW,UAAU;oBACvB,qBACE,8OAAC;;0CACC,8OAAC;gCACC,GAAE;gCACF,QAAO;gCACP,aAAY;gCACZ,MAAK;gCACL,eAAc;gCACd,iBAAiB;;;;;;0CAEnB,8OAAC;gCACC,GAAE;gCACF,QAAO;gCACP,aAAY;gCACZ,MAAK;gCACL,eAAc;gCACd,iBAAiB;;;;;;;;;;;;gBAIzB,OAAO,IAAI,WAAW,UAAU;oBAC9B,qBACE,8OAAC;;0CACC,8OAAC;gCACC,GAAE;gCACF,QAAO;gCACP,aAAY;gCACZ,MAAK;gCACL,iBAAiB;;;;;;0CAEnB,8OAAC;gCACC,GAAE;gCACF,QAAO;gCACP,aAAY;gCACZ,MAAK;gCACL,iBAAiB;;;;;;;;;;;;gBAIzB,OAAO,IAAI,WAAW,YAAY;oBAChC,qBACE,8OAAC;;0CACC,8OAAC;gCACC,GAAE;gCACF,QAAO;gCACP,aAAY;gCACZ,MAAK;gCACL,iBAAiB;;;;;;0CAEnB,8OAAC;gCACC,GAAE;gCACF,QAAO;gCACP,aAAY;gCACZ,MAAK;gCACL,iBAAiB;;;;;;;;;;;;gBAIzB;gBACA,qBACE,8OAAC;;sCACC,8OAAC;4BACC,GAAE;4BACF,QAAQ,KAAK,KAAK;4BAClB,aAAa,KAAK,WAAW,IAAI;4BACjC,MAAK;4BACL,eAAc;4BACd,iBAAiB;;;;;;sCAEnB,8OAAC;4BACC,GAAE;4BACF,QAAQ,KAAK,KAAK;4BAClB,aAAa,KAAK,WAAW,IAAI;4BACjC,MAAK;4BACL,eAAc;4BACd,iBAAiB;;;;;;;;;;;;YAKzB,KAAK;gBACH,IAAI,WAAW,UAAU;oBACvB,qBACE,8OAAC;;0CACC,8OAAC;gCAAQ,IAAG;gCAAM,IAAG;gCAAM,IAAG;gCAAK,IAAG;gCAAK,MAAK;gCAAQ,QAAO;gCAAO,aAAY;;;;;;0CAClF,8OAAC;gCAAQ,IAAG;gCAAM,IAAG;gCAAM,IAAG;gCAAK,IAAG;gCAAK,MAAK;gCAAQ,QAAO;gCAAO,aAAY;;;;;;0CAClF,8OAAC;gCAAO,IAAG;gCAAM,IAAG;gCAAM,GAAE;gCAAI,MAAK;;;;;;0CACrC,8OAAC;gCAAO,IAAG;gCAAM,IAAG;gCAAM,GAAE;gCAAI,MAAK;;;;;;;;;;;;gBAG3C,OAAO,IAAI,WAAW,UAAU;oBAC9B,qBACE,8OAAC;;0CACC,8OAAC;gCAAQ,IAAG;gCAAM,IAAG;gCAAM,IAAG;gCAAK,IAAG;gCAAK,MAAK;;;;;;0CAChD,8OAAC;gCAAQ,IAAG;gCAAM,IAAG;gCAAM,IAAG;gCAAK,IAAG;gCAAK,MAAK;;;;;;0CAChD,8OAAC;gCAAO,IAAG;gCAAM,IAAG;gCAAM,GAAE;gCAAI,MAAK;;;;;;0CACrC,8OAAC;gCAAO,IAAG;gCAAM,IAAG;gCAAM,GAAE;gCAAI,MAAK;;;;;;;;;;;;gBAG3C,OAAO,IAAI,WAAW,YAAY;oBAChC,qBACE,8OAAC;;0CACC,8OAAC;gCAAQ,IAAG;gCAAM,IAAG;gCAAM,IAAG;gCAAK,IAAG;gCAAK,MAAK;;;;;;0CAChD,8OAAC;gCAAQ,IAAG;gCAAM,IAAG;gCAAM,IAAG;gCAAK,IAAG;gCAAK,MAAK;;;;;;0CAChD,8OAAC;gCAAO,IAAG;gCAAM,IAAG;gCAAM,GAAE;gCAAI,MAAK;;;;;;0CACrC,8OAAC;gCAAO,IAAG;gCAAM,IAAG;gCAAM,GAAE;gCAAI,MAAK;;;;;;;;;;;;gBAG3C;gBACA,qBACE,8OAAC;;sCACC,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,QAAQ,KAAK,KAAK;4BAClB,aAAa,KAAK,WAAW,IAAI;4BACjC,MAAK;4BACL,iBAAiB;;;;;;sCAEnB,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,QAAQ,KAAK,KAAK;4BAClB,aAAa,KAAK,WAAW,IAAI;4BACjC,MAAK;4BACL,iBAAiB;;;;;;sCAEnB,8OAAC;4BAAO,IAAG;4BAAM,IAAG;4BAAM,GAAE;4BAAI,MAAM,KAAK,KAAK;;;;;;sCAChD,8OAAC;4BAAO,IAAG;4BAAM,IAAG;4BAAM,GAAE;4BAAI,MAAM,KAAK,KAAK;;;;;;;;;;;;YAItD,KAAK;YACL,KAAK;gBACH,MAAM,YAAY,WAAW,aAAa,UAAW,WAAW,WAAW,SAAS;gBACpF,qBACE,8OAAC;oBACC,GAAE;oBACF,QAAQ;oBACR,aAAa,KAAK,WAAW,IAAI;oBACjC,eAAc;oBACd,iBAAiB;;;;;;YAIvB,KAAK;gBACH,IAAI,WAAW,UAAU;oBACvB,qBACE,8OAAC;kCACC,cAAA,8OAAC;4BACC,GAAE;4BACF,MAAK;4BACL,QAAO;4BACP,aAAY;;;;;;;;;;;gBAIpB,OAAO,IAAI,WAAW,YAAY;oBAChC,qBACE,8OAAC;wBAAO,IAAG;wBAAM,IAAG;wBAAK,GAAE;wBAAI,MAAK;;;;;;gBAExC;gBACA,qBACE,8OAAC;;sCACC,8OAAC;4BACC,GAAE;4BACF,MAAM,KAAK,KAAK;4BAChB,QAAQ,KAAK,KAAK;4BAClB,aAAY;;;;;;sCAEd,8OAAC;4BAAO,IAAG;4BAAM,IAAG;4BAAM,GAAE;4BAAI,MAAM,KAAK,KAAK;;;;;;sCAChD,8OAAC;4BAAO,IAAG;4BAAM,IAAG;4BAAM,GAAE;4BAAI,MAAM,KAAK,KAAK;;;;;;;;;;;;YAItD,KAAK;gBACH,MAAM,aAAa,WAAW,aAAa,UAAU;gBACrD,qBACE,8OAAC;;sCACC,8OAAC;4BACC,GAAE;4BACF,QAAQ;4BACR,aAAa,WAAW,aAAa,IAAI;4BACzC,MAAK;4BACL,iBAAiB;;;;;;sCAEnB,8OAAC;4BACC,GAAE;4BACF,QAAQ;4BACR,aAAa,WAAW,aAAa,IAAI;4BACzC,MAAK;4BACL,iBAAiB;;;;;;;;;;;;YAKzB,KAAK;gBACH,MAAM,WAAW,WAAW,WAAW,YAAa,WAAW,aAAa,YAAY,KAAK,KAAK;gBAClG,qBACE,8OAAC;oBACC,IAAG;oBACH,IAAI,WAAW,aAAa,MAAM;oBAClC,IAAI,WAAW,aAAa,KAAK;oBACjC,IAAI,WAAW,aAAa,KAAK;oBACjC,MAAM;oBACN,QAAQ,WAAW,aAAa,UAAU,KAAK,KAAK;oBACpD,aAAY;;;;;;YAIlB;gBACE,qBACE,8OAAC;oBACC,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,MAAM,KAAK,KAAK;oBAChB,QAAQ,KAAK,KAAK;;;;;;QAG1B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW;QAAW,OAAO;YAAE,UAAU;QAAW;;0BAEvD,8OAAC;gBAAI,OAAO;oBACV,aAAa;oBACb,QAAQ;oBACR,cAAc;oBACd,UAAU;oBACV,iBAAiB;gBACnB;0BACG;;;;;;0BAIH,8OAAC;gBAAI,OAAO;oBACV,WAAW;oBACX,SAAS;oBACT,iBAAiB;oBACjB,cAAc;oBACd,QAAQ;oBACR,WAAW;gBACb;;kCACE,8OAAC;wBAAI,OAAO;4BACV,SAAS;4BACT,gBAAgB;4BAChB,YAAY;4BACZ,cAAc;wBAChB;;0CACE,8OAAC;gCAAG,OAAO;oCACT,UAAU;oCACV,YAAY;oCACZ,OAAO;oCACP,YAAY;gCACd;0CAAG;;;;;;0CAGH,8OAAC;gCAAK,OAAO;oCACX,UAAU;oCACV,OAAO;gCACT;;oCAAG;oCACG,cAAc;oCAAE;oCAAI,KAAK,YAAY,CAAC,MAAM;;;;;;;;;;;;;kCAKpD,8OAAC;wBAAI,OAAO;4BACV,OAAO;4BACP,QAAQ;4BACR,iBAAiB;4BACjB,cAAc;4BACd,cAAc;4BACd,UAAU;wBACZ;kCACE,cAAA,8OAAC;4BACC,OAAO;gCACL,QAAQ;gCACR,iBAAiB;gCACjB,cAAc;gCACd,YAAY;gCACZ,OAAO,GAAG,AAAC,CAAC,cAAc,QAAQ,IAAI,KAAK,YAAY,CAAC,MAAM,GAAI,IAAI,CAAC,CAAC;4BAC1E;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,OAAO;4BACV,SAAS;4BACT,KAAK;4BACL,gBAAgB;wBAClB;;0CACE,8OAAC;gCACC,SAAS,IAAM,kBAAkB,CAAC;gCAClC,OAAO;oCACL,iBAAiB,YAAY,YAAY;oCACzC,OAAO;oCACP,QAAQ;oCACR,SAAS;oCACT,cAAc;oCACd,QAAQ;oCACR,UAAU;oCACV,YAAY;gCACd;0CAEC,YAAY,OAAO;;;;;;0CAGtB,8OAAC;gCACC,SAAS;gCACT,OAAO;oCACL,iBAAiB;oCACjB,OAAO;oCACP,QAAQ;oCACR,SAAS;oCACT,cAAc;oCACd,QAAQ;oCACR,UAAU;oCACV,YAAY;gCACd;0CACD;;;;;;;;;;;;kCAMH,8OAAC;wBAAI,OAAO;4BACV,WAAW;4BACX,SAAS;4BACT,iBAAiB;4BACjB,cAAc;4BACd,YAAY;wBACd;kCACE,cAAA,8OAAC;4BAAE,OAAO;gCACR,UAAU;gCACV,OAAO;gCACP,QAAQ;4BACV;;8CACE,8OAAC;;wCAAQ,KAAK,YAAY,CAAC,YAAY,EAAE;wCAAK;;;;;;;gCAC7C,KAAK,YAAY,CAAC,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 2160, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/utils/maskImageGenerator.ts"], "sourcesContent": ["// 京剧脸谱图片生成器\n// 使用Canvas API生成高质量的脸谱图片\n\nexport interface MaskImageConfig {\n  id: string;\n  name: string;\n  character: string;\n  mainColors: string[];\n  faceShape: 'oval' | 'round' | 'angular';\n  eyeStyle: 'normal' | 'fierce' | 'gentle';\n  decorations: string[];\n}\n\nexport class MaskImageGenerator {\n  private canvas: HTMLCanvasElement;\n  private ctx: CanvasRenderingContext2D;\n\n  constructor(width: number = 300, height: number = 300) {\n    this.canvas = document.createElement('canvas');\n    this.canvas.width = width;\n    this.canvas.height = height;\n    this.ctx = this.canvas.getContext('2d')!;\n  }\n\n  generateMaskImage(config: MaskImageConfig): string {\n    const { ctx, canvas } = this;\n    const centerX = canvas.width / 2;\n    const centerY = canvas.height / 2;\n\n    // 清空画布\n    ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n    // 设置高质量渲染\n    ctx.imageSmoothingEnabled = true;\n    ctx.imageSmoothingQuality = 'high';\n\n    // 设置统一的背景\n    const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, Math.max(canvas.width, canvas.height) / 2);\n    gradient.addColorStop(0, '#FFFFFF');\n    gradient.addColorStop(1, '#F8F9FA');\n    ctx.fillStyle = gradient;\n    ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n    // 绘制脸部轮廓\n    this.drawFaceShape(config, centerX, centerY);\n\n    // 绘制五官（按正确顺序）\n    this.drawEyebrows(config, centerX, centerY);\n    this.drawEyes(config, centerX, centerY);\n    this.drawNose(config, centerX, centerY);\n    this.drawMouth(config, centerX, centerY);\n\n    // 绘制装饰\n    this.drawDecorations(config, centerX, centerY);\n\n    // 移除角色名称绘制，让脸谱图片更加纯净\n    // this.drawCharacterName(config, centerX, centerY);\n\n    return canvas.toDataURL('image/png', 0.95);\n  }\n\n  private drawFaceShape(config: MaskImageConfig, centerX: number, centerY: number) {\n    const { ctx } = this;\n\n    // 创建更精美的渐变效果\n    const gradient = ctx.createRadialGradient(centerX, centerY - 40, 0, centerX, centerY, 130);\n    gradient.addColorStop(0, config.mainColors[0]);\n    gradient.addColorStop(0.7, config.mainColors[1] || config.mainColors[0]);\n    gradient.addColorStop(1, this.darkenColor(config.mainColors[1] || config.mainColors[0], 0.2));\n\n    ctx.fillStyle = gradient;\n    ctx.strokeStyle = '#2D3748';\n    ctx.lineWidth = 4;\n    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';\n    ctx.shadowBlur = 8;\n    ctx.shadowOffsetX = 2;\n    ctx.shadowOffsetY = 2;\n\n    ctx.beginPath();\n    if (config.faceShape === 'oval') {\n      ctx.ellipse(centerX, centerY - 10, 105, 125, 0, 0, 2 * Math.PI);\n    } else if (config.faceShape === 'round') {\n      ctx.arc(centerX, centerY - 5, 115, 0, 2 * Math.PI);\n    } else {\n      // angular - 更精细的棱角脸型\n      ctx.moveTo(centerX - 95, centerY - 110);\n      ctx.lineTo(centerX + 95, centerY - 110);\n      ctx.lineTo(centerX + 115, centerY - 10);\n      ctx.lineTo(centerX + 95, centerY + 125);\n      ctx.lineTo(centerX - 95, centerY + 125);\n      ctx.lineTo(centerX - 115, centerY - 10);\n      ctx.closePath();\n    }\n    ctx.fill();\n    ctx.stroke();\n\n    // 重置阴影\n    ctx.shadowColor = 'transparent';\n    ctx.shadowBlur = 0;\n    ctx.shadowOffsetX = 0;\n    ctx.shadowOffsetY = 0;\n  }\n\n  // 辅助函数：加深颜色\n  private darkenColor(color: string, factor: number): string {\n    if (color.startsWith('#')) {\n      const hex = color.slice(1);\n      const r = parseInt(hex.substr(0, 2), 16);\n      const g = parseInt(hex.substr(2, 2), 16);\n      const b = parseInt(hex.substr(4, 2), 16);\n\n      const newR = Math.floor(r * (1 - factor));\n      const newG = Math.floor(g * (1 - factor));\n      const newB = Math.floor(b * (1 - factor));\n\n      return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;\n    }\n    return color;\n  }\n\n  private drawEyes(config: MaskImageConfig, centerX: number, centerY: number) {\n    const { ctx } = this;\n    const eyeY = centerY - 20;\n    const eyeWidth = config.eyeStyle === 'fierce' ? 25 : 20;\n    const eyeHeight = config.eyeStyle === 'gentle' ? 12 : 15;\n\n    // 左眼\n    ctx.fillStyle = 'white';\n    ctx.strokeStyle = '#000';\n    ctx.lineWidth = 2;\n    ctx.beginPath();\n    ctx.ellipse(centerX - 35, eyeY, eyeWidth, eyeHeight, 0, 0, 2 * Math.PI);\n    ctx.fill();\n    ctx.stroke();\n\n    // 右眼\n    ctx.beginPath();\n    ctx.ellipse(centerX + 35, eyeY, eyeWidth, eyeHeight, 0, 0, 2 * Math.PI);\n    ctx.fill();\n    ctx.stroke();\n\n    // 眼珠\n    ctx.fillStyle = '#000';\n    ctx.beginPath();\n    ctx.arc(centerX - 35, eyeY, 8, 0, 2 * Math.PI);\n    ctx.fill();\n    ctx.beginPath();\n    ctx.arc(centerX + 35, eyeY, 8, 0, 2 * Math.PI);\n    ctx.fill();\n  }\n\n  private drawEyebrows(config: MaskImageConfig, centerX: number, centerY: number) {\n    const { ctx } = this;\n    const browY = centerY - 50;\n    \n    ctx.strokeStyle = '#000';\n    ctx.lineWidth = config.eyeStyle === 'fierce' ? 8 : 5;\n    ctx.lineCap = 'round';\n\n    // 左眉\n    ctx.beginPath();\n    if (config.eyeStyle === 'fierce') {\n      ctx.moveTo(centerX - 60, browY);\n      ctx.quadraticCurveTo(centerX - 35, browY - 15, centerX - 10, browY);\n    } else {\n      ctx.moveTo(centerX - 55, browY);\n      ctx.quadraticCurveTo(centerX - 35, browY - 10, centerX - 15, browY);\n    }\n    ctx.stroke();\n\n    // 右眉\n    ctx.beginPath();\n    if (config.eyeStyle === 'fierce') {\n      ctx.moveTo(centerX + 10, browY);\n      ctx.quadraticCurveTo(centerX + 35, browY - 15, centerX + 60, browY);\n    } else {\n      ctx.moveTo(centerX + 15, browY);\n      ctx.quadraticCurveTo(centerX + 35, browY - 10, centerX + 55, browY);\n    }\n    ctx.stroke();\n  }\n\n  private drawNose(config: MaskImageConfig, centerX: number, centerY: number) {\n    const { ctx } = this;\n    \n    ctx.strokeStyle = '#000';\n    ctx.lineWidth = 3;\n    ctx.lineCap = 'round';\n\n    ctx.beginPath();\n    ctx.moveTo(centerX, centerY + 10);\n    ctx.lineTo(centerX, centerY + 40);\n    ctx.moveTo(centerX - 8, centerY + 35);\n    ctx.lineTo(centerX + 8, centerY + 35);\n    ctx.stroke();\n  }\n\n  private drawMouth(config: MaskImageConfig, centerX: number, centerY: number) {\n    const { ctx } = this;\n    const mouthY = centerY + 60;\n    \n    ctx.fillStyle = config.mainColors[0] === '#FFFFFF' ? '#FF0000' : '#8B0000';\n    ctx.strokeStyle = '#000';\n    ctx.lineWidth = 2;\n\n    ctx.beginPath();\n    ctx.ellipse(centerX, mouthY, 15, 8, 0, 0, 2 * Math.PI);\n    ctx.fill();\n    ctx.stroke();\n  }\n\n  private drawDecorations(config: MaskImageConfig, centerX: number, centerY: number) {\n    const { ctx } = this;\n    \n    // 根据角色添加特殊装饰\n    if (config.decorations.includes('forehead-mark')) {\n      ctx.fillStyle = '#FFD700';\n      ctx.strokeStyle = '#000';\n      ctx.lineWidth = 2;\n      ctx.beginPath();\n      ctx.moveTo(centerX, centerY - 80);\n      ctx.lineTo(centerX + 10, centerY - 60);\n      ctx.lineTo(centerX - 10, centerY - 60);\n      ctx.closePath();\n      ctx.fill();\n      ctx.stroke();\n    }\n\n    if (config.decorations.includes('cheek-patterns')) {\n      ctx.strokeStyle = config.mainColors[1] || '#000';\n      ctx.lineWidth = 2;\n      \n      // 左脸装饰\n      ctx.beginPath();\n      ctx.arc(centerX - 70, centerY + 20, 15, 0, 2 * Math.PI);\n      ctx.stroke();\n      \n      // 右脸装饰\n      ctx.beginPath();\n      ctx.arc(centerX + 70, centerY + 20, 15, 0, 2 * Math.PI);\n      ctx.stroke();\n    }\n\n    if (config.decorations.includes('beard')) {\n      ctx.strokeStyle = '#000';\n      ctx.lineWidth = 4;\n      ctx.lineCap = 'round';\n      \n      // 胡须线条\n      for (let i = 0; i < 3; i++) {\n        ctx.beginPath();\n        ctx.moveTo(centerX - 60 + i * 20, centerY + 80 + i * 5);\n        ctx.quadraticCurveTo(centerX, centerY + 100 + i * 5, centerX + 60 - i * 20, centerY + 80 + i * 5);\n        ctx.stroke();\n      }\n    }\n  }\n\n  // 已移除角色名称绘制函数，让脸谱图片更加纯净\n  // private drawCharacterName(config: MaskImageConfig, centerX: number, centerY: number) {\n  //   // 功能已移除，角色名称现在显示在卡片的文字区域\n  // }\n}\n\n// 预定义的脸谱配置\nexport const maskConfigs: Record<string, MaskImageConfig> = {\n  guanyu: {\n    id: 'guanyu',\n    name: '关羽',\n    character: '关羽',\n    mainColors: ['#DC2626', '#991B1B'],\n    faceShape: 'oval',\n    eyeStyle: 'fierce',\n    decorations: ['forehead-mark', 'beard']\n  },\n  caocao: {\n    id: 'caocao',\n    name: '曹操',\n    character: '曹操',\n    mainColors: ['#FFFFFF', '#F3F4F6'],\n    faceShape: 'angular',\n    eyeStyle: 'fierce',\n    decorations: ['cheek-patterns']\n  },\n  zhangfei: {\n    id: 'zhangfei',\n    name: '张飞',\n    character: '张飞',\n    mainColors: ['#1F2937', '#000000'],\n    faceShape: 'round',\n    eyeStyle: 'fierce',\n    decorations: ['beard']\n  },\n  huangzhong: {\n    id: 'huangzhong',\n    name: '黄忠',\n    character: '黄忠',\n    mainColors: ['#FFD700', '#F59E0B'],\n    faceShape: 'oval',\n    eyeStyle: 'normal',\n    decorations: ['forehead-mark', 'beard']\n  },\n  diaochan: {\n    id: 'diaochan',\n    name: '貂蝉',\n    character: '貂蝉',\n    mainColors: ['#FFC0CB', '#F8BBD9'],\n    faceShape: 'oval',\n    eyeStyle: 'gentle',\n    decorations: ['forehead-mark']\n  },\n  baozhen: {\n    id: 'baozhen',\n    name: '包拯',\n    character: '包拯',\n    mainColors: ['#000000', '#1F2937'],\n    faceShape: 'round',\n    eyeStyle: 'fierce',\n    decorations: ['forehead-mark']\n  },\n  douerdun: {\n    id: 'douerdun',\n    name: '窦尔敦',\n    character: '窦尔敦',\n    mainColors: ['#1E40AF', '#3B82F6'],\n    faceShape: 'angular',\n    eyeStyle: 'fierce',\n    decorations: ['cheek-patterns']\n  },\n  dianwei: {\n    id: 'dianwei',\n    name: '典韦',\n    character: '典韦',\n    mainColors: ['#7C2D12', '#DC2626'],\n    faceShape: 'round',\n    eyeStyle: 'fierce',\n    decorations: ['beard']\n  },\n  likui: {\n    id: 'likui',\n    name: '李逵',\n    character: '李逵',\n    mainColors: ['#1F2937', '#000000'],\n    faceShape: 'round',\n    eyeStyle: 'fierce',\n    decorations: ['beard']\n  },\n  sunwukong: {\n    id: 'sunwukong',\n    name: '孙悟空',\n    character: '孙悟空',\n    mainColors: ['#F59E0B', '#FFD700'],\n    faceShape: 'oval',\n    eyeStyle: 'fierce',\n    decorations: ['forehead-mark', 'cheek-patterns']\n  },\n  zhubaijie: {\n    id: 'zhubaijie',\n    name: '猪八戒',\n    character: '猪八戒',\n    mainColors: ['#EC4899', '#F472B6'],\n    faceShape: 'round',\n    eyeStyle: 'normal',\n    decorations: ['cheek-patterns']\n  },\n  baigu: {\n    id: 'baigu',\n    name: '白骨精',\n    character: '白骨精',\n    mainColors: ['#F3F4F6', '#E5E7EB'],\n    faceShape: 'angular',\n    eyeStyle: 'fierce',\n    decorations: ['forehead-mark']\n  },\n  huajiangjun: {\n    id: 'huajiangjun',\n    name: '花脸将军',\n    character: '花脸将军',\n    mainColors: ['#7C3AED', '#A855F7'],\n    faceShape: 'oval',\n    eyeStyle: 'fierce',\n    decorations: ['forehead-mark', 'cheek-patterns']\n  },\n  qingyi: {\n    id: 'qingyi',\n    name: '青衣花旦',\n    character: '青衣',\n    mainColors: ['#10B981', '#34D399'],\n    faceShape: 'oval',\n    eyeStyle: 'gentle',\n    decorations: ['forehead-mark']\n  },\n  xiaosheng: {\n    id: 'xiaosheng',\n    name: '小生角色',\n    character: '小生',\n    mainColors: ['#3B82F6', '#60A5FA'],\n    faceShape: 'oval',\n    eyeStyle: 'gentle',\n    decorations: []\n  }\n};\n"], "names": [], "mappings": "AAAA,YAAY;AACZ,yBAAyB;;;;;AAYlB,MAAM;IACH,OAA0B;IAC1B,IAA8B;IAEtC,YAAY,QAAgB,GAAG,EAAE,SAAiB,GAAG,CAAE;QACrD,IAAI,CAAC,MAAM,GAAG,SAAS,aAAa,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG;QACpB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;QACrB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IACpC;IAEA,kBAAkB,MAAuB,EAAU;QACjD,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,IAAI;QAC5B,MAAM,UAAU,OAAO,KAAK,GAAG;QAC/B,MAAM,UAAU,OAAO,MAAM,GAAG;QAEhC,OAAO;QACP,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QAE/C,UAAU;QACV,IAAI,qBAAqB,GAAG;QAC5B,IAAI,qBAAqB,GAAG;QAE5B,UAAU;QACV,MAAM,WAAW,IAAI,oBAAoB,CAAC,SAAS,SAAS,GAAG,SAAS,SAAS,KAAK,GAAG,CAAC,OAAO,KAAK,EAAE,OAAO,MAAM,IAAI;QACzH,SAAS,YAAY,CAAC,GAAG;QACzB,SAAS,YAAY,CAAC,GAAG;QACzB,IAAI,SAAS,GAAG;QAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QAE9C,SAAS;QACT,IAAI,CAAC,aAAa,CAAC,QAAQ,SAAS;QAEpC,cAAc;QACd,IAAI,CAAC,YAAY,CAAC,QAAQ,SAAS;QACnC,IAAI,CAAC,QAAQ,CAAC,QAAQ,SAAS;QAC/B,IAAI,CAAC,QAAQ,CAAC,QAAQ,SAAS;QAC/B,IAAI,CAAC,SAAS,CAAC,QAAQ,SAAS;QAEhC,OAAO;QACP,IAAI,CAAC,eAAe,CAAC,QAAQ,SAAS;QAEtC,qBAAqB;QACrB,oDAAoD;QAEpD,OAAO,OAAO,SAAS,CAAC,aAAa;IACvC;IAEQ,cAAc,MAAuB,EAAE,OAAe,EAAE,OAAe,EAAE;QAC/E,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI;QAEpB,aAAa;QACb,MAAM,WAAW,IAAI,oBAAoB,CAAC,SAAS,UAAU,IAAI,GAAG,SAAS,SAAS;QACtF,SAAS,YAAY,CAAC,GAAG,OAAO,UAAU,CAAC,EAAE;QAC7C,SAAS,YAAY,CAAC,KAAK,OAAO,UAAU,CAAC,EAAE,IAAI,OAAO,UAAU,CAAC,EAAE;QACvE,SAAS,YAAY,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,UAAU,CAAC,EAAE,IAAI,OAAO,UAAU,CAAC,EAAE,EAAE;QAExF,IAAI,SAAS,GAAG;QAChB,IAAI,WAAW,GAAG;QAClB,IAAI,SAAS,GAAG;QAChB,IAAI,WAAW,GAAG;QAClB,IAAI,UAAU,GAAG;QACjB,IAAI,aAAa,GAAG;QACpB,IAAI,aAAa,GAAG;QAEpB,IAAI,SAAS;QACb,IAAI,OAAO,SAAS,KAAK,QAAQ;YAC/B,IAAI,OAAO,CAAC,SAAS,UAAU,IAAI,KAAK,KAAK,GAAG,GAAG,IAAI,KAAK,EAAE;QAChE,OAAO,IAAI,OAAO,SAAS,KAAK,SAAS;YACvC,IAAI,GAAG,CAAC,SAAS,UAAU,GAAG,KAAK,GAAG,IAAI,KAAK,EAAE;QACnD,OAAO;YACL,qBAAqB;YACrB,IAAI,MAAM,CAAC,UAAU,IAAI,UAAU;YACnC,IAAI,MAAM,CAAC,UAAU,IAAI,UAAU;YACnC,IAAI,MAAM,CAAC,UAAU,KAAK,UAAU;YACpC,IAAI,MAAM,CAAC,UAAU,IAAI,UAAU;YACnC,IAAI,MAAM,CAAC,UAAU,IAAI,UAAU;YACnC,IAAI,MAAM,CAAC,UAAU,KAAK,UAAU;YACpC,IAAI,SAAS;QACf;QACA,IAAI,IAAI;QACR,IAAI,MAAM;QAEV,OAAO;QACP,IAAI,WAAW,GAAG;QAClB,IAAI,UAAU,GAAG;QACjB,IAAI,aAAa,GAAG;QACpB,IAAI,aAAa,GAAG;IACtB;IAEA,YAAY;IACJ,YAAY,KAAa,EAAE,MAAc,EAAU;QACzD,IAAI,MAAM,UAAU,CAAC,MAAM;YACzB,MAAM,MAAM,MAAM,KAAK,CAAC;YACxB,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;YACrC,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;YACrC,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;YAErC,MAAM,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM;YACvC,MAAM,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM;YACvC,MAAM,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM;YAEvC,OAAO,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,OAAO,KAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,OAAO,KAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,MAAM;QAC3H;QACA,OAAO;IACT;IAEQ,SAAS,MAAuB,EAAE,OAAe,EAAE,OAAe,EAAE;QAC1E,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI;QACpB,MAAM,OAAO,UAAU;QACvB,MAAM,WAAW,OAAO,QAAQ,KAAK,WAAW,KAAK;QACrD,MAAM,YAAY,OAAO,QAAQ,KAAK,WAAW,KAAK;QAEtD,KAAK;QACL,IAAI,SAAS,GAAG;QAChB,IAAI,WAAW,GAAG;QAClB,IAAI,SAAS,GAAG;QAChB,IAAI,SAAS;QACb,IAAI,OAAO,CAAC,UAAU,IAAI,MAAM,UAAU,WAAW,GAAG,GAAG,IAAI,KAAK,EAAE;QACtE,IAAI,IAAI;QACR,IAAI,MAAM;QAEV,KAAK;QACL,IAAI,SAAS;QACb,IAAI,OAAO,CAAC,UAAU,IAAI,MAAM,UAAU,WAAW,GAAG,GAAG,IAAI,KAAK,EAAE;QACtE,IAAI,IAAI;QACR,IAAI,MAAM;QAEV,KAAK;QACL,IAAI,SAAS,GAAG;QAChB,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,UAAU,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,EAAE;QAC7C,IAAI,IAAI;QACR,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,UAAU,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,EAAE;QAC7C,IAAI,IAAI;IACV;IAEQ,aAAa,MAAuB,EAAE,OAAe,EAAE,OAAe,EAAE;QAC9E,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI;QACpB,MAAM,QAAQ,UAAU;QAExB,IAAI,WAAW,GAAG;QAClB,IAAI,SAAS,GAAG,OAAO,QAAQ,KAAK,WAAW,IAAI;QACnD,IAAI,OAAO,GAAG;QAEd,KAAK;QACL,IAAI,SAAS;QACb,IAAI,OAAO,QAAQ,KAAK,UAAU;YAChC,IAAI,MAAM,CAAC,UAAU,IAAI;YACzB,IAAI,gBAAgB,CAAC,UAAU,IAAI,QAAQ,IAAI,UAAU,IAAI;QAC/D,OAAO;YACL,IAAI,MAAM,CAAC,UAAU,IAAI;YACzB,IAAI,gBAAgB,CAAC,UAAU,IAAI,QAAQ,IAAI,UAAU,IAAI;QAC/D;QACA,IAAI,MAAM;QAEV,KAAK;QACL,IAAI,SAAS;QACb,IAAI,OAAO,QAAQ,KAAK,UAAU;YAChC,IAAI,MAAM,CAAC,UAAU,IAAI;YACzB,IAAI,gBAAgB,CAAC,UAAU,IAAI,QAAQ,IAAI,UAAU,IAAI;QAC/D,OAAO;YACL,IAAI,MAAM,CAAC,UAAU,IAAI;YACzB,IAAI,gBAAgB,CAAC,UAAU,IAAI,QAAQ,IAAI,UAAU,IAAI;QAC/D;QACA,IAAI,MAAM;IACZ;IAEQ,SAAS,MAAuB,EAAE,OAAe,EAAE,OAAe,EAAE;QAC1E,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI;QAEpB,IAAI,WAAW,GAAG;QAClB,IAAI,SAAS,GAAG;QAChB,IAAI,OAAO,GAAG;QAEd,IAAI,SAAS;QACb,IAAI,MAAM,CAAC,SAAS,UAAU;QAC9B,IAAI,MAAM,CAAC,SAAS,UAAU;QAC9B,IAAI,MAAM,CAAC,UAAU,GAAG,UAAU;QAClC,IAAI,MAAM,CAAC,UAAU,GAAG,UAAU;QAClC,IAAI,MAAM;IACZ;IAEQ,UAAU,MAAuB,EAAE,OAAe,EAAE,OAAe,EAAE;QAC3E,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI;QACpB,MAAM,SAAS,UAAU;QAEzB,IAAI,SAAS,GAAG,OAAO,UAAU,CAAC,EAAE,KAAK,YAAY,YAAY;QACjE,IAAI,WAAW,GAAG;QAClB,IAAI,SAAS,GAAG;QAEhB,IAAI,SAAS;QACb,IAAI,OAAO,CAAC,SAAS,QAAQ,IAAI,GAAG,GAAG,GAAG,IAAI,KAAK,EAAE;QACrD,IAAI,IAAI;QACR,IAAI,MAAM;IACZ;IAEQ,gBAAgB,MAAuB,EAAE,OAAe,EAAE,OAAe,EAAE;QACjF,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI;QAEpB,aAAa;QACb,IAAI,OAAO,WAAW,CAAC,QAAQ,CAAC,kBAAkB;YAChD,IAAI,SAAS,GAAG;YAChB,IAAI,WAAW,GAAG;YAClB,IAAI,SAAS,GAAG;YAChB,IAAI,SAAS;YACb,IAAI,MAAM,CAAC,SAAS,UAAU;YAC9B,IAAI,MAAM,CAAC,UAAU,IAAI,UAAU;YACnC,IAAI,MAAM,CAAC,UAAU,IAAI,UAAU;YACnC,IAAI,SAAS;YACb,IAAI,IAAI;YACR,IAAI,MAAM;QACZ;QAEA,IAAI,OAAO,WAAW,CAAC,QAAQ,CAAC,mBAAmB;YACjD,IAAI,WAAW,GAAG,OAAO,UAAU,CAAC,EAAE,IAAI;YAC1C,IAAI,SAAS,GAAG;YAEhB,OAAO;YACP,IAAI,SAAS;YACb,IAAI,GAAG,CAAC,UAAU,IAAI,UAAU,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE;YACtD,IAAI,MAAM;YAEV,OAAO;YACP,IAAI,SAAS;YACb,IAAI,GAAG,CAAC,UAAU,IAAI,UAAU,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE;YACtD,IAAI,MAAM;QACZ;QAEA,IAAI,OAAO,WAAW,CAAC,QAAQ,CAAC,UAAU;YACxC,IAAI,WAAW,GAAG;YAClB,IAAI,SAAS,GAAG;YAChB,IAAI,OAAO,GAAG;YAEd,OAAO;YACP,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBAC1B,IAAI,SAAS;gBACb,IAAI,MAAM,CAAC,UAAU,KAAK,IAAI,IAAI,UAAU,KAAK,IAAI;gBACrD,IAAI,gBAAgB,CAAC,SAAS,UAAU,MAAM,IAAI,GAAG,UAAU,KAAK,IAAI,IAAI,UAAU,KAAK,IAAI;gBAC/F,IAAI,MAAM;YACZ;QACF;IACF;AAMF;AAGO,MAAM,cAA+C;IAC1D,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;YAAiB;SAAQ;IACzC;IACA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;SAAiB;IACjC;IACA,UAAU;QACR,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;SAAQ;IACxB;IACA,YAAY;QACV,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;YAAiB;SAAQ;IACzC;IACA,UAAU;QACR,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;SAAgB;IAChC;IACA,SAAS;QACP,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;SAAgB;IAChC;IACA,UAAU;QACR,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;SAAiB;IACjC;IACA,SAAS;QACP,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;SAAQ;IACxB;IACA,OAAO;QACL,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;SAAQ;IACxB;IACA,WAAW;QACT,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;YAAiB;SAAiB;IAClD;IACA,WAAW;QACT,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;SAAiB;IACjC;IACA,OAAO;QACL,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;SAAgB;IAChC;IACA,aAAa;QACX,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;YAAiB;SAAiB;IAClD;IACA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa;YAAC;SAAgB;IAChC;IACA,WAAW;QACT,IAAI;QACJ,MAAM;QACN,WAAW;QACX,YAAY;YAAC;YAAW;SAAU;QAClC,WAAW;QACX,UAAU;QACV,aAAa,EAAE;IACjB;AACF", "debugId": null}}, {"offset": {"line": 2590, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/mask/MaskImage.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { OperaMask } from '@/types/mask';\nimport { MaskImageGenerator, maskConfigs } from '@/utils/maskImageGenerator';\n\ninterface MaskImageProps {\n  mask: OperaMask;\n  width?: number;\n  height?: number;\n  className?: string;\n}\n\n// 真实脸谱图片URL映射\n// 为特定角色使用真实的京剧脸谱图片\nconst maskImageUrls: Record<string, string> = {\n  // 已添加真实脸谱图片的角色\n  'guanyu': 'https://d.bmcx.com/lianpu/d/0072.jpg', // 关羽红脸真实图片\n  'baogong': 'https://d.bmcx.com/lianpu/d/0018.jpg', // 包拯黑脸真实图片\n  'caocao': 'https://d.bmcx.com/lianpu/d/0028.jpg', // 曹操白脸真实图片\n  'zhangfei': 'https://d.bmcx.com/lianpu/d/0324.jpg', // 张飞黑脸真实图片\n  'doulujin': 'https://d.bmcx.com/lianpu/d/0056.jpg', // 窦尔敦蓝脸真实图片\n  'yangqilang': 'https://img1.baidu.com/it/u=348325659,1868481632&fm=253&fmt=auto&app=138&f=JPEG?w=351&h=441', // 杨七郎真实图片\n  'jianggan': 'https://d.bmcx.com/lianpu/d/0117.jpg', // 蒋干白脸真实图片\n  'liubei': 'https://img1.baidu.com/it/u=93431987,3113680563&fm=253&fmt=auto&app=138&f=JPEG?w=412&h=502', // 刘备真实图片\n  'sunwukong': 'https://d.bmcx.com/lianpu/d/0234.jpg', // 孙悟空金脸真实图片\n\n  // 其他角色暂时使用Canvas生成，后续可以逐步替换\n  'huangzhong': '', // 黄忠黄脸 - 使用Canvas生成\n  'diaochan': '', // 貂蝉 - 使用Canvas生成\n  'dianwei': '', // 典韦 - 使用Canvas生成\n  'likui': '', // 李逵 - 使用Canvas生成\n  'zhubaijie': '', // 猪八戒 - 使用Canvas生成\n  'baigu': '', // 白骨精 - 使用Canvas生成\n  'huajiangjun': '', // 花脸将军 - 使用Canvas生成\n  'qingyi': '', // 青衣花旦 - 使用Canvas生成\n  'xiaosheng': '', // 小生角色 - 使用Canvas生成\n  'yangguifei': '', // 杨贵妃 - 使用Canvas生成\n  'machao': '' // 马超 - 使用Canvas生成\n};\n\nexport function MaskImage({ mask, width = 300, height = 300, className }: MaskImageProps) {\n  const [canvasImage, setCanvasImage] = useState<string | null>(null);\n  const [realImage, setRealImage] = useState<string | null>(null);\n  const [imageError, setImageError] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // 获取真实脸谱图片URL\n  const realImageUrl = maskImageUrls[mask.id];\n\n  // 生成Canvas图片作为后备方案\n  useEffect(() => {\n    const config = maskConfigs[mask.id];\n    if (config) {\n      const generator = new MaskImageGenerator(width, height);\n      const generatedImage = generator.generateMaskImage(config);\n      setCanvasImage(generatedImage);\n    } else {\n      // 如果没有配置，使用默认配置\n      const defaultConfig = {\n        id: mask.id,\n        name: mask.name,\n        character: mask.character,\n        mainColors: mask.mainColors,\n        faceShape: 'oval' as const,\n        eyeStyle: 'normal' as const,\n        decorations: ['forehead-mark']\n      };\n      const generator = new MaskImageGenerator(width, height);\n      const generatedImage = generator.generateMaskImage(defaultConfig);\n      setCanvasImage(generatedImage);\n    }\n  }, [mask.id, mask.name, mask.character, mask.mainColors, width, height]);\n\n  // 如果有真实图片URL，尝试加载真实图片\n  useEffect(() => {\n    if (realImageUrl) {\n      const img = new Image();\n      // 对于外部图片，尝试不设置crossOrigin以避免CORS问题\n      if (realImageUrl.startsWith('http')) {\n        // 外部图片不设置crossOrigin\n      } else {\n        img.crossOrigin = 'anonymous';\n      }\n\n      // 设置加载超时\n      const timeout = setTimeout(() => {\n        setImageError(true);\n        setRealImage(null);\n        setIsLoading(false);\n        console.warn(`图片加载超时: ${realImageUrl}`);\n      }, 5000); // 5秒超时\n\n      img.onload = () => {\n        clearTimeout(timeout);\n        setRealImage(realImageUrl);\n        setImageError(false);\n        setIsLoading(false);\n        console.log(`✅ 成功加载真实脸谱图片: ${mask.name} - ${realImageUrl}`);\n      };\n\n      img.onerror = (error) => {\n        clearTimeout(timeout);\n        setImageError(true);\n        setRealImage(null);\n        setIsLoading(false);\n        console.warn(`❌ 真实脸谱图片加载失败，回退到Canvas生成: ${mask.name} - ${realImageUrl}`, error);\n      };\n\n      console.log(`🔄 开始加载真实脸谱图片: ${mask.name} - ${realImageUrl}`);\n      img.src = realImageUrl;\n\n      // 清理函数\n      return () => {\n        clearTimeout(timeout);\n      };\n    } else {\n      // 没有真实图片URL，直接使用Canvas生成的图片\n      setIsLoading(false);\n    }\n  }, [realImageUrl, mask.name]);\n\n  // 如果正在加载，显示加载状态\n  if (isLoading) {\n    return renderLoadingState();\n  }\n\n  // 优先使用真实图片，如果加载失败或没有真实图片则使用Canvas生成的图片\n  if (realImage && !imageError) {\n    return renderRealImage();\n  } else if (canvasImage) {\n    return renderCanvasImage();\n  } else {\n    return renderLoadingState();\n  }\n\n  // 渲染加载状态\n  function renderLoadingState() {\n    return (\n      <div\n        className={className}\n        style={{\n          width,\n          height,\n          position: 'relative',\n          overflow: 'hidden',\n          borderRadius: '0.5rem',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          backgroundColor: '#F3F4F6',\n          color: '#9CA3AF'\n        }}\n      >\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🎭</div>\n          <div style={{ fontSize: '0.875rem' }}>生成中...</div>\n        </div>\n      </div>\n    );\n  }\n\n  // 真实图片渲染\n  function renderRealImage() {\n    return (\n      <div\n        className={className}\n        style={{\n          width,\n          height,\n          position: 'relative',\n          overflow: 'hidden',\n          borderRadius: '0.5rem',\n          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',\n          transition: 'transform 0.3s ease, box-shadow 0.3s ease'\n        }}\n        title={`${mask.name} - 真实脸谱图片`}\n      >\n        <img\n          src={realImage!}\n          alt={`${mask.name} - ${mask.character}`}\n          style={{\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover',\n            display: 'block'\n          }}\n        />\n\n        {/* 真实图片标识 */}\n        <div\n          style={{\n            position: 'absolute',\n            top: '8px',\n            right: '8px',\n            background: 'rgba(34, 197, 94, 0.8)',\n            color: 'white',\n            fontSize: '10px',\n            padding: '2px 6px',\n            borderRadius: '4px',\n            fontWeight: 'bold'\n          }}\n        >\n          真实脸谱\n        </div>\n\n        {/* 统一的悬停效果 */}\n        <div\n          style={{\n            position: 'absolute',\n            inset: 0,\n            background: 'transparent',\n            transition: 'background 0.3s ease'\n          }}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.background = 'rgba(0, 0, 0, 0.1)';\n            e.currentTarget.parentElement!.style.transform = 'scale(1.02)';\n            e.currentTarget.parentElement!.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.2)';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.background = 'transparent';\n            e.currentTarget.parentElement!.style.transform = 'scale(1)';\n            e.currentTarget.parentElement!.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';\n          }}\n        />\n      </div>\n    );\n  }\n\n  // Canvas生成的图片渲染\n  function renderCanvasImage() {\n    return (\n      <div\n        className={className}\n        style={{\n          width,\n          height,\n          position: 'relative',\n          overflow: 'hidden',\n          borderRadius: '0.5rem',\n          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',\n          transition: 'transform 0.3s ease, box-shadow 0.3s ease'\n        }}\n        title={`${mask.name} - Canvas生成脸谱`}\n      >\n        <img\n          src={canvasImage!}\n          alt={`${mask.name} - ${mask.character}`}\n          style={{\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover',\n            display: 'block'\n          }}\n        />\n\n        {/* Canvas生成标识 */}\n        <div\n          style={{\n            position: 'absolute',\n            top: '8px',\n            right: '8px',\n            background: 'rgba(59, 130, 246, 0.8)',\n            color: 'white',\n            fontSize: '10px',\n            padding: '2px 6px',\n            borderRadius: '4px',\n            fontWeight: 'bold'\n          }}\n        >\n          AI生成\n        </div>\n\n        {/* 统一的悬停效果 */}\n        <div\n          style={{\n            position: 'absolute',\n            inset: 0,\n            background: 'transparent',\n            transition: 'background 0.3s ease'\n          }}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.background = 'rgba(0, 0, 0, 0.1)';\n            e.currentTarget.parentElement!.style.transform = 'scale(1.02)';\n            e.currentTarget.parentElement!.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.2)';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.background = 'transparent';\n            e.currentTarget.parentElement!.style.transform = 'scale(1)';\n            e.currentTarget.parentElement!.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';\n          }}\n        />\n      </div>\n    );\n  }\n\n  // SVG后备方案\n  function generateFallbackSVG() {\n    const baseProps = {\n      width,\n      height,\n      viewBox: \"0 0 300 300\",\n      className,\n      style: { borderRadius: '0.5rem' }\n    };\n\n    // 简化的SVG后备方案\n    return (\n      <svg {...baseProps}>\n        <defs>\n          <radialGradient id={`gradient-${mask.id}`} cx=\"50%\" cy=\"40%\" r=\"60%\">\n            <stop offset=\"0%\" stopColor={mask.mainColors[0]} />\n            <stop offset=\"100%\" stopColor={mask.mainColors[1] || mask.mainColors[0]} />\n          </radialGradient>\n        </defs>\n\n        {/* 脸部轮廓 */}\n        <ellipse\n          cx=\"150\"\n          cy=\"150\"\n          rx=\"120\"\n          ry=\"140\"\n          fill={`url(#gradient-${mask.id})`}\n          stroke=\"#333\"\n          strokeWidth=\"3\"\n        />\n\n        {/* 基础五官 */}\n        <path d=\"M 90 110 Q 120 90 150 110\" stroke=\"#000\" strokeWidth=\"4\" fill=\"none\"/>\n        <path d=\"M 150 110 Q 180 90 210 110\" stroke=\"#000\" strokeWidth=\"4\" fill=\"none\"/>\n        <ellipse cx=\"120\" cy=\"140\" rx=\"15\" ry=\"10\" fill=\"white\" stroke=\"#000\" strokeWidth=\"2\"/>\n        <ellipse cx=\"180\" cy=\"140\" rx=\"15\" ry=\"10\" fill=\"white\" stroke=\"#000\" strokeWidth=\"2\"/>\n        <circle cx=\"120\" cy=\"140\" r=\"6\" fill=\"#000\"/>\n        <circle cx=\"180\" cy=\"140\" r=\"6\" fill=\"#000\"/>\n        <path d=\"M 150 160 L 150 180\" stroke=\"#000\" strokeWidth=\"3\"/>\n        <ellipse cx=\"150\" cy=\"200\" rx=\"10\" ry=\"6\" fill=\"#000\"/>\n\n        {/* 角色名称 */}\n        <text\n          x=\"150\"\n          y=\"260\"\n          textAnchor=\"middle\"\n          fill=\"white\"\n          fontSize=\"24\"\n          fontWeight=\"bold\"\n          fontFamily=\"serif\"\n          stroke=\"#000\"\n          strokeWidth=\"1\"\n        >\n          {mask.character}\n        </text>\n      </svg>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAaA,cAAc;AACd,mBAAmB;AACnB,MAAM,gBAAwC;IAC5C,eAAe;IACf,UAAU;IACV,WAAW;IACX,UAAU;IACV,YAAY;IACZ,YAAY;IACZ,cAAc;IACd,YAAY;IACZ,UAAU;IACV,aAAa;IAEb,4BAA4B;IAC5B,cAAc;IACd,YAAY;IACZ,WAAW;IACX,SAAS;IACT,aAAa;IACb,SAAS;IACT,eAAe;IACf,UAAU;IACV,aAAa;IACb,cAAc;IACd,UAAU,GAAG,kBAAkB;AACjC;AAEO,SAAS,UAAU,EAAE,IAAI,EAAE,QAAQ,GAAG,EAAE,SAAS,GAAG,EAAE,SAAS,EAAkB;IACtF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,cAAc;IACd,MAAM,eAAe,aAAa,CAAC,KAAK,EAAE,CAAC;IAE3C,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,kIAAA,CAAA,cAAW,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,QAAQ;YACV,MAAM,YAAY,IAAI,kIAAA,CAAA,qBAAkB,CAAC,OAAO;YAChD,MAAM,iBAAiB,UAAU,iBAAiB,CAAC;YACnD,eAAe;QACjB,OAAO;YACL,gBAAgB;YAChB,MAAM,gBAAgB;gBACpB,IAAI,KAAK,EAAE;gBACX,MAAM,KAAK,IAAI;gBACf,WAAW,KAAK,SAAS;gBACzB,YAAY,KAAK,UAAU;gBAC3B,WAAW;gBACX,UAAU;gBACV,aAAa;oBAAC;iBAAgB;YAChC;YACA,MAAM,YAAY,IAAI,kIAAA,CAAA,qBAAkB,CAAC,OAAO;YAChD,MAAM,iBAAiB,UAAU,iBAAiB,CAAC;YACnD,eAAe;QACjB;IACF,GAAG;QAAC,KAAK,EAAE;QAAE,KAAK,IAAI;QAAE,KAAK,SAAS;QAAE,KAAK,UAAU;QAAE;QAAO;KAAO;IAEvE,sBAAsB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc;YAChB,MAAM,MAAM,IAAI;YAChB,mCAAmC;YACnC,IAAI,aAAa,UAAU,CAAC,SAAS;YACnC,qBAAqB;YACvB,OAAO;gBACL,IAAI,WAAW,GAAG;YACpB;YAEA,SAAS;YACT,MAAM,UAAU,WAAW;gBACzB,cAAc;gBACd,aAAa;gBACb,aAAa;gBACb,QAAQ,IAAI,CAAC,CAAC,QAAQ,EAAE,cAAc;YACxC,GAAG,OAAO,OAAO;YAEjB,IAAI,MAAM,GAAG;gBACX,aAAa;gBACb,aAAa;gBACb,cAAc;gBACd,aAAa;gBACb,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE,cAAc;YAC5D;YAEA,IAAI,OAAO,GAAG,CAAC;gBACb,aAAa;gBACb,cAAc;gBACd,aAAa;gBACb,aAAa;gBACb,QAAQ,IAAI,CAAC,CAAC,0BAA0B,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE,cAAc,EAAE;YAC3E;YAEA,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE,cAAc;YAC3D,IAAI,GAAG,GAAG;YAEV,OAAO;YACP,OAAO;gBACL,aAAa;YACf;QACF,OAAO;YACL,4BAA4B;YAC5B,aAAa;QACf;IACF,GAAG;QAAC;QAAc,KAAK,IAAI;KAAC;IAE5B,gBAAgB;IAChB,IAAI,WAAW;QACb,OAAO;IACT;IAEA,uCAAuC;IACvC,IAAI,aAAa,CAAC,YAAY;QAC5B,OAAO;IACT,OAAO,IAAI,aAAa;QACtB,OAAO;IACT,OAAO;QACL,OAAO;IACT;;;IAEA,SAAS;IACT,SAAS;QACP,qBACE,8OAAC;YACC,WAAW;YACX,OAAO;gBACL;gBACA;gBACA,UAAU;gBACV,UAAU;gBACV,cAAc;gBACd,SAAS;gBACT,YAAY;gBACZ,gBAAgB;gBAChB,iBAAiB;gBACjB,OAAO;YACT;sBAEA,cAAA,8OAAC;gBAAI,OAAO;oBAAE,WAAW;gBAAS;;kCAChC,8OAAC;wBAAI,OAAO;4BAAE,UAAU;4BAAQ,cAAc;wBAAS;kCAAG;;;;;;kCAC1D,8OAAC;wBAAI,OAAO;4BAAE,UAAU;wBAAW;kCAAG;;;;;;;;;;;;;;;;;IAI9C;IAEA,SAAS;IACT,SAAS;QACP,qBACE,8OAAC;YACC,WAAW;YACX,OAAO;gBACL;gBACA;gBACA,UAAU;gBACV,UAAU;gBACV,cAAc;gBACd,WAAW;gBACX,YAAY;YACd;YACA,OAAO,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC;;8BAE9B,8OAAC;oBACC,KAAK;oBACL,KAAK,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,KAAK,SAAS,EAAE;oBACvC,OAAO;wBACL,OAAO;wBACP,QAAQ;wBACR,WAAW;wBACX,SAAS;oBACX;;;;;;8BAIF,8OAAC;oBACC,OAAO;wBACL,UAAU;wBACV,KAAK;wBACL,OAAO;wBACP,YAAY;wBACZ,OAAO;wBACP,UAAU;wBACV,SAAS;wBACT,cAAc;wBACd,YAAY;oBACd;8BACD;;;;;;8BAKD,8OAAC;oBACC,OAAO;wBACL,UAAU;wBACV,OAAO;wBACP,YAAY;wBACZ,YAAY;oBACd;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;wBACnC,EAAE,aAAa,CAAC,aAAa,CAAE,KAAK,CAAC,SAAS,GAAG;wBACjD,EAAE,aAAa,CAAC,aAAa,CAAE,KAAK,CAAC,SAAS,GAAG;oBACnD;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;wBACnC,EAAE,aAAa,CAAC,aAAa,CAAE,KAAK,CAAC,SAAS,GAAG;wBACjD,EAAE,aAAa,CAAC,aAAa,CAAE,KAAK,CAAC,SAAS,GAAG;oBACnD;;;;;;;;;;;;IAIR;IAEA,gBAAgB;IAChB,SAAS;QACP,qBACE,8OAAC;YACC,WAAW;YACX,OAAO;gBACL;gBACA;gBACA,UAAU;gBACV,UAAU;gBACV,cAAc;gBACd,WAAW;gBACX,YAAY;YACd;YACA,OAAO,GAAG,KAAK,IAAI,CAAC,aAAa,CAAC;;8BAElC,8OAAC;oBACC,KAAK;oBACL,KAAK,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,KAAK,SAAS,EAAE;oBACvC,OAAO;wBACL,OAAO;wBACP,QAAQ;wBACR,WAAW;wBACX,SAAS;oBACX;;;;;;8BAIF,8OAAC;oBACC,OAAO;wBACL,UAAU;wBACV,KAAK;wBACL,OAAO;wBACP,YAAY;wBACZ,OAAO;wBACP,UAAU;wBACV,SAAS;wBACT,cAAc;wBACd,YAAY;oBACd;8BACD;;;;;;8BAKD,8OAAC;oBACC,OAAO;wBACL,UAAU;wBACV,OAAO;wBACP,YAAY;wBACZ,YAAY;oBACd;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;wBACnC,EAAE,aAAa,CAAC,aAAa,CAAE,KAAK,CAAC,SAAS,GAAG;wBACjD,EAAE,aAAa,CAAC,aAAa,CAAE,KAAK,CAAC,SAAS,GAAG;oBACnD;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;wBACnC,EAAE,aAAa,CAAC,aAAa,CAAE,KAAK,CAAC,SAAS,GAAG;wBACjD,EAAE,aAAa,CAAC,aAAa,CAAE,KAAK,CAAC,SAAS,GAAG;oBACnD;;;;;;;;;;;;IAIR;IAEA,UAAU;IACV,SAAS;QACP,MAAM,YAAY;YAChB;YACA;YACA,SAAS;YACT;YACA,OAAO;gBAAE,cAAc;YAAS;QAClC;QAEA,aAAa;QACb,qBACE,8OAAC;YAAK,GAAG,SAAS;;8BAChB,8OAAC;8BACC,cAAA,8OAAC;wBAAe,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;wBAAE,IAAG;wBAAM,IAAG;wBAAM,GAAE;;0CAC7D,8OAAC;gCAAK,QAAO;gCAAK,WAAW,KAAK,UAAU,CAAC,EAAE;;;;;;0CAC/C,8OAAC;gCAAK,QAAO;gCAAO,WAAW,KAAK,UAAU,CAAC,EAAE,IAAI,KAAK,UAAU,CAAC,EAAE;;;;;;;;;;;;;;;;;8BAK3E,8OAAC;oBACC,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,MAAM,CAAC,cAAc,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;oBACjC,QAAO;oBACP,aAAY;;;;;;8BAId,8OAAC;oBAAK,GAAE;oBAA4B,QAAO;oBAAO,aAAY;oBAAI,MAAK;;;;;;8BACvE,8OAAC;oBAAK,GAAE;oBAA6B,QAAO;oBAAO,aAAY;oBAAI,MAAK;;;;;;8BACxE,8OAAC;oBAAQ,IAAG;oBAAM,IAAG;oBAAM,IAAG;oBAAK,IAAG;oBAAK,MAAK;oBAAQ,QAAO;oBAAO,aAAY;;;;;;8BAClF,8OAAC;oBAAQ,IAAG;oBAAM,IAAG;oBAAM,IAAG;oBAAK,IAAG;oBAAK,MAAK;oBAAQ,QAAO;oBAAO,aAAY;;;;;;8BAClF,8OAAC;oBAAO,IAAG;oBAAM,IAAG;oBAAM,GAAE;oBAAI,MAAK;;;;;;8BACrC,8OAAC;oBAAO,IAAG;oBAAM,IAAG;oBAAM,GAAE;oBAAI,MAAK;;;;;;8BACrC,8OAAC;oBAAK,GAAE;oBAAsB,QAAO;oBAAO,aAAY;;;;;;8BACxD,8OAAC;oBAAQ,IAAG;oBAAM,IAAG;oBAAM,IAAG;oBAAK,IAAG;oBAAI,MAAK;;;;;;8BAG/C,8OAAC;oBACC,GAAE;oBACF,GAAE;oBACF,YAAW;oBACX,MAAK;oBACL,UAAS;oBACT,YAAW;oBACX,YAAW;oBACX,QAAO;oBACP,aAAY;8BAEX,KAAK,SAAS;;;;;;;;;;;;IAIvB;AACF", "debugId": null}}, {"offset": {"line": 3106, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/navigation/Breadcrumb.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useRouter } from 'next/navigation';\n\ninterface BreadcrumbItem {\n  label: string;\n  href?: string;\n  current?: boolean;\n}\n\ninterface BreadcrumbProps {\n  items: BreadcrumbItem[];\n  className?: string;\n}\n\nexport function Breadcrumb({ items, className }: BreadcrumbProps) {\n  const router = useRouter();\n  \n  const handleClick = (href?: string, event?: React.MouseEvent) => {\n    if (event) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n    if (href) {\n      console.log('面包屑导航点击:', href);\n      console.log('Router对象:', router);\n      try {\n        router.push(href);\n        console.log('面包屑路由跳转已执行');\n      } catch (error) {\n        console.error('面包屑路由跳转失败，使用window.location作为后备:', error);\n        window.location.href = href;\n      }\n    }\n  };\n  \n  return (\n    <nav\n      aria-label=\"面包屑导航\"\n      className={className}\n      style={{\n        padding: '1rem 0',\n        borderBottom: '1px solid #E5E7EB'\n      }}\n    >\n      <ol style={{\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem',\n        fontSize: '0.875rem',\n        color: '#6B7280'\n      }}>\n        {items.map((item, index) => (\n          <li key={index} style={{ display: 'flex', alignItems: 'center' }}>\n            {index > 0 && (\n              <span style={{ margin: '0 0.5rem', color: '#D1D5DB' }}>\n                /\n              </span>\n            )}\n            {item.current ? (\n              <span style={{\n                color: '#1F2937',\n                fontWeight: '500',\n                fontFamily: '\"Noto Serif SC\", serif'\n              }}>\n                {item.label}\n              </span>\n            ) : (\n              <button\n                onClick={(e) => handleClick(item.href, e)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  textDecoration: 'none',\n                  fontSize: 'inherit',\n                  padding: 0,\n                  transition: 'color 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.color = '#B91C1C';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.color = '#6B7280';\n                }}\n              >\n                {item.label}\n              </button>\n            )}\n          </li>\n        ))}\n      </ol>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAgBO,SAAS,WAAW,EAAE,KAAK,EAAE,SAAS,EAAmB;IAC9D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,cAAc,CAAC,MAAe;QAClC,IAAI,OAAO;YACT,MAAM,cAAc;YACpB,MAAM,eAAe;QACvB;QACA,IAAI,MAAM;YACR,QAAQ,GAAG,CAAC,YAAY;YACxB,QAAQ,GAAG,CAAC,aAAa;YACzB,IAAI;gBACF,OAAO,IAAI,CAAC;gBACZ,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF;IACF;IAEA,qBACE,8OAAC;QACC,cAAW;QACX,WAAW;QACX,OAAO;YACL,SAAS;YACT,cAAc;QAChB;kBAEA,cAAA,8OAAC;YAAG,OAAO;gBACT,SAAS;gBACT,YAAY;gBACZ,KAAK;gBACL,UAAU;gBACV,OAAO;YACT;sBACG,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oBAAe,OAAO;wBAAE,SAAS;wBAAQ,YAAY;oBAAS;;wBAC5D,QAAQ,mBACP,8OAAC;4BAAK,OAAO;gCAAE,QAAQ;gCAAY,OAAO;4BAAU;sCAAG;;;;;;wBAIxD,KAAK,OAAO,iBACX,8OAAC;4BAAK,OAAO;gCACX,OAAO;gCACP,YAAY;gCACZ,YAAY;4BACd;sCACG,KAAK,KAAK;;;;;iDAGb,8OAAC;4BACC,SAAS,CAAC,IAAM,YAAY,KAAK,IAAI,EAAE;4BACvC,OAAO;gCACL,YAAY;gCACZ,QAAQ;gCACR,OAAO;gCACP,QAAQ;gCACR,gBAAgB;gCAChB,UAAU;gCACV,SAAS;gCACT,YAAY;4BACd;4BACA,cAAc,CAAC;gCACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;4BAChC;4BACA,cAAc,CAAC;gCACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;4BAChC;sCAEC,KAAK,KAAK;;;;;;;mBAlCR;;;;;;;;;;;;;;;AA0CnB", "debugId": null}}, {"offset": {"line": 3221, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/hooks/useAppState.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { MaskFilter, OperaMask } from '@/types/mask';\n\ninterface AppState {\n  // 筛选状态\n  filter: MaskFilter;\n  // 最近查看的脸谱\n  recentlyViewed: string[];\n  // 收藏的脸谱\n  favorites: string[];\n  // 搜索历史\n  searchHistory: string[];\n}\n\nconst STORAGE_KEY = 'beijing-opera-masks-state';\n\nconst defaultState: AppState = {\n  filter: {},\n  recentlyViewed: [],\n  favorites: [],\n  searchHistory: []\n};\n\nexport function useAppState() {\n  const [state, setState] = useState<AppState>(defaultState);\n  \n  // 从localStorage加载状态\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      try {\n        const saved = localStorage.getItem(STORAGE_KEY);\n        if (saved) {\n          const parsedState = JSON.parse(saved);\n          setState(prevState => ({ ...prevState, ...parsedState }));\n        }\n      } catch (error) {\n        console.warn('Failed to load app state from localStorage:', error);\n      }\n    }\n  }, []);\n  \n  // 保存状态到localStorage\n  const saveState = (newState: Partial<AppState>) => {\n    const updatedState = { ...state, ...newState };\n    setState(updatedState);\n    \n    if (typeof window !== 'undefined') {\n      try {\n        localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedState));\n      } catch (error) {\n        console.warn('Failed to save app state to localStorage:', error);\n      }\n    }\n  };\n  \n  // 更新筛选条件\n  const updateFilter = (filter: MaskFilter) => {\n    saveState({ filter });\n  };\n  \n  // 添加到最近查看\n  const addToRecentlyViewed = (maskId: string) => {\n    const updated = [maskId, ...state.recentlyViewed.filter(id => id !== maskId)].slice(0, 10);\n    saveState({ recentlyViewed: updated });\n  };\n  \n  // 切换收藏状态\n  const toggleFavorite = (maskId: string) => {\n    const updated = state.favorites.includes(maskId)\n      ? state.favorites.filter(id => id !== maskId)\n      : [...state.favorites, maskId];\n    saveState({ favorites: updated });\n  };\n  \n  // 添加搜索历史\n  const addToSearchHistory = (searchTerm: string) => {\n    if (!searchTerm.trim()) return;\n    \n    const updated = [\n      searchTerm,\n      ...state.searchHistory.filter(term => term !== searchTerm)\n    ].slice(0, 10);\n    saveState({ searchHistory: updated });\n  };\n  \n  // 清除搜索历史\n  const clearSearchHistory = () => {\n    saveState({ searchHistory: [] });\n  };\n  \n  // 检查是否收藏\n  const isFavorite = (maskId: string) => {\n    return state.favorites.includes(maskId);\n  };\n  \n  // 获取最近查看的脸谱\n  const getRecentlyViewedMasks = (masks: OperaMask[]) => {\n    return state.recentlyViewed\n      .map(id => masks.find(mask => mask.id === id))\n      .filter(Boolean) as OperaMask[];\n  };\n  \n  // 获取收藏的脸谱\n  const getFavoriteMasks = (masks: OperaMask[]) => {\n    return state.favorites\n      .map(id => masks.find(mask => mask.id === id))\n      .filter(Boolean) as OperaMask[];\n  };\n  \n  return {\n    // 状态\n    filter: state.filter,\n    recentlyViewed: state.recentlyViewed,\n    favorites: state.favorites,\n    searchHistory: state.searchHistory,\n    \n    // 操作\n    updateFilter,\n    addToRecentlyViewed,\n    toggleFavorite,\n    addToSearchHistory,\n    clearSearchHistory,\n    \n    // 辅助函数\n    isFavorite,\n    getRecentlyViewedMasks,\n    getFavoriteMasks\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAgBA,MAAM,cAAc;AAEpB,MAAM,eAAyB;IAC7B,QAAQ,CAAC;IACT,gBAAgB,EAAE;IAClB,WAAW,EAAE;IACb,eAAe,EAAE;AACnB;AAEO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IAE7C,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;;IAWF,GAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,YAAY,CAAC;QACjB,MAAM,eAAe;YAAE,GAAG,KAAK;YAAE,GAAG,QAAQ;QAAC;QAC7C,SAAS;QAET;;IAOF;IAEA,SAAS;IACT,MAAM,eAAe,CAAC;QACpB,UAAU;YAAE;QAAO;IACrB;IAEA,UAAU;IACV,MAAM,sBAAsB,CAAC;QAC3B,MAAM,UAAU;YAAC;eAAW,MAAM,cAAc,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;SAAQ,CAAC,KAAK,CAAC,GAAG;QACvF,UAAU;YAAE,gBAAgB;QAAQ;IACtC;IAEA,SAAS;IACT,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,MAAM,SAAS,CAAC,QAAQ,CAAC,UACrC,MAAM,SAAS,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO,UACpC;eAAI,MAAM,SAAS;YAAE;SAAO;QAChC,UAAU;YAAE,WAAW;QAAQ;IACjC;IAEA,SAAS;IACT,MAAM,qBAAqB,CAAC;QAC1B,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,MAAM,UAAU;YACd;eACG,MAAM,aAAa,CAAC,MAAM,CAAC,CAAA,OAAQ,SAAS;SAChD,CAAC,KAAK,CAAC,GAAG;QACX,UAAU;YAAE,eAAe;QAAQ;IACrC;IAEA,SAAS;IACT,MAAM,qBAAqB;QACzB,UAAU;YAAE,eAAe,EAAE;QAAC;IAChC;IAEA,SAAS;IACT,MAAM,aAAa,CAAC;QAClB,OAAO,MAAM,SAAS,CAAC,QAAQ,CAAC;IAClC;IAEA,YAAY;IACZ,MAAM,yBAAyB,CAAC;QAC9B,OAAO,MAAM,cAAc,CACxB,GAAG,CAAC,CAAA,KAAM,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,KACzC,MAAM,CAAC;IACZ;IAEA,UAAU;IACV,MAAM,mBAAmB,CAAC;QACxB,OAAO,MAAM,SAAS,CACnB,GAAG,CAAC,CAAA,KAAM,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,KACzC,MAAM,CAAC;IACZ;IAEA,OAAO;QACL,KAAK;QACL,QAAQ,MAAM,MAAM;QACpB,gBAAgB,MAAM,cAAc;QACpC,WAAW,MAAM,SAAS;QAC1B,eAAe,MAAM,aAAa;QAElC,KAAK;QACL;QACA;QACA;QACA;QACA;QAEA,OAAO;QACP;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 3328, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/ui/Typography.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\n// 字体样式配置\nexport const fontStyles = {\n  // 主标题 - 使用马善政字体（传统书法风格）\n  mainTitle: {\n    fontFamily: 'var(--font-ma-shan-zheng), \"<PERSON>\", \"<PERSON>Ti\", \"楷体\", cursive',\n    fontSize: '2.5rem',\n    fontWeight: '400',\n    lineHeight: '1.2',\n    color: '#B91C1C',\n    textShadow: '2px 2px 4px rgba(0,0,0,0.1)'\n  },\n  \n  // 页面标题 - 使用思源宋体\n  pageTitle: {\n    fontFamily: 'var(--font-noto-serif-sc), \"Noto Serif SC\", \"SimSun\", \"宋体\", serif',\n    fontSize: '2rem',\n    fontWeight: '700',\n    lineHeight: '1.3',\n    color: '#1F2937'\n  },\n  \n  // 章节标题\n  sectionTitle: {\n    fontFamily: 'var(--font-noto-serif-sc), \"Noto Serif SC\", \"SimSun\", \"宋体\", serif',\n    fontSize: '1.5rem',\n    fontWeight: '600',\n    lineHeight: '1.4',\n    color: '#374151'\n  },\n  \n  // 子标题\n  subtitle: {\n    fontFamily: 'var(--font-noto-serif-sc), \"Noto Serif SC\", \"SimSun\", \"宋体\", serif',\n    fontSize: '1.25rem',\n    fontWeight: '500',\n    lineHeight: '1.5',\n    color: '#4B5563'\n  },\n  \n  // 正文 - 使用思源黑体\n  body: {\n    fontFamily: 'var(--font-noto-sans-sc), \"Noto Sans SC\", \"Microsoft YaHei\", \"微软雅黑\", sans-serif',\n    fontSize: '1rem',\n    fontWeight: '400',\n    lineHeight: '1.6',\n    color: '#6B7280'\n  },\n  \n  // 重要文本\n  emphasis: {\n    fontFamily: 'var(--font-noto-serif-sc), \"Noto Serif SC\", \"SimSun\", \"宋体\", serif',\n    fontSize: '1rem',\n    fontWeight: '600',\n    lineHeight: '1.6',\n    color: '#374151'\n  },\n  \n  // 小字说明\n  caption: {\n    fontFamily: 'var(--font-noto-sans-sc), \"Noto Sans SC\", \"Microsoft YaHei\", \"微软雅黑\", sans-serif',\n    fontSize: '0.875rem',\n    fontWeight: '400',\n    lineHeight: '1.5',\n    color: '#9CA3AF'\n  },\n  \n  // 按钮文字\n  button: {\n    fontFamily: 'var(--font-noto-sans-sc), \"Noto Sans SC\", \"Microsoft YaHei\", \"微软雅黑\", sans-serif',\n    fontSize: '0.875rem',\n    fontWeight: '500',\n    lineHeight: '1.4'\n  }\n};\n\n// 字体组件\ninterface TypographyProps {\n  variant: keyof typeof fontStyles;\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n  as?: keyof React.JSX.IntrinsicElements;\n}\n\nexport function Typography({ \n  variant, \n  children, \n  className, \n  style, \n  as = 'div' \n}: TypographyProps) {\n  const Component = as;\n  const variantStyle = fontStyles[variant];\n  \n  return (\n    <Component\n      className={className}\n      style={{\n        ...variantStyle,\n        ...style\n      }}\n    >\n      {children}\n    </Component>\n  );\n}\n\n// 预定义的字体组件\nexport function MainTitle({ children, ...props }: Omit<TypographyProps, 'variant'>) {\n  return <Typography variant=\"mainTitle\" as=\"h1\" {...props}>{children}</Typography>;\n}\n\nexport function PageTitle({ children, ...props }: Omit<TypographyProps, 'variant'>) {\n  return <Typography variant=\"pageTitle\" as=\"h1\" {...props}>{children}</Typography>;\n}\n\nexport function SectionTitle({ children, ...props }: Omit<TypographyProps, 'variant'>) {\n  return <Typography variant=\"sectionTitle\" as=\"h2\" {...props}>{children}</Typography>;\n}\n\nexport function Subtitle({ children, ...props }: Omit<TypographyProps, 'variant'>) {\n  return <Typography variant=\"subtitle\" as=\"h3\" {...props}>{children}</Typography>;\n}\n\nexport function BodyText({ children, ...props }: Omit<TypographyProps, 'variant'>) {\n  return <Typography variant=\"body\" as=\"p\" {...props}>{children}</Typography>;\n}\n\nexport function EmphasisText({ children, ...props }: Omit<TypographyProps, 'variant'>) {\n  return <Typography variant=\"emphasis\" as=\"span\" {...props}>{children}</Typography>;\n}\n\nexport function CaptionText({ children, ...props }: Omit<TypographyProps, 'variant'>) {\n  return <Typography variant=\"caption\" as=\"span\" {...props}>{children}</Typography>;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAKO,MAAM,aAAa;IACxB,wBAAwB;IACxB,WAAW;QACT,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,YAAY;IACd;IAEA,gBAAgB;IAChB,WAAW;QACT,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,OAAO;IACT;IAEA,OAAO;IACP,cAAc;QACZ,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,OAAO;IACT;IAEA,MAAM;IACN,UAAU;QACR,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,OAAO;IACT;IAEA,cAAc;IACd,MAAM;QACJ,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,OAAO;IACT;IAEA,OAAO;IACP,UAAU;QACR,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,OAAO;IACT;IAEA,OAAO;IACP,SAAS;QACP,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,OAAO;IACT;IAEA,OAAO;IACP,QAAQ;QACN,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,YAAY;IACd;AACF;AAWO,SAAS,WAAW,EACzB,OAAO,EACP,QAAQ,EACR,SAAS,EACT,KAAK,EACL,KAAK,KAAK,EACM;IAChB,MAAM,YAAY;IAClB,MAAM,eAAe,UAAU,CAAC,QAAQ;IAExC,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;YACL,GAAG,YAAY;YACf,GAAG,KAAK;QACV;kBAEC;;;;;;AAGP;AAGO,SAAS,UAAU,EAAE,QAAQ,EAAE,GAAG,OAAyC;IAChF,qBAAO,8OAAC;QAAW,SAAQ;QAAY,IAAG;QAAM,GAAG,KAAK;kBAAG;;;;;;AAC7D;AAEO,SAAS,UAAU,EAAE,QAAQ,EAAE,GAAG,OAAyC;IAChF,qBAAO,8OAAC;QAAW,SAAQ;QAAY,IAAG;QAAM,GAAG,KAAK;kBAAG;;;;;;AAC7D;AAEO,SAAS,aAAa,EAAE,QAAQ,EAAE,GAAG,OAAyC;IACnF,qBAAO,8OAAC;QAAW,SAAQ;QAAe,IAAG;QAAM,GAAG,KAAK;kBAAG;;;;;;AAChE;AAEO,SAAS,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAyC;IAC/E,qBAAO,8OAAC;QAAW,SAAQ;QAAW,IAAG;QAAM,GAAG,KAAK;kBAAG;;;;;;AAC5D;AAEO,SAAS,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAyC;IAC/E,qBAAO,8OAAC;QAAW,SAAQ;QAAO,IAAG;QAAK,GAAG,KAAK;kBAAG;;;;;;AACvD;AAEO,SAAS,aAAa,EAAE,QAAQ,EAAE,GAAG,OAAyC;IACnF,qBAAO,8OAAC;QAAW,SAAQ;QAAW,IAAG;QAAQ,GAAG,KAAK;kBAAG;;;;;;AAC9D;AAEO,SAAS,YAAY,EAAE,QAAQ,EAAE,GAAG,OAAyC;IAClF,qBAAO,8OAAC;QAAW,SAAQ;QAAU,IAAG;QAAQ,GAAG,KAAK;kBAAG;;;;;;AAC7D", "debugId": null}}, {"offset": {"line": 3512, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/app/mask/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useRouter } from 'next/navigation';\nimport { operaMasks } from '@/data/masks';\nimport { OperaMask } from '@/types/mask';\nimport { MaskDrawingAnimation } from '@/components/animation/MaskDrawingAnimation';\nimport { MaskImage } from '@/components/mask/MaskImage';\nimport { Breadcrumb } from '@/components/navigation/Breadcrumb';\nimport { useAppState } from '@/hooks/useAppState';\nimport { PageTitle, SectionTitle, BodyText, EmphasisText, fontStyles } from '@/components/ui/Typography';\nimport { ThemeToggle } from '@/components/ui/ThemeToggle';\nimport { useTheme } from '@/components/providers/ThemeProvider';\n\nexport default function MaskDetailPage() {\n  const params = useParams();\n  const router = useRouter();\n  const maskId = params.id as string;\n  const [showAnimation, setShowAnimation] = useState(false);\n  const [isAnimationPlaying, setIsAnimationPlaying] = useState(false);\n  const { addToRecentlyViewed, toggleFavorite, isFavorite } = useAppState();\n  const { colors, styles } = useTheme();\n\n  const mask = operaMasks.find(m => m.id === maskId);\n\n  // 记录访问\n  useEffect(() => {\n    if (mask) {\n      addToRecentlyViewed(mask.id);\n    }\n  }, [mask, addToRecentlyViewed]);\n  \n  if (!mask) {\n    return (\n      <div style={{\n        minHeight: '100vh',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#F9FAFB',\n        fontFamily: '\"Noto Sans SC\", sans-serif'\n      }}>\n        <div style={{ textAlign: 'center' }}>\n          <h1 style={{ fontSize: '2rem', color: '#1F2937', marginBottom: '1rem' }}>\n            脸谱未找到\n          </h1>\n          <p style={{ color: '#6B7280', marginBottom: '2rem' }}>\n            抱歉，您访问的脸谱不存在。\n          </p>\n          <button\n            onClick={() => router.push('/')}\n            style={{\n              backgroundColor: '#B91C1C',\n              color: 'white',\n              padding: '0.75rem 1.5rem',\n              borderRadius: '0.5rem',\n              border: 'none',\n              cursor: 'pointer',\n              fontSize: '1rem'\n            }}\n          >\n            返回首页\n          </button>\n        </div>\n      </div>\n    );\n  }\n  \n  return (\n    <div style={{\n      minHeight: '100vh',\n      backgroundColor: '#F9FAFB',\n      fontFamily: '\"Noto Sans SC\", sans-serif'\n    }}>\n      {/* 头部导航 */}\n      <header style={{\n        backgroundColor: 'white',\n        borderBottom: '2px solid #F59E0B',\n        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n        position: 'sticky',\n        top: 0,\n        zIndex: 40\n      }}>\n        <div style={{\n          maxWidth: '1200px',\n          margin: '0 auto',\n          padding: '1rem 1.5rem',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        }}>\n          <button\n            onClick={(e) => {\n              e.preventDefault();\n              e.stopPropagation();\n              console.log('返回首页按钮被点击');\n              console.log('Router对象:', router);\n              try {\n                router.push('/');\n                console.log('路由跳转已执行');\n              } catch (error) {\n                console.error('路由跳转失败，使用window.location作为后备:', error);\n                window.location.href = '/';\n              }\n            }}\n            style={{\n              backgroundColor: 'transparent',\n              border: '2px solid #B91C1C',\n              color: '#B91C1C',\n              padding: '0.5rem 1rem',\n              borderRadius: '0.5rem',\n              cursor: 'pointer',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              transition: 'all 0.2s ease'\n            }}\n            onMouseEnter={(e) => {\n              e.currentTarget.style.backgroundColor = '#B91C1C';\n              e.currentTarget.style.color = 'white';\n            }}\n            onMouseLeave={(e) => {\n              e.currentTarget.style.backgroundColor = 'transparent';\n              e.currentTarget.style.color = '#B91C1C';\n            }}\n          >\n            ← 返回首页\n          </button>\n          <PageTitle>\n            {mask.name}\n          </PageTitle>\n          <div style={{ display: 'flex', gap: '0.5rem' }}>\n            <button\n              onClick={() => toggleFavorite(mask.id)}\n              style={{\n                backgroundColor: isFavorite(mask.id) ? '#EF4444' : '#6B7280',\n                color: 'white',\n                border: 'none',\n                padding: '0.5rem 1rem',\n                borderRadius: '0.5rem',\n                cursor: 'pointer',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              }}\n            >\n              {isFavorite(mask.id) ? '❤️' : '🤍'}\n              {isFavorite(mask.id) ? '已收藏' : '收藏'}\n            </button>\n            <button\n              onClick={() => setShowAnimation(!showAnimation)}\n              style={{\n                backgroundColor: showAnimation ? '#EF4444' : '#10B981',\n                color: 'white',\n                border: 'none',\n                padding: '0.5rem 1rem',\n                borderRadius: '0.5rem',\n                cursor: 'pointer',\n                fontSize: '0.875rem',\n                fontWeight: '500'\n              }}\n            >\n              {showAnimation ? '关闭动画' : '绘制动画'}\n            </button>\n          </div>\n        </div>\n      </header>\n      \n      {/* 主要内容 */}\n      <main style={{ padding: '2rem 1.5rem' }}>\n        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>\n          {/* 面包屑导航 */}\n          <Breadcrumb\n            items={[\n              { label: '首页', href: '/' },\n              { label: '脸谱详情', current: true }\n            ]}\n          />\n        </div>\n\n        <div style={{\n          maxWidth: '1200px',\n          margin: '0 auto',\n          display: 'grid',\n          gridTemplateColumns: '1fr 1fr',\n          gap: '3rem',\n          alignItems: 'start'\n        }}>\n          {/* 左侧：脸谱图片和基本信息 */}\n          <div style={{ position: 'sticky', top: '120px' }}>\n            {/* 脸谱图片或动画 */}\n            {showAnimation ? (\n              <div style={{ marginBottom: '2rem' }}>\n                <MaskDrawingAnimation\n                  mask={mask}\n                  isPlaying={isAnimationPlaying}\n                  onPlayStateChange={setIsAnimationPlaying}\n                  speed={1}\n                />\n              </div>\n            ) : (\n              <div style={{\n                aspectRatio: '1',\n                marginBottom: '2rem',\n                position: 'relative',\n                borderRadius: '1rem',\n                overflow: 'hidden',\n                boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',\n                border: '3px solid #F59E0B'\n              }}>\n                {/* 使用MaskImage组件显示脸谱 */}\n                <MaskImage\n                  mask={mask}\n                  width={400}\n                  height={400}\n                  className=\"detail-mask-image\"\n                />\n\n                {/* 分类标签 */}\n                <div style={{\n                  position: 'absolute',\n                  top: '1rem',\n                  left: '1rem',\n                  backgroundColor: 'rgba(0,0,0,0.8)',\n                  color: 'white',\n                  padding: '0.5rem 1rem',\n                  borderRadius: '9999px',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  zIndex: 20\n                }}>\n                  {mask.roleCategory}\n                </div>\n\n                <div style={{\n                  position: 'absolute',\n                  top: '1rem',\n                  right: '1rem',\n                  backgroundColor: 'rgba(0,0,0,0.8)',\n                  color: 'white',\n                  padding: '0.5rem 1rem',\n                  borderRadius: '9999px',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  zIndex: 20\n                }}>\n                  {mask.colorCategory}\n                </div>\n\n                {/* 受欢迎程度星级 */}\n                <div style={{\n                  position: 'absolute',\n                  bottom: '1rem',\n                  right: '1rem',\n                  display: 'flex',\n                  gap: '0.25rem',\n                  zIndex: 20\n                }}>\n                  {Array.from({ length: 5 }).map((_, i) => (\n                    <span\n                      key={i}\n                      style={{\n                        color: i < Math.floor(mask.popularity / 2) ? '#F59E0B' : 'rgba(255,255,255,0.5)',\n                        fontSize: '1.25rem',\n                        textShadow: '0 1px 2px rgba(0,0,0,0.5)'\n                      }}\n                    >\n                      ★\n                    </span>\n                  ))}\n                </div>\n              </div>\n            )}\n            \n            {/* 主要颜色 */}\n            <div style={{\n              backgroundColor: 'white',\n              padding: '1.5rem',\n              borderRadius: '1rem',\n              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',\n              border: '2px solid #F59E0B'\n            }}>\n              <h3 style={{\n                fontSize: '1.25rem',\n                fontWeight: '600',\n                color: '#1F2937',\n                marginBottom: '1rem',\n                fontFamily: '\"Noto Serif SC\", serif'\n              }}>\n                主要色彩\n              </h3>\n              <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>\n                {mask.mainColors.map((color, index) => (\n                  <div key={index} style={{ textAlign: 'center' }}>\n                    <div\n                      style={{\n                        width: '3rem',\n                        height: '3rem',\n                        borderRadius: '50%',\n                        backgroundColor: color,\n                        border: '3px solid #D1D5DB',\n                        marginBottom: '0.5rem',\n                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n                      }}\n                    />\n                    <span style={{\n                      fontSize: '0.75rem',\n                      color: '#6B7280',\n                      fontFamily: 'monospace'\n                    }}>\n                      {color}\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n          \n          {/* 右侧：详细信息 */}\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>\n            {/* 基本信息 */}\n            <div style={{\n              backgroundColor: 'white',\n              padding: '2rem',\n              borderRadius: '1rem',\n              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',\n              border: '2px solid #F59E0B'\n            }}>\n              <h2 style={{\n                fontSize: '1.5rem',\n                fontWeight: 'bold',\n                color: '#1F2937',\n                marginBottom: '1.5rem',\n                fontFamily: '\"Noto Serif SC\", serif'\n              }}>\n                基本信息\n              </h2>\n              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>\n                <div>\n                  <span style={{ fontWeight: '600', color: '#374151' }}>角色名称：</span>\n                  <span style={{ color: '#6B7280' }}>{mask.character}</span>\n                </div>\n                <div>\n                  <span style={{ fontWeight: '600', color: '#374151' }}>行当分类：</span>\n                  <span style={{ color: '#6B7280' }}>{mask.roleCategory}</span>\n                </div>\n                <div>\n                  <span style={{ fontWeight: '600', color: '#374151' }}>颜色分类：</span>\n                  <span style={{ color: '#6B7280' }}>{mask.colorCategory}</span>\n                </div>\n                <div>\n                  <span style={{ fontWeight: '600', color: '#374151' }}>绘制难度：</span>\n                  <span style={{ color: '#6B7280' }}>\n                    {mask.difficulty === 'easy' ? '简单' : mask.difficulty === 'medium' ? '中等' : '困难'}\n                  </span>\n                </div>\n                <div>\n                  <span style={{ fontWeight: '600', color: '#374151' }}>受欢迎程度：</span>\n                  <span style={{ color: '#6B7280' }}>{mask.popularity}/10</span>\n                </div>\n                <div>\n                  <span style={{ fontWeight: '600', color: '#374151' }}>历史时期：</span>\n                  <span style={{ color: '#6B7280' }}>{mask.culturalBackground.historicalPeriod}</span>\n                </div>\n              </div>\n            </div>\n            \n            {/* 文化背景 */}\n            <div style={{\n              backgroundColor: 'white',\n              padding: '2rem',\n              borderRadius: '1rem',\n              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',\n              border: '2px solid #F59E0B'\n            }}>\n              <h2 style={{\n                fontSize: '1.5rem',\n                fontWeight: 'bold',\n                color: '#1F2937',\n                marginBottom: '1.5rem',\n                fontFamily: '\"Noto Serif SC\", serif'\n              }}>\n                文化背景\n              </h2>\n              <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n                <div>\n                  <h3 style={{ fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>\n                    历史起源\n                  </h3>\n                  <p style={{ color: '#6B7280', lineHeight: '1.6' }}>\n                    {mask.culturalBackground.origin}\n                  </p>\n                </div>\n                <div>\n                  <h3 style={{ fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>\n                    性格特点\n                  </h3>\n                  <p style={{ color: '#6B7280', lineHeight: '1.6' }}>\n                    {mask.culturalBackground.personality}\n                  </p>\n                </div>\n                <div>\n                  <h3 style={{ fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>\n                    象征意义\n                  </h3>\n                  <p style={{ color: '#6B7280', lineHeight: '1.6' }}>\n                    {mask.culturalBackground.symbolism}\n                  </p>\n                </div>\n              </div>\n            </div>\n            \n            {/* 色彩寓意 */}\n            <div style={{\n              backgroundColor: 'white',\n              padding: '2rem',\n              borderRadius: '1rem',\n              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',\n              border: '2px solid #F59E0B'\n            }}>\n              <h2 style={{\n                fontSize: '1.5rem',\n                fontWeight: 'bold',\n                color: '#1F2937',\n                marginBottom: '1.5rem',\n                fontFamily: '\"Noto Serif SC\", serif'\n              }}>\n                色彩寓意\n              </h2>\n              <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n                {Object.entries(mask.colorMeaning).map(([color, meaning]) => (\n                  <div key={color} style={{\n                    padding: '1rem',\n                    backgroundColor: '#F9FAFB',\n                    borderRadius: '0.5rem',\n                    borderLeft: '4px solid #F59E0B'\n                  }}>\n                    <span style={{ fontWeight: '600', color: '#374151' }}>{color}：</span>\n                    <span style={{ color: '#6B7280' }}>{meaning}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n            \n            {/* 相关剧目 */}\n            <div style={{\n              backgroundColor: 'white',\n              padding: '2rem',\n              borderRadius: '1rem',\n              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',\n              border: '2px solid #F59E0B'\n            }}>\n              <h2 style={{\n                fontSize: '1.5rem',\n                fontWeight: 'bold',\n                color: '#1F2937',\n                marginBottom: '1.5rem',\n                fontFamily: '\"Noto Serif SC\", serif'\n              }}>\n                相关剧目\n              </h2>\n              <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n                {mask.relatedOperas.map((opera, index) => (\n                  <div key={index} style={{\n                    padding: '1.5rem',\n                    backgroundColor: '#F9FAFB',\n                    borderRadius: '0.75rem',\n                    border: '1px solid #E5E7EB'\n                  }}>\n                    <h3 style={{\n                      fontSize: '1.125rem',\n                      fontWeight: '600',\n                      color: '#1F2937',\n                      marginBottom: '0.5rem',\n                      fontFamily: '\"Noto Serif SC\", serif'\n                    }}>\n                      {opera.name}\n                    </h3>\n                    <p style={{ color: '#6B7280', marginBottom: '0.5rem', lineHeight: '1.6' }}>\n                      {opera.description}\n                    </p>\n                    <span style={{\n                      fontSize: '0.875rem',\n                      color: '#F59E0B',\n                      fontWeight: '500'\n                    }}>\n                      {opera.period}\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </div>\n            \n            {/* 标签 */}\n            <div style={{\n              backgroundColor: 'white',\n              padding: '2rem',\n              borderRadius: '1rem',\n              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',\n              border: '2px solid #F59E0B'\n            }}>\n              <h2 style={{\n                fontSize: '1.5rem',\n                fontWeight: 'bold',\n                color: '#1F2937',\n                marginBottom: '1.5rem',\n                fontFamily: '\"Noto Serif SC\", serif'\n              }}>\n                相关标签\n              </h2>\n              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.75rem' }}>\n                {mask.tags.map((tag, index) => (\n                  <span\n                    key={index}\n                    style={{\n                      padding: '0.75rem 1rem',\n                      fontSize: '0.875rem',\n                      backgroundColor: 'rgba(245, 158, 11, 0.1)',\n                      color: '#F59E0B',\n                      border: '2px solid #F59E0B',\n                      borderRadius: '9999px',\n                      fontWeight: '500'\n                    }}\n                  >\n                    {tag}\n                  </span>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AAZA;;;;;;;;;;;AAce,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,OAAO,EAAE;IACxB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,EAAE,mBAAmB,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IACtE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAElC,MAAM,OAAO,oHAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAE3C,OAAO;IACP,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,oBAAoB,KAAK,EAAE;QAC7B;IACF,GAAG;QAAC;QAAM;KAAoB;IAE9B,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,OAAO;gBACV,WAAW;gBACX,SAAS;gBACT,YAAY;gBACZ,gBAAgB;gBAChB,iBAAiB;gBACjB,YAAY;YACd;sBACE,cAAA,8OAAC;gBAAI,OAAO;oBAAE,WAAW;gBAAS;;kCAChC,8OAAC;wBAAG,OAAO;4BAAE,UAAU;4BAAQ,OAAO;4BAAW,cAAc;wBAAO;kCAAG;;;;;;kCAGzE,8OAAC;wBAAE,OAAO;4BAAE,OAAO;4BAAW,cAAc;wBAAO;kCAAG;;;;;;kCAGtD,8OAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,OAAO;4BACL,iBAAiB;4BACjB,OAAO;4BACP,SAAS;4BACT,cAAc;4BACd,QAAQ;4BACR,QAAQ;4BACR,UAAU;wBACZ;kCACD;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,OAAO;YACV,WAAW;YACX,iBAAiB;YACjB,YAAY;QACd;;0BAEE,8OAAC;gBAAO,OAAO;oBACb,iBAAiB;oBACjB,cAAc;oBACd,WAAW;oBACX,UAAU;oBACV,KAAK;oBACL,QAAQ;gBACV;0BACE,cAAA,8OAAC;oBAAI,OAAO;wBACV,UAAU;wBACV,QAAQ;wBACR,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,YAAY;oBACd;;sCACE,8OAAC;4BACC,SAAS,CAAC;gCACR,EAAE,cAAc;gCAChB,EAAE,eAAe;gCACjB,QAAQ,GAAG,CAAC;gCACZ,QAAQ,GAAG,CAAC,aAAa;gCACzB,IAAI;oCACF,OAAO,IAAI,CAAC;oCACZ,QAAQ,GAAG,CAAC;gCACd,EAAE,OAAO,OAAO;oCACd,QAAQ,KAAK,CAAC,iCAAiC;oCAC/C,OAAO,QAAQ,CAAC,IAAI,GAAG;gCACzB;4BACF;4BACA,OAAO;gCACL,iBAAiB;gCACjB,QAAQ;gCACR,OAAO;gCACP,SAAS;gCACT,cAAc;gCACd,QAAQ;gCACR,UAAU;gCACV,YAAY;gCACZ,YAAY;4BACd;4BACA,cAAc,CAAC;gCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;4BAChC;4BACA,cAAc,CAAC;gCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;4BAChC;sCACD;;;;;;sCAGD,8OAAC,sIAAA,CAAA,YAAS;sCACP,KAAK,IAAI;;;;;;sCAEZ,8OAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,KAAK;4BAAS;;8CAC3C,8OAAC;oCACC,SAAS,IAAM,eAAe,KAAK,EAAE;oCACrC,OAAO;wCACL,iBAAiB,WAAW,KAAK,EAAE,IAAI,YAAY;wCACnD,OAAO;wCACP,QAAQ;wCACR,SAAS;wCACT,cAAc;wCACd,QAAQ;wCACR,UAAU;wCACV,YAAY;wCACZ,SAAS;wCACT,YAAY;wCACZ,KAAK;oCACP;;wCAEC,WAAW,KAAK,EAAE,IAAI,OAAO;wCAC7B,WAAW,KAAK,EAAE,IAAI,QAAQ;;;;;;;8CAEjC,8OAAC;oCACC,SAAS,IAAM,iBAAiB,CAAC;oCACjC,OAAO;wCACL,iBAAiB,gBAAgB,YAAY;wCAC7C,OAAO;wCACP,QAAQ;wCACR,SAAS;wCACT,cAAc;wCACd,QAAQ;wCACR,UAAU;wCACV,YAAY;oCACd;8CAEC,gBAAgB,SAAS;;;;;;;;;;;;;;;;;;;;;;;0BAOlC,8OAAC;gBAAK,OAAO;oBAAE,SAAS;gBAAc;;kCACpC,8OAAC;wBAAI,OAAO;4BAAE,UAAU;4BAAU,QAAQ;wBAAS;kCAEjD,cAAA,8OAAC,8IAAA,CAAA,aAAU;4BACT,OAAO;gCACL;oCAAE,OAAO;oCAAM,MAAM;gCAAI;gCACzB;oCAAE,OAAO;oCAAQ,SAAS;gCAAK;6BAChC;;;;;;;;;;;kCAIL,8OAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,QAAQ;4BACR,SAAS;4BACT,qBAAqB;4BACrB,KAAK;4BACL,YAAY;wBACd;;0CAEE,8OAAC;gCAAI,OAAO;oCAAE,UAAU;oCAAU,KAAK;gCAAQ;;oCAE5C,8BACC,8OAAC;wCAAI,OAAO;4CAAE,cAAc;wCAAO;kDACjC,cAAA,8OAAC,uJAAA,CAAA,uBAAoB;4CACnB,MAAM;4CACN,WAAW;4CACX,mBAAmB;4CACnB,OAAO;;;;;;;;;;6DAIX,8OAAC;wCAAI,OAAO;4CACV,aAAa;4CACb,cAAc;4CACd,UAAU;4CACV,cAAc;4CACd,UAAU;4CACV,WAAW;4CACX,QAAQ;wCACV;;0DAEE,8OAAC,uIAAA,CAAA,YAAS;gDACR,MAAM;gDACN,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;0DAIZ,8OAAC;gDAAI,OAAO;oDACV,UAAU;oDACV,KAAK;oDACL,MAAM;oDACN,iBAAiB;oDACjB,OAAO;oDACP,SAAS;oDACT,cAAc;oDACd,UAAU;oDACV,YAAY;oDACZ,QAAQ;gDACV;0DACG,KAAK,YAAY;;;;;;0DAGpB,8OAAC;gDAAI,OAAO;oDACV,UAAU;oDACV,KAAK;oDACL,OAAO;oDACP,iBAAiB;oDACjB,OAAO;oDACP,SAAS;oDACT,cAAc;oDACd,UAAU;oDACV,YAAY;oDACZ,QAAQ;gDACV;0DACG,KAAK,aAAa;;;;;;0DAIrB,8OAAC;gDAAI,OAAO;oDACV,UAAU;oDACV,QAAQ;oDACR,OAAO;oDACP,SAAS;oDACT,KAAK;oDACL,QAAQ;gDACV;0DACG,MAAM,IAAI,CAAC;oDAAE,QAAQ;gDAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;wDAEC,OAAO;4DACL,OAAO,IAAI,KAAK,KAAK,CAAC,KAAK,UAAU,GAAG,KAAK,YAAY;4DACzD,UAAU;4DACV,YAAY;wDACd;kEACD;uDANM;;;;;;;;;;;;;;;;kDAef,8OAAC;wCAAI,OAAO;4CACV,iBAAiB;4CACjB,SAAS;4CACT,cAAc;4CACd,WAAW;4CACX,QAAQ;wCACV;;0DACE,8OAAC;gDAAG,OAAO;oDACT,UAAU;oDACV,YAAY;oDACZ,OAAO;oDACP,cAAc;oDACd,YAAY;gDACd;0DAAG;;;;;;0DAGH,8OAAC;gDAAI,OAAO;oDAAE,SAAS;oDAAQ,KAAK;oDAAQ,YAAY;gDAAS;0DAC9D,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,8OAAC;wDAAgB,OAAO;4DAAE,WAAW;wDAAS;;0EAC5C,8OAAC;gEACC,OAAO;oEACL,OAAO;oEACP,QAAQ;oEACR,cAAc;oEACd,iBAAiB;oEACjB,QAAQ;oEACR,cAAc;oEACd,WAAW;gEACb;;;;;;0EAEF,8OAAC;gEAAK,OAAO;oEACX,UAAU;oEACV,OAAO;oEACP,YAAY;gEACd;0EACG;;;;;;;uDAjBK;;;;;;;;;;;;;;;;;;;;;;0CA0BlB,8OAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,eAAe;oCAAU,KAAK;gCAAO;;kDAElE,8OAAC;wCAAI,OAAO;4CACV,iBAAiB;4CACjB,SAAS;4CACT,cAAc;4CACd,WAAW;4CACX,QAAQ;wCACV;;0DACE,8OAAC;gDAAG,OAAO;oDACT,UAAU;oDACV,YAAY;oDACZ,OAAO;oDACP,cAAc;oDACd,YAAY;gDACd;0DAAG;;;;;;0DAGH,8OAAC;gDAAI,OAAO;oDAAE,SAAS;oDAAQ,qBAAqB;oDAAW,KAAK;gDAAO;;kEACzE,8OAAC;;0EACC,8OAAC;gEAAK,OAAO;oEAAE,YAAY;oEAAO,OAAO;gEAAU;0EAAG;;;;;;0EACtD,8OAAC;gEAAK,OAAO;oEAAE,OAAO;gEAAU;0EAAI,KAAK,SAAS;;;;;;;;;;;;kEAEpD,8OAAC;;0EACC,8OAAC;gEAAK,OAAO;oEAAE,YAAY;oEAAO,OAAO;gEAAU;0EAAG;;;;;;0EACtD,8OAAC;gEAAK,OAAO;oEAAE,OAAO;gEAAU;0EAAI,KAAK,YAAY;;;;;;;;;;;;kEAEvD,8OAAC;;0EACC,8OAAC;gEAAK,OAAO;oEAAE,YAAY;oEAAO,OAAO;gEAAU;0EAAG;;;;;;0EACtD,8OAAC;gEAAK,OAAO;oEAAE,OAAO;gEAAU;0EAAI,KAAK,aAAa;;;;;;;;;;;;kEAExD,8OAAC;;0EACC,8OAAC;gEAAK,OAAO;oEAAE,YAAY;oEAAO,OAAO;gEAAU;0EAAG;;;;;;0EACtD,8OAAC;gEAAK,OAAO;oEAAE,OAAO;gEAAU;0EAC7B,KAAK,UAAU,KAAK,SAAS,OAAO,KAAK,UAAU,KAAK,WAAW,OAAO;;;;;;;;;;;;kEAG/E,8OAAC;;0EACC,8OAAC;gEAAK,OAAO;oEAAE,YAAY;oEAAO,OAAO;gEAAU;0EAAG;;;;;;0EACtD,8OAAC;gEAAK,OAAO;oEAAE,OAAO;gEAAU;;oEAAI,KAAK,UAAU;oEAAC;;;;;;;;;;;;;kEAEtD,8OAAC;;0EACC,8OAAC;gEAAK,OAAO;oEAAE,YAAY;oEAAO,OAAO;gEAAU;0EAAG;;;;;;0EACtD,8OAAC;gEAAK,OAAO;oEAAE,OAAO;gEAAU;0EAAI,KAAK,kBAAkB,CAAC,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;kDAMlF,8OAAC;wCAAI,OAAO;4CACV,iBAAiB;4CACjB,SAAS;4CACT,cAAc;4CACd,WAAW;4CACX,QAAQ;wCACV;;0DACE,8OAAC;gDAAG,OAAO;oDACT,UAAU;oDACV,YAAY;oDACZ,OAAO;oDACP,cAAc;oDACd,YAAY;gDACd;0DAAG;;;;;;0DAGH,8OAAC;gDAAI,OAAO;oDAAE,SAAS;oDAAQ,eAAe;oDAAU,KAAK;gDAAS;;kEACpE,8OAAC;;0EACC,8OAAC;gEAAG,OAAO;oEAAE,YAAY;oEAAO,OAAO;oEAAW,cAAc;gEAAS;0EAAG;;;;;;0EAG5E,8OAAC;gEAAE,OAAO;oEAAE,OAAO;oEAAW,YAAY;gEAAM;0EAC7C,KAAK,kBAAkB,CAAC,MAAM;;;;;;;;;;;;kEAGnC,8OAAC;;0EACC,8OAAC;gEAAG,OAAO;oEAAE,YAAY;oEAAO,OAAO;oEAAW,cAAc;gEAAS;0EAAG;;;;;;0EAG5E,8OAAC;gEAAE,OAAO;oEAAE,OAAO;oEAAW,YAAY;gEAAM;0EAC7C,KAAK,kBAAkB,CAAC,WAAW;;;;;;;;;;;;kEAGxC,8OAAC;;0EACC,8OAAC;gEAAG,OAAO;oEAAE,YAAY;oEAAO,OAAO;oEAAW,cAAc;gEAAS;0EAAG;;;;;;0EAG5E,8OAAC;gEAAE,OAAO;oEAAE,OAAO;oEAAW,YAAY;gEAAM;0EAC7C,KAAK,kBAAkB,CAAC,SAAS;;;;;;;;;;;;;;;;;;;;;;;;kDAO1C,8OAAC;wCAAI,OAAO;4CACV,iBAAiB;4CACjB,SAAS;4CACT,cAAc;4CACd,WAAW;4CACX,QAAQ;wCACV;;0DACE,8OAAC;gDAAG,OAAO;oDACT,UAAU;oDACV,YAAY;oDACZ,OAAO;oDACP,cAAc;oDACd,YAAY;gDACd;0DAAG;;;;;;0DAGH,8OAAC;gDAAI,OAAO;oDAAE,SAAS;oDAAQ,eAAe;oDAAU,KAAK;gDAAO;0DACjE,OAAO,OAAO,CAAC,KAAK,YAAY,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,QAAQ,iBACtD,8OAAC;wDAAgB,OAAO;4DACtB,SAAS;4DACT,iBAAiB;4DACjB,cAAc;4DACd,YAAY;wDACd;;0EACE,8OAAC;gEAAK,OAAO;oEAAE,YAAY;oEAAO,OAAO;gEAAU;;oEAAI;oEAAM;;;;;;;0EAC7D,8OAAC;gEAAK,OAAO;oEAAE,OAAO;gEAAU;0EAAI;;;;;;;uDAP5B;;;;;;;;;;;;;;;;kDAchB,8OAAC;wCAAI,OAAO;4CACV,iBAAiB;4CACjB,SAAS;4CACT,cAAc;4CACd,WAAW;4CACX,QAAQ;wCACV;;0DACE,8OAAC;gDAAG,OAAO;oDACT,UAAU;oDACV,YAAY;oDACZ,OAAO;oDACP,cAAc;oDACd,YAAY;gDACd;0DAAG;;;;;;0DAGH,8OAAC;gDAAI,OAAO;oDAAE,SAAS;oDAAQ,eAAe;oDAAU,KAAK;gDAAS;0DACnE,KAAK,aAAa,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC9B,8OAAC;wDAAgB,OAAO;4DACtB,SAAS;4DACT,iBAAiB;4DACjB,cAAc;4DACd,QAAQ;wDACV;;0EACE,8OAAC;gEAAG,OAAO;oEACT,UAAU;oEACV,YAAY;oEACZ,OAAO;oEACP,cAAc;oEACd,YAAY;gEACd;0EACG,MAAM,IAAI;;;;;;0EAEb,8OAAC;gEAAE,OAAO;oEAAE,OAAO;oEAAW,cAAc;oEAAU,YAAY;gEAAM;0EACrE,MAAM,WAAW;;;;;;0EAEpB,8OAAC;gEAAK,OAAO;oEACX,UAAU;oEACV,OAAO;oEACP,YAAY;gEACd;0EACG,MAAM,MAAM;;;;;;;uDAvBP;;;;;;;;;;;;;;;;kDA+BhB,8OAAC;wCAAI,OAAO;4CACV,iBAAiB;4CACjB,SAAS;4CACT,cAAc;4CACd,WAAW;4CACX,QAAQ;wCACV;;0DACE,8OAAC;gDAAG,OAAO;oDACT,UAAU;oDACV,YAAY;oDACZ,OAAO;oDACP,cAAc;oDACd,YAAY;gDACd;0DAAG;;;;;;0DAGH,8OAAC;gDAAI,OAAO;oDAAE,SAAS;oDAAQ,UAAU;oDAAQ,KAAK;gDAAU;0DAC7D,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,8OAAC;wDAEC,OAAO;4DACL,SAAS;4DACT,UAAU;4DACV,iBAAiB;4DACjB,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,YAAY;wDACd;kEAEC;uDAXI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBzB", "debugId": null}}]}