/** @type {import('next').NextConfig} */
const nextConfig = {
  // 启用实验性功能
  experimental: {
    // 优化包大小
    optimizePackageImports: ['react', 'react-dom'],
  },
  
  // 图片优化配置
  images: {
    // 允许的外部图片域名 - 添加真实脸谱图片域名
    domains: [
      'via.placeholder.com',
      'd.bmcx.com',           // 主要脸谱图片源
      'img1.baidu.com',       // 百度图片源
      'milei.dpdns.org'       // 自己的域名
    ],
    // 图片格式优化
    formats: ['image/webp', 'image/avif'],
    // 图片尺寸配置
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    // 远程图片模式配置
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'd.bmcx.com',
        port: '',
        pathname: '/lianpu/d/**',
      },
      {
        protocol: 'https',
        hostname: 'img1.baidu.com',
        port: '',
        pathname: '/it/**',
      },
    ],
  },
  
  // 压缩配置
  compress: true,
  
  // 生产环境优化
  ...(process.env.NODE_ENV === 'production' && {
    // 输出配置
    output: 'standalone',
    
    // 静态资源优化
    assetPrefix: process.env.ASSET_PREFIX || '',
    
    // 重定向配置
    async redirects() {
      return [
        // 可以添加重定向规则
      ];
    },
    
    // 头部配置
    async headers() {
      return [
        {
          source: '/(.*)',
          headers: [
            {
              key: 'X-Frame-Options',
              value: 'DENY',
            },
            {
              key: 'X-Content-Type-Options',
              value: 'nosniff',
            },
            {
              key: 'Referrer-Policy',
              value: 'origin-when-cross-origin',
            },
          ],
        },
        {
          source: '/api/(.*)',
          headers: [
            {
              key: 'Cache-Control',
              value: 'public, max-age=3600, stale-while-revalidate=86400',
            },
          ],
        },
        {
          source: '/_next/static/(.*)',
          headers: [
            {
              key: 'Cache-Control',
              value: 'public, max-age=31536000, immutable',
            },
          ],
        },
      ];
    },
  }),
  
  // 环境变量配置
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  
  // Webpack 配置
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // 生产环境优化
    if (!dev && !isServer) {
      // 代码分割优化
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            enforce: true,
          },
        },
      };
    }
    
    return config;
  },
  
  // TypeScript 配置
  typescript: {
    // 生产构建时忽略类型错误（不推荐，但可以加快构建）
    // ignoreBuildErrors: false,
  },
  
  // ESLint 配置
  eslint: {
    // 生产构建时忽略 ESLint 错误（不推荐）
    // ignoreDuringBuilds: false,
  },
};

module.exports = nextConfig;
