// 京剧脸谱图片生成器
// 使用Canvas API生成高质量的脸谱图片

export interface MaskImageConfig {
  id: string;
  name: string;
  character: string;
  mainColors: string[];
  faceShape: 'oval' | 'round' | 'angular';
  eyeStyle: 'normal' | 'fierce' | 'gentle';
  decorations: string[];
}

export class MaskImageGenerator {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;

  constructor(width: number = 300, height: number = 300) {
    this.canvas = document.createElement('canvas');
    this.canvas.width = width;
    this.canvas.height = height;
    this.ctx = this.canvas.getContext('2d')!;
  }

  generateMaskImage(config: MaskImageConfig): string {
    const { ctx, canvas } = this;
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 设置高质量渲染
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    // 设置统一的背景
    const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, Math.max(canvas.width, canvas.height) / 2);
    gradient.addColorStop(0, '#FFFFFF');
    gradient.addColorStop(1, '#F8F9FA');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 绘制脸部轮廓
    this.drawFaceShape(config, centerX, centerY);

    // 绘制五官（按正确顺序）
    this.drawEyebrows(config, centerX, centerY);
    this.drawEyes(config, centerX, centerY);
    this.drawNose(config, centerX, centerY);
    this.drawMouth(config, centerX, centerY);

    // 绘制装饰
    this.drawDecorations(config, centerX, centerY);

    // 添加角色名称（在底部黑色条带上）
    this.drawCharacterName(config, centerX, centerY);

    return canvas.toDataURL('image/png', 0.95);
  }

  private drawFaceShape(config: MaskImageConfig, centerX: number, centerY: number) {
    const { ctx } = this;

    // 创建更精美的渐变效果
    const gradient = ctx.createRadialGradient(centerX, centerY - 40, 0, centerX, centerY, 130);
    gradient.addColorStop(0, config.mainColors[0]);
    gradient.addColorStop(0.7, config.mainColors[1] || config.mainColors[0]);
    gradient.addColorStop(1, this.darkenColor(config.mainColors[1] || config.mainColors[0], 0.2));

    ctx.fillStyle = gradient;
    ctx.strokeStyle = '#2D3748';
    ctx.lineWidth = 4;
    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
    ctx.shadowBlur = 8;
    ctx.shadowOffsetX = 2;
    ctx.shadowOffsetY = 2;

    ctx.beginPath();
    if (config.faceShape === 'oval') {
      ctx.ellipse(centerX, centerY - 10, 105, 125, 0, 0, 2 * Math.PI);
    } else if (config.faceShape === 'round') {
      ctx.arc(centerX, centerY - 5, 115, 0, 2 * Math.PI);
    } else {
      // angular - 更精细的棱角脸型
      ctx.moveTo(centerX - 95, centerY - 110);
      ctx.lineTo(centerX + 95, centerY - 110);
      ctx.lineTo(centerX + 115, centerY - 10);
      ctx.lineTo(centerX + 95, centerY + 125);
      ctx.lineTo(centerX - 95, centerY + 125);
      ctx.lineTo(centerX - 115, centerY - 10);
      ctx.closePath();
    }
    ctx.fill();
    ctx.stroke();

    // 重置阴影
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
  }

  // 辅助函数：加深颜色
  private darkenColor(color: string, factor: number): string {
    if (color.startsWith('#')) {
      const hex = color.slice(1);
      const r = parseInt(hex.substr(0, 2), 16);
      const g = parseInt(hex.substr(2, 2), 16);
      const b = parseInt(hex.substr(4, 2), 16);

      const newR = Math.floor(r * (1 - factor));
      const newG = Math.floor(g * (1 - factor));
      const newB = Math.floor(b * (1 - factor));

      return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
    }
    return color;
  }

  private drawEyes(config: MaskImageConfig, centerX: number, centerY: number) {
    const { ctx } = this;
    const eyeY = centerY - 20;
    const eyeWidth = config.eyeStyle === 'fierce' ? 25 : 20;
    const eyeHeight = config.eyeStyle === 'gentle' ? 12 : 15;

    // 左眼
    ctx.fillStyle = 'white';
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.ellipse(centerX - 35, eyeY, eyeWidth, eyeHeight, 0, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();

    // 右眼
    ctx.beginPath();
    ctx.ellipse(centerX + 35, eyeY, eyeWidth, eyeHeight, 0, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();

    // 眼珠
    ctx.fillStyle = '#000';
    ctx.beginPath();
    ctx.arc(centerX - 35, eyeY, 8, 0, 2 * Math.PI);
    ctx.fill();
    ctx.beginPath();
    ctx.arc(centerX + 35, eyeY, 8, 0, 2 * Math.PI);
    ctx.fill();
  }

  private drawEyebrows(config: MaskImageConfig, centerX: number, centerY: number) {
    const { ctx } = this;
    const browY = centerY - 50;
    
    ctx.strokeStyle = '#000';
    ctx.lineWidth = config.eyeStyle === 'fierce' ? 8 : 5;
    ctx.lineCap = 'round';

    // 左眉
    ctx.beginPath();
    if (config.eyeStyle === 'fierce') {
      ctx.moveTo(centerX - 60, browY);
      ctx.quadraticCurveTo(centerX - 35, browY - 15, centerX - 10, browY);
    } else {
      ctx.moveTo(centerX - 55, browY);
      ctx.quadraticCurveTo(centerX - 35, browY - 10, centerX - 15, browY);
    }
    ctx.stroke();

    // 右眉
    ctx.beginPath();
    if (config.eyeStyle === 'fierce') {
      ctx.moveTo(centerX + 10, browY);
      ctx.quadraticCurveTo(centerX + 35, browY - 15, centerX + 60, browY);
    } else {
      ctx.moveTo(centerX + 15, browY);
      ctx.quadraticCurveTo(centerX + 35, browY - 10, centerX + 55, browY);
    }
    ctx.stroke();
  }

  private drawNose(config: MaskImageConfig, centerX: number, centerY: number) {
    const { ctx } = this;
    
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 3;
    ctx.lineCap = 'round';

    ctx.beginPath();
    ctx.moveTo(centerX, centerY + 10);
    ctx.lineTo(centerX, centerY + 40);
    ctx.moveTo(centerX - 8, centerY + 35);
    ctx.lineTo(centerX + 8, centerY + 35);
    ctx.stroke();
  }

  private drawMouth(config: MaskImageConfig, centerX: number, centerY: number) {
    const { ctx } = this;
    const mouthY = centerY + 60;
    
    ctx.fillStyle = config.mainColors[0] === '#FFFFFF' ? '#FF0000' : '#8B0000';
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 2;

    ctx.beginPath();
    ctx.ellipse(centerX, mouthY, 15, 8, 0, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();
  }

  private drawDecorations(config: MaskImageConfig, centerX: number, centerY: number) {
    const { ctx } = this;
    
    // 根据角色添加特殊装饰
    if (config.decorations.includes('forehead-mark')) {
      ctx.fillStyle = '#FFD700';
      ctx.strokeStyle = '#000';
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(centerX, centerY - 80);
      ctx.lineTo(centerX + 10, centerY - 60);
      ctx.lineTo(centerX - 10, centerY - 60);
      ctx.closePath();
      ctx.fill();
      ctx.stroke();
    }

    if (config.decorations.includes('cheek-patterns')) {
      ctx.strokeStyle = config.mainColors[1] || '#000';
      ctx.lineWidth = 2;
      
      // 左脸装饰
      ctx.beginPath();
      ctx.arc(centerX - 70, centerY + 20, 15, 0, 2 * Math.PI);
      ctx.stroke();
      
      // 右脸装饰
      ctx.beginPath();
      ctx.arc(centerX + 70, centerY + 20, 15, 0, 2 * Math.PI);
      ctx.stroke();
    }

    if (config.decorations.includes('beard')) {
      ctx.strokeStyle = '#000';
      ctx.lineWidth = 4;
      ctx.lineCap = 'round';
      
      // 胡须线条
      for (let i = 0; i < 3; i++) {
        ctx.beginPath();
        ctx.moveTo(centerX - 60 + i * 20, centerY + 80 + i * 5);
        ctx.quadraticCurveTo(centerX, centerY + 100 + i * 5, centerX + 60 - i * 20, centerY + 80 + i * 5);
        ctx.stroke();
      }
    }
  }

  private drawCharacterName(config: MaskImageConfig, centerX: number, centerY: number) {
    const { ctx } = this;
    const nameBarY = this.canvas.height - 50;
    const nameBarHeight = 50;

    // 绘制名称背景条带
    const nameGradient = ctx.createLinearGradient(0, nameBarY, 0, nameBarY + nameBarHeight);
    nameGradient.addColorStop(0, 'rgba(0, 0, 0, 0.9)');
    nameGradient.addColorStop(1, 'rgba(0, 0, 0, 0.7)');

    ctx.fillStyle = nameGradient;
    ctx.fillRect(0, nameBarY, this.canvas.width, nameBarHeight);

    // 添加顶部边框
    ctx.strokeStyle = config.mainColors[0];
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.moveTo(0, nameBarY);
    ctx.lineTo(this.canvas.width, nameBarY);
    ctx.stroke();

    // 绘制角色名称
    ctx.fillStyle = 'white';
    ctx.font = 'bold 24px "Noto Serif SC", serif';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.shadowColor = 'rgba(0, 0, 0, 0.8)';
    ctx.shadowBlur = 4;
    ctx.shadowOffsetX = 1;
    ctx.shadowOffsetY = 1;

    ctx.fillText(config.character, centerX, nameBarY + nameBarHeight / 2);

    // 重置阴影
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
  }
}

// 预定义的脸谱配置
export const maskConfigs: Record<string, MaskImageConfig> = {
  guanyu: {
    id: 'guanyu',
    name: '关羽',
    character: '关羽',
    mainColors: ['#DC2626', '#991B1B'],
    faceShape: 'oval',
    eyeStyle: 'fierce',
    decorations: ['forehead-mark', 'beard']
  },
  caocao: {
    id: 'caocao',
    name: '曹操',
    character: '曹操',
    mainColors: ['#FFFFFF', '#F3F4F6'],
    faceShape: 'angular',
    eyeStyle: 'fierce',
    decorations: ['cheek-patterns']
  },
  zhangfei: {
    id: 'zhangfei',
    name: '张飞',
    character: '张飞',
    mainColors: ['#1F2937', '#000000'],
    faceShape: 'round',
    eyeStyle: 'fierce',
    decorations: ['beard']
  },
  huangzhong: {
    id: 'huangzhong',
    name: '黄忠',
    character: '黄忠',
    mainColors: ['#FFD700', '#F59E0B'],
    faceShape: 'oval',
    eyeStyle: 'normal',
    decorations: ['forehead-mark', 'beard']
  },
  diaochan: {
    id: 'diaochan',
    name: '貂蝉',
    character: '貂蝉',
    mainColors: ['#FFC0CB', '#F8BBD9'],
    faceShape: 'oval',
    eyeStyle: 'gentle',
    decorations: ['forehead-mark']
  },
  baozhen: {
    id: 'baozhen',
    name: '包拯',
    character: '包拯',
    mainColors: ['#000000', '#1F2937'],
    faceShape: 'round',
    eyeStyle: 'fierce',
    decorations: ['forehead-mark']
  },
  douerdun: {
    id: 'douerdun',
    name: '窦尔敦',
    character: '窦尔敦',
    mainColors: ['#1E40AF', '#3B82F6'],
    faceShape: 'angular',
    eyeStyle: 'fierce',
    decorations: ['cheek-patterns']
  },
  dianwei: {
    id: 'dianwei',
    name: '典韦',
    character: '典韦',
    mainColors: ['#7C2D12', '#DC2626'],
    faceShape: 'round',
    eyeStyle: 'fierce',
    decorations: ['beard']
  },
  likui: {
    id: 'likui',
    name: '李逵',
    character: '李逵',
    mainColors: ['#1F2937', '#000000'],
    faceShape: 'round',
    eyeStyle: 'fierce',
    decorations: ['beard']
  },
  sunwukong: {
    id: 'sunwukong',
    name: '孙悟空',
    character: '孙悟空',
    mainColors: ['#F59E0B', '#FFD700'],
    faceShape: 'oval',
    eyeStyle: 'fierce',
    decorations: ['forehead-mark', 'cheek-patterns']
  },
  zhubaijie: {
    id: 'zhubaijie',
    name: '猪八戒',
    character: '猪八戒',
    mainColors: ['#EC4899', '#F472B6'],
    faceShape: 'round',
    eyeStyle: 'normal',
    decorations: ['cheek-patterns']
  },
  baigu: {
    id: 'baigu',
    name: '白骨精',
    character: '白骨精',
    mainColors: ['#F3F4F6', '#E5E7EB'],
    faceShape: 'angular',
    eyeStyle: 'fierce',
    decorations: ['forehead-mark']
  },
  huajiangjun: {
    id: 'huajiangjun',
    name: '花脸将军',
    character: '花脸将军',
    mainColors: ['#7C3AED', '#A855F7'],
    faceShape: 'oval',
    eyeStyle: 'fierce',
    decorations: ['forehead-mark', 'cheek-patterns']
  },
  qingyi: {
    id: 'qingyi',
    name: '青衣花旦',
    character: '青衣',
    mainColors: ['#10B981', '#34D399'],
    faceShape: 'oval',
    eyeStyle: 'gentle',
    decorations: ['forehead-mark']
  },
  xiaosheng: {
    id: 'xiaosheng',
    name: '小生角色',
    character: '小生',
    mainColors: ['#3B82F6', '#60A5FA'],
    faceShape: 'oval',
    eyeStyle: 'gentle',
    decorations: []
  }
};
