# 🌐 Domain Configuration Guide
## Beijing Opera Masks Cultural Exhibition Platform

### 📋 Overview
This guide provides step-by-step instructions for configuring domain name mapping to point to **milei7.dpdns.org** for the Beijing Opera Masks cultural exhibition platform.

### 🎯 Target Configuration
- **Source Domain**: Your custom domain (to be configured)
- **Target Domain**: milei7.dpdns.org
- **Current Development**: http://localhost:3002
- **Production URL**: https://milei7.dpdns.org

---

## 🔧 Technical Configuration

### 1. DNS Configuration Options

#### Option A: CNAME Record (Recommended)
```dns
Type: CNAME
Name: www (or your subdomain)
Value: milei7.dpdns.org
TTL: 300 (5 minutes)
```

#### Option B: A Record
```dns
Type: A
Name: @ (root domain) or www
Value: [IP address of milei7.dpdns.org]
TTL: 300 (5 minutes)
```

#### Option C: Domain Forwarding
```
Source: yourdomain.com
Target: https://milei7.dpdns.org
Type: 301 Permanent Redirect
```

### 2. Application Configuration

#### Environment Variables
The application is configured to use environment variables for domain settings:

**Production (.env.production)**:
```env
NEXT_PUBLIC_BASE_URL=https://milei7.dpdns.org
NODE_ENV=production
NEXT_PUBLIC_SITE_NAME=京剧脸谱文化展示平台
```

**Development (.env.local)**:
```env
NEXT_PUBLIC_BASE_URL=http://localhost:3002
NODE_ENV=development
```

#### Next.js Configuration
The `next.config.js` has been updated to support:
- ✅ External image domains (d.bmcx.com, img1.baidu.com)
- ✅ Remote image patterns for mask images
- ✅ Production optimization
- ✅ Security headers
- ✅ Cache configuration

---

## 🚀 Deployment Steps

### Step 1: Build for Production
```bash
# Navigate to project directory
cd beijing-opera-masks

# Install dependencies
npm install

# Build for production
npm run build

# Start production server
npm start
```

### Step 2: Server Configuration
The application will run on the configured port and be accessible via the mapped domain.

### Step 3: SSL/HTTPS Setup
Ensure SSL certificate is configured for the domain:
- ✅ HTTPS redirect enabled
- ✅ Security headers configured
- ✅ Mixed content prevention

---

## 🧪 Testing Checklist

### Pre-Deployment Testing
- [ ] Application builds successfully
- [ ] All 9 real mask images load correctly
- [ ] Navigation between pages works
- [ ] Modal dialogs function properly
- [ ] Responsive design works on mobile

### Post-Deployment Testing
- [ ] Domain resolves correctly
- [ ] HTTPS certificate is valid
- [ ] All mask images load from external sources
- [ ] Sitemap.xml generates correctly
- [ ] Robots.txt is accessible
- [ ] Performance is acceptable

### Functionality Testing
- [ ] Homepage displays 9 characters with real masks
- [ ] Character detail pages load correctly
- [ ] Modal popups work properly
- [ ] Theme switching functions
- [ ] Animation features work
- [ ] External image loading (bmcx.com, baidu.com)

---

## 🔍 Troubleshooting

### Common Issues

#### 1. Images Not Loading
**Problem**: External mask images fail to load
**Solution**: 
- Check Next.js image domains configuration
- Verify CORS headers
- Test image URLs directly

#### 2. Domain Not Resolving
**Problem**: Custom domain doesn't point to milei7.dpdns.org
**Solution**:
- Verify DNS records propagation (use dig or nslookup)
- Check TTL settings
- Wait for DNS propagation (up to 48 hours)

#### 3. SSL Certificate Issues
**Problem**: HTTPS not working
**Solution**:
- Verify SSL certificate installation
- Check certificate validity
- Ensure proper redirect configuration

#### 4. Performance Issues
**Problem**: Slow loading times
**Solution**:
- Enable image optimization
- Check CDN configuration
- Monitor external image sources

### Debug Commands
```bash
# Check DNS resolution
nslookup milei7.dpdns.org

# Test domain connectivity
curl -I https://milei7.dpdns.org

# Check SSL certificate
openssl s_client -connect milei7.dpdns.org:443

# Test image loading
curl -I https://d.bmcx.com/lianpu/d/0072.jpg
```

---

## 📊 Current Application Status

### ✅ Ready for Production
- **Real Mask Images**: 9 characters configured
- **Image Sources**: d.bmcx.com (7), baidu.com (2)
- **Features**: Homepage grid, detail pages, modals
- **Responsive**: Mobile and desktop optimized
- **Performance**: Optimized builds and caching

### 🎭 Character Coverage
| Character | ID | Image Source | Status |
|-----------|----|--------------| -------|
| 关羽 | guanyu | d.bmcx.com | ✅ |
| 包拯 | baogong | d.bmcx.com | ✅ |
| 曹操 | caocao | d.bmcx.com | ✅ |
| 张飞 | zhangfei | d.bmcx.com | ✅ |
| 窦尔敦 | doulujin | d.bmcx.com | ✅ |
| 杨七郎 | yangqilang | baidu.com | ✅ |
| 蒋干 | jianggan | d.bmcx.com | ✅ |
| 刘备 | liubei | baidu.com | ✅ |
| 孙悟空 | sunwukong | d.bmcx.com | ✅ |

---

## 🔐 Security Considerations

### Headers Configuration
```javascript
// Security headers already configured in next.config.js
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
Referrer-Policy: origin-when-cross-origin
```

### Image Security
- ✅ Whitelisted image domains
- ✅ Remote pattern validation
- ✅ CORS handling for external images

### Performance Security
- ✅ Cache headers configured
- ✅ Static asset optimization
- ✅ Compression enabled

---

## 📞 Support Information

### Configuration Files Modified
- ✅ `.env.production` - Production environment variables
- ✅ `next.config.js` - Image domains and security headers
- ✅ `src/app/sitemap.ts` - Dynamic sitemap generation
- ✅ `src/app/robots.ts` - SEO configuration

### Key Features Verified
- ✅ 9 real Beijing Opera mask images
- ✅ Intelligent image loading with Canvas fallback
- ✅ Responsive design for all devices
- ✅ Cultural accuracy and educational value
- ✅ Performance optimization

**Configuration Date**: 2025-07-22
**Platform Status**: ✅ Ready for Production Deployment
**Domain Target**: milei7.dpdns.org
