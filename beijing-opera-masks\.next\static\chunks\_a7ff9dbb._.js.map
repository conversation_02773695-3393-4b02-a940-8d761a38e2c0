{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/performance/PerformanceMonitor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\ninterface PerformanceMetrics {\n  fcp?: number; // First Contentful Paint\n  lcp?: number; // Largest Contentful Paint\n  fid?: number; // First Input Delay\n  cls?: number; // Cumulative Layout Shift\n  ttfb?: number; // Time to First Byte\n}\n\nexport function PerformanceMonitor() {\n  useEffect(() => {\n    // 只在生产环境中启用性能监控\n    if (process.env.NODE_ENV !== 'production') {\n      return;\n    }\n    \n    const metrics: PerformanceMetrics = {};\n    \n    // 监控 First Contentful Paint\n    const observeFCP = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        entries.forEach((entry) => {\n          if (entry.name === 'first-contentful-paint') {\n            metrics.fcp = entry.startTime;\n            console.log('FCP:', entry.startTime);\n          }\n        });\n      });\n      observer.observe({ entryTypes: ['paint'] });\n    };\n    \n    // 监控 Largest Contentful Paint\n    const observeLCP = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        const lastEntry = entries[entries.length - 1];\n        metrics.lcp = lastEntry.startTime;\n        console.log('LCP:', lastEntry.startTime);\n      });\n      observer.observe({ entryTypes: ['largest-contentful-paint'] });\n    };\n    \n    // 监控 First Input Delay\n    const observeFID = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        entries.forEach((entry: any) => {\n          metrics.fid = entry.processingStart - entry.startTime;\n          console.log('FID:', metrics.fid);\n        });\n      });\n      observer.observe({ entryTypes: ['first-input'] });\n    };\n    \n    // 监控 Cumulative Layout Shift\n    const observeCLS = () => {\n      let clsValue = 0;\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        entries.forEach((entry: any) => {\n          if (!entry.hadRecentInput) {\n            clsValue += entry.value;\n          }\n        });\n        metrics.cls = clsValue;\n        console.log('CLS:', clsValue);\n      });\n      observer.observe({ entryTypes: ['layout-shift'] });\n    };\n    \n    // 监控 Time to First Byte\n    const observeTTFB = () => {\n      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;\n      if (navigationEntry) {\n        metrics.ttfb = navigationEntry.responseStart - navigationEntry.requestStart;\n        console.log('TTFB:', metrics.ttfb);\n      }\n    };\n    \n    // 检查浏览器支持\n    if ('PerformanceObserver' in window) {\n      observeFCP();\n      observeLCP();\n      observeFID();\n      observeCLS();\n      observeTTFB();\n    }\n    \n    // 页面卸载时发送性能数据\n    const sendMetrics = () => {\n      // 这里可以发送到分析服务\n      console.log('Performance Metrics:', metrics);\n      \n      // 示例：发送到Google Analytics或其他分析服务\n      // if (window.gtag) {\n      //   window.gtag('event', 'web_vitals', {\n      //     custom_map: {\n      //       metric_fcp: 'fcp',\n      //       metric_lcp: 'lcp',\n      //       metric_fid: 'fid',\n      //       metric_cls: 'cls',\n      //       metric_ttfb: 'ttfb'\n      //     },\n      //     fcp: metrics.fcp,\n      //     lcp: metrics.lcp,\n      //     fid: metrics.fid,\n      //     cls: metrics.cls,\n      //     ttfb: metrics.ttfb\n      //   });\n      // }\n    };\n    \n    // 监听页面卸载事件\n    window.addEventListener('beforeunload', sendMetrics);\n    \n    // 监听页面可见性变化\n    document.addEventListener('visibilitychange', () => {\n      if (document.visibilityState === 'hidden') {\n        sendMetrics();\n      }\n    });\n    \n    return () => {\n      window.removeEventListener('beforeunload', sendMetrics);\n      document.removeEventListener('visibilitychange', sendMetrics);\n    };\n  }, []);\n  \n  return null; // 这个组件不渲染任何内容\n}\n\n// 性能优化工具函数\nexport const performanceUtils = {\n  // 预加载关键资源\n  preloadResource: (href: string, as: string) => {\n    const link = document.createElement('link');\n    link.rel = 'preload';\n    link.href = href;\n    link.as = as;\n    document.head.appendChild(link);\n  },\n  \n  // 预连接到外部域名\n  preconnect: (href: string) => {\n    const link = document.createElement('link');\n    link.rel = 'preconnect';\n    link.href = href;\n    document.head.appendChild(link);\n  },\n  \n  // DNS预解析\n  dnsPrefetch: (href: string) => {\n    const link = document.createElement('link');\n    link.rel = 'dns-prefetch';\n    link.href = href;\n    document.head.appendChild(link);\n  },\n  \n  // 延迟执行非关键代码\n  defer: (callback: () => void, delay: number = 0) => {\n    if ('requestIdleCallback' in window) {\n      requestIdleCallback(callback);\n    } else {\n      setTimeout(callback, delay);\n    }\n  },\n  \n  // 检查网络连接质量\n  getNetworkInfo: () => {\n    if ('connection' in navigator) {\n      const connection = (navigator as any).connection;\n      return {\n        effectiveType: connection.effectiveType,\n        downlink: connection.downlink,\n        rtt: connection.rtt,\n        saveData: connection.saveData\n      };\n    }\n    return null;\n  }\n};\n"], "names": [], "mappings": ";;;;AAeQ;AAbR;;AAFA;;AAYO,SAAS;;IACd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,gBAAgB;YAChB,wCAA2C;gBACzC;YACF;;;YAEA,MAAM;YAEN,4BAA4B;YAC5B,MAAM;YAaN,8BAA8B;YAC9B,MAAM;YAUN,uBAAuB;YACvB,MAAM;YAWN,6BAA6B;YAC7B,MAAM;YAeN,wBAAwB;YACxB,MAAM;YAiBN,cAAc;YACd,MAAM;QAqCR;uCAAG,EAAE;IAEL,OAAO,MAAM,cAAc;AAC7B;GAzHgB;KAAA;AA4HT,MAAM,mBAAmB;IAC9B,UAAU;IACV,iBAAiB,CAAC,MAAc;QAC9B,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,GAAG,GAAG;QACX,KAAK,IAAI,GAAG;QACZ,KAAK,EAAE,GAAG;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,WAAW;IACX,YAAY,CAAC;QACX,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,GAAG,GAAG;QACX,KAAK,IAAI,GAAG;QACZ,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,SAAS;IACT,aAAa,CAAC;QACZ,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,GAAG,GAAG;QACX,KAAK,IAAI,GAAG;QACZ,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,YAAY;IACZ,OAAO,SAAC;YAAsB,yEAAgB;QAC5C,IAAI,yBAAyB,QAAQ;YACnC,oBAAoB;QACtB,OAAO;YACL,WAAW,UAAU;QACvB;IACF;IAEA,WAAW;IACX,gBAAgB;QACd,IAAI,gBAAgB,WAAW;YAC7B,MAAM,aAAa,AAAC,UAAkB,UAAU;YAChD,OAAO;gBACL,eAAe,WAAW,aAAa;gBACvC,UAAU,WAAW,QAAQ;gBAC7B,KAAK,WAAW,GAAG;gBACnB,UAAU,WAAW,QAAQ;YAC/B;QACF;QACA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/providers/ThemeProvider.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\nexport type Theme = 'light' | 'dark';\n\n// 主题颜色配置\nexport const themeColors = {\n  light: {\n    background: '#FFFFFF',\n    backgroundSecondary: '#F9FAFB',\n    backgroundTertiary: '#F3F4F6',\n    textPrimary: '#1F2937',\n    textSecondary: '#374151',\n    textTertiary: '#6B7280',\n    textMuted: '#9CA3AF',\n    border: '#E5E7EB',\n    borderSecondary: '#D1D5DB',\n    primary: '#B91C1C',\n    primaryHover: '#991B1B',\n    secondary: '#F59E0B',\n    secondaryHover: '#D97706',\n    success: '#10B981',\n    warning: '#F59E0B',\n    error: '#EF4444',\n    shadow: 'rgba(0, 0, 0, 0.1)',\n    shadowHover: 'rgba(0, 0, 0, 0.2)',\n  },\n  dark: {\n    background: '#0F1419',\n    backgroundSecondary: '#1A1F2E',\n    backgroundTertiary: '#252A3A',\n    textPrimary: '#F9FAFB',\n    textSecondary: '#E5E7EB',\n    textTertiary: '#D1D5DB',\n    textMuted: '#9CA3AF',\n    border: '#374151',\n    borderSecondary: '#4B5563',\n    primary: '#DC2626',\n    primaryHover: '#EF4444',\n    secondary: '#FBBF24',\n    secondaryHover: '#FCD34D',\n    success: '#34D399',\n    warning: '#FBBF24',\n    error: '#F87171',\n    shadow: 'rgba(0, 0, 0, 0.3)',\n    shadowHover: 'rgba(0, 0, 0, 0.5)',\n  }\n};\n\ninterface ThemeContextType {\n  theme: Theme;\n  toggleTheme: () => void;\n  setTheme: (theme: Theme) => void;\n  colors: typeof themeColors.light;\n  styles: {\n    card: React.CSSProperties;\n    navigation: React.CSSProperties;\n    button: {\n      primary: React.CSSProperties;\n      secondary: React.CSSProperties;\n    };\n  };\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport function useTheme() {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n}\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n}\n\nexport function ThemeProvider({ children }: ThemeProviderProps) {\n  const [theme, setThemeState] = useState<Theme>('light');\n\n  // 从localStorage加载主题\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const savedTheme = localStorage.getItem('beijing-opera-theme') as Theme;\n      if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {\n        setThemeState(savedTheme);\n      } else {\n        // 检测系统主题偏好\n        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n        setThemeState(prefersDark ? 'dark' : 'light');\n      }\n    }\n  }, []);\n\n  // 设置主题\n  const setTheme = (newTheme: Theme) => {\n    setThemeState(newTheme);\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('beijing-opera-theme', newTheme);\n      document.documentElement.setAttribute('data-theme', newTheme);\n    }\n  };\n\n  // 切换主题\n  const toggleTheme = () => {\n    const newTheme = theme === 'light' ? 'dark' : 'light';\n    setTheme(newTheme);\n  };\n\n  // 获取当前主题的颜色\n  const colors = themeColors[theme];\n\n  // 生成样式\n  const styles = {\n    card: {\n      backgroundColor: colors.backgroundSecondary,\n      border: `1px solid ${colors.border}`,\n      boxShadow: `0 4px 6px -1px ${colors.shadow}`,\n      transition: 'all 0.3s ease'\n    },\n    navigation: {\n      backgroundColor: colors.backgroundSecondary,\n      borderBottom: `2px solid ${colors.secondary}`,\n      boxShadow: `0 4px 6px -1px ${colors.shadow}`\n    },\n    button: {\n      primary: {\n        backgroundColor: colors.primary,\n        color: colors.background,\n        border: `2px solid ${colors.primary}`,\n      },\n      secondary: {\n        backgroundColor: 'transparent',\n        color: colors.primary,\n        border: `2px solid ${colors.primary}`,\n      }\n    }\n  };\n\n  // 应用主题到body\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      document.body.style.backgroundColor = colors.background;\n      document.body.style.color = colors.textPrimary;\n      document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';\n      document.documentElement.setAttribute('data-theme', theme);\n    }\n  }, [theme, colors]);\n\n  const contextValue: ThemeContextType = {\n    theme,\n    toggleTheme,\n    setTheme,\n    colors,\n    styles\n  };\n\n  return (\n    <ThemeContext.Provider value={contextValue}>\n      {children}\n    </ThemeContext.Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;;;AAFA;;AAOO,MAAM,cAAc;IACzB,OAAO;QACL,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QACpB,aAAa;QACb,eAAe;QACf,cAAc;QACd,WAAW;QACX,QAAQ;QACR,iBAAiB;QACjB,SAAS;QACT,cAAc;QACd,WAAW;QACX,gBAAgB;QAChB,SAAS;QACT,SAAS;QACT,OAAO;QACP,QAAQ;QACR,aAAa;IACf;IACA,MAAM;QACJ,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QACpB,aAAa;QACb,eAAe;QACf,cAAc;QACd,WAAW;QACX,QAAQ;QACR,iBAAiB;QACjB,SAAS;QACT,cAAc;QACd,WAAW;QACX,gBAAgB;QAChB,SAAS;QACT,SAAS;QACT,OAAO;QACP,QAAQ;QACR,aAAa;IACf;AACF;AAiBA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAYT,SAAS,cAAc,KAAgC;QAAhC,EAAE,QAAQ,EAAsB,GAAhC;;IAC5B,MAAM,CAAC,OAAO,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAE/C,oBAAoB;IACpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,wCAAmC;gBACjC,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,cAAc,CAAC,eAAe,WAAW,eAAe,MAAM,GAAG;oBACnE,cAAc;gBAChB,OAAO;oBACL,WAAW;oBACX,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO;oBAC7E,cAAc,cAAc,SAAS;gBACvC;YACF;QACF;kCAAG,EAAE;IAEL,OAAO;IACP,MAAM,WAAW,CAAC;QAChB,cAAc;QACd,wCAAmC;YACjC,aAAa,OAAO,CAAC,uBAAuB;YAC5C,SAAS,eAAe,CAAC,YAAY,CAAC,cAAc;QACtD;IACF;IAEA,OAAO;IACP,MAAM,cAAc;QAClB,MAAM,WAAW,UAAU,UAAU,SAAS;QAC9C,SAAS;IACX;IAEA,YAAY;IACZ,MAAM,SAAS,WAAW,CAAC,MAAM;IAEjC,OAAO;IACP,MAAM,SAAS;QACb,MAAM;YACJ,iBAAiB,OAAO,mBAAmB;YAC3C,QAAQ,AAAC,aAA0B,OAAd,OAAO,MAAM;YAClC,WAAW,AAAC,kBAA+B,OAAd,OAAO,MAAM;YAC1C,YAAY;QACd;QACA,YAAY;YACV,iBAAiB,OAAO,mBAAmB;YAC3C,cAAc,AAAC,aAA6B,OAAjB,OAAO,SAAS;YAC3C,WAAW,AAAC,kBAA+B,OAAd,OAAO,MAAM;QAC5C;QACA,QAAQ;YACN,SAAS;gBACP,iBAAiB,OAAO,OAAO;gBAC/B,OAAO,OAAO,UAAU;gBACxB,QAAQ,AAAC,aAA2B,OAAf,OAAO,OAAO;YACrC;YACA,WAAW;gBACT,iBAAiB;gBACjB,OAAO,OAAO,OAAO;gBACrB,QAAQ,AAAC,aAA2B,OAAf,OAAO,OAAO;YACrC;QACF;IACF;IAEA,YAAY;IACZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,wCAAmC;gBACjC,SAAS,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,OAAO,UAAU;gBACvD,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO,WAAW;gBAC9C,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG;gBACjC,SAAS,eAAe,CAAC,YAAY,CAAC,cAAc;YACtD;QACF;kCAAG;QAAC;QAAO;KAAO;IAElB,MAAM,eAAiC;QACrC;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;IArFgB;KAAA", "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 475, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}