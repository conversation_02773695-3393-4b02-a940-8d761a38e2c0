import { Normalizers } from '../../normalizers';
import type { Normalizer } from '../../normalizer';
import { PrefixingNormalizer } from '../../prefixing-normalizer';
export declare class AppBundlePathNormalizer extends PrefixingNormalizer {
    constructor();
    normalize(page: string): string;
}
export declare class DevA<PERSON><PERSON><PERSON>lePathNormalizer extends Normalizers {
    constructor(pageNormalizer: Normalizer, isTurbopack: boolean);
    normalize(filename: string): string;
}
