'use client';

import React, { useState, useEffect, useRef } from 'react';
import { OperaMask, DrawingStep } from '@/types/mask';

interface MaskDrawingAnimationProps {
  mask: OperaMask;
  isPlaying: boolean;
  onPlayStateChange: (isPlaying: boolean) => void;
  speed?: number;
  className?: string;
}

export function MaskDrawingAnimation({
  mask,
  isPlaying,
  onPlayStateChange,
  speed = 1,
  className
}: MaskDrawingAnimationProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const animationRef = useRef<number | null>(null);
  
  // 重置动画
  const resetAnimation = () => {
    setCurrentStep(0);
    setProgress(0);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
      animationRef.current = null;
    }
  };
  
  // 播放动画
  useEffect(() => {
    if (isPlaying && currentStep < mask.drawingSteps.length) {
      const currentStepData = mask.drawingSteps[currentStep];
      const stepDuration = currentStepData.duration / speed;
      const frameRate = 60; // 60 FPS
      const totalFrames = (stepDuration / 1000) * frameRate;
      let frame = 0;
      
      const animate = () => {
        frame++;
        const stepProgress = Math.min(frame / totalFrames, 1);
        setProgress(stepProgress);
        
        if (stepProgress < 1) {
          animationRef.current = requestAnimationFrame(animate);
        } else {
          // 当前步骤完成，移动到下一步
          setTimeout(() => {
            if (currentStep < mask.drawingSteps.length - 1) {
              setCurrentStep(prev => prev + 1);
              setProgress(0);
            } else {
              // 动画完成
              onPlayStateChange(false);
            }
          }, 200); // 短暂停顿
        }
      };
      
      animationRef.current = requestAnimationFrame(animate);
    }
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isPlaying, currentStep, speed, mask.drawingSteps, onPlayStateChange]);
  
  // 暂停时清理动画
  useEffect(() => {
    if (!isPlaying && animationRef.current) {
      cancelAnimationFrame(animationRef.current);
      animationRef.current = null;
    }
  }, [isPlaying]);
  
  // 生成SVG路径
  const generateMaskSVG = () => {
    const steps = mask.drawingSteps.slice(0, currentStep + 1);
    const currentStepData = mask.drawingSteps[currentStep];
    
    return (
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 300 300"
        style={{ background: 'white', borderRadius: '1rem' }}
      >
        {/* 脸部轮廓 */}
        <ellipse
          cx="150"
          cy="150"
          rx="120"
          ry="140"
          fill="#FFF8DC"
          stroke="#D4A574"
          strokeWidth="2"
        />
        
        {/* 绘制步骤 */}
        {steps.map((step, index) => {
          const isCurrentStep = index === currentStep;
          const opacity = isCurrentStep ? progress : 1;
          const strokeDasharray = isCurrentStep ? `${progress * 100} 100` : undefined;
          
          return (
            <g key={step.id} opacity={opacity}>
              {renderStepElement(step, strokeDasharray)}
            </g>
          );
        })}
        
        {/* 当前步骤标签 */}
        <text
          x="150"
          y="280"
          textAnchor="middle"
          fontSize="14"
          fill="#1F2937"
          fontWeight="600"
        >
          {currentStepData?.name || '完成'}
        </text>
      </svg>
    );
  };
  
  // 渲染步骤元素
  const renderStepElement = (step: DrawingStep, strokeDasharray?: string) => {
    switch (step.name) {
      case '底色':
        return (
          <ellipse
            cx="150"
            cy="150"
            rx="115"
            ry="135"
            fill={step.color}
            fillOpacity="0.8"
          />
        );
      
      case '眉毛':
        return (
          <g>
            <path
              d="M 100 120 Q 130 110 160 120"
              stroke={step.color}
              strokeWidth={step.strokeWidth || 4}
              fill="none"
              strokeLinecap="round"
              strokeDasharray={strokeDasharray}
            />
            <path
              d="M 140 120 Q 170 110 200 120"
              stroke={step.color}
              strokeWidth={step.strokeWidth || 4}
              fill="none"
              strokeLinecap="round"
              strokeDasharray={strokeDasharray}
            />
          </g>
        );
      
      case '眼部':
        return (
          <g>
            <ellipse
              cx="120"
              cy="140"
              rx="15"
              ry="10"
              stroke={step.color}
              strokeWidth={step.strokeWidth || 3}
              fill="white"
              strokeDasharray={strokeDasharray}
            />
            <ellipse
              cx="180"
              cy="140"
              rx="15"
              ry="10"
              stroke={step.color}
              strokeWidth={step.strokeWidth || 3}
              fill="white"
              strokeDasharray={strokeDasharray}
            />
            <circle cx="120" cy="140" r="5" fill={step.color} />
            <circle cx="180" cy="140" r="5" fill={step.color} />
          </g>
        );
      
      case '鼻梁':
      case '鼻部':
        return (
          <path
            d="M 150 160 L 150 180 M 140 185 L 160 185"
            stroke={step.color}
            strokeWidth={step.strokeWidth || 3}
            strokeLinecap="round"
            strokeDasharray={strokeDasharray}
          />
        );
      
      case '装饰':
        return (
          <g>
            <path
              d="M 150 100 L 155 110 L 145 110 Z"
              fill={step.color}
              stroke={step.color}
              strokeWidth="1"
            />
            <circle cx="120" cy="180" r="3" fill={step.color} />
            <circle cx="180" cy="180" r="3" fill={step.color} />
          </g>
        );
      
      case '胡须':
        return (
          <g>
            <path
              d="M 120 200 Q 150 220 180 200"
              stroke={step.color}
              strokeWidth={step.strokeWidth || 2}
              fill="none"
              strokeDasharray={strokeDasharray}
            />
            <path
              d="M 110 210 Q 150 230 190 210"
              stroke={step.color}
              strokeWidth={step.strokeWidth || 2}
              fill="none"
              strokeDasharray={strokeDasharray}
            />
          </g>
        );
      
      case '唇部':
        return (
          <ellipse
            cx="150"
            cy="200"
            rx="12"
            ry="6"
            fill={step.color}
            stroke={step.color}
            strokeWidth="1"
          />
        );
      
      default:
        return (
          <circle
            cx="150"
            cy="150"
            r="5"
            fill={step.color}
            stroke={step.color}
          />
        );
    }
  };
  
  return (
    <div className={className} style={{ position: 'relative' }}>
      {/* SVG 动画区域 */}
      <div style={{
        aspectRatio: '1',
        border: '3px solid #F59E0B',
        borderRadius: '1rem',
        overflow: 'hidden',
        backgroundColor: 'white'
      }}>
        {generateMaskSVG()}
      </div>
      
      {/* 控制面板 */}
      <div style={{
        marginTop: '1rem',
        padding: '1rem',
        backgroundColor: 'white',
        borderRadius: '0.5rem',
        border: '2px solid #F59E0B',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1rem'
        }}>
          <h3 style={{
            fontSize: '1.125rem',
            fontWeight: '600',
            color: '#1F2937',
            fontFamily: '"Noto Serif SC", serif'
          }}>
            绘制过程演示
          </h3>
          <span style={{
            fontSize: '0.875rem',
            color: '#6B7280'
          }}>
            步骤 {currentStep + 1} / {mask.drawingSteps.length}
          </span>
        </div>
        
        {/* 进度条 */}
        <div style={{
          width: '100%',
          height: '6px',
          backgroundColor: '#E5E7EB',
          borderRadius: '3px',
          marginBottom: '1rem',
          overflow: 'hidden'
        }}>
          <div
            style={{
              height: '100%',
              backgroundColor: '#F59E0B',
              borderRadius: '3px',
              transition: 'width 0.1s ease',
              width: `${((currentStep + progress) / mask.drawingSteps.length) * 100}%`
            }}
          />
        </div>
        
        {/* 控制按钮 */}
        <div style={{
          display: 'flex',
          gap: '0.5rem',
          justifyContent: 'center'
        }}>
          <button
            onClick={() => onPlayStateChange(!isPlaying)}
            style={{
              backgroundColor: isPlaying ? '#EF4444' : '#10B981',
              color: 'white',
              border: 'none',
              padding: '0.5rem 1rem',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              fontSize: '0.875rem',
              fontWeight: '500'
            }}
          >
            {isPlaying ? '暂停' : '播放'}
          </button>
          
          <button
            onClick={resetAnimation}
            style={{
              backgroundColor: '#6B7280',
              color: 'white',
              border: 'none',
              padding: '0.5rem 1rem',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              fontSize: '0.875rem',
              fontWeight: '500'
            }}
          >
            重置
          </button>
        </div>
        
        {/* 当前步骤描述 */}
        <div style={{
          marginTop: '1rem',
          padding: '0.75rem',
          backgroundColor: '#F9FAFB',
          borderRadius: '0.5rem',
          borderLeft: '4px solid #F59E0B'
        }}>
          <p style={{
            fontSize: '0.875rem',
            color: '#374151',
            margin: 0
          }}>
            <strong>{mask.drawingSteps[currentStep]?.name}：</strong>
            {mask.drawingSteps[currentStep]?.description}
          </p>
        </div>
      </div>
    </div>
  );
}
