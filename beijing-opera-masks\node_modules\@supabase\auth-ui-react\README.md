# Supabase Auth UI React

Supabase Auth UI is a collection of pre built UI components that work seamlessly with @supabase/auth-helpers.

The main purpose of these components is to allow developers to get working on their apps quickly, but also be able to use their own style/css.

Auth UI is kept deliberately separate from @supabase/auth-helpers so that developers can migrate away from pre-built UI components as their UI system naturally matures.

<img width="552" alt="Screenshot 2022-04-30 at 9 30 25 PM" src="https://user-images.githubusercontent.com/8291514/166107630-edb5190c-1d27-4757-8960-11ef14f87af1.png">
