"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/mask/[id]/page",{

/***/ "(app-pages-browser)/./src/app/mask/[id]/page.tsx":
/*!************************************!*\
  !*** ./src/app/mask/[id]/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MaskDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _data_masks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/masks */ \"(app-pages-browser)/./src/data/masks.ts\");\n/* harmony import */ var _services_maskService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/maskService */ \"(app-pages-browser)/./src/services/maskService.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/providers/ThemeProvider */ \"(app-pages-browser)/./src/components/providers/ThemeProvider.tsx\");\n/* harmony import */ var _components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/navigation/SimpleNavbar */ \"(app-pages-browser)/./src/components/navigation/SimpleNavbar.tsx\");\n/* harmony import */ var _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useLocalStorage */ \"(app-pages-browser)/./src/hooks/useLocalStorage.ts\");\n/* harmony import */ var _components_animation_SimpleDrawingAnimation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/animation/SimpleDrawingAnimation */ \"(app-pages-browser)/./src/components/animation/SimpleDrawingAnimation.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction MaskDetailPage() {\n    var _mask_images, _mask_images1;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const maskId = params.id;\n    const [mask, setMask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAnimation, setShowAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { colors } = (0,_components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const { toggleFavorite, isFavorite } = (0,_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useFavorites)();\n    const { addToRecentlyViewed } = (0,_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useRecentlyViewed)();\n    // 加载脸谱数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MaskDetailPage.useEffect\": ()=>{\n            const loadMask = {\n                \"MaskDetailPage.useEffect.loadMask\": async ()=>{\n                    try {\n                        let maskData = _data_masks__WEBPACK_IMPORTED_MODULE_3__.operaMasks; // 默认使用静态数据\n                        if ((0,_lib_supabase__WEBPACK_IMPORTED_MODULE_5__.isSupabaseConfigured)()) {\n                            try {\n                                maskData = await _services_maskService__WEBPACK_IMPORTED_MODULE_4__.MaskService.getAllApprovedMasks();\n                            } catch (error) {\n                                console.error('Error loading masks from database:', error);\n                            }\n                        }\n                        const foundMask = maskData.find({\n                            \"MaskDetailPage.useEffect.loadMask.foundMask\": (m)=>m.id === maskId\n                        }[\"MaskDetailPage.useEffect.loadMask.foundMask\"]);\n                        setMask(foundMask || null);\n                        // 添加到最近浏览记录\n                        if (foundMask) {\n                            addToRecentlyViewed(foundMask.id);\n                        }\n                    } catch (error) {\n                        console.error('Error loading mask:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"MaskDetailPage.useEffect.loadMask\"];\n            loadMask();\n        }\n    }[\"MaskDetailPage.useEffect\"], [\n        maskId,\n        addToRecentlyViewed\n    ]);\n    // 加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                backgroundColor: colors.background,\n                color: colors.textPrimary\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__.SimpleNavbar, {\n                    showBackButton: true,\n                    title: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        minHeight: 'calc(100vh - 80px)'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '3rem',\n                                    marginBottom: '1rem',\n                                    animation: 'pulse 2s infinite'\n                                },\n                                children: \"\\uD83C\\uDFAD\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"正在加载脸谱信息...\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, this);\n    }\n    // 脸谱未找到\n    if (!mask) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                backgroundColor: colors.background,\n                color: colors.textPrimary\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__.SimpleNavbar, {\n                    showBackButton: true,\n                    title: \"脸谱未找到\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        minHeight: 'calc(100vh - 80px)'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontSize: '2rem',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"脸谱未找到\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    color: colors.textSecondary,\n                                    marginBottom: '2rem'\n                                },\n                                children: \"抱歉，您访问的脸谱不存在。\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/'),\n                                style: {\n                                    backgroundColor: colors.primary,\n                                    color: 'white',\n                                    padding: '0.75rem 1.5rem',\n                                    borderRadius: '0.5rem',\n                                    border: 'none',\n                                    cursor: 'pointer',\n                                    fontSize: '1rem'\n                                },\n                                children: \"返回首页\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            backgroundColor: colors.background,\n            color: colors.textPrimary\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__.SimpleNavbar, {\n                showBackButton: true,\n                title: mask.name\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '1200px',\n                    margin: '0 auto',\n                    padding: '2rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'grid',\n                            gridTemplateColumns: '1fr 1fr',\n                            gap: '3rem',\n                            '@media (max-width: 768px)': {\n                                gridTemplateColumns: '1fr'\n                            }\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: colors.backgroundSecondary,\n                                        borderRadius: '12px',\n                                        padding: '2rem',\n                                        textAlign: 'center'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: ((_mask_images = mask.images) === null || _mask_images === void 0 ? void 0 : _mask_images.fullSize) || mask.imageUrl || ((_mask_images1 = mask.images) === null || _mask_images1 === void 0 ? void 0 : _mask_images1.thumbnail),\n                                            alt: mask.name,\n                                            style: {\n                                                width: '100%',\n                                                maxWidth: '400px',\n                                                height: 'auto',\n                                                borderRadius: '8px',\n                                                marginBottom: '1rem'\n                                            },\n                                            onError: (e)=>{\n                                                const target = e.target;\n                                                target.src = \"https://via.placeholder.com/400x400/DC143C/FFFFFF?text=\".concat(encodeURIComponent(mask.name));\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                gap: '1rem',\n                                                marginTop: '1rem',\n                                                justifyContent: 'center'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowAnimation(!showAnimation),\n                                                    style: {\n                                                        backgroundColor: colors.primary,\n                                                        color: 'white',\n                                                        padding: '0.75rem 1.5rem',\n                                                        borderRadius: '0.5rem',\n                                                        border: 'none',\n                                                        cursor: 'pointer',\n                                                        fontSize: '1rem'\n                                                    },\n                                                    children: showAnimation ? '隐藏绘制过程' : '观看绘制过程'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>toggleFavorite(mask.id),\n                                                    style: {\n                                                        backgroundColor: isFavorite(mask.id) ? colors.primary : 'transparent',\n                                                        color: isFavorite(mask.id) ? 'white' : colors.primary,\n                                                        border: \"2px solid \".concat(colors.primary),\n                                                        padding: '0.75rem 1.5rem',\n                                                        borderRadius: '0.5rem',\n                                                        cursor: 'pointer',\n                                                        fontSize: '1rem',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        gap: '0.5rem'\n                                                    },\n                                                    children: isFavorite(mask.id) ? '❤️ 已收藏' : '🤍 收藏'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            backgroundColor: colors.backgroundSecondary,\n                                            borderRadius: '12px',\n                                            padding: '2rem',\n                                            marginBottom: '2rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                style: {\n                                                    fontSize: '1.5rem',\n                                                    fontWeight: 'bold',\n                                                    marginBottom: '1rem',\n                                                    color: colors.textPrimary\n                                                },\n                                                children: \"基本信息\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginBottom: '1rem'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"角色：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \" \",\n                                                    mask.character\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginBottom: '1rem'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"行当：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \" \",\n                                                    mask.roleCategory\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginBottom: '1rem'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"色彩分类：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \" \",\n                                                    mask.colorCategory\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, this),\n                                            mask.mainColors && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginBottom: '1rem'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"主要色彩：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'flex',\n                                                            gap: '0.5rem',\n                                                            marginTop: '0.5rem'\n                                                        },\n                                                        children: mask.mainColors.map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '30px',\n                                                                    height: '30px',\n                                                                    borderRadius: '50%',\n                                                                    backgroundColor: color,\n                                                                    border: '2px solid rgba(0,0,0,0.1)'\n                                                                },\n                                                                title: color\n                                                            }, index, false, {\n                                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            backgroundColor: colors.backgroundSecondary,\n                                            borderRadius: '12px',\n                                            padding: '2rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                style: {\n                                                    fontSize: '1.5rem',\n                                                    fontWeight: 'bold',\n                                                    marginBottom: '1rem',\n                                                    color: colors.textPrimary\n                                                },\n                                                children: \"文化背景\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    lineHeight: '1.6'\n                                                },\n                                                children: typeof mask.culturalBackground === 'string' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: mask.culturalBackground\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                marginBottom: '1rem'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"历史起源：\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    style: {\n                                                                        marginTop: '0.5rem'\n                                                                    },\n                                                                    children: mask.culturalBackground.origin\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                marginBottom: '1rem'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"性格特点：\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    style: {\n                                                                        marginTop: '0.5rem'\n                                                                    },\n                                                                    children: mask.culturalBackground.personality\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                marginBottom: '1rem'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"象征意义：\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    style: {\n                                                                        marginTop: '0.5rem'\n                                                                    },\n                                                                    children: mask.culturalBackground.symbolism\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    showAnimation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animation_SimpleDrawingAnimation__WEBPACK_IMPORTED_MODULE_9__.SimpleDrawingAnimation, {\n                        maskName: mask.name\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, this);\n}\n_s(MaskDetailPage, \"1OydzzeE+F7DM5O1RwNKSjUnD/s=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__.useTheme,\n        _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useFavorites,\n        _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useRecentlyViewed\n    ];\n});\n_c = MaskDetailPage;\nvar _c;\n$RefreshReg$(_c, \"MaskDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbWFzay9baWRdL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRW1EO0FBQ0k7QUFDYjtBQUVXO0FBQ0M7QUFDVTtBQUNJO0FBQ007QUFDYTtBQUV4RSxTQUFTYTtRQWtKSEMsY0FBMENBOztJQWpKN0QsTUFBTUMsU0FBU1osMERBQVNBO0lBQ3hCLE1BQU1hLFNBQVNaLDBEQUFTQTtJQUN4QixNQUFNYSxTQUFTRixPQUFPRyxFQUFFO0lBQ3hCLE1BQU0sQ0FBQ0osTUFBTUssUUFBUSxHQUFHbEIsK0NBQVFBLENBQW1CO0lBQ25ELE1BQU0sQ0FBQ21CLFNBQVNDLFdBQVcsR0FBR3BCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ3FCLGVBQWVDLGlCQUFpQixHQUFHdEIsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxFQUFFdUIsTUFBTSxFQUFFLEdBQUdoQiw2RUFBUUE7SUFDM0IsTUFBTSxFQUFFaUIsY0FBYyxFQUFFQyxVQUFVLEVBQUUsR0FBR2hCLG9FQUFZQTtJQUNuRCxNQUFNLEVBQUVpQixtQkFBbUIsRUFBRSxHQUFHaEIseUVBQWlCQTtJQUVqRCxTQUFTO0lBQ1RULGdEQUFTQTtvQ0FBQztZQUNSLE1BQU0wQjtxREFBVztvQkFDZixJQUFJO3dCQUNGLElBQUlDLFdBQVd4QixtREFBVUEsRUFBRSxXQUFXO3dCQUV0QyxJQUFJRSxtRUFBb0JBLElBQUk7NEJBQzFCLElBQUk7Z0NBQ0ZzQixXQUFXLE1BQU12Qiw4REFBV0EsQ0FBQ3dCLG1CQUFtQjs0QkFDbEQsRUFBRSxPQUFPQyxPQUFPO2dDQUNkQyxRQUFRRCxLQUFLLENBQUMsc0NBQXNDQTs0QkFDdEQ7d0JBQ0Y7d0JBRUEsTUFBTUUsWUFBWUosU0FBU0ssSUFBSTsyRUFBQ0MsQ0FBQUEsSUFBS0EsRUFBRWpCLEVBQUUsS0FBS0Q7O3dCQUM5Q0UsUUFBUWMsYUFBYTt3QkFFckIsWUFBWTt3QkFDWixJQUFJQSxXQUFXOzRCQUNiTixvQkFBb0JNLFVBQVVmLEVBQUU7d0JBQ2xDO29CQUNGLEVBQUUsT0FBT2EsT0FBTzt3QkFDZEMsUUFBUUQsS0FBSyxDQUFDLHVCQUF1QkE7b0JBQ3ZDLFNBQVU7d0JBQ1JWLFdBQVc7b0JBQ2I7Z0JBQ0Y7O1lBRUFPO1FBQ0Y7bUNBQUc7UUFBQ1g7UUFBUVU7S0FBb0I7SUFFaEMsT0FBTztJQUNQLElBQUlQLFNBQVM7UUFDWCxxQkFDRSw4REFBQ2dCO1lBQUlDLE9BQU87Z0JBQ1ZDLFdBQVc7Z0JBQ1hDLGlCQUFpQmYsT0FBT2dCLFVBQVU7Z0JBQ2xDQyxPQUFPakIsT0FBT2tCLFdBQVc7WUFDM0I7OzhCQUNFLDhEQUFDakMsNkVBQVlBO29CQUFDa0MsY0FBYztvQkFBQ0MsT0FBTTs7Ozs7OzhCQUNuQyw4REFBQ1I7b0JBQUlDLE9BQU87d0JBQ1ZRLFNBQVM7d0JBQ1RDLFlBQVk7d0JBQ1pDLGdCQUFnQjt3QkFDaEJULFdBQVc7b0JBQ2I7OEJBQ0UsNEVBQUNGO3dCQUFJQyxPQUFPOzRCQUFFVyxXQUFXO3dCQUFTOzswQ0FDaEMsOERBQUNaO2dDQUFJQyxPQUFPO29DQUNWWSxVQUFVO29DQUNWQyxjQUFjO29DQUNkQyxXQUFXO2dDQUNiOzBDQUFHOzs7Ozs7MENBR0gsOERBQUNDOzBDQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUtiO0lBRUEsUUFBUTtJQUNSLElBQUksQ0FBQ3RDLE1BQU07UUFDVCxxQkFDRSw4REFBQ3NCO1lBQUlDLE9BQU87Z0JBQ1ZDLFdBQVc7Z0JBQ1hDLGlCQUFpQmYsT0FBT2dCLFVBQVU7Z0JBQ2xDQyxPQUFPakIsT0FBT2tCLFdBQVc7WUFDM0I7OzhCQUNFLDhEQUFDakMsNkVBQVlBO29CQUFDa0MsY0FBYztvQkFBQ0MsT0FBTTs7Ozs7OzhCQUNuQyw4REFBQ1I7b0JBQUlDLE9BQU87d0JBQ1ZRLFNBQVM7d0JBQ1RDLFlBQVk7d0JBQ1pDLGdCQUFnQjt3QkFDaEJULFdBQVc7b0JBQ2I7OEJBQ0UsNEVBQUNGO3dCQUFJQyxPQUFPOzRCQUFFVyxXQUFXO3dCQUFTOzswQ0FDaEMsOERBQUNLO2dDQUFHaEIsT0FBTztvQ0FBRVksVUFBVTtvQ0FBUUMsY0FBYztnQ0FBTzswQ0FBRzs7Ozs7OzBDQUd2RCw4REFBQ0U7Z0NBQUVmLE9BQU87b0NBQUVJLE9BQU9qQixPQUFPOEIsYUFBYTtvQ0FBRUosY0FBYztnQ0FBTzswQ0FBRzs7Ozs7OzBDQUdqRSw4REFBQ0s7Z0NBQ0NDLFNBQVMsSUFBTXhDLE9BQU95QyxJQUFJLENBQUM7Z0NBQzNCcEIsT0FBTztvQ0FDTEUsaUJBQWlCZixPQUFPa0MsT0FBTztvQ0FDL0JqQixPQUFPO29DQUNQa0IsU0FBUztvQ0FDVEMsY0FBYztvQ0FDZEMsUUFBUTtvQ0FDUkMsUUFBUTtvQ0FDUmIsVUFBVTtnQ0FDWjswQ0FDRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFPWDtJQUVBLHFCQUNFLDhEQUFDYjtRQUFJQyxPQUFPO1lBQ1ZDLFdBQVc7WUFDWEMsaUJBQWlCZixPQUFPZ0IsVUFBVTtZQUNsQ0MsT0FBT2pCLE9BQU9rQixXQUFXO1FBQzNCOzswQkFFRSw4REFBQ2pDLDZFQUFZQTtnQkFBQ2tDLGNBQWM7Z0JBQUNDLE9BQU85QixLQUFLaUQsSUFBSTs7Ozs7OzBCQUc3Qyw4REFBQzNCO2dCQUFJQyxPQUFPO29CQUNWMkIsVUFBVTtvQkFDVkMsUUFBUTtvQkFDUk4sU0FBUztnQkFDWDs7a0NBQ0UsOERBQUN2Qjt3QkFBSUMsT0FBTzs0QkFDVlEsU0FBUzs0QkFDVHFCLHFCQUFxQjs0QkFDckJDLEtBQUs7NEJBQ0wsNkJBQTZCO2dDQUMzQkQscUJBQXFCOzRCQUN2Qjt3QkFDRjs7MENBRUUsOERBQUM5QjswQ0FDQyw0RUFBQ0E7b0NBQUlDLE9BQU87d0NBQ1ZFLGlCQUFpQmYsT0FBTzRDLG1CQUFtQjt3Q0FDM0NSLGNBQWM7d0NBQ2RELFNBQVM7d0NBQ1RYLFdBQVc7b0NBQ2I7O3NEQUNFLDhEQUFDcUI7NENBQ0NDLEtBQUt4RCxFQUFBQSxlQUFBQSxLQUFLeUQsTUFBTSxjQUFYekQsbUNBQUFBLGFBQWEwRCxRQUFRLEtBQUkxRCxLQUFLMkQsUUFBUSxNQUFJM0QsZ0JBQUFBLEtBQUt5RCxNQUFNLGNBQVh6RCxvQ0FBQUEsY0FBYTRELFNBQVM7NENBQ3JFQyxLQUFLN0QsS0FBS2lELElBQUk7NENBQ2QxQixPQUFPO2dEQUNMdUMsT0FBTztnREFDUFosVUFBVTtnREFDVmEsUUFBUTtnREFDUmpCLGNBQWM7Z0RBQ2RWLGNBQWM7NENBQ2hCOzRDQUNBNEIsU0FBUyxDQUFDQztnREFDUixNQUFNQyxTQUFTRCxFQUFFQyxNQUFNO2dEQUN2QkEsT0FBT1YsR0FBRyxHQUFHLDBEQUF3RixPQUE5QlcsbUJBQW1CbkUsS0FBS2lELElBQUk7NENBQ3JHOzs7Ozs7c0RBSUYsOERBQUMzQjs0Q0FBSUMsT0FBTztnREFDVlEsU0FBUztnREFDVHNCLEtBQUs7Z0RBQ0xlLFdBQVc7Z0RBQ1huQyxnQkFBZ0I7NENBQ2xCOzs4REFDRSw4REFBQ1E7b0RBQ0NDLFNBQVMsSUFBTWpDLGlCQUFpQixDQUFDRDtvREFDakNlLE9BQU87d0RBQ0xFLGlCQUFpQmYsT0FBT2tDLE9BQU87d0RBQy9CakIsT0FBTzt3REFDUGtCLFNBQVM7d0RBQ1RDLGNBQWM7d0RBQ2RDLFFBQVE7d0RBQ1JDLFFBQVE7d0RBQ1JiLFVBQVU7b0RBQ1o7OERBRUMzQixnQkFBZ0IsV0FBVzs7Ozs7OzhEQUc5Qiw4REFBQ2lDO29EQUNDQyxTQUFTLElBQU0vQixlQUFlWCxLQUFLSSxFQUFFO29EQUNyQ21CLE9BQU87d0RBQ0xFLGlCQUFpQmIsV0FBV1osS0FBS0ksRUFBRSxJQUFJTSxPQUFPa0MsT0FBTyxHQUFHO3dEQUN4RGpCLE9BQU9mLFdBQVdaLEtBQUtJLEVBQUUsSUFBSSxVQUFVTSxPQUFPa0MsT0FBTzt3REFDckRHLFFBQVEsYUFBNEIsT0FBZnJDLE9BQU9rQyxPQUFPO3dEQUNuQ0MsU0FBUzt3REFDVEMsY0FBYzt3REFDZEUsUUFBUTt3REFDUmIsVUFBVTt3REFDVkosU0FBUzt3REFDVEMsWUFBWTt3REFDWnFCLEtBQUs7b0RBQ1A7OERBRUN6QyxXQUFXWixLQUFLSSxFQUFFLElBQUksV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTzFDLDhEQUFDa0I7O2tEQUNDLDhEQUFDQTt3Q0FBSUMsT0FBTzs0Q0FDVkUsaUJBQWlCZixPQUFPNEMsbUJBQW1COzRDQUMzQ1IsY0FBYzs0Q0FDZEQsU0FBUzs0Q0FDVFQsY0FBYzt3Q0FDaEI7OzBEQUNFLDhEQUFDaUM7Z0RBQUc5QyxPQUFPO29EQUNUWSxVQUFVO29EQUNWbUMsWUFBWTtvREFDWmxDLGNBQWM7b0RBQ2RULE9BQU9qQixPQUFPa0IsV0FBVztnREFDM0I7MERBQUc7Ozs7OzswREFJSCw4REFBQ047Z0RBQUlDLE9BQU87b0RBQUVhLGNBQWM7Z0RBQU87O2tFQUNqQyw4REFBQ21DO2tFQUFPOzs7Ozs7b0RBQVk7b0RBQUV2RSxLQUFLd0UsU0FBUzs7Ozs7OzswREFHdEMsOERBQUNsRDtnREFBSUMsT0FBTztvREFBRWEsY0FBYztnREFBTzs7a0VBQ2pDLDhEQUFDbUM7a0VBQU87Ozs7OztvREFBWTtvREFBRXZFLEtBQUt5RSxZQUFZOzs7Ozs7OzBEQUd6Qyw4REFBQ25EO2dEQUFJQyxPQUFPO29EQUFFYSxjQUFjO2dEQUFPOztrRUFDakMsOERBQUNtQztrRUFBTzs7Ozs7O29EQUFjO29EQUFFdkUsS0FBSzBFLGFBQWE7Ozs7Ozs7NENBSTNDMUUsS0FBSzJFLFVBQVUsa0JBQ2QsOERBQUNyRDtnREFBSUMsT0FBTztvREFBRWEsY0FBYztnREFBTzs7a0VBQ2pDLDhEQUFDbUM7a0VBQU87Ozs7OztrRUFDUiw4REFBQ2pEO3dEQUFJQyxPQUFPOzREQUFFUSxTQUFTOzREQUFRc0IsS0FBSzs0REFBVWUsV0FBVzt3REFBUztrRUFDL0RwRSxLQUFLMkUsVUFBVSxDQUFDQyxHQUFHLENBQUMsQ0FBQ2pELE9BQU9rRCxzQkFDM0IsOERBQUN2RDtnRUFFQ0MsT0FBTztvRUFDTHVDLE9BQU87b0VBQ1BDLFFBQVE7b0VBQ1JqQixjQUFjO29FQUNkckIsaUJBQWlCRTtvRUFDakJvQixRQUFRO2dFQUNWO2dFQUNBakIsT0FBT0g7K0RBUkZrRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFpQmpCLDhEQUFDdkQ7d0NBQUlDLE9BQU87NENBQ1ZFLGlCQUFpQmYsT0FBTzRDLG1CQUFtQjs0Q0FDM0NSLGNBQWM7NENBQ2RELFNBQVM7d0NBQ1g7OzBEQUNFLDhEQUFDd0I7Z0RBQUc5QyxPQUFPO29EQUNUWSxVQUFVO29EQUNWbUMsWUFBWTtvREFDWmxDLGNBQWM7b0RBQ2RULE9BQU9qQixPQUFPa0IsV0FBVztnREFDM0I7MERBQUc7Ozs7OzswREFJSCw4REFBQ047Z0RBQUlDLE9BQU87b0RBQUV1RCxZQUFZO2dEQUFNOzBEQUM3QixPQUFPOUUsS0FBSytFLGtCQUFrQixLQUFLLHlCQUNsQyw4REFBQ3pDOzhEQUFHdEMsS0FBSytFLGtCQUFrQjs7Ozs7eUVBRTNCOztzRUFDRSw4REFBQ3pEOzREQUFJQyxPQUFPO2dFQUFFYSxjQUFjOzREQUFPOzs4RUFDakMsOERBQUNtQzs4RUFBTzs7Ozs7OzhFQUNSLDhEQUFDakM7b0VBQUVmLE9BQU87d0VBQUU2QyxXQUFXO29FQUFTOzhFQUFJcEUsS0FBSytFLGtCQUFrQixDQUFDQyxNQUFNOzs7Ozs7Ozs7Ozs7c0VBR3BFLDhEQUFDMUQ7NERBQUlDLE9BQU87Z0VBQUVhLGNBQWM7NERBQU87OzhFQUNqQyw4REFBQ21DOzhFQUFPOzs7Ozs7OEVBQ1IsOERBQUNqQztvRUFBRWYsT0FBTzt3RUFBRTZDLFdBQVc7b0VBQVM7OEVBQUlwRSxLQUFLK0Usa0JBQWtCLENBQUNFLFdBQVc7Ozs7Ozs7Ozs7OztzRUFHekUsOERBQUMzRDs0REFBSUMsT0FBTztnRUFBRWEsY0FBYzs0REFBTzs7OEVBQ2pDLDhEQUFDbUM7OEVBQU87Ozs7Ozs4RUFDUiw4REFBQ2pDO29FQUFFZixPQUFPO3dFQUFFNkMsV0FBVztvRUFBUzs4RUFBSXBFLEtBQUsrRSxrQkFBa0IsQ0FBQ0csU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkFVbEYxRSwrQkFDQyw4REFBQ1YsZ0dBQXNCQTt3QkFBQ3FGLFVBQVVuRixLQUFLaUQsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS3JEO0dBaFR3QmxEOztRQUNQVixzREFBU0E7UUFDVEMsc0RBQVNBO1FBS0xJLHlFQUFRQTtRQUNZRSxnRUFBWUE7UUFDbkJDLHFFQUFpQkE7OztLQVQzQkUiLCJzb3VyY2VzIjpbIkU6XFwyLUFJcHJvZ3JhbVxcMy1BdWdtZW50XFwxLXRlc3QxXFxiZWlqaW5nLW9wZXJhLW1hc2tzXFxzcmNcXGFwcFxcbWFza1xcW2lkXVxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVBhcmFtcywgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IG9wZXJhTWFza3MgfSBmcm9tICdAL2RhdGEvbWFza3MnO1xuaW1wb3J0IHsgT3BlcmFNYXNrIH0gZnJvbSAnQC90eXBlcy9tYXNrJztcbmltcG9ydCB7IE1hc2tTZXJ2aWNlIH0gZnJvbSAnQC9zZXJ2aWNlcy9tYXNrU2VydmljZSc7XG5pbXBvcnQgeyBpc1N1cGFiYXNlQ29uZmlndXJlZCB9IGZyb20gJ0AvbGliL3N1cGFiYXNlJztcbmltcG9ydCB7IHVzZVRoZW1lIH0gZnJvbSAnQC9jb21wb25lbnRzL3Byb3ZpZGVycy9UaGVtZVByb3ZpZGVyJztcbmltcG9ydCB7IFNpbXBsZU5hdmJhciB9IGZyb20gJ0AvY29tcG9uZW50cy9uYXZpZ2F0aW9uL1NpbXBsZU5hdmJhcic7XG5pbXBvcnQgeyB1c2VGYXZvcml0ZXMsIHVzZVJlY2VudGx5Vmlld2VkIH0gZnJvbSAnQC9ob29rcy91c2VMb2NhbFN0b3JhZ2UnO1xuaW1wb3J0IHsgU2ltcGxlRHJhd2luZ0FuaW1hdGlvbiB9IGZyb20gJ0AvY29tcG9uZW50cy9hbmltYXRpb24vU2ltcGxlRHJhd2luZ0FuaW1hdGlvbic7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE1hc2tEZXRhaWxQYWdlKCkge1xuICBjb25zdCBwYXJhbXMgPSB1c2VQYXJhbXMoKTtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IG1hc2tJZCA9IHBhcmFtcy5pZCBhcyBzdHJpbmc7XG4gIGNvbnN0IFttYXNrLCBzZXRNYXNrXSA9IHVzZVN0YXRlPE9wZXJhTWFzayB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW3Nob3dBbmltYXRpb24sIHNldFNob3dBbmltYXRpb25dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCB7IGNvbG9ycyB9ID0gdXNlVGhlbWUoKTtcbiAgY29uc3QgeyB0b2dnbGVGYXZvcml0ZSwgaXNGYXZvcml0ZSB9ID0gdXNlRmF2b3JpdGVzKCk7XG4gIGNvbnN0IHsgYWRkVG9SZWNlbnRseVZpZXdlZCB9ID0gdXNlUmVjZW50bHlWaWV3ZWQoKTtcblxuICAvLyDliqDovb3ohLjosLHmlbDmja5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBsb2FkTWFzayA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGxldCBtYXNrRGF0YSA9IG9wZXJhTWFza3M7IC8vIOm7mOiupOS9v+eUqOmdmeaAgeaVsOaNrlxuICAgICAgICBcbiAgICAgICAgaWYgKGlzU3VwYWJhc2VDb25maWd1cmVkKCkpIHtcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgbWFza0RhdGEgPSBhd2FpdCBNYXNrU2VydmljZS5nZXRBbGxBcHByb3ZlZE1hc2tzKCk7XG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgbWFza3MgZnJvbSBkYXRhYmFzZTonLCBlcnJvcik7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICBjb25zdCBmb3VuZE1hc2sgPSBtYXNrRGF0YS5maW5kKG0gPT4gbS5pZCA9PT0gbWFza0lkKTtcbiAgICAgICAgc2V0TWFzayhmb3VuZE1hc2sgfHwgbnVsbCk7XG5cbiAgICAgICAgLy8g5re75Yqg5Yiw5pyA6L+R5rWP6KeI6K6w5b2VXG4gICAgICAgIGlmIChmb3VuZE1hc2spIHtcbiAgICAgICAgICBhZGRUb1JlY2VudGx5Vmlld2VkKGZvdW5kTWFzay5pZCk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgbWFzazonLCBlcnJvcik7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgbG9hZE1hc2soKTtcbiAgfSwgW21hc2tJZCwgYWRkVG9SZWNlbnRseVZpZXdlZF0pO1xuXG4gIC8vIOWKoOi9veeKtuaAgVxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgIG1pbkhlaWdodDogJzEwMHZoJyxcbiAgICAgICAgYmFja2dyb3VuZENvbG9yOiBjb2xvcnMuYmFja2dyb3VuZCxcbiAgICAgICAgY29sb3I6IGNvbG9ycy50ZXh0UHJpbWFyeVxuICAgICAgfX0+XG4gICAgICAgIDxTaW1wbGVOYXZiYXIgc2hvd0JhY2tCdXR0b24gdGl0bGU9XCLliqDovb3kuK0uLi5cIiAvPlxuICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgICBtaW5IZWlnaHQ6ICdjYWxjKDEwMHZoIC0gODBweCknXG4gICAgICAgIH19PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgdGV4dEFsaWduOiAnY2VudGVyJyB9fT5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgXG4gICAgICAgICAgICAgIGZvbnRTaXplOiAnM3JlbScsIFxuICAgICAgICAgICAgICBtYXJnaW5Cb3R0b206ICcxcmVtJyxcbiAgICAgICAgICAgICAgYW5pbWF0aW9uOiAncHVsc2UgMnMgaW5maW5pdGUnXG4gICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAg8J+OrVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8cD7mraPlnKjliqDovb3ohLjosLHkv6Hmga8uLi48L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIC8vIOiEuOiwseacquaJvuWIsFxuICBpZiAoIW1hc2spIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICBtaW5IZWlnaHQ6ICcxMDB2aCcsXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogY29sb3JzLmJhY2tncm91bmQsXG4gICAgICAgIGNvbG9yOiBjb2xvcnMudGV4dFByaW1hcnlcbiAgICAgIH19PlxuICAgICAgICA8U2ltcGxlTmF2YmFyIHNob3dCYWNrQnV0dG9uIHRpdGxlPVwi6IS46LCx5pyq5om+5YiwXCIgLz5cbiAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgICAgbWluSGVpZ2h0OiAnY2FsYygxMDB2aCAtIDgwcHgpJ1xuICAgICAgICB9fT5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IHRleHRBbGlnbjogJ2NlbnRlcicgfX0+XG4gICAgICAgICAgICA8aDEgc3R5bGU9e3sgZm9udFNpemU6ICcycmVtJywgbWFyZ2luQm90dG9tOiAnMXJlbScgfX0+XG4gICAgICAgICAgICAgIOiEuOiwseacquaJvuWIsFxuICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgIDxwIHN0eWxlPXt7IGNvbG9yOiBjb2xvcnMudGV4dFNlY29uZGFyeSwgbWFyZ2luQm90dG9tOiAnMnJlbScgfX0+XG4gICAgICAgICAgICAgIOaKseatie+8jOaCqOiuv+mXrueahOiEuOiwseS4jeWtmOWcqOOAglxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaCgnLycpfVxuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogY29sb3JzLnByaW1hcnksXG4gICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgcGFkZGluZzogJzAuNzVyZW0gMS41cmVtJyxcbiAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcwLjVyZW0nLFxuICAgICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMXJlbSdcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg6L+U5Zue6aaW6aG1XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICBtaW5IZWlnaHQ6ICcxMDB2aCcsXG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6IGNvbG9ycy5iYWNrZ3JvdW5kLFxuICAgICAgY29sb3I6IGNvbG9ycy50ZXh0UHJpbWFyeVxuICAgIH19PlxuICAgICAgey8qIOWvvOiIquagjyAqL31cbiAgICAgIDxTaW1wbGVOYXZiYXIgc2hvd0JhY2tCdXR0b24gdGl0bGU9e21hc2submFtZX0gLz5cblxuICAgICAgey8qIOS4u+imgeWGheWuuSAqL31cbiAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgbWF4V2lkdGg6ICcxMjAwcHgnLFxuICAgICAgICBtYXJnaW46ICcwIGF1dG8nLFxuICAgICAgICBwYWRkaW5nOiAnMnJlbSdcbiAgICAgIH19PlxuICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgZGlzcGxheTogJ2dyaWQnLFxuICAgICAgICAgIGdyaWRUZW1wbGF0ZUNvbHVtbnM6ICcxZnIgMWZyJyxcbiAgICAgICAgICBnYXA6ICczcmVtJyxcbiAgICAgICAgICAnQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSc6IHtcbiAgICAgICAgICAgIGdyaWRUZW1wbGF0ZUNvbHVtbnM6ICcxZnInXG4gICAgICAgICAgfVxuICAgICAgICB9fT5cbiAgICAgICAgICB7Lyog5bem5L6n77ya6IS46LCx5Zu+54mHICovfVxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogY29sb3JzLmJhY2tncm91bmRTZWNvbmRhcnksXG4gICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzEycHgnLFxuICAgICAgICAgICAgICBwYWRkaW5nOiAnMnJlbScsXG4gICAgICAgICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcidcbiAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgc3JjPXttYXNrLmltYWdlcz8uZnVsbFNpemUgfHwgbWFzay5pbWFnZVVybCB8fCBtYXNrLmltYWdlcz8udGh1bWJuYWlsfVxuICAgICAgICAgICAgICAgIGFsdD17bWFzay5uYW1lfVxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgICAgICAgICAgbWF4V2lkdGg6ICc0MDBweCcsXG4gICAgICAgICAgICAgICAgICBoZWlnaHQ6ICdhdXRvJyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzhweCcsXG4gICAgICAgICAgICAgICAgICBtYXJnaW5Cb3R0b206ICcxcmVtJ1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgb25FcnJvcj17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgIGNvbnN0IHRhcmdldCA9IGUudGFyZ2V0IGFzIEhUTUxJbWFnZUVsZW1lbnQ7XG4gICAgICAgICAgICAgICAgICB0YXJnZXQuc3JjID0gYGh0dHBzOi8vdmlhLnBsYWNlaG9sZGVyLmNvbS80MDB4NDAwL0RDMTQzQy9GRkZGRkY/dGV4dD0ke2VuY29kZVVSSUNvbXBvbmVudChtYXNrLm5hbWUpfWA7XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIHsvKiDliqjnlLvmjInpkq7lkozmlLbol4/mjInpkq4gKi99XG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgZ2FwOiAnMXJlbScsXG4gICAgICAgICAgICAgICAgbWFyZ2luVG9wOiAnMXJlbScsXG4gICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInXG4gICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dBbmltYXRpb24oIXNob3dBbmltYXRpb24pfVxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBjb2xvcnMucHJpbWFyeSxcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcwLjc1cmVtIDEuNXJlbScsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzAuNXJlbScsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxcmVtJ1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7c2hvd0FuaW1hdGlvbiA/ICfpmpDol4/nu5jliLbov4fnqIsnIDogJ+ingueci+e7mOWItui/h+eoiyd9XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB0b2dnbGVGYXZvcml0ZShtYXNrLmlkKX1cbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogaXNGYXZvcml0ZShtYXNrLmlkKSA/IGNvbG9ycy5wcmltYXJ5IDogJ3RyYW5zcGFyZW50JyxcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6IGlzRmF2b3JpdGUobWFzay5pZCkgPyAnd2hpdGUnIDogY29sb3JzLnByaW1hcnksXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlcjogYDJweCBzb2xpZCAke2NvbG9ycy5wcmltYXJ5fWAsXG4gICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcwLjc1cmVtIDEuNXJlbScsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzAuNXJlbScsXG4gICAgICAgICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzFyZW0nLFxuICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgICBnYXA6ICcwLjVyZW0nXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHtpc0Zhdm9yaXRlKG1hc2suaWQpID8gJ+KdpO+4jyDlt7LmlLbol48nIDogJ/CfpI0g5pS26JePJ31cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiDlj7PkvqfvvJrohLjosLHkv6Hmga8gKi99XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBjb2xvcnMuYmFja2dyb3VuZFNlY29uZGFyeSxcbiAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTJweCcsXG4gICAgICAgICAgICAgIHBhZGRpbmc6ICcycmVtJyxcbiAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnMnJlbSdcbiAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICA8aDIgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEuNXJlbScsXG4gICAgICAgICAgICAgICAgZm9udFdlaWdodDogJ2JvbGQnLFxuICAgICAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzFyZW0nLFxuICAgICAgICAgICAgICAgIGNvbG9yOiBjb2xvcnMudGV4dFByaW1hcnlcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAg5Z+65pys5L+h5oGvXG4gICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PlxuICAgICAgICAgICAgICAgIDxzdHJvbmc+6KeS6Imy77yaPC9zdHJvbmc+IHttYXNrLmNoYXJhY3Rlcn1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PlxuICAgICAgICAgICAgICAgIDxzdHJvbmc+6KGM5b2T77yaPC9zdHJvbmc+IHttYXNrLnJvbGVDYXRlZ29yeX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PlxuICAgICAgICAgICAgICAgIDxzdHJvbmc+6Imy5b2p5YiG57G777yaPC9zdHJvbmc+IHttYXNrLmNvbG9yQ2F0ZWdvcnl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiDkuLvopoHpopzoibIgKi99XG4gICAgICAgICAgICAgIHttYXNrLm1haW5Db2xvcnMgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgbWFyZ2luQm90dG9tOiAnMXJlbScgfX0+XG4gICAgICAgICAgICAgICAgICA8c3Ryb25nPuS4u+imgeiJsuW9qe+8mjwvc3Ryb25nPlxuICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGdhcDogJzAuNXJlbScsIG1hcmdpblRvcDogJzAuNXJlbScgfX0+XG4gICAgICAgICAgICAgICAgICAgIHttYXNrLm1haW5Db2xvcnMubWFwKChjb2xvciwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6ICczMHB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnMzBweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzUwJScsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogY29sb3IsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcjogJzJweCBzb2xpZCByZ2JhKDAsMCwwLDAuMSknXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9e2NvbG9yfVxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIOaWh+WMluiDjOaZryAqL31cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBjb2xvcnMuYmFja2dyb3VuZFNlY29uZGFyeSxcbiAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTJweCcsXG4gICAgICAgICAgICAgIHBhZGRpbmc6ICcycmVtJ1xuICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgIDxoMiBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMS41cmVtJyxcbiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCcsXG4gICAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnMXJlbScsXG4gICAgICAgICAgICAgICAgY29sb3I6IGNvbG9ycy50ZXh0UHJpbWFyeVxuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICDmlofljJbog4zmma9cbiAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgbGluZUhlaWdodDogJzEuNicgfX0+XG4gICAgICAgICAgICAgICAge3R5cGVvZiBtYXNrLmN1bHR1cmFsQmFja2dyb3VuZCA9PT0gJ3N0cmluZycgPyAoXG4gICAgICAgICAgICAgICAgICA8cD57bWFzay5jdWx0dXJhbEJhY2tncm91bmR9PC9wPlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PlxuICAgICAgICAgICAgICAgICAgICAgIDxzdHJvbmc+5Y6G5Y+y6LW35rqQ77yaPC9zdHJvbmc+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgc3R5bGU9e3sgbWFyZ2luVG9wOiAnMC41cmVtJyB9fT57bWFzay5jdWx0dXJhbEJhY2tncm91bmQub3JpZ2lufTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PlxuICAgICAgICAgICAgICAgICAgICAgIDxzdHJvbmc+5oCn5qC854m554K577yaPC9zdHJvbmc+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgc3R5bGU9e3sgbWFyZ2luVG9wOiAnMC41cmVtJyB9fT57bWFzay5jdWx0dXJhbEJhY2tncm91bmQucGVyc29uYWxpdHl9PC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgbWFyZ2luQm90dG9tOiAnMXJlbScgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgPHN0cm9uZz7osaHlvoHmhI/kuYnvvJo8L3N0cm9uZz5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBzdHlsZT17eyBtYXJnaW5Ub3A6ICcwLjVyZW0nIH19PnttYXNrLmN1bHR1cmFsQmFja2dyb3VuZC5zeW1ib2xpc219PC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDnu5jliLbliqjnlLsgKi99XG4gICAgICAgIHtzaG93QW5pbWF0aW9uICYmIChcbiAgICAgICAgICA8U2ltcGxlRHJhd2luZ0FuaW1hdGlvbiBtYXNrTmFtZT17bWFzay5uYW1lfSAvPlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVBhcmFtcyIsInVzZVJvdXRlciIsIm9wZXJhTWFza3MiLCJNYXNrU2VydmljZSIsImlzU3VwYWJhc2VDb25maWd1cmVkIiwidXNlVGhlbWUiLCJTaW1wbGVOYXZiYXIiLCJ1c2VGYXZvcml0ZXMiLCJ1c2VSZWNlbnRseVZpZXdlZCIsIlNpbXBsZURyYXdpbmdBbmltYXRpb24iLCJNYXNrRGV0YWlsUGFnZSIsIm1hc2siLCJwYXJhbXMiLCJyb3V0ZXIiLCJtYXNrSWQiLCJpZCIsInNldE1hc2siLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInNob3dBbmltYXRpb24iLCJzZXRTaG93QW5pbWF0aW9uIiwiY29sb3JzIiwidG9nZ2xlRmF2b3JpdGUiLCJpc0Zhdm9yaXRlIiwiYWRkVG9SZWNlbnRseVZpZXdlZCIsImxvYWRNYXNrIiwibWFza0RhdGEiLCJnZXRBbGxBcHByb3ZlZE1hc2tzIiwiZXJyb3IiLCJjb25zb2xlIiwiZm91bmRNYXNrIiwiZmluZCIsIm0iLCJkaXYiLCJzdHlsZSIsIm1pbkhlaWdodCIsImJhY2tncm91bmRDb2xvciIsImJhY2tncm91bmQiLCJjb2xvciIsInRleHRQcmltYXJ5Iiwic2hvd0JhY2tCdXR0b24iLCJ0aXRsZSIsImRpc3BsYXkiLCJhbGlnbkl0ZW1zIiwianVzdGlmeUNvbnRlbnQiLCJ0ZXh0QWxpZ24iLCJmb250U2l6ZSIsIm1hcmdpbkJvdHRvbSIsImFuaW1hdGlvbiIsInAiLCJoMSIsInRleHRTZWNvbmRhcnkiLCJidXR0b24iLCJvbkNsaWNrIiwicHVzaCIsInByaW1hcnkiLCJwYWRkaW5nIiwiYm9yZGVyUmFkaXVzIiwiYm9yZGVyIiwiY3Vyc29yIiwibmFtZSIsIm1heFdpZHRoIiwibWFyZ2luIiwiZ3JpZFRlbXBsYXRlQ29sdW1ucyIsImdhcCIsImJhY2tncm91bmRTZWNvbmRhcnkiLCJpbWciLCJzcmMiLCJpbWFnZXMiLCJmdWxsU2l6ZSIsImltYWdlVXJsIiwidGh1bWJuYWlsIiwiYWx0Iiwid2lkdGgiLCJoZWlnaHQiLCJvbkVycm9yIiwiZSIsInRhcmdldCIsImVuY29kZVVSSUNvbXBvbmVudCIsIm1hcmdpblRvcCIsImgyIiwiZm9udFdlaWdodCIsInN0cm9uZyIsImNoYXJhY3RlciIsInJvbGVDYXRlZ29yeSIsImNvbG9yQ2F0ZWdvcnkiLCJtYWluQ29sb3JzIiwibWFwIiwiaW5kZXgiLCJsaW5lSGVpZ2h0IiwiY3VsdHVyYWxCYWNrZ3JvdW5kIiwib3JpZ2luIiwicGVyc29uYWxpdHkiLCJzeW1ib2xpc20iLCJtYXNrTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/mask/[id]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/animation/SimpleDrawingAnimation.tsx":
/*!*************************************************************!*\
  !*** ./src/components/animation/SimpleDrawingAnimation.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleDrawingAnimation: () => (/* binding */ SimpleDrawingAnimation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/ThemeProvider */ \"(app-pages-browser)/./src/components/providers/ThemeProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ SimpleDrawingAnimation auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SimpleDrawingAnimation(param) {\n    let { maskName, steps } = param;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { colors } = (0,_components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    // 默认绘制步骤\n    const defaultSteps = [\n        {\n            title: '绘制脸部轮廓',\n            description: '首先用淡色勾勒出脸部的基本轮廓，确定五官位置',\n            duration: 3000\n        },\n        {\n            title: '涂抹底色',\n            description: '用主色调涂抹整个脸部，这是脸谱的基础色彩',\n            duration: 4000\n        },\n        {\n            title: '描绘眉毛和眼部',\n            description: '用深色勾勒眉毛和眼部轮廓，突出角色的神态',\n            duration: 3500\n        },\n        {\n            title: '绘制特色纹样',\n            description: '添加角色特有的纹样和装饰，体现角色性格',\n            duration: 3000\n        },\n        {\n            title: '添加装饰细节',\n            description: '在适当位置添加金色或其他装饰，增加华贵感',\n            duration: 2500\n        },\n        {\n            title: '完善细节',\n            description: '最后完善各种细节，确保脸谱的完整性和美观性',\n            duration: 4000\n        }\n    ];\n    const drawingSteps = steps || defaultSteps;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleDrawingAnimation.useEffect\": ()=>{\n            if (!isPlaying) return;\n            const timer = setTimeout({\n                \"SimpleDrawingAnimation.useEffect.timer\": ()=>{\n                    if (currentStep < drawingSteps.length - 1) {\n                        setCurrentStep({\n                            \"SimpleDrawingAnimation.useEffect.timer\": (prev)=>prev + 1\n                        }[\"SimpleDrawingAnimation.useEffect.timer\"]);\n                    } else {\n                        setIsPlaying(false);\n                        setCurrentStep(0);\n                    }\n                }\n            }[\"SimpleDrawingAnimation.useEffect.timer\"], drawingSteps[currentStep].duration);\n            return ({\n                \"SimpleDrawingAnimation.useEffect\": ()=>clearTimeout(timer)\n            })[\"SimpleDrawingAnimation.useEffect\"];\n        }\n    }[\"SimpleDrawingAnimation.useEffect\"], [\n        currentStep,\n        isPlaying,\n        drawingSteps\n    ]);\n    const startAnimation = ()=>{\n        setCurrentStep(0);\n        setIsPlaying(true);\n    };\n    const stopAnimation = ()=>{\n        setIsPlaying(false);\n        setCurrentStep(0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            backgroundColor: colors.backgroundSecondary,\n            borderRadius: '12px',\n            padding: '2rem',\n            marginTop: '2rem'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                style: {\n                    fontSize: '1.25rem',\n                    fontWeight: 'bold',\n                    marginBottom: '1.5rem',\n                    color: colors.textPrimary,\n                    textAlign: 'center'\n                },\n                children: [\n                    \"\\uD83C\\uDFA8 \",\n                    maskName,\n                    \" 绘制过程\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\components\\\\animation\\\\SimpleDrawingAnimation.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    gap: '1rem',\n                    justifyContent: 'center',\n                    marginBottom: '2rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startAnimation,\n                        disabled: isPlaying,\n                        style: {\n                            backgroundColor: isPlaying ? colors.border : colors.primary,\n                            color: 'white',\n                            padding: '0.75rem 1.5rem',\n                            borderRadius: '0.5rem',\n                            border: 'none',\n                            cursor: isPlaying ? 'not-allowed' : 'pointer',\n                            fontSize: '1rem'\n                        },\n                        children: isPlaying ? '播放中...' : '开始播放'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\components\\\\animation\\\\SimpleDrawingAnimation.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: stopAnimation,\n                        disabled: !isPlaying,\n                        style: {\n                            backgroundColor: !isPlaying ? colors.border : colors.textSecondary,\n                            color: 'white',\n                            padding: '0.75rem 1.5rem',\n                            borderRadius: '0.5rem',\n                            border: 'none',\n                            cursor: !isPlaying ? 'not-allowed' : 'pointer',\n                            fontSize: '1rem'\n                        },\n                        children: \"停止\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\components\\\\animation\\\\SimpleDrawingAnimation.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\components\\\\animation\\\\SimpleDrawingAnimation.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: colors.background,\n                    borderRadius: '8px',\n                    padding: '1.5rem',\n                    marginBottom: '1rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            marginBottom: '1rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: colors.primary,\n                                    color: 'white',\n                                    borderRadius: '50%',\n                                    width: '30px',\n                                    height: '30px',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    fontSize: '0.875rem',\n                                    fontWeight: 'bold',\n                                    marginRight: '1rem'\n                                },\n                                children: currentStep + 1\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\components\\\\animation\\\\SimpleDrawingAnimation.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                style: {\n                                    fontSize: '1.125rem',\n                                    fontWeight: 'bold',\n                                    color: colors.textPrimary,\n                                    margin: 0\n                                },\n                                children: drawingSteps[currentStep].title\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\components\\\\animation\\\\SimpleDrawingAnimation.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\components\\\\animation\\\\SimpleDrawingAnimation.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: colors.textSecondary,\n                            lineHeight: '1.6',\n                            margin: 0\n                        },\n                        children: drawingSteps[currentStep].description\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\components\\\\animation\\\\SimpleDrawingAnimation.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\components\\\\animation\\\\SimpleDrawingAnimation.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: colors.border,\n                    borderRadius: '10px',\n                    height: '8px',\n                    overflow: 'hidden',\n                    marginBottom: '1rem'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: colors.primary,\n                        height: '100%',\n                        width: \"\".concat((currentStep + 1) / drawingSteps.length * 100, \"%\"),\n                        transition: 'width 0.3s ease',\n                        borderRadius: '10px'\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\components\\\\animation\\\\SimpleDrawingAnimation.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\components\\\\animation\\\\SimpleDrawingAnimation.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                    gap: '0.5rem'\n                },\n                children: drawingSteps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '0.5rem',\n                            borderRadius: '6px',\n                            backgroundColor: index <= currentStep ? colors.primary + '20' : colors.border + '50',\n                            border: index === currentStep ? \"2px solid \".concat(colors.primary) : '1px solid transparent',\n                            transition: 'all 0.3s ease'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '0.875rem',\n                                    fontWeight: 'bold',\n                                    color: index <= currentStep ? colors.primary : colors.textSecondary,\n                                    marginBottom: '0.25rem'\n                                },\n                                children: [\n                                    \"步骤 \",\n                                    index + 1\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\components\\\\animation\\\\SimpleDrawingAnimation.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '0.75rem',\n                                    color: colors.textSecondary\n                                },\n                                children: step.title\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\components\\\\animation\\\\SimpleDrawingAnimation.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\components\\\\animation\\\\SimpleDrawingAnimation.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\components\\\\animation\\\\SimpleDrawingAnimation.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this),\n            isPlaying && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center',\n                    marginTop: '1rem',\n                    color: colors.textSecondary,\n                    fontSize: '0.875rem'\n                },\n                children: [\n                    \"预计完成时间: \",\n                    Math.ceil(drawingSteps[currentStep].duration / 1000),\n                    \" 秒\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\components\\\\animation\\\\SimpleDrawingAnimation.tsx\",\n                lineNumber: 239,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\components\\\\animation\\\\SimpleDrawingAnimation.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleDrawingAnimation, \"vU8gPPWIrjdmtU0sk3Cp+EGahTU=\", false, function() {\n    return [\n        _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c = SimpleDrawingAnimation;\nvar _c;\n$RefreshReg$(_c, \"SimpleDrawingAnimation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/animation/SimpleDrawingAnimation.tsx\n"));

/***/ })

});