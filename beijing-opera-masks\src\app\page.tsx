'use client';

import React, { useState, useEffect } from 'react';
import { operaMasks } from '@/data/masks';
import { OperaMask } from '@/types/mask';
import { MaskService } from '@/services/maskService';
import { isSupabaseConfigured } from '@/lib/supabase';
import { useTheme } from '@/components/providers/ThemeProvider';

export default function Home() {
  const [masks, setMasks] = useState<OperaMask[]>(operaMasks);
  const [loading, setLoading] = useState(false);
  const [selectedMask, setSelectedMask] = useState<OperaMask | null>(null);
  const { colors } = useTheme();

  // 加载脸谱数据
  useEffect(() => {
    const loadMasks = async () => {
      if (!isSupabaseConfigured()) {
        console.log('Using static mask data');
        return;
      }

      setLoading(true);
      try {
        const maskData = await MaskService.getAllApprovedMasks();
        setMasks(maskData);
      } catch (error) {
        console.error('Error loading masks:', error);
      } finally {
        setLoading(false);
      }
    };

    loadMasks();
  }, []);

  const handleMaskClick = (mask: OperaMask) => {
    setSelectedMask(mask);
  };

  const closeModal = () => {
    setSelectedMask(null);
  };

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: colors.background,
      color: colors.text,
      padding: '2rem'
    }}>
      {/* 标题 */}
      <div style={{
        textAlign: 'center',
        marginBottom: '3rem'
      }}>
        <h1 style={{
          fontSize: '2.5rem',
          fontWeight: 'bold',
          marginBottom: '1rem',
          background: 'linear-gradient(135deg, #DC2626, #B91C1C)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          fontFamily: '"Ma Shan Zheng", cursive'
        }}>
          🎭 京剧脸谱文化展示
        </h1>
        <p style={{
          fontSize: '1.125rem',
          color: colors.textSecondary,
          maxWidth: '600px',
          margin: '0 auto'
        }}>
          探索中国传统京剧脸谱艺术的魅力，了解每个角色背后的文化内涵
        </p>
      </div>

      {/* 加载状态 */}
      {loading && (
        <div style={{
          textAlign: 'center',
          padding: '2rem',
          color: colors.textSecondary
        }}>
          正在加载脸谱数据...
        </div>
      )}

      {/* 脸谱网格 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '2rem',
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        {masks.map((mask) => (
          <div
            key={mask.id}
            onClick={() => handleMaskClick(mask)}
            style={{
              backgroundColor: colors.cardBackground,
              borderRadius: '12px',
              padding: '1.5rem',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              border: `1px solid ${colors.border}`,
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
          >
            {/* 脸谱图片 */}
            <div style={{
              width: '100%',
              height: '200px',
              borderRadius: '8px',
              overflow: 'hidden',
              marginBottom: '1rem'
            }}>
              <img
                src={mask.imageUrl || mask.images?.fullSize}
                alt={mask.name}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover'
                }}
              />
            </div>

            {/* 脸谱信息 */}
            <h3 style={{
              fontSize: '1.25rem',
              fontWeight: 'bold',
              marginBottom: '0.5rem',
              color: colors.text
            }}>
              {mask.name}
            </h3>

            <p style={{
              fontSize: '0.875rem',
              color: colors.textSecondary,
              marginBottom: '1rem'
            }}>
              角色: {mask.character}
            </p>

            {/* 颜色主题 */}
            {(mask.colorTheme || mask.mainColors) && (
              <div style={{
                display: 'flex',
                gap: '0.5rem',
                marginBottom: '1rem'
              }}>
                {(mask.colorTheme || mask.mainColors || []).slice(0, 3).map((color, index) => (
                  <div
                    key={index}
                    style={{
                      width: '20px',
                      height: '20px',
                      borderRadius: '50%',
                      backgroundColor: color,
                      border: '1px solid rgba(0,0,0,0.1)'
                    }}
                    title={color}
                  />
                ))}
              </div>
            )}

            {/* 性格特征 */}
            {(mask.personalityTraits || mask.tags) && (
              <div style={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: '0.5rem'
              }}>
                {(mask.personalityTraits || mask.tags || []).slice(0, 3).map((trait, index) => (
                  <span
                    key={index}
                    style={{
                      fontSize: '0.75rem',
                      padding: '0.25rem 0.5rem',
                      backgroundColor: colors.primary + '20',
                      color: colors.primary,
                      borderRadius: '12px'
                    }}
                  >
                    {trait}
                  </span>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* 模态框 */}
      {selectedMask && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '1rem'
        }}>
          <div style={{
            backgroundColor: colors.background,
            borderRadius: '12px',
            padding: '2rem',
            maxWidth: '600px',
            width: '100%',
            maxHeight: '80vh',
            overflow: 'auto'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '1.5rem'
            }}>
              <h2 style={{
                fontSize: '1.5rem',
                fontWeight: 'bold',
                color: colors.text
              }}>
                {selectedMask.name}
              </h2>
              <button
                onClick={closeModal}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '1.5rem',
                  cursor: 'pointer',
                  color: colors.textSecondary
                }}
              >
                ×
              </button>
            </div>

            <img
              src={selectedMask.imageUrl || selectedMask.images?.fullSize}
              alt={selectedMask.name}
              style={{
                width: '100%',
                height: '300px',
                objectFit: 'cover',
                borderRadius: '8px',
                marginBottom: '1.5rem'
              }}
            />

            <div style={{
              fontSize: '0.875rem',
              color: colors.textSecondary,
              lineHeight: '1.6'
            }}>
              <p><strong>角色:</strong> {selectedMask.character}</p>
              <p><strong>文化背景:</strong></p>
              <p style={{ marginLeft: '1rem', marginBottom: '1rem' }}>
                {typeof selectedMask.culturalBackground === 'string' 
                  ? selectedMask.culturalBackground 
                  : selectedMask.culturalBackground?.origin || '传统京剧脸谱艺术'}
              </p>
              
              {(selectedMask.personalityTraits || selectedMask.tags) && (
                <>
                  <p><strong>特征:</strong></p>
                  <div style={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: '0.5rem',
                    marginLeft: '1rem'
                  }}>
                    {(selectedMask.personalityTraits || selectedMask.tags || []).map((trait, index) => (
                      <span
                        key={index}
                        style={{
                          fontSize: '0.75rem',
                          padding: '0.25rem 0.5rem',
                          backgroundColor: colors.primary + '20',
                          color: colors.primary,
                          borderRadius: '12px'
                        }}
                      >
                        {trait}
                      </span>
                    ))}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
