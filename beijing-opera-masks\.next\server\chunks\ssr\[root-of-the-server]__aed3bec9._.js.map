{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/performance/PerformanceMonitor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\ninterface PerformanceMetrics {\n  fcp?: number; // First Contentful Paint\n  lcp?: number; // Largest Contentful Paint\n  fid?: number; // First Input Delay\n  cls?: number; // Cumulative Layout Shift\n  ttfb?: number; // Time to First Byte\n}\n\nexport function PerformanceMonitor() {\n  useEffect(() => {\n    // 只在生产环境中启用性能监控\n    if (process.env.NODE_ENV !== 'production') {\n      return;\n    }\n    \n    const metrics: PerformanceMetrics = {};\n    \n    // 监控 First Contentful Paint\n    const observeFCP = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        entries.forEach((entry) => {\n          if (entry.name === 'first-contentful-paint') {\n            metrics.fcp = entry.startTime;\n            console.log('FCP:', entry.startTime);\n          }\n        });\n      });\n      observer.observe({ entryTypes: ['paint'] });\n    };\n    \n    // 监控 Largest Contentful Paint\n    const observeLCP = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        const lastEntry = entries[entries.length - 1];\n        metrics.lcp = lastEntry.startTime;\n        console.log('LCP:', lastEntry.startTime);\n      });\n      observer.observe({ entryTypes: ['largest-contentful-paint'] });\n    };\n    \n    // 监控 First Input Delay\n    const observeFID = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        entries.forEach((entry: any) => {\n          metrics.fid = entry.processingStart - entry.startTime;\n          console.log('FID:', metrics.fid);\n        });\n      });\n      observer.observe({ entryTypes: ['first-input'] });\n    };\n    \n    // 监控 Cumulative Layout Shift\n    const observeCLS = () => {\n      let clsValue = 0;\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        entries.forEach((entry: any) => {\n          if (!entry.hadRecentInput) {\n            clsValue += entry.value;\n          }\n        });\n        metrics.cls = clsValue;\n        console.log('CLS:', clsValue);\n      });\n      observer.observe({ entryTypes: ['layout-shift'] });\n    };\n    \n    // 监控 Time to First Byte\n    const observeTTFB = () => {\n      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;\n      if (navigationEntry) {\n        metrics.ttfb = navigationEntry.responseStart - navigationEntry.requestStart;\n        console.log('TTFB:', metrics.ttfb);\n      }\n    };\n    \n    // 检查浏览器支持\n    if ('PerformanceObserver' in window) {\n      observeFCP();\n      observeLCP();\n      observeFID();\n      observeCLS();\n      observeTTFB();\n    }\n    \n    // 页面卸载时发送性能数据\n    const sendMetrics = () => {\n      // 这里可以发送到分析服务\n      console.log('Performance Metrics:', metrics);\n      \n      // 示例：发送到Google Analytics或其他分析服务\n      // if (window.gtag) {\n      //   window.gtag('event', 'web_vitals', {\n      //     custom_map: {\n      //       metric_fcp: 'fcp',\n      //       metric_lcp: 'lcp',\n      //       metric_fid: 'fid',\n      //       metric_cls: 'cls',\n      //       metric_ttfb: 'ttfb'\n      //     },\n      //     fcp: metrics.fcp,\n      //     lcp: metrics.lcp,\n      //     fid: metrics.fid,\n      //     cls: metrics.cls,\n      //     ttfb: metrics.ttfb\n      //   });\n      // }\n    };\n    \n    // 监听页面卸载事件\n    window.addEventListener('beforeunload', sendMetrics);\n    \n    // 监听页面可见性变化\n    document.addEventListener('visibilitychange', () => {\n      if (document.visibilityState === 'hidden') {\n        sendMetrics();\n      }\n    });\n    \n    return () => {\n      window.removeEventListener('beforeunload', sendMetrics);\n      document.removeEventListener('visibilitychange', sendMetrics);\n    };\n  }, []);\n  \n  return null; // 这个组件不渲染任何内容\n}\n\n// 性能优化工具函数\nexport const performanceUtils = {\n  // 预加载关键资源\n  preloadResource: (href: string, as: string) => {\n    const link = document.createElement('link');\n    link.rel = 'preload';\n    link.href = href;\n    link.as = as;\n    document.head.appendChild(link);\n  },\n  \n  // 预连接到外部域名\n  preconnect: (href: string) => {\n    const link = document.createElement('link');\n    link.rel = 'preconnect';\n    link.href = href;\n    document.head.appendChild(link);\n  },\n  \n  // DNS预解析\n  dnsPrefetch: (href: string) => {\n    const link = document.createElement('link');\n    link.rel = 'dns-prefetch';\n    link.href = href;\n    document.head.appendChild(link);\n  },\n  \n  // 延迟执行非关键代码\n  defer: (callback: () => void, delay: number = 0) => {\n    if ('requestIdleCallback' in window) {\n      requestIdleCallback(callback);\n    } else {\n      setTimeout(callback, delay);\n    }\n  },\n  \n  // 检查网络连接质量\n  getNetworkInfo: () => {\n    if ('connection' in navigator) {\n      const connection = (navigator as any).connection;\n      return {\n        effectiveType: connection.effectiveType,\n        downlink: connection.downlink,\n        rtt: connection.rtt,\n        saveData: connection.saveData\n      };\n    }\n    return null;\n  }\n};\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAYO,SAAS;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB;QAChB,wCAA2C;YACzC;QACF;;;QAEA,MAAM;QAEN,4BAA4B;QAC5B,MAAM;QAaN,8BAA8B;QAC9B,MAAM;QAUN,uBAAuB;QACvB,MAAM;QAWN,6BAA6B;QAC7B,MAAM;QAeN,wBAAwB;QACxB,MAAM;QAiBN,cAAc;QACd,MAAM;IAqCR,GAAG,EAAE;IAEL,OAAO,MAAM,cAAc;AAC7B;AAGO,MAAM,mBAAmB;IAC9B,UAAU;IACV,iBAAiB,CAAC,MAAc;QAC9B,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,GAAG,GAAG;QACX,KAAK,IAAI,GAAG;QACZ,KAAK,EAAE,GAAG;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,WAAW;IACX,YAAY,CAAC;QACX,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,GAAG,GAAG;QACX,KAAK,IAAI,GAAG;QACZ,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,SAAS;IACT,aAAa,CAAC;QACZ,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,GAAG,GAAG;QACX,KAAK,IAAI,GAAG;QACZ,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,YAAY;IACZ,OAAO,CAAC,UAAsB,QAAgB,CAAC;QAC7C,IAAI,yBAAyB,QAAQ;YACnC,oBAAoB;QACtB,OAAO;YACL,WAAW,UAAU;QACvB;IACF;IAEA,WAAW;IACX,gBAAgB;QACd,IAAI,gBAAgB,WAAW;YAC7B,MAAM,aAAa,AAAC,UAAkB,UAAU;YAChD,OAAO;gBACL,eAAe,WAAW,aAAa;gBACvC,UAAU,WAAW,QAAQ;gBAC7B,KAAK,WAAW,GAAG;gBACnB,UAAU,WAAW,QAAQ;YAC/B;QACF;QACA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/providers/ThemeProvider.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\nexport type Theme = 'light' | 'dark';\n\n// 主题颜色配置\nexport const themeColors = {\n  light: {\n    background: '#FFFFFF',\n    backgroundSecondary: '#F9FAFB',\n    backgroundTertiary: '#F3F4F6',\n    textPrimary: '#1F2937',\n    textSecondary: '#374151',\n    textTertiary: '#6B7280',\n    textMuted: '#9CA3AF',\n    border: '#E5E7EB',\n    borderSecondary: '#D1D5DB',\n    primary: '#B91C1C',\n    primaryHover: '#991B1B',\n    secondary: '#F59E0B',\n    secondaryHover: '#D97706',\n    success: '#10B981',\n    warning: '#F59E0B',\n    error: '#EF4444',\n    shadow: 'rgba(0, 0, 0, 0.1)',\n    shadowHover: 'rgba(0, 0, 0, 0.2)',\n  },\n  dark: {\n    background: '#0F1419',\n    backgroundSecondary: '#1A1F2E',\n    backgroundTertiary: '#252A3A',\n    textPrimary: '#F9FAFB',\n    textSecondary: '#E5E7EB',\n    textTertiary: '#D1D5DB',\n    textMuted: '#9CA3AF',\n    border: '#374151',\n    borderSecondary: '#4B5563',\n    primary: '#DC2626',\n    primaryHover: '#EF4444',\n    secondary: '#FBBF24',\n    secondaryHover: '#FCD34D',\n    success: '#34D399',\n    warning: '#FBBF24',\n    error: '#F87171',\n    shadow: 'rgba(0, 0, 0, 0.3)',\n    shadowHover: 'rgba(0, 0, 0, 0.5)',\n  }\n};\n\ninterface ThemeContextType {\n  theme: Theme;\n  toggleTheme: () => void;\n  setTheme: (theme: Theme) => void;\n  colors: typeof themeColors.light;\n  styles: {\n    card: React.CSSProperties;\n    navigation: React.CSSProperties;\n    button: {\n      primary: React.CSSProperties;\n      secondary: React.CSSProperties;\n    };\n  };\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport function useTheme() {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n}\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n}\n\nexport function ThemeProvider({ children }: ThemeProviderProps) {\n  const [theme, setThemeState] = useState<Theme>('light');\n\n  // 从localStorage加载主题\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const savedTheme = localStorage.getItem('beijing-opera-theme') as Theme;\n      if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {\n        setThemeState(savedTheme);\n      } else {\n        // 检测系统主题偏好\n        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n        setThemeState(prefersDark ? 'dark' : 'light');\n      }\n    }\n  }, []);\n\n  // 设置主题\n  const setTheme = (newTheme: Theme) => {\n    setThemeState(newTheme);\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('beijing-opera-theme', newTheme);\n      document.documentElement.setAttribute('data-theme', newTheme);\n    }\n  };\n\n  // 切换主题\n  const toggleTheme = () => {\n    const newTheme = theme === 'light' ? 'dark' : 'light';\n    setTheme(newTheme);\n  };\n\n  // 获取当前主题的颜色\n  const colors = themeColors[theme];\n\n  // 生成样式\n  const styles = {\n    card: {\n      backgroundColor: colors.backgroundSecondary,\n      border: `1px solid ${colors.border}`,\n      boxShadow: `0 4px 6px -1px ${colors.shadow}`,\n      transition: 'all 0.3s ease'\n    },\n    navigation: {\n      backgroundColor: colors.backgroundSecondary,\n      borderBottom: `2px solid ${colors.secondary}`,\n      boxShadow: `0 4px 6px -1px ${colors.shadow}`\n    },\n    button: {\n      primary: {\n        backgroundColor: colors.primary,\n        color: colors.background,\n        border: `2px solid ${colors.primary}`,\n      },\n      secondary: {\n        backgroundColor: 'transparent',\n        color: colors.primary,\n        border: `2px solid ${colors.primary}`,\n      }\n    }\n  };\n\n  // 应用主题到body\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      document.body.style.backgroundColor = colors.background;\n      document.body.style.color = colors.textPrimary;\n      document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';\n      document.documentElement.setAttribute('data-theme', theme);\n    }\n  }, [theme, colors]);\n\n  const contextValue: ThemeContextType = {\n    theme,\n    toggleTheme,\n    setTheme,\n    colors,\n    styles\n  };\n\n  return (\n    <ThemeContext.Provider value={contextValue}>\n      {children}\n    </ThemeContext.Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAOO,MAAM,cAAc;IACzB,OAAO;QACL,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QACpB,aAAa;QACb,eAAe;QACf,cAAc;QACd,WAAW;QACX,QAAQ;QACR,iBAAiB;QACjB,SAAS;QACT,cAAc;QACd,WAAW;QACX,gBAAgB;QAChB,SAAS;QACT,SAAS;QACT,OAAO;QACP,QAAQ;QACR,aAAa;IACf;IACA,MAAM;QACJ,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QACpB,aAAa;QACb,eAAe;QACf,cAAc;QACd,WAAW;QACX,QAAQ;QACR,iBAAiB;QACjB,SAAS;QACT,cAAc;QACd,WAAW;QACX,gBAAgB;QAChB,SAAS;QACT,SAAS;QACT,OAAO;QACP,QAAQ;QACR,aAAa;IACf;AACF;AAiBA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,MAAM,CAAC,OAAO,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAE/C,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;;IAUF,GAAG,EAAE;IAEL,OAAO;IACP,MAAM,WAAW,CAAC;QAChB,cAAc;QACd;;IAIF;IAEA,OAAO;IACP,MAAM,cAAc;QAClB,MAAM,WAAW,UAAU,UAAU,SAAS;QAC9C,SAAS;IACX;IAEA,YAAY;IACZ,MAAM,SAAS,WAAW,CAAC,MAAM;IAEjC,OAAO;IACP,MAAM,SAAS;QACb,MAAM;YACJ,iBAAiB,OAAO,mBAAmB;YAC3C,QAAQ,CAAC,UAAU,EAAE,OAAO,MAAM,EAAE;YACpC,WAAW,CAAC,eAAe,EAAE,OAAO,MAAM,EAAE;YAC5C,YAAY;QACd;QACA,YAAY;YACV,iBAAiB,OAAO,mBAAmB;YAC3C,cAAc,CAAC,UAAU,EAAE,OAAO,SAAS,EAAE;YAC7C,WAAW,CAAC,eAAe,EAAE,OAAO,MAAM,EAAE;QAC9C;QACA,QAAQ;YACN,SAAS;gBACP,iBAAiB,OAAO,OAAO;gBAC/B,OAAO,OAAO,UAAU;gBACxB,QAAQ,CAAC,UAAU,EAAE,OAAO,OAAO,EAAE;YACvC;YACA,WAAW;gBACT,iBAAiB;gBACjB,OAAO,OAAO,OAAO;gBACrB,QAAQ,CAAC,UAAU,EAAE,OAAO,OAAO,EAAE;YACvC;QACF;IACF;IAEA,YAAY;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;;IAMF,GAAG;QAAC;QAAO;KAAO;IAElB,MAAM,eAAiC;QACrC;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}