'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { operaMasks } from '@/data/masks';
import { OperaMask } from '@/types/mask';
import { MaskService } from '@/services/maskService';
import { isSupabaseConfigured } from '@/lib/supabase';
import { useTheme } from '@/components/providers/ThemeProvider';
import { SimpleNavbar } from '@/components/navigation/SimpleNavbar';

export default function Home() {
  const [masks, setMasks] = useState<OperaMask[]>(operaMasks);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState<string>('all');
  const router = useRouter();
  const { colors } = useTheme();

  // 加载脸谱数据
  useEffect(() => {
    const loadMasks = async () => {
      if (!isSupabaseConfigured()) {
        console.log('Using static mask data');
        return;
      }

      setLoading(true);
      try {
        const maskData = await MaskService.getAllApprovedMasks();
        setMasks(maskData);
      } catch (error) {
        console.error('Error loading masks:', error);
      } finally {
        setLoading(false);
      }
    };

    loadMasks();
  }, []);

  // 筛选脸谱
  const filteredMasks = masks.filter(mask => {
    // 搜索筛选
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      const matchesName = mask.name.toLowerCase().includes(searchLower);
      const matchesCharacter = mask.character.toLowerCase().includes(searchLower);
      if (!matchesName && !matchesCharacter) return false;
    }

    // 角色筛选
    if (selectedRole !== 'all') {
      if (mask.roleCategory !== selectedRole) return false;
    }

    return true;
  });

  const handleMaskClick = (mask: OperaMask) => {
    // 导航到详情页面
    router.push(`/mask/${mask.id}`);
  };



  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: colors.background,
      color: colors.textPrimary
    }}>
      {/* 导航栏 */}
      <SimpleNavbar />

      <div style={{ padding: '2rem' }}>
        {/* 标题 */}
        <div style={{
          textAlign: 'center',
          marginBottom: '3rem'
        }}>
          <h1 style={{
            fontSize: '2.5rem',
            fontWeight: 'bold',
            marginBottom: '1rem',
            background: 'linear-gradient(135deg, #DC2626, #B91C1C)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontFamily: '"Ma Shan Zheng", cursive'
          }}>
            🎭 京剧脸谱文化展示
          </h1>
        <p style={{
          fontSize: '1.125rem',
          color: colors.textSecondary,
          maxWidth: '600px',
          margin: '0 auto'
        }}>
          探索中国传统京剧脸谱艺术的魅力，了解每个角色背后的文化内涵
        </p>
      </div>

      {/* 筛选控件 */}
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto 3rem auto',
        padding: '0 2rem'
      }}>
        <div style={{
          display: 'flex',
          gap: '1rem',
          flexWrap: 'wrap',
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: '2rem'
        }}>
          {/* 搜索框 */}
          <input
            type="text"
            placeholder="搜索脸谱名称或角色..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              padding: '0.75rem 1rem',
              borderRadius: '0.5rem',
              border: `1px solid ${colors.border}`,
              backgroundColor: colors.backgroundSecondary,
              color: colors.textPrimary,
              fontSize: '1rem',
              minWidth: '250px'
            }}
          />

          {/* 角色筛选 */}
          <select
            value={selectedRole}
            onChange={(e) => setSelectedRole(e.target.value)}
            style={{
              padding: '0.75rem 1rem',
              borderRadius: '0.5rem',
              border: `1px solid ${colors.border}`,
              backgroundColor: colors.backgroundSecondary,
              color: colors.textPrimary,
              fontSize: '1rem'
            }}
          >
            <option value="all">所有角色</option>
            <option value="生">生角</option>
            <option value="旦">旦角</option>
            <option value="净">净角</option>
            <option value="丑">丑角</option>
          </select>
        </div>

        {/* 结果统计 */}
        <div style={{
          textAlign: 'center',
          color: colors.textSecondary,
          marginBottom: '1rem'
        }}>
          找到 {filteredMasks.length} 个脸谱
        </div>
      </div>

      {/* 加载状态 */}
      {loading && (
        <div style={{
          textAlign: 'center',
          padding: '2rem',
          color: colors.textSecondary
        }}>
          正在加载脸谱数据...
        </div>
      )}

      {/* 脸谱网格 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '2rem',
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        {filteredMasks.map((mask) => (
          <div
            key={mask.id}
            onClick={() => handleMaskClick(mask)}
            style={{
              backgroundColor: colors.backgroundSecondary,
              borderRadius: '12px',
              padding: '1.5rem',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              border: `1px solid ${colors.border}`,
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
          >
            {/* 脸谱图片 */}
            <div style={{
              width: '100%',
              height: '200px',
              borderRadius: '8px',
              overflow: 'hidden',
              marginBottom: '1rem'
            }}>
              <img
                src={mask.images?.fullSize || mask.imageUrl || mask.images?.thumbnail}
                alt={mask.name}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover'
                }}
                onError={(e) => {
                  // 图片加载失败时的备用处理
                  const target = e.target as HTMLImageElement;
                  target.src = `https://via.placeholder.com/300x300/DC143C/FFFFFF?text=${encodeURIComponent(mask.name)}`;
                }}
              />
            </div>

            {/* 脸谱信息 */}
            <h3 style={{
              fontSize: '1.25rem',
              fontWeight: 'bold',
              marginBottom: '0.5rem',
              color: colors.textPrimary
            }}>
              {mask.name}
            </h3>

            <p style={{
              fontSize: '0.875rem',
              color: colors.textSecondary,
              marginBottom: '1rem'
            }}>
              角色: {mask.character}
            </p>

            {/* 颜色主题 */}
            {(mask.colorTheme || mask.mainColors) && (
              <div style={{
                display: 'flex',
                gap: '0.5rem',
                marginBottom: '1rem'
              }}>
                {(mask.colorTheme || mask.mainColors || []).slice(0, 3).map((color, index) => (
                  <div
                    key={index}
                    style={{
                      width: '20px',
                      height: '20px',
                      borderRadius: '50%',
                      backgroundColor: color,
                      border: '1px solid rgba(0,0,0,0.1)'
                    }}
                    title={color}
                  />
                ))}
              </div>
            )}

            {/* 性格特征 */}
            {(mask.personalityTraits || mask.tags) && (
              <div style={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: '0.5rem'
              }}>
                {(mask.personalityTraits || mask.tags || []).slice(0, 3).map((trait, index) => (
                  <span
                    key={index}
                    style={{
                      fontSize: '0.75rem',
                      padding: '0.25rem 0.5rem',
                      backgroundColor: colors.primary + '20',
                      color: colors.primary,
                      borderRadius: '12px'
                    }}
                  >
                    {trait}
                  </span>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
      </div>
    </div>
  );
}
