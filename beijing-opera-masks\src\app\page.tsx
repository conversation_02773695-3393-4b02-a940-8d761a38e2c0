'use client';

import React, { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { operaMasks } from '@/data/masks';
import { OperaMask, MaskFilter } from '@/types/mask';
import { useAppState } from '@/hooks/useAppState';
import { filterMasks } from '@/utils/maskUtils';
import { MaskImage } from '@/components/mask/MaskImage';
import { MainTitle, SectionTitle, BodyText, fontStyles } from '@/components/ui/Typography';
import { ThemeToggle } from '@/components/ui/ThemeToggle';
import { useTheme } from '@/components/providers/ThemeProvider';
import {
  CloudPattern,
  GeometricPattern,
  FloralPattern,
  TraditionalBorder,
  InkWashBackground,
  AnimatedDecoration,
  FloatingDecoration
} from '@/components/decorations/TraditionalDecorations';

export default function Home() {
  const [selectedMask, setSelectedMask] = useState<OperaMask | null>(null);
  const router = useRouter();
  const { colors, styles } = useTheme();
  const {
    filter,
    updateFilter,
    addToRecentlyViewed,
    toggleFavorite,
    isFavorite,
    getRecentlyViewedMasks
  } = useAppState();

  // 过滤脸谱
  const filteredMasks = useMemo(() => {
    return filterMasks(operaMasks, filter);
  }, [filter]);

  // 最近查看的脸谱
  const recentlyViewedMasks = getRecentlyViewedMasks(operaMasks);

  const handleMaskClick = (mask: OperaMask) => {
    setSelectedMask(mask);
  };

  const handleViewDetails = (mask: OperaMask) => {
    addToRecentlyViewed(mask.id);
    router.push(`/mask/${mask.id}`);
  };

  const handleFilterChange = (newFilter: MaskFilter) => {
    updateFilter(newFilter);
  };

  const handleCloseModal = () => {
    setSelectedMask(null);
  };

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: colors.background,
      color: colors.textPrimary,
      fontFamily: '"Noto Sans SC", sans-serif',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* 水墨风格背景 */}
      <InkWashBackground />

      {/* 装饰性云纹 */}
      <div style={{ position: 'absolute', top: '5rem', left: '2rem', zIndex: 0 }}>
        <FloatingDecoration duration={4000}>
          <CloudPattern width={120} height={60} color={colors.secondary} opacity={0.2} />
        </FloatingDecoration>
      </div>

      <div style={{ position: 'absolute', top: '10rem', right: '5rem', zIndex: 0 }}>
        <FloatingDecoration duration={5000}>
          <GeometricPattern size={80} color={colors.primary} opacity={0.15} />
        </FloatingDecoration>
      </div>

      <div style={{ position: 'absolute', bottom: '10rem', left: '25%', zIndex: 0 }}>
        <FloatingDecoration duration={6000}>
          <FloralPattern size={100} color={colors.secondary} opacity={0.2} />
        </FloatingDecoration>
      </div>
      {/* 头部 */}
      <header style={{
        ...styles.navigation,
        position: 'sticky',
        top: 0,
        zIndex: 40
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '1rem 1.5rem',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <h1 style={{
            ...fontStyles.pageTitle,
            color: colors.textPrimary
          }}>
            京剧脸谱文化展示平台
          </h1>
          <ThemeToggle />
        </div>
      </header>

      {/* 主要内容 */}
      <main style={{
        padding: '2rem 1.5rem',
        backgroundColor: colors.background,
        minHeight: '100vh'
      }}>
        {/* 英雄区域 */}
        <AnimatedDecoration delay={300}>
          <section style={{
            textAlign: 'center',
            padding: '3rem 2rem',
            background: `linear-gradient(135deg, ${colors.primary}10 0%, transparent 50%, ${colors.secondary}10 100%)`,
            borderRadius: '0.75rem',
            marginBottom: '3rem',
            border: `2px solid ${colors.border}`,
            position: 'relative',
            zIndex: 10,
            boxShadow: `0 8px 32px ${colors.shadow}`
          }}>
            {/* 顶部装饰边框 */}
            <div style={{ marginBottom: '2rem' }}>
              <TraditionalBorder color={colors.secondary} height={6} />
            </div>

            <AnimatedDecoration delay={600}>
              <MainTitle style={{
                marginBottom: '1rem',
                textShadow: `2px 2px 4px ${colors.shadow}`
              }}>
                探索京剧脸谱的艺术魅力
              </MainTitle>
            </AnimatedDecoration>

            <AnimatedDecoration delay={900}>
              <div
                className="hero-description"
                style={{
                  maxWidth: '50rem',
                  margin: '0 auto 2rem',
                  padding: '0 1rem'
                }}
              >
                <BodyText style={{
                  fontSize: '1.125rem',
                  lineHeight: '1.6',
                  textAlign: 'center',
                  display: 'block'
                }}>
                  深入了解中国传统戏曲文化，感受脸谱艺术的独特魅力与深厚内涵
                </BodyText>
              </div>

              {/* 内联样式用于响应式设计 */}
              <style jsx>{`
                .hero-description {
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }

                @media (max-width: 768px) {
                  .hero-description {
                    white-space: normal;
                    overflow: visible;
                    text-overflow: unset;
                  }
                }

                @media (max-width: 480px) {
                  .hero-description {
                    padding: 0 0.5rem;
                  }
                }
              `}</style>
            </AnimatedDecoration>

            {/* 底部装饰边框 */}
            <div style={{ marginTop: '2rem' }}>
              <TraditionalBorder color={colors.primary} height={4} />
            </div>

            {/* 角落装饰 */}
            <div style={{ position: 'absolute', top: '1rem', left: '1rem' }}>
              <GeometricPattern size={40} color={colors.secondary} opacity={0.3} />
            </div>
            <div style={{ position: 'absolute', top: '1rem', right: '1rem' }}>
              <GeometricPattern size={40} color={colors.secondary} opacity={0.3} />
            </div>
          </section>
        </AnimatedDecoration>

        {/* 脸谱网格 */}
        <AnimatedDecoration delay={1200}>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
            gap: '2rem',
            maxWidth: '1200px',
            margin: '0 auto',
            position: 'relative',
            zIndex: 10
          }}>
            {filteredMasks.map((mask, index) => (
              <AnimatedDecoration
                key={mask.id}
                delay={1500 + index * 100}
                duration={800}
              >
                <div
                  onClick={() => handleMaskClick(mask)}
                  style={{
                    ...styles.card,
                    borderRadius: '1rem',
                    border: `2px solid ${colors.secondary}`,
                    overflow: 'hidden',
                    cursor: 'pointer',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    position: 'relative',
                    boxShadow: `0 4px 6px -1px ${colors.shadow}, 0 2px 4px -1px ${colors.shadow}`
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-4px) scale(1.02)';
                    e.currentTarget.style.boxShadow = `0 20px 25px -5px ${colors.shadowHover}, 0 10px 10px -5px ${colors.shadowHover}`;
                    e.currentTarget.style.borderColor = colors.primary;
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0) scale(1)';
                    e.currentTarget.style.boxShadow = `0 4px 6px -1px ${colors.shadow}, 0 2px 4px -1px ${colors.shadow}`;
                    e.currentTarget.style.borderColor = colors.secondary;
                  }}
                >
              {/* 脸谱图片 - 纯净显示，无覆盖层 */}
              <div style={{
                aspectRatio: '1',
                position: 'relative',
                backgroundColor: '#F8F9FA',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                padding: '1rem'
              }}>
                <MaskImage
                  mask={mask}
                  width={260}
                  height={260}
                />

                {/* 分类标签 - 优化位置和样式 */}
                <div style={{
                  position: 'absolute',
                  top: '0.75rem',
                  left: '0.75rem',
                  backgroundColor: 'rgba(0,0,0,0.85)',
                  color: 'white',
                  padding: '0.375rem 0.75rem',
                  borderRadius: '1rem',
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  backdropFilter: 'blur(4px)',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}>
                  {mask.roleCategory}
                </div>

                <div style={{
                  position: 'absolute',
                  top: '0.75rem',
                  right: '0.75rem',
                  backgroundColor: 'rgba(0,0,0,0.85)',
                  color: 'white',
                  padding: '0.375rem 0.75rem',
                  borderRadius: '1rem',
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  backdropFilter: 'blur(4px)',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}>
                  {mask.colorCategory}
                </div>
              </div>

              {/* 脸谱信息 - 重新设计布局 */}
              <div style={{
                padding: '1.25rem',
                backgroundColor: colors.background,
                borderTop: `1px solid ${colors.border}`
              }}>
                {/* 角色名称 - 移到卡片信息区域 */}
                <div style={{
                  textAlign: 'center',
                  marginBottom: '1rem',
                  paddingBottom: '0.75rem',
                  borderBottom: `1px solid ${colors.border}`
                }}>
                  <h2 style={{
                    fontSize: '1.5rem',
                    fontWeight: '700',
                    color: colors.textPrimary,
                    marginBottom: '0.25rem',
                    fontFamily: '"Noto Serif SC", serif'
                  }}>
                    {mask.character}
                  </h2>
                  <h3 style={{
                    fontSize: '1rem',
                    fontWeight: '500',
                    color: colors.textSecondary,
                    fontFamily: '"Noto Serif SC", serif'
                  }}>
                    {mask.name}
                  </h3>
                </div>

                {/* 角色描述 */}
                <p style={{
                  fontSize: '0.875rem',
                  color: colors.textSecondary,
                  marginBottom: '1rem',
                  lineHeight: '1.5',
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden'
                }}>
                  {mask.culturalBackground.personality}
                </p>

                {/* 主要颜色 */}
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem',
                  marginBottom: '1rem'
                }}>
                  <span style={{
                    fontSize: '0.875rem',
                    color: colors.textSecondary,
                    fontWeight: '500'
                  }}>
                    主要色彩:
                  </span>
                  <div style={{ display: 'flex', gap: '0.375rem' }}>
                    {mask.mainColors.slice(0, 3).map((color, index) => (
                      <div
                        key={index}
                        style={{
                          width: '1.25rem',
                          height: '1.25rem',
                          borderRadius: '50%',
                          backgroundColor: color,
                          border: `2px solid ${colors.border}`,
                          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                        }}
                        title={color}
                      />
                    ))}
                  </div>
                </div>

                {/* 标签 */}
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.375rem' }}>
                  {mask.tags.slice(0, 3).map((tag, index) => (
                    <span
                      key={index}
                      style={{
                        padding: '0.375rem 0.75rem',
                        fontSize: '0.75rem',
                        fontWeight: '500',
                        backgroundColor: colors.backgroundTertiary,
                        color: colors.textTertiary,
                        borderRadius: '1rem',
                        border: `1px solid ${colors.border}`,
                        transition: 'all 0.2s ease'
                      }}
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
              </AnimatedDecoration>
          ))}
        </div>
        </AnimatedDecoration>
      </main>

      {/* 简单的模态框 */}
      {selectedMask && (
        <div
          style={{
            position: 'fixed',
            inset: 0,
            zIndex: 50,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            backdropFilter: 'blur(4px)'
          }}
          onClick={handleCloseModal}
        >
          <div
            style={{
              backgroundColor: 'white',
              borderRadius: '0.75rem',
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
              border: '2px solid #F59E0B',
              maxWidth: '90vw',
              maxHeight: '90vh',
              overflow: 'auto',
              position: 'relative'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* 关闭按钮 */}
            <button
              onClick={handleCloseModal}
              style={{
                position: 'absolute',
                top: '1rem',
                right: '1rem',
                backgroundColor: 'transparent',
                border: 'none',
                fontSize: '1.5rem',
                cursor: 'pointer',
                zIndex: 10,
                color: '#6B7280'
              }}
            >
              ×
            </button>

            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '1.5rem',
              padding: '1.5rem'
            }}>
              {/* 脸谱图片 */}
              <div>
                <div style={{
                  aspectRatio: '1',
                  marginBottom: '1rem',
                  borderRadius: '0.5rem',
                  overflow: 'hidden',
                  border: '2px solid #F59E0B'
                }}>
                  {/* 使用MaskImage组件显示脸谱 */}
                  <MaskImage
                    mask={selectedMask}
                    width={300}
                    height={300}
                    className="modal-mask-image"
                  />
                </div>
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  {selectedMask.mainColors.map((color, index) => (
                    <div
                      key={index}
                      style={{
                        width: '2rem',
                        height: '2rem',
                        borderRadius: '50%',
                        backgroundColor: color,
                        border: '2px solid #D1D5DB'
                      }}
                      title={color}
                    />
                  ))}
                </div>
              </div>

              {/* 脸谱信息 */}
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
                <div>
                  <h2 style={{
                    fontSize: '1.5rem',
                    fontWeight: 'bold',
                    color: '#1F2937',
                    marginBottom: '0.5rem',
                    fontFamily: '"Noto Serif SC", serif'
                  }}>
                    {selectedMask.name}
                  </h2>
                  <p style={{ color: '#6B7280', fontWeight: '500' }}>
                    {selectedMask.character}
                  </p>
                </div>

                <div>
                  <h3 style={{
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    color: '#1F2937',
                    marginBottom: '0.5rem'
                  }}>
                    基本信息
                  </h3>
                  <div style={{ fontSize: '0.875rem', display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                    <p><span style={{ fontWeight: '500' }}>行当:</span> {selectedMask.roleCategory}</p>
                    <p><span style={{ fontWeight: '500' }}>颜色分类:</span> {selectedMask.colorCategory}</p>
                    <p><span style={{ fontWeight: '500' }}>绘制难度:</span> {selectedMask.difficulty}</p>
                  </div>
                </div>

                <div>
                  <h3 style={{
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    color: '#1F2937',
                    marginBottom: '0.5rem'
                  }}>
                    文化背景
                  </h3>
                  <div style={{ fontSize: '0.875rem', color: '#6B7280', display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                    <p><span style={{ fontWeight: '500' }}>历史起源:</span> {selectedMask.culturalBackground.origin}</p>
                    <p><span style={{ fontWeight: '500' }}>性格特点:</span> {selectedMask.culturalBackground.personality}</p>
                    <p><span style={{ fontWeight: '500' }}>象征意义:</span> {selectedMask.culturalBackground.symbolism}</p>
                  </div>
                </div>

                <div>
                  <h3 style={{
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    color: '#1F2937',
                    marginBottom: '0.5rem'
                  }}>
                    相关剧目
                  </h3>
                  <div style={{ fontSize: '0.875rem', display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                    {selectedMask.relatedOperas.map((opera, index) => (
                      <div key={index}>
                        <p style={{ fontWeight: '500' }}>{opera.name}</p>
                        <p style={{ color: '#6B7280' }}>{opera.description}</p>
                      </div>
                    ))}
                  </div>
                </div>

                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.5rem', marginBottom: '1.5rem' }}>
                  {selectedMask.tags.map((tag, index) => (
                    <span
                      key={index}
                      style={{
                        padding: '0.5rem 0.75rem',
                        fontSize: '0.75rem',
                        backgroundColor: 'rgba(245, 158, 11, 0.2)',
                        color: '#F59E0B',
                        border: '1px solid #F59E0B',
                        borderRadius: '9999px'
                      }}
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                {/* 查看详情按钮 */}
                <button
                  onClick={() => handleViewDetails(selectedMask)}
                  style={{
                    width: '100%',
                    backgroundColor: '#B91C1C',
                    color: 'white',
                    padding: '0.75rem 1.5rem',
                    borderRadius: '0.5rem',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '1rem',
                    fontWeight: '600',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#991B1B';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#B91C1C';
                  }}
                >
                  查看完整详情 →
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
