// 数据库连接测试脚本
// 运行命令: node test-database-connection.js

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function testDatabaseConnection() {
  console.log('🔍 开始测试数据库连接...\n');

  // 检查环境变量
  console.log('📋 检查环境变量:');
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  
  if (!supabaseUrl) {
    console.log('❌ NEXT_PUBLIC_SUPABASE_URL 未设置');
    return;
  }
  if (!supabaseAnonKey) {
    console.log('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY 未设置');
    return;
  }
  
  console.log('✅ NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl);
  console.log('✅ NEXT_PUBLIC_SUPABASE_ANON_KEY:', supabaseAnonKey.substring(0, 20) + '...');
  console.log('');

  // 创建Supabase客户端
  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  try {
    // 测试1: 检查数据库连接
    console.log('🔗 测试1: 检查数据库连接');

    // 首先测试基本连接
    try {
      const { data, error } = await supabase
        .from('masks')
        .select('*')
        .limit(1);

      if (error) {
        console.log('❌ 数据库连接失败:', error.message);
        console.log('💡 这可能表示数据库表尚未创建，请先执行SQL迁移脚本');
        return;
      }
      console.log('✅ 数据库连接成功');
    } catch (err) {
      console.log('❌ 数据库连接异常:', err.message);
      return;
    }
    console.log('');

    // 测试2: 检查表结构
    console.log('📊 测试2: 检查表结构');
    const tables = ['masks', 'drawing_steps', 'user_profiles', 'user_likes', 'user_favorites'];
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);
        
        if (error) {
          console.log(`❌ 表 ${table} 不存在或无法访问:`, error.message);
        } else {
          console.log(`✅ 表 ${table} 存在且可访问`);
        }
      } catch (err) {
        console.log(`❌ 表 ${table} 检查失败:`, err.message);
      }
    }
    console.log('');

    // 测试3: 检查初始数据
    console.log('📝 测试3: 检查初始数据');
    const { data: masks, error: masksError } = await supabase
      .from('masks')
      .select('id, name, character, is_official, role_type')
      .eq('is_official', true)
      .order('id');

    if (masksError) {
      console.log('❌ 获取脸谱数据失败:', masksError.message);
    } else {
      console.log(`✅ 找到 ${masks.length} 个官方脸谱:`);
      masks.forEach(mask => {
        console.log(`   - ${mask.name} (${mask.character}) [${mask.role_type}] ID: ${mask.id}`);
      });

      // 验证预期的脸谱是否都存在
      const expectedMasks = ['guanyu', 'baogong', 'caocao', 'zhangfei', 'douerdun', 'yangqilang', 'jianggan', 'liubei', 'sunwukong'];
      const foundIds = masks.map(m => m.id);
      const missingMasks = expectedMasks.filter(id => !foundIds.includes(id));

      if (missingMasks.length === 0) {
        console.log('✅ 所有预期的官方脸谱都已正确导入');
      } else {
        console.log('⚠️  缺少以下脸谱:', missingMasks.join(', '));
      }
    }
    console.log('');

    // 测试4: 检查绘制步骤数据
    console.log('🎨 测试4: 检查绘制步骤数据');
    const { data: steps, error: stepsError } = await supabase
      .from('drawing_steps')
      .select('mask_id, title, step_number')
      .order('mask_id, step_number');

    if (stepsError) {
      console.log('❌ 获取绘制步骤失败:', stepsError.message);
    } else {
      console.log(`✅ 找到 ${steps.length} 个绘制步骤:`);

      // 按mask_id分组显示
      const stepsByMask = {};
      steps.forEach(step => {
        if (!stepsByMask[step.mask_id]) {
          stepsByMask[step.mask_id] = [];
        }
        stepsByMask[step.mask_id].push(step);
      });

      Object.keys(stepsByMask).forEach(maskId => {
        console.log(`   - ${maskId}: ${stepsByMask[maskId].length} 个步骤`);
      });

      // 验证关键脸谱的绘制步骤
      const expectedSteps = { 'guanyu': 6, 'baogong': 6, 'caocao': 6 };
      Object.keys(expectedSteps).forEach(maskId => {
        const actualCount = stepsByMask[maskId]?.length || 0;
        const expectedCount = expectedSteps[maskId];
        if (actualCount === expectedCount) {
          console.log(`   ✅ ${maskId}: ${actualCount}/${expectedCount} 步骤正确`);
        } else {
          console.log(`   ⚠️  ${maskId}: ${actualCount}/${expectedCount} 步骤不匹配`);
        }
      });
    }
    console.log('');

    // 测试5: 检查RLS策略
    console.log('🔒 测试5: 检查行级安全策略');
    try {
      // 尝试插入测试数据（应该失败，因为没有认证）
      const { data: insertData, error: insertError } = await supabase
        .from('masks')
        .insert({
          name: '测试脸谱',
          character: '测试角色',
          role_type: 'jing',
          color_theme: ['红色'],
          image_url: 'https://example.com/test.jpg',
          cultural_background: '测试背景',
          personality_traits: ['测试特征']
        });
      
      if (insertError) {
        if (insertError.message.includes('new row violates row-level security policy')) {
          console.log('✅ RLS策略正常工作（未认证用户无法插入数据）');
        } else {
          console.log('⚠️  插入失败，但不是RLS原因:', insertError.message);
        }
      } else {
        console.log('⚠️  意外：未认证用户可以插入数据，请检查RLS配置');
      }
    } catch (err) {
      console.log('⚠️  RLS测试出错:', err.message);
    }
    console.log('');

    console.log('🎉 数据库连接测试完成！');
    console.log('');
    console.log('📋 测试结果总结:');
    console.log('✅ 如果所有测试都通过，您可以开始使用应用程序');
    console.log('❌ 如果有测试失败，请检查相应的配置');
    console.log('');
    console.log('🚀 下一步: 在浏览器中访问应用程序并测试用户注册功能');

  } catch (error) {
    console.log('❌ 测试过程中发生错误:', error.message);
    console.log('');
    console.log('🔧 可能的解决方案:');
    console.log('1. 检查网络连接');
    console.log('2. 验证Supabase项目URL和API密钥');
    console.log('3. 确认SQL迁移脚本已正确执行');
    console.log('4. 检查Supabase项目是否正常运行');
  }
}

// 运行测试
testDatabaseConnection();
