'use client';

import { useState, useEffect } from 'react';
import { MaskFilter, OperaMask } from '@/types/mask';

interface AppState {
  // 筛选状态
  filter: MaskFilter;
  // 最近查看的脸谱
  recentlyViewed: string[];
  // 收藏的脸谱
  favorites: string[];
  // 搜索历史
  searchHistory: string[];
}

const STORAGE_KEY = 'beijing-opera-masks-state';

const defaultState: AppState = {
  filter: {},
  recentlyViewed: [],
  favorites: [],
  searchHistory: []
};

export function useAppState() {
  const [state, setState] = useState<AppState>(defaultState);
  
  // 从localStorage加载状态
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const saved = localStorage.getItem(STORAGE_KEY);
        if (saved) {
          const parsedState = JSON.parse(saved);
          setState(prevState => ({ ...prevState, ...parsedState }));
        }
      } catch (error) {
        console.warn('Failed to load app state from localStorage:', error);
      }
    }
  }, []);
  
  // 保存状态到localStorage
  const saveState = (newState: Partial<AppState>) => {
    const updatedState = { ...state, ...newState };
    setState(updatedState);
    
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedState));
      } catch (error) {
        console.warn('Failed to save app state to localStorage:', error);
      }
    }
  };
  
  // 更新筛选条件
  const updateFilter = (filter: MaskFilter) => {
    saveState({ filter });
  };
  
  // 添加到最近查看
  const addToRecentlyViewed = (maskId: string) => {
    const updated = [maskId, ...state.recentlyViewed.filter(id => id !== maskId)].slice(0, 10);
    saveState({ recentlyViewed: updated });
  };
  
  // 切换收藏状态
  const toggleFavorite = (maskId: string) => {
    const updated = state.favorites.includes(maskId)
      ? state.favorites.filter(id => id !== maskId)
      : [...state.favorites, maskId];
    saveState({ favorites: updated });
  };
  
  // 添加搜索历史
  const addToSearchHistory = (searchTerm: string) => {
    if (!searchTerm.trim()) return;
    
    const updated = [
      searchTerm,
      ...state.searchHistory.filter(term => term !== searchTerm)
    ].slice(0, 10);
    saveState({ searchHistory: updated });
  };
  
  // 清除搜索历史
  const clearSearchHistory = () => {
    saveState({ searchHistory: [] });
  };
  
  // 检查是否收藏
  const isFavorite = (maskId: string) => {
    return state.favorites.includes(maskId);
  };
  
  // 获取最近查看的脸谱
  const getRecentlyViewedMasks = (masks: OperaMask[]) => {
    return state.recentlyViewed
      .map(id => masks.find(mask => mask.id === id))
      .filter(Boolean) as OperaMask[];
  };
  
  // 获取收藏的脸谱
  const getFavoriteMasks = (masks: OperaMask[]) => {
    return state.favorites
      .map(id => masks.find(mask => mask.id === id))
      .filter(Boolean) as OperaMask[];
  };
  
  return {
    // 状态
    filter: state.filter,
    recentlyViewed: state.recentlyViewed,
    favorites: state.favorites,
    searchHistory: state.searchHistory,
    
    // 操作
    updateFilter,
    addToRecentlyViewed,
    toggleFavorite,
    addToSearchHistory,
    clearSearchHistory,
    
    // 辅助函数
    isFavorite,
    getRecentlyViewedMasks,
    getFavoriteMasks
  };
}
