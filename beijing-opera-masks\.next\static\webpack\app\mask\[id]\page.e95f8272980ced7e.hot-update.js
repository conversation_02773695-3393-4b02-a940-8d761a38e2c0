"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/mask/[id]/page",{

/***/ "(app-pages-browser)/./src/hooks/useLocalStorage.ts":
/*!**************************************!*\
  !*** ./src/hooks/useLocalStorage.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFavorites: () => (/* binding */ useFavorites),\n/* harmony export */   useLocalStorage: () => (/* binding */ useLocalStorage),\n/* harmony export */   useRecentlyViewed: () => (/* binding */ useRecentlyViewed)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_useEffect_useState_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=useEffect,useState!=!react */ \"(app-pages-browser)/__barrel_optimize__?names=useEffect,useState!=!./node_modules/next/dist/compiled/react/index.js\");\n\n// 通用的localStorage hook\nfunction useLocalStorage(key, initialValue) {\n    // 添加客户端渲染状态\n    const [isClient, setIsClient] = (0,_barrel_optimize_names_useEffect_useState_react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // 获取初始值 - 服务器端始终返回初始值\n    const [storedValue, setStoredValue] = (0,_barrel_optimize_names_useEffect_useState_react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialValue);\n    // 客户端挂载后读取localStorage\n    (0,_barrel_optimize_names_useEffect_useState_react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useLocalStorage.useEffect\": ()=>{\n            setIsClient(true);\n            try {\n                const item = window.localStorage.getItem(key);\n                if (item) {\n                    setStoredValue(JSON.parse(item));\n                }\n            } catch (error) {\n                console.error('Error reading localStorage key \"'.concat(key, '\":'), error);\n            }\n        }\n    }[\"useLocalStorage.useEffect\"], [\n        key\n    ]);\n    // 设置值的函数\n    const setValue = (value)=>{\n        try {\n            const valueToStore = value instanceof Function ? value(storedValue) : value;\n            setStoredValue(valueToStore);\n            if (isClient) {\n                window.localStorage.setItem(key, JSON.stringify(valueToStore));\n            }\n        } catch (error) {\n            console.error('Error setting localStorage key \"'.concat(key, '\":'), error);\n        }\n    };\n    return [\n        storedValue,\n        setValue,\n        isClient\n    ];\n}\n// 收藏功能hook\nfunction useFavorites() {\n    const [favorites, setFavorites, isClient] = useLocalStorage('opera-mask-favorites', []);\n    const toggleFavorite = (maskId)=>{\n        setFavorites((prev)=>{\n            if (prev.includes(maskId)) {\n                return prev.filter((id)=>id !== maskId);\n            } else {\n                return [\n                    ...prev,\n                    maskId\n                ];\n            }\n        });\n    };\n    const isFavorite = (maskId)=>{\n        return favorites.includes(maskId);\n    };\n    return {\n        favorites,\n        toggleFavorite,\n        isFavorite,\n        isClient\n    };\n}\n// 最近浏览功能hook\nfunction useRecentlyViewed() {\n    const [recentlyViewed, setRecentlyViewed] = useLocalStorage('opera-mask-recent', []);\n    const addToRecentlyViewed = (maskId)=>{\n        setRecentlyViewed((prev)=>{\n            // 移除已存在的项目\n            const filtered = prev.filter((id)=>id !== maskId);\n            // 添加到开头，限制最多10个\n            return [\n                maskId,\n                ...filtered\n            ].slice(0, 10);\n        });\n    };\n    return {\n        recentlyViewed,\n        addToRecentlyViewed\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useLocalStorage.ts\n"));

/***/ })

});