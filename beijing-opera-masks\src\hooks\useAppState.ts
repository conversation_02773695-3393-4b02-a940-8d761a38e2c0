'use client';

import { useState, useEffect, useCallback } from 'react';
import { MaskFilter, OperaMask } from '@/types/mask';

interface AppState {
  // 筛选状态
  filter: MaskFilter;
  // 最近查看的脸谱
  recentlyViewed: string[];
  // 收藏的脸谱
  favorites: string[];
  // 搜索历史
  searchHistory: string[];
}

const STORAGE_KEY = 'beijing-opera-masks-state';

const defaultState: AppState = {
  filter: {},
  recentlyViewed: [],
  favorites: [],
  searchHistory: []
};

export function useAppState() {
  const [state, setState] = useState<AppState>(defaultState);
  
  // 从localStorage加载状态
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const saved = localStorage.getItem(STORAGE_KEY);
        if (saved) {
          const parsedState = JSON.parse(saved);
          setState(prevState => ({ ...prevState, ...parsedState }));
        }
      } catch (error) {
        console.warn('Failed to load app state from localStorage:', error);
      }
    }
  }, []);
  
  // 保存状态到localStorage
  const saveState = useCallback((newState: Partial<AppState>) => {
    setState(prevState => {
      const updatedState = { ...prevState, ...newState };

      if (typeof window !== 'undefined') {
        try {
          localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedState));
        } catch (error) {
          console.warn('Failed to save app state to localStorage:', error);
        }
      }

      return updatedState;
    });
  }, []);
  
  // 更新筛选条件
  const updateFilter = useCallback((filter: MaskFilter) => {
    saveState({ filter });
  }, [saveState]);
  
  // 添加到最近查看
  const addToRecentlyViewed = useCallback((maskId: string) => {
    setState(prevState => {
      const updated = [maskId, ...prevState.recentlyViewed.filter(id => id !== maskId)].slice(0, 10);
      const updatedState = { ...prevState, recentlyViewed: updated };

      if (typeof window !== 'undefined') {
        try {
          localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedState));
        } catch (error) {
          console.warn('Failed to save app state to localStorage:', error);
        }
      }

      return updatedState;
    });
  }, []);
  
  // 切换收藏状态
  const toggleFavorite = useCallback((maskId: string) => {
    setState(prevState => {
      const updated = prevState.favorites.includes(maskId)
        ? prevState.favorites.filter(id => id !== maskId)
        : [...prevState.favorites, maskId];
      const updatedState = { ...prevState, favorites: updated };

      if (typeof window !== 'undefined') {
        try {
          localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedState));
        } catch (error) {
          console.warn('Failed to save app state to localStorage:', error);
        }
      }

      return updatedState;
    });
  }, []);
  
  // 添加搜索历史
  const addToSearchHistory = useCallback((searchTerm: string) => {
    if (!searchTerm.trim()) return;

    setState(prevState => {
      const updated = [
        searchTerm,
        ...prevState.searchHistory.filter(term => term !== searchTerm)
      ].slice(0, 10);
      const updatedState = { ...prevState, searchHistory: updated };

      if (typeof window !== 'undefined') {
        try {
          localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedState));
        } catch (error) {
          console.warn('Failed to save app state to localStorage:', error);
        }
      }

      return updatedState;
    });
  }, []);

  // 清除搜索历史
  const clearSearchHistory = useCallback(() => {
    saveState({ searchHistory: [] });
  }, [saveState]);
  
  // 检查是否收藏
  const isFavorite = (maskId: string) => {
    return state.favorites.includes(maskId);
  };
  
  // 获取最近查看的脸谱
  const getRecentlyViewedMasks = (masks: OperaMask[]) => {
    return state.recentlyViewed
      .map(id => masks.find(mask => mask.id === id))
      .filter(Boolean) as OperaMask[];
  };
  
  // 获取收藏的脸谱
  const getFavoriteMasks = (masks: OperaMask[]) => {
    return state.favorites
      .map(id => masks.find(mask => mask.id === id))
      .filter(Boolean) as OperaMask[];
  };
  
  return {
    // 状态
    filter: state.filter,
    recentlyViewed: state.recentlyViewed,
    favorites: state.favorites,
    searchHistory: state.searchHistory,
    
    // 操作
    updateFilter,
    addToRecentlyViewed,
    toggleFavorite,
    addToSearchHistory,
    clearSearchHistory,
    
    // 辅助函数
    isFavorite,
    getRecentlyViewedMasks,
    getFavoriteMasks
  };
}
