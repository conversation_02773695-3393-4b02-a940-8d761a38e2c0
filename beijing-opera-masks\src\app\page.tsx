'use client';

import React, { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { operaMasks } from '@/data/masks';
import { OperaMask, MaskFilter } from '@/types/mask';
import { useAppState } from '@/hooks/useAppState';
import { filterMasks } from '@/utils/maskUtils';

export default function Home() {
  const [selectedMask, setSelectedMask] = useState<OperaMask | null>(null);
  const router = useRouter();
  const {
    filter,
    updateFilter,
    addToRecentlyViewed,
    toggleFavorite,
    isFavorite,
    getRecentlyViewedMasks
  } = useAppState();

  // 过滤脸谱
  const filteredMasks = useMemo(() => {
    return filterMasks(operaMasks, filter);
  }, [filter]);

  // 最近查看的脸谱
  const recentlyViewedMasks = getRecentlyViewedMasks(operaMasks);

  const handleMaskClick = (mask: OperaMask) => {
    setSelectedMask(mask);
  };

  const handleViewDetails = (mask: OperaMask) => {
    addToRecentlyViewed(mask.id);
    router.push(`/mask/${mask.id}`);
  };

  const handleFilterChange = (newFilter: MaskFilter) => {
    updateFilter(newFilter);
  };

  const handleCloseModal = () => {
    setSelectedMask(null);
  };

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#F9FAFB',
      fontFamily: '"Noto Sans SC", sans-serif'
    }}>
      {/* 头部 */}
      <header style={{
        backgroundColor: 'white',
        borderBottom: '2px solid #F59E0B',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        position: 'sticky',
        top: 0,
        zIndex: 40
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '1rem 1.5rem',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <h1 style={{
            fontSize: '1.5rem',
            fontWeight: 'bold',
            color: '#1F2937',
            fontFamily: '"Noto Serif SC", serif'
          }}>
            京剧脸谱文化展示平台
          </h1>
        </div>
      </header>

      {/* 主要内容 */}
      <main style={{ padding: '2rem 1.5rem' }}>
        {/* 英雄区域 */}
        <section style={{
          textAlign: 'center',
          padding: '3rem 0',
          background: 'linear-gradient(135deg, rgba(185, 28, 28, 0.05) 0%, transparent 50%, rgba(245, 158, 11, 0.05) 100%)',
          borderRadius: '0.75rem',
          marginBottom: '3rem'
        }}>
          <h2 style={{
            fontSize: '2rem',
            fontWeight: 'bold',
            color: '#1F2937',
            fontFamily: '"Noto Serif SC", serif',
            marginBottom: '1rem'
          }}>
            探索京剧脸谱的艺术魅力
          </h2>
          <p style={{
            fontSize: '1.125rem',
            color: '#6B7280',
            maxWidth: '32rem',
            margin: '0 auto 2rem'
          }}>
            深入了解中国传统戏曲文化，感受脸谱艺术的独特魅力与深厚内涵
          </p>
        </section>

        {/* 脸谱网格 */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
          gap: '2rem',
          maxWidth: '1200px',
          margin: '0 auto'
        }}>
          {filteredMasks.map((mask) => (
            <div
              key={mask.id}
              onClick={() => handleMaskClick(mask)}
              style={{
                backgroundColor: 'white',
                borderRadius: '0.75rem',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                border: '2px solid #F59E0B',
                overflow: 'hidden',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                position: 'relative'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'scale(1.05)';
                e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'scale(1)';
                e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
              }}
            >
              {/* 脸谱图片占位 */}
              <div style={{
                aspectRatio: '1',
                background: `linear-gradient(135deg, ${mask.mainColors[0]} 0%, ${mask.mainColors[1] || mask.mainColors[0]} 100%)`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: '2rem',
                fontWeight: 'bold',
                position: 'relative'
              }}>
                <div style={{
                  position: 'absolute',
                  inset: 0,
                  background: 'linear-gradient(to top, rgba(0,0,0,0.2) 0%, transparent 100%)'
                }} />
                <span style={{ position: 'relative', zIndex: 10 }}>
                  {mask.character}
                </span>

                {/* 分类标签 */}
                <div style={{
                  position: 'absolute',
                  top: '0.75rem',
                  left: '0.75rem',
                  backgroundColor: 'rgba(0,0,0,0.7)',
                  color: 'white',
                  padding: '0.25rem 0.5rem',
                  borderRadius: '9999px',
                  fontSize: '0.75rem',
                  fontWeight: '500'
                }}>
                  {mask.roleCategory}
                </div>

                <div style={{
                  position: 'absolute',
                  top: '0.75rem',
                  right: '0.75rem',
                  backgroundColor: 'rgba(0,0,0,0.7)',
                  color: 'white',
                  padding: '0.25rem 0.5rem',
                  borderRadius: '9999px',
                  fontSize: '0.75rem',
                  fontWeight: '500'
                }}>
                  {mask.colorCategory}
                </div>
              </div>

              {/* 脸谱信息 */}
              <div style={{ padding: '1rem' }}>
                <h3 style={{
                  fontSize: '1.125rem',
                  fontWeight: '600',
                  color: '#1F2937',
                  marginBottom: '0.5rem',
                  fontFamily: '"Noto Serif SC", serif'
                }}>
                  {mask.name}
                </h3>
                <p style={{
                  fontSize: '0.875rem',
                  color: '#6B7280',
                  marginBottom: '0.75rem',
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden'
                }}>
                  {mask.culturalBackground.personality}
                </p>

                {/* 主要颜色 */}
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  marginBottom: '0.75rem'
                }}>
                  <span style={{ fontSize: '0.75rem', color: '#6B7280' }}>主要色彩:</span>
                  <div style={{ display: 'flex', gap: '0.25rem' }}>
                    {mask.mainColors.slice(0, 3).map((color, index) => (
                      <div
                        key={index}
                        style={{
                          width: '1rem',
                          height: '1rem',
                          borderRadius: '50%',
                          backgroundColor: color,
                          border: '1px solid #D1D5DB'
                        }}
                        title={color}
                      />
                    ))}
                  </div>
                </div>

                {/* 标签 */}
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.25rem' }}>
                  {mask.tags.slice(0, 3).map((tag, index) => (
                    <span
                      key={index}
                      style={{
                        padding: '0.25rem 0.5rem',
                        fontSize: '0.75rem',
                        backgroundColor: '#F3F4F6',
                        color: '#6B7280',
                        borderRadius: '9999px'
                      }}
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </main>

      {/* 简单的模态框 */}
      {selectedMask && (
        <div
          style={{
            position: 'fixed',
            inset: 0,
            zIndex: 50,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            backdropFilter: 'blur(4px)'
          }}
          onClick={handleCloseModal}
        >
          <div
            style={{
              backgroundColor: 'white',
              borderRadius: '0.75rem',
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
              border: '2px solid #F59E0B',
              maxWidth: '90vw',
              maxHeight: '90vh',
              overflow: 'auto',
              position: 'relative'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* 关闭按钮 */}
            <button
              onClick={handleCloseModal}
              style={{
                position: 'absolute',
                top: '1rem',
                right: '1rem',
                backgroundColor: 'transparent',
                border: 'none',
                fontSize: '1.5rem',
                cursor: 'pointer',
                zIndex: 10,
                color: '#6B7280'
              }}
            >
              ×
            </button>

            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '1.5rem',
              padding: '1.5rem'
            }}>
              {/* 脸谱图片 */}
              <div>
                <div style={{
                  aspectRatio: '1',
                  background: `linear-gradient(135deg, ${selectedMask.mainColors[0]} 0%, ${selectedMask.mainColors[1] || selectedMask.mainColors[0]} 100%)`,
                  borderRadius: '0.5rem',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '3rem',
                  fontWeight: 'bold',
                  marginBottom: '1rem'
                }}>
                  {selectedMask.character}
                </div>
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  {selectedMask.mainColors.map((color, index) => (
                    <div
                      key={index}
                      style={{
                        width: '2rem',
                        height: '2rem',
                        borderRadius: '50%',
                        backgroundColor: color,
                        border: '2px solid #D1D5DB'
                      }}
                      title={color}
                    />
                  ))}
                </div>
              </div>

              {/* 脸谱信息 */}
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
                <div>
                  <h2 style={{
                    fontSize: '1.5rem',
                    fontWeight: 'bold',
                    color: '#1F2937',
                    marginBottom: '0.5rem',
                    fontFamily: '"Noto Serif SC", serif'
                  }}>
                    {selectedMask.name}
                  </h2>
                  <p style={{ color: '#6B7280', fontWeight: '500' }}>
                    {selectedMask.character}
                  </p>
                </div>

                <div>
                  <h3 style={{
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    color: '#1F2937',
                    marginBottom: '0.5rem'
                  }}>
                    基本信息
                  </h3>
                  <div style={{ fontSize: '0.875rem', display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                    <p><span style={{ fontWeight: '500' }}>行当:</span> {selectedMask.roleCategory}</p>
                    <p><span style={{ fontWeight: '500' }}>颜色分类:</span> {selectedMask.colorCategory}</p>
                    <p><span style={{ fontWeight: '500' }}>绘制难度:</span> {selectedMask.difficulty}</p>
                  </div>
                </div>

                <div>
                  <h3 style={{
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    color: '#1F2937',
                    marginBottom: '0.5rem'
                  }}>
                    文化背景
                  </h3>
                  <div style={{ fontSize: '0.875rem', color: '#6B7280', display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                    <p><span style={{ fontWeight: '500' }}>历史起源:</span> {selectedMask.culturalBackground.origin}</p>
                    <p><span style={{ fontWeight: '500' }}>性格特点:</span> {selectedMask.culturalBackground.personality}</p>
                    <p><span style={{ fontWeight: '500' }}>象征意义:</span> {selectedMask.culturalBackground.symbolism}</p>
                  </div>
                </div>

                <div>
                  <h3 style={{
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    color: '#1F2937',
                    marginBottom: '0.5rem'
                  }}>
                    相关剧目
                  </h3>
                  <div style={{ fontSize: '0.875rem', display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                    {selectedMask.relatedOperas.map((opera, index) => (
                      <div key={index}>
                        <p style={{ fontWeight: '500' }}>{opera.name}</p>
                        <p style={{ color: '#6B7280' }}>{opera.description}</p>
                      </div>
                    ))}
                  </div>
                </div>

                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.5rem', marginBottom: '1.5rem' }}>
                  {selectedMask.tags.map((tag, index) => (
                    <span
                      key={index}
                      style={{
                        padding: '0.5rem 0.75rem',
                        fontSize: '0.75rem',
                        backgroundColor: 'rgba(245, 158, 11, 0.2)',
                        color: '#F59E0B',
                        border: '1px solid #F59E0B',
                        borderRadius: '9999px'
                      }}
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                {/* 查看详情按钮 */}
                <button
                  onClick={() => handleViewDetails(selectedMask)}
                  style={{
                    width: '100%',
                    backgroundColor: '#B91C1C',
                    color: 'white',
                    padding: '0.75rem 1.5rem',
                    borderRadius: '0.5rem',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '1rem',
                    fontWeight: '600',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#991B1B';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#B91C1C';
                  }}
                >
                  查看完整详情 →
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
