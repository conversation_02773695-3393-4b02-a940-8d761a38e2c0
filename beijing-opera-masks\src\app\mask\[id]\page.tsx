'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { operaMasks } from '@/data/masks';
import { OperaMask } from '@/types/mask';
import { MaskService } from '@/services/maskService';
import { isSupabaseConfigured } from '@/lib/supabase';
import { useTheme } from '@/components/providers/ThemeProvider';
import { SimpleNavbar } from '@/components/navigation/SimpleNavbar';

export default function MaskDetailPage() {
  const params = useParams();
  const router = useRouter();
  const maskId = params.id as string;
  const [mask, setMask] = useState<OperaMask | null>(null);
  const [loading, setLoading] = useState(true);
  const [showAnimation, setShowAnimation] = useState(false);
  const { colors } = useTheme();

  // 加载脸谱数据
  useEffect(() => {
    const loadMask = async () => {
      try {
        let maskData = operaMasks; // 默认使用静态数据
        
        if (isSupabaseConfigured()) {
          try {
            maskData = await MaskService.getAllApprovedMasks();
          } catch (error) {
            console.error('Error loading masks from database:', error);
          }
        }
        
        const foundMask = maskData.find(m => m.id === maskId);
        setMask(foundMask || null);
      } catch (error) {
        console.error('Error loading mask:', error);
      } finally {
        setLoading(false);
      }
    };

    loadMask();
  }, [maskId]);

  // 加载状态
  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        backgroundColor: colors.background,
        color: colors.textPrimary
      }}>
        <SimpleNavbar showBackButton title="加载中..." />
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: 'calc(100vh - 80px)'
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ 
              fontSize: '3rem', 
              marginBottom: '1rem',
              animation: 'pulse 2s infinite'
            }}>
              🎭
            </div>
            <p>正在加载脸谱信息...</p>
          </div>
        </div>
      </div>
    );
  }

  // 脸谱未找到
  if (!mask) {
    return (
      <div style={{
        minHeight: '100vh',
        backgroundColor: colors.background,
        color: colors.textPrimary
      }}>
        <SimpleNavbar showBackButton title="脸谱未找到" />
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: 'calc(100vh - 80px)'
        }}>
          <div style={{ textAlign: 'center' }}>
            <h1 style={{ fontSize: '2rem', marginBottom: '1rem' }}>
              脸谱未找到
            </h1>
            <p style={{ color: colors.textSecondary, marginBottom: '2rem' }}>
              抱歉，您访问的脸谱不存在。
            </p>
            <button
              onClick={() => router.push('/')}
              style={{
                backgroundColor: colors.primary,
                color: 'white',
                padding: '0.75rem 1.5rem',
                borderRadius: '0.5rem',
                border: 'none',
                cursor: 'pointer',
                fontSize: '1rem'
              }}
            >
              返回首页
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: colors.background,
      color: colors.textPrimary
    }}>
      {/* 导航栏 */}
      <SimpleNavbar showBackButton title={mask.name} />

      {/* 主要内容 */}
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '2rem'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '3rem',
          '@media (max-width: 768px)': {
            gridTemplateColumns: '1fr'
          }
        }}>
          {/* 左侧：脸谱图片 */}
          <div>
            <div style={{
              backgroundColor: colors.backgroundSecondary,
              borderRadius: '12px',
              padding: '2rem',
              textAlign: 'center'
            }}>
              <img
                src={mask.images?.fullSize || mask.imageUrl || mask.images?.thumbnail}
                alt={mask.name}
                style={{
                  width: '100%',
                  maxWidth: '400px',
                  height: 'auto',
                  borderRadius: '8px',
                  marginBottom: '1rem'
                }}
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = `https://via.placeholder.com/400x400/DC143C/FFFFFF?text=${encodeURIComponent(mask.name)}`;
                }}
              />
              
              {/* 动画按钮 */}
              <button
                onClick={() => setShowAnimation(!showAnimation)}
                style={{
                  backgroundColor: colors.primary,
                  color: 'white',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '0.5rem',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '1rem',
                  marginTop: '1rem'
                }}
              >
                {showAnimation ? '隐藏绘制过程' : '观看绘制过程'}
              </button>
            </div>
          </div>

          {/* 右侧：脸谱信息 */}
          <div>
            <div style={{
              backgroundColor: colors.backgroundSecondary,
              borderRadius: '12px',
              padding: '2rem',
              marginBottom: '2rem'
            }}>
              <h2 style={{
                fontSize: '1.5rem',
                fontWeight: 'bold',
                marginBottom: '1rem',
                color: colors.textPrimary
              }}>
                基本信息
              </h2>
              
              <div style={{ marginBottom: '1rem' }}>
                <strong>角色：</strong> {mask.character}
              </div>
              
              <div style={{ marginBottom: '1rem' }}>
                <strong>行当：</strong> {mask.roleCategory}
              </div>
              
              <div style={{ marginBottom: '1rem' }}>
                <strong>色彩分类：</strong> {mask.colorCategory}
              </div>

              {/* 主要颜色 */}
              {mask.mainColors && (
                <div style={{ marginBottom: '1rem' }}>
                  <strong>主要色彩：</strong>
                  <div style={{ display: 'flex', gap: '0.5rem', marginTop: '0.5rem' }}>
                    {mask.mainColors.map((color, index) => (
                      <div
                        key={index}
                        style={{
                          width: '30px',
                          height: '30px',
                          borderRadius: '50%',
                          backgroundColor: color,
                          border: '2px solid rgba(0,0,0,0.1)'
                        }}
                        title={color}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* 文化背景 */}
            <div style={{
              backgroundColor: colors.backgroundSecondary,
              borderRadius: '12px',
              padding: '2rem'
            }}>
              <h2 style={{
                fontSize: '1.5rem',
                fontWeight: 'bold',
                marginBottom: '1rem',
                color: colors.textPrimary
              }}>
                文化背景
              </h2>
              
              <div style={{ lineHeight: '1.6' }}>
                {typeof mask.culturalBackground === 'string' ? (
                  <p>{mask.culturalBackground}</p>
                ) : (
                  <>
                    <div style={{ marginBottom: '1rem' }}>
                      <strong>历史起源：</strong>
                      <p style={{ marginTop: '0.5rem' }}>{mask.culturalBackground.origin}</p>
                    </div>
                    
                    <div style={{ marginBottom: '1rem' }}>
                      <strong>性格特点：</strong>
                      <p style={{ marginTop: '0.5rem' }}>{mask.culturalBackground.personality}</p>
                    </div>
                    
                    <div style={{ marginBottom: '1rem' }}>
                      <strong>象征意义：</strong>
                      <p style={{ marginTop: '0.5rem' }}>{mask.culturalBackground.symbolism}</p>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
