// 京剧脸谱数据类型定义

// 行当分类
export type RoleCategory = '生' | '旦' | '净' | '丑';

// 脸谱颜色分类
export type ColorCategory = '红脸' | '黑脸' | '白脸' | '蓝脸' | '绿脸' | '黄脸' | '金脸' | '银脸';

// 脸谱绘制步骤
export interface DrawingStep {
  id: string;
  stepNumber: number;
  title: string;
  description: string;
  duration: number; // 动画持续时间（毫秒）
  svgPath?: string; // SVG路径数据

  // 兼容旧版本字段
  name?: string;
  color?: string;
  strokeWidth?: number;
}

// 相关剧目
export interface RelatedOpera {
  name: string;
  description: string;
  period: string; // 朝代或时期
}

// 脸谱主要数据结构
export interface OperaMask {
  id: string;
  name: string; // 脸谱名称
  character: string; // 角色名称
  roleType: 'sheng' | 'dan' | 'jing' | 'chou'; // 行当分类
  colorTheme: string[]; // 颜色主题
  imageUrl: string; // 脸谱图片URL
  thumbnailUrl?: string; // 缩略图URL
  culturalBackground: string; // 文化背景描述
  personalityTraits: string[]; // 性格特征
  storyDescription?: string; // 故事描述
  dynasty?: string; // 朝代
  isOfficial: boolean; // 是否为官方脸谱
  isApproved: boolean; // 是否已审核
  createdBy?: string; // 创建者ID
  createdAt: string; // 创建时间
  updatedAt: string; // 更新时间
  likesCount: number; // 点赞数
  viewsCount: number; // 浏览数
  drawingSteps: DrawingStep[]; // 绘制步骤

  // 兼容旧版本的字段
  roleCategory?: RoleCategory; // 行当分类（旧版本）
  colorCategory?: ColorCategory; // 颜色分类（旧版本）
  mainColors?: string[]; // 主要颜色（旧版本）
  images?: {
    thumbnail: string;
    fullSize: string;
    drawingSteps?: string[];
  };
  relatedOperas?: RelatedOpera[];
  colorMeaning?: { [color: string]: string };
  difficulty?: 'easy' | 'medium' | 'hard';
  popularity?: number;
  tags?: string[];
}

// 脸谱筛选条件
export interface MaskFilter {
  roleCategory?: RoleCategory;
  colorCategory?: ColorCategory;
  difficulty?: 'easy' | 'medium' | 'hard';
  searchTerm?: string;
}

// 脸谱展示模式
export type DisplayMode = 'grid' | 'list' | 'category';

// 动画控制状态
export interface AnimationState {
  isPlaying: boolean;
  currentStep: number;
  speed: number; // 播放速度倍数
  loop: boolean;
}
