"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_masks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/masks */ \"(app-pages-browser)/./src/data/masks.ts\");\n/* harmony import */ var _services_maskService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/maskService */ \"(app-pages-browser)/./src/services/maskService.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers/ThemeProvider */ \"(app-pages-browser)/./src/components/providers/ThemeProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Home() {\n    var _selectedMask_images, _selectedMask_images1, _selectedMask_culturalBackground;\n    _s();\n    const [masks, setMasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_data_masks__WEBPACK_IMPORTED_MODULE_2__.operaMasks);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedMask, setSelectedMask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { colors } = (0,_components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    // 加载脸谱数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const loadMasks = {\n                \"Home.useEffect.loadMasks\": async ()=>{\n                    if (!(0,_lib_supabase__WEBPACK_IMPORTED_MODULE_4__.isSupabaseConfigured)()) {\n                        console.log('Using static mask data');\n                        return;\n                    }\n                    setLoading(true);\n                    try {\n                        const maskData = await _services_maskService__WEBPACK_IMPORTED_MODULE_3__.MaskService.getAllApprovedMasks();\n                        setMasks(maskData);\n                    } catch (error) {\n                        console.error('Error loading masks:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"Home.useEffect.loadMasks\"];\n            loadMasks();\n        }\n    }[\"Home.useEffect\"], []);\n    const handleMaskClick = (mask)=>{\n        setSelectedMask(mask);\n    };\n    const closeModal = ()=>{\n        setSelectedMask(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            backgroundColor: colors.background,\n            color: colors.text,\n            padding: '2rem'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center',\n                    marginBottom: '3rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            fontSize: '2.5rem',\n                            fontWeight: 'bold',\n                            marginBottom: '1rem',\n                            background: 'linear-gradient(135deg, #DC2626, #B91C1C)',\n                            WebkitBackgroundClip: 'text',\n                            WebkitTextFillColor: 'transparent',\n                            fontFamily: '\"Ma Shan Zheng\", cursive'\n                        },\n                        children: \"\\uD83C\\uDFAD 京剧脸谱文化展示\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            fontSize: '1.125rem',\n                            color: colors.textSecondary,\n                            maxWidth: '600px',\n                            margin: '0 auto'\n                        },\n                        children: \"探索中国传统京剧脸谱艺术的魅力，了解每个角色背后的文化内涵\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center',\n                    padding: '2rem',\n                    color: colors.textSecondary\n                },\n                children: \"正在加载脸谱数据...\"\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                    gap: '2rem',\n                    maxWidth: '1200px',\n                    margin: '0 auto'\n                },\n                children: masks.map((mask)=>{\n                    var _mask_images, _mask_images1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: ()=>handleMaskClick(mask),\n                        style: {\n                            backgroundColor: colors.cardBackground,\n                            borderRadius: '12px',\n                            padding: '1.5rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.3s ease',\n                            border: \"1px solid \".concat(colors.border),\n                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: '100%',\n                                    height: '200px',\n                                    borderRadius: '8px',\n                                    overflow: 'hidden',\n                                    marginBottom: '1rem'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: ((_mask_images = mask.images) === null || _mask_images === void 0 ? void 0 : _mask_images.fullSize) || mask.imageUrl || ((_mask_images1 = mask.images) === null || _mask_images1 === void 0 ? void 0 : _mask_images1.thumbnail),\n                                    alt: mask.name,\n                                    style: {\n                                        width: '100%',\n                                        height: '100%',\n                                        objectFit: 'cover'\n                                    },\n                                    onError: (e)=>{\n                                        // 图片加载失败时的备用处理\n                                        const target = e.target;\n                                        target.src = \"https://via.placeholder.com/300x300/DC143C/FFFFFF?text=\".concat(encodeURIComponent(mask.name));\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    fontSize: '1.25rem',\n                                    fontWeight: 'bold',\n                                    marginBottom: '0.5rem',\n                                    color: colors.textPrimary\n                                },\n                                children: mask.name\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: '0.875rem',\n                                    color: colors.textSecondary,\n                                    marginBottom: '1rem'\n                                },\n                                children: [\n                                    \"角色: \",\n                                    mask.character\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this),\n                            (mask.colorTheme || mask.mainColors) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '0.5rem',\n                                    marginBottom: '1rem'\n                                },\n                                children: (mask.colorTheme || mask.mainColors || []).slice(0, 3).map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: '20px',\n                                            height: '20px',\n                                            borderRadius: '50%',\n                                            backgroundColor: color,\n                                            border: '1px solid rgba(0,0,0,0.1)'\n                                        },\n                                        title: color\n                                    }, index, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 15\n                            }, this),\n                            (mask.personalityTraits || mask.tags) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexWrap: 'wrap',\n                                    gap: '0.5rem'\n                                },\n                                children: (mask.personalityTraits || mask.tags || []).slice(0, 3).map((trait, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: '0.75rem',\n                                            padding: '0.25rem 0.5rem',\n                                            backgroundColor: colors.primary + '20',\n                                            color: colors.primary,\n                                            borderRadius: '12px'\n                                        },\n                                        children: trait\n                                    }, index, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, mask.id, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            selectedMask && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    zIndex: 1000,\n                    padding: '1rem'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: colors.background,\n                        borderRadius: '12px',\n                        padding: '2rem',\n                        maxWidth: '600px',\n                        width: '100%',\n                        maxHeight: '80vh',\n                        overflow: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center',\n                                marginBottom: '1.5rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        fontSize: '1.5rem',\n                                        fontWeight: 'bold',\n                                        color: colors.textPrimary\n                                    },\n                                    children: selectedMask.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: closeModal,\n                                    style: {\n                                        background: 'none',\n                                        border: 'none',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer',\n                                        color: colors.textSecondary\n                                    },\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: ((_selectedMask_images = selectedMask.images) === null || _selectedMask_images === void 0 ? void 0 : _selectedMask_images.fullSize) || selectedMask.imageUrl || ((_selectedMask_images1 = selectedMask.images) === null || _selectedMask_images1 === void 0 ? void 0 : _selectedMask_images1.thumbnail),\n                            alt: selectedMask.name,\n                            style: {\n                                width: '100%',\n                                height: '300px',\n                                objectFit: 'cover',\n                                borderRadius: '8px',\n                                marginBottom: '1.5rem'\n                            },\n                            onError: (e)=>{\n                                const target = e.target;\n                                target.src = \"https://via.placeholder.com/600x300/DC143C/FFFFFF?text=\".concat(encodeURIComponent(selectedMask.name));\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '0.875rem',\n                                color: colors.textSecondary,\n                                lineHeight: '1.6'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"角色:\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 18\n                                        }, this),\n                                        \" \",\n                                        selectedMask.character\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"文化背景:\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 18\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        marginLeft: '1rem',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: typeof selectedMask.culturalBackground === 'string' ? selectedMask.culturalBackground : ((_selectedMask_culturalBackground = selectedMask.culturalBackground) === null || _selectedMask_culturalBackground === void 0 ? void 0 : _selectedMask_culturalBackground.origin) || '传统京剧脸谱艺术'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this),\n                                (selectedMask.personalityTraits || selectedMask.tags) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"特征:\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 22\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                flexWrap: 'wrap',\n                                                gap: '0.5rem',\n                                                marginLeft: '1rem'\n                                            },\n                                            children: (selectedMask.personalityTraits || selectedMask.tags || []).map((trait, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.75rem',\n                                                        padding: '0.25rem 0.5rem',\n                                                        backgroundColor: colors.primary + '20',\n                                                        color: colors.primary,\n                                                        borderRadius: '12px'\n                                                    },\n                                                    children: trait\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"Vfoqi3Tz0LvR925DWBUyKoanCxA=\", false, function() {\n    return [\n        _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_5__.useTheme\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});