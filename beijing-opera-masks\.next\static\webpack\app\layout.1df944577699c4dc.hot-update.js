"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/components/performance/PerformanceMonitor.tsx":
/*!***********************************************************!*\
  !*** ./src/components/performance/PerformanceMonitor.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerformanceMonitor: () => (/* binding */ PerformanceMonitor),\n/* harmony export */   performanceUtils: () => (/* binding */ performanceUtils)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_useEffect_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=useEffect!=!react */ \"(app-pages-browser)/__barrel_optimize__?names=useEffect!=!./node_modules/next/dist/compiled/react/index.js\");\n/* __next_internal_client_entry_do_not_use__ PerformanceMonitor,performanceUtils auto */ var _s = $RefreshSig$();\n\nfunction PerformanceMonitor() {\n    _s();\n    (0,_barrel_optimize_names_useEffect_react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"PerformanceMonitor.useEffect\": ()=>{\n            // 只在生产环境中启用性能监控\n            if (true) {\n                return;\n            }\n            const metrics = {};\n            // 监控 First Contentful Paint\n            const observeFCP = {\n                \"PerformanceMonitor.useEffect.observeFCP\": ()=>{\n                    const observer = new PerformanceObserver({\n                        \"PerformanceMonitor.useEffect.observeFCP\": (list)=>{\n                            const entries = list.getEntries();\n                            entries.forEach({\n                                \"PerformanceMonitor.useEffect.observeFCP\": (entry)=>{\n                                    if (entry.name === 'first-contentful-paint') {\n                                        metrics.fcp = entry.startTime;\n                                        console.log('FCP:', entry.startTime);\n                                    }\n                                }\n                            }[\"PerformanceMonitor.useEffect.observeFCP\"]);\n                        }\n                    }[\"PerformanceMonitor.useEffect.observeFCP\"]);\n                    observer.observe({\n                        entryTypes: [\n                            'paint'\n                        ]\n                    });\n                }\n            }[\"PerformanceMonitor.useEffect.observeFCP\"];\n            // 监控 Largest Contentful Paint\n            const observeLCP = {\n                \"PerformanceMonitor.useEffect.observeLCP\": ()=>{\n                    const observer = new PerformanceObserver({\n                        \"PerformanceMonitor.useEffect.observeLCP\": (list)=>{\n                            const entries = list.getEntries();\n                            const lastEntry = entries[entries.length - 1];\n                            metrics.lcp = lastEntry.startTime;\n                            console.log('LCP:', lastEntry.startTime);\n                        }\n                    }[\"PerformanceMonitor.useEffect.observeLCP\"]);\n                    observer.observe({\n                        entryTypes: [\n                            'largest-contentful-paint'\n                        ]\n                    });\n                }\n            }[\"PerformanceMonitor.useEffect.observeLCP\"];\n            // 监控 First Input Delay\n            const observeFID = {\n                \"PerformanceMonitor.useEffect.observeFID\": ()=>{\n                    const observer = new PerformanceObserver({\n                        \"PerformanceMonitor.useEffect.observeFID\": (list)=>{\n                            const entries = list.getEntries();\n                            entries.forEach({\n                                \"PerformanceMonitor.useEffect.observeFID\": (entry)=>{\n                                    metrics.fid = entry.processingStart - entry.startTime;\n                                    console.log('FID:', metrics.fid);\n                                }\n                            }[\"PerformanceMonitor.useEffect.observeFID\"]);\n                        }\n                    }[\"PerformanceMonitor.useEffect.observeFID\"]);\n                    observer.observe({\n                        entryTypes: [\n                            'first-input'\n                        ]\n                    });\n                }\n            }[\"PerformanceMonitor.useEffect.observeFID\"];\n            // 监控 Cumulative Layout Shift\n            const observeCLS = {\n                \"PerformanceMonitor.useEffect.observeCLS\": ()=>{\n                    let clsValue = 0;\n                    const observer = new PerformanceObserver({\n                        \"PerformanceMonitor.useEffect.observeCLS\": (list)=>{\n                            const entries = list.getEntries();\n                            entries.forEach({\n                                \"PerformanceMonitor.useEffect.observeCLS\": (entry)=>{\n                                    if (!entry.hadRecentInput) {\n                                        clsValue += entry.value;\n                                    }\n                                }\n                            }[\"PerformanceMonitor.useEffect.observeCLS\"]);\n                            metrics.cls = clsValue;\n                            console.log('CLS:', clsValue);\n                        }\n                    }[\"PerformanceMonitor.useEffect.observeCLS\"]);\n                    observer.observe({\n                        entryTypes: [\n                            'layout-shift'\n                        ]\n                    });\n                }\n            }[\"PerformanceMonitor.useEffect.observeCLS\"];\n            // 监控 Time to First Byte\n            const observeTTFB = {\n                \"PerformanceMonitor.useEffect.observeTTFB\": ()=>{\n                    const navigationEntry = performance.getEntriesByType('navigation')[0];\n                    if (navigationEntry) {\n                        metrics.ttfb = navigationEntry.responseStart - navigationEntry.requestStart;\n                        console.log('TTFB:', metrics.ttfb);\n                    }\n                }\n            }[\"PerformanceMonitor.useEffect.observeTTFB\"];\n            // 检查浏览器支持\n            if ('PerformanceObserver' in window) {\n                observeFCP();\n                observeLCP();\n                observeFID();\n                observeCLS();\n                observeTTFB();\n            }\n            // 页面卸载时发送性能数据\n            const sendMetrics = {\n                \"PerformanceMonitor.useEffect.sendMetrics\": ()=>{\n                    // 这里可以发送到分析服务\n                    console.log('Performance Metrics:', metrics);\n                // 示例：发送到Google Analytics或其他分析服务\n                // if (window.gtag) {\n                //   window.gtag('event', 'web_vitals', {\n                //     custom_map: {\n                //       metric_fcp: 'fcp',\n                //       metric_lcp: 'lcp',\n                //       metric_fid: 'fid',\n                //       metric_cls: 'cls',\n                //       metric_ttfb: 'ttfb'\n                //     },\n                //     fcp: metrics.fcp,\n                //     lcp: metrics.lcp,\n                //     fid: metrics.fid,\n                //     cls: metrics.cls,\n                //     ttfb: metrics.ttfb\n                //   });\n                // }\n                }\n            }[\"PerformanceMonitor.useEffect.sendMetrics\"];\n            // 监听页面卸载事件\n            window.addEventListener('beforeunload', sendMetrics);\n            // 监听页面可见性变化\n            document.addEventListener('visibilitychange', {\n                \"PerformanceMonitor.useEffect\": ()=>{\n                    if (document.visibilityState === 'hidden') {\n                        sendMetrics();\n                    }\n                }\n            }[\"PerformanceMonitor.useEffect\"]);\n            return ({\n                \"PerformanceMonitor.useEffect\": ()=>{\n                    window.removeEventListener('beforeunload', sendMetrics);\n                    document.removeEventListener('visibilitychange', sendMetrics);\n                }\n            })[\"PerformanceMonitor.useEffect\"];\n        }\n    }[\"PerformanceMonitor.useEffect\"], []);\n    return null; // 这个组件不渲染任何内容\n}\n_s(PerformanceMonitor, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = PerformanceMonitor;\n// 性能优化工具函数\nconst performanceUtils = {\n    // 预加载关键资源\n    preloadResource: (href, as)=>{\n        const link = document.createElement('link');\n        link.rel = 'preload';\n        link.href = href;\n        link.as = as;\n        document.head.appendChild(link);\n    },\n    // 预连接到外部域名\n    preconnect: (href)=>{\n        const link = document.createElement('link');\n        link.rel = 'preconnect';\n        link.href = href;\n        document.head.appendChild(link);\n    },\n    // DNS预解析\n    dnsPrefetch: (href)=>{\n        const link = document.createElement('link');\n        link.rel = 'dns-prefetch';\n        link.href = href;\n        document.head.appendChild(link);\n    },\n    // 延迟执行非关键代码\n    defer: function(callback) {\n        let delay = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        if ('requestIdleCallback' in window) {\n            requestIdleCallback(callback);\n        } else {\n            setTimeout(callback, delay);\n        }\n    },\n    // 检查网络连接质量\n    getNetworkInfo: ()=>{\n        if ('connection' in navigator) {\n            const connection = navigator.connection;\n            return {\n                effectiveType: connection.effectiveType,\n                downlink: connection.downlink,\n                rtt: connection.rtt,\n                saveData: connection.saveData\n            };\n        }\n        return null;\n    }\n};\nvar _c;\n$RefreshReg$(_c, \"PerformanceMonitor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/performance/PerformanceMonitor.tsx\n"));

/***/ })

});