import { supabase } from '@/lib/supabase';

export class UserInteractionService {
  // 点赞脸谱
  static async likeMask(userId: string, maskId: string): Promise<void> {
    const { error } = await supabase
      .from('user_likes')
      .insert({
        user_id: userId,
        mask_id: maskId
      });

    if (error) {
      console.error('Error liking mask:', error);
      throw error;
    }
  }

  // 取消点赞
  static async unlikeMask(userId: string, maskId: string): Promise<void> {
    const { error } = await supabase
      .from('user_likes')
      .delete()
      .eq('user_id', userId)
      .eq('mask_id', maskId);

    if (error) {
      console.error('Error unliking mask:', error);
      throw error;
    }
  }

  // 检查用户是否已点赞
  static async isLiked(userId: string, maskId: string): Promise<boolean> {
    const { data, error } = await supabase
      .from('user_likes')
      .select('id')
      .eq('user_id', userId)
      .eq('mask_id', maskId)
      .single();

    if (error && error.code === 'PGRST116') {
      return false; // 未找到记录，未点赞
    }

    if (error) {
      console.error('Error checking like status:', error);
      throw error;
    }

    return !!data;
  }

  // 收藏脸谱
  static async favoriteMask(userId: string, maskId: string): Promise<void> {
    const { error } = await supabase
      .from('user_favorites')
      .insert({
        user_id: userId,
        mask_id: maskId
      });

    if (error) {
      console.error('Error favoriting mask:', error);
      throw error;
    }
  }

  // 取消收藏
  static async unfavoriteMask(userId: string, maskId: string): Promise<void> {
    const { error } = await supabase
      .from('user_favorites')
      .delete()
      .eq('user_id', userId)
      .eq('mask_id', maskId);

    if (error) {
      console.error('Error unfavoriting mask:', error);
      throw error;
    }
  }

  // 检查用户是否已收藏
  static async isFavorited(userId: string, maskId: string): Promise<boolean> {
    const { data, error } = await supabase
      .from('user_favorites')
      .select('id')
      .eq('user_id', userId)
      .eq('mask_id', maskId)
      .single();

    if (error && error.code === 'PGRST116') {
      return false; // 未找到记录，未收藏
    }

    if (error) {
      console.error('Error checking favorite status:', error);
      throw error;
    }

    return !!data;
  }

  // 获取用户收藏的脸谱
  static async getUserFavorites(userId: string): Promise<any[]> {
    const { data, error } = await supabase
      .from('user_favorites')
      .select(`
        mask_id,
        created_at,
        masks (
          id,
          name,
          character,
          role_type,
          color_theme,
          image_url,
          thumbnail_url,
          cultural_background,
          personality_traits,
          likes_count,
          views_count,
          is_official
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching user favorites:', error);
      throw error;
    }

    return data.map(item => ({
      ...item.masks,
      favorited_at: item.created_at
    }));
  }

  // 获取用户点赞的脸谱
  static async getUserLikes(userId: string): Promise<any[]> {
    const { data, error } = await supabase
      .from('user_likes')
      .select(`
        mask_id,
        created_at,
        masks (
          id,
          name,
          character,
          role_type,
          color_theme,
          image_url,
          thumbnail_url,
          cultural_background,
          personality_traits,
          likes_count,
          views_count,
          is_official
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching user likes:', error);
      throw error;
    }

    return data.map(item => ({
      ...item.masks,
      liked_at: item.created_at
    }));
  }

  // 批量检查点赞状态
  static async getBatchLikeStatus(userId: string, maskIds: string[]): Promise<Record<string, boolean>> {
    const { data, error } = await supabase
      .from('user_likes')
      .select('mask_id')
      .eq('user_id', userId)
      .in('mask_id', maskIds);

    if (error) {
      console.error('Error fetching batch like status:', error);
      throw error;
    }

    const likedMasks = new Set(data.map(item => item.mask_id));
    const result: Record<string, boolean> = {};
    
    maskIds.forEach(maskId => {
      result[maskId] = likedMasks.has(maskId);
    });

    return result;
  }

  // 批量检查收藏状态
  static async getBatchFavoriteStatus(userId: string, maskIds: string[]): Promise<Record<string, boolean>> {
    const { data, error } = await supabase
      .from('user_favorites')
      .select('mask_id')
      .eq('user_id', userId)
      .in('mask_id', maskIds);

    if (error) {
      console.error('Error fetching batch favorite status:', error);
      throw error;
    }

    const favoritedMasks = new Set(data.map(item => item.mask_id));
    const result: Record<string, boolean> = {};
    
    maskIds.forEach(maskId => {
      result[maskId] = favoritedMasks.has(maskId);
    });

    return result;
  }

  // 获取脸谱的点赞用户列表
  static async getMaskLikers(maskId: string, limit: number = 20): Promise<any[]> {
    const { data, error } = await supabase
      .from('user_likes')
      .select(`
        user_id,
        created_at,
        user_profiles (
          username,
          display_name,
          avatar_url
        )
      `)
      .eq('mask_id', maskId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching mask likers:', error);
      throw error;
    }

    return data;
  }
}
