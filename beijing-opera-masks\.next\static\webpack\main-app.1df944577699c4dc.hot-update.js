"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main-app",{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/client/app-find-source-map-url.js ***!
  \******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"findSourceMapURL\", ({\n    enumerable: true,\n    get: function() {\n        return findSourceMapURL;\n    }\n}));\nconst basePath =  false || '';\nconst pathname = \"\" + basePath + \"/__nextjs_source-map\";\nconst findSourceMapURL =  true ? function findSourceMapURL(filename) {\n    if (filename === '') {\n        return null;\n    }\n    if (filename.startsWith(document.location.origin) && filename.includes('/_next/static')) {\n        // This is a request for a client chunk. This can only happen when\n        // using Turbopack. In this case, since we control how those source\n        // maps are generated, we can safely assume that the sourceMappingURL\n        // is relative to the filename, with an added `.map` extension. The\n        // browser can just request this file, and it gets served through the\n        // normal dev server, without the need to route this through\n        // the `/__nextjs_source-map` dev middleware.\n        return \"\" + filename + \".map\";\n    }\n    const url = new URL(pathname, document.location.origin);\n    url.searchParams.set('filename', filename);\n    return url.href;\n} : 0;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-find-source-map-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-index.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/client/app-index.js ***!
  \****************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hydrate\", ({\n    enumerable: true,\n    get: function() {\n        return hydrate;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n__webpack_require__(/*! ./app-globals */ \"(app-pages-browser)/./node_modules/next/dist/client/app-globals.js\");\nconst _client = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/client.js\"));\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _client1 = __webpack_require__(/*! react-server-dom-webpack/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/client.browser.js\");\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ../shared/lib/head-manager-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _onrecoverableerror = __webpack_require__(/*! ./react-client-callbacks/on-recoverable-error */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js\");\nconst _errorboundarycallbacks = __webpack_require__(/*! ./react-client-callbacks/error-boundary-callbacks */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js\");\nconst _appcallserver = __webpack_require__(/*! ./app-call-server */ \"(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js\");\nconst _appfindsourcemapurl = __webpack_require__(/*! ./app-find-source-map-url */ \"(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\");\nconst _approuterinstance = __webpack_require__(/*! ./components/app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _approuter = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./components/app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\"));\nconst _createinitialrouterstate = __webpack_require__(/*! ./components/router-reducer/create-initial-router-state */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _appbuildid = __webpack_require__(/*! ./app-build-id */ \"(app-pages-browser)/./node_modules/next/dist/client/app-build-id.js\");\nconst _isbot = __webpack_require__(/*! ../shared/lib/router/utils/is-bot */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\n/// <reference types=\"react-dom/experimental\" />\nconst createFromReadableStream = _client1.createFromReadableStream;\nconst appElement = document;\nconst encoder = new TextEncoder();\nlet initialServerDataBuffer = undefined;\nlet initialServerDataWriter = undefined;\nlet initialServerDataLoaded = false;\nlet initialServerDataFlushed = false;\nlet initialFormStateData = null;\nfunction nextServerDataCallback(seg) {\n    if (seg[0] === 0) {\n        initialServerDataBuffer = [];\n    } else if (seg[0] === 1) {\n        if (!initialServerDataBuffer) throw Object.defineProperty(new Error('Unexpected server data: missing bootstrap script.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E18\",\n            enumerable: false,\n            configurable: true\n        });\n        if (initialServerDataWriter) {\n            initialServerDataWriter.enqueue(encoder.encode(seg[1]));\n        } else {\n            initialServerDataBuffer.push(seg[1]);\n        }\n    } else if (seg[0] === 2) {\n        initialFormStateData = seg[1];\n    } else if (seg[0] === 3) {\n        if (!initialServerDataBuffer) throw Object.defineProperty(new Error('Unexpected server data: missing bootstrap script.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E18\",\n            enumerable: false,\n            configurable: true\n        });\n        // Decode the base64 string back to binary data.\n        const binaryString = atob(seg[1]);\n        const decodedChunk = new Uint8Array(binaryString.length);\n        for(var i = 0; i < binaryString.length; i++){\n            decodedChunk[i] = binaryString.charCodeAt(i);\n        }\n        if (initialServerDataWriter) {\n            initialServerDataWriter.enqueue(decodedChunk);\n        } else {\n            initialServerDataBuffer.push(decodedChunk);\n        }\n    }\n}\nfunction isStreamErrorOrUnfinished(ctr) {\n    // If `desiredSize` is null, it means the stream is closed or errored. If it is lower than 0, the stream is still unfinished.\n    return ctr.desiredSize === null || ctr.desiredSize < 0;\n}\n// There might be race conditions between `nextServerDataRegisterWriter` and\n// `DOMContentLoaded`. The former will be called when React starts to hydrate\n// the root, the latter will be called when the DOM is fully loaded.\n// For streaming, the former is called first due to partial hydration.\n// For non-streaming, the latter can be called first.\n// Hence, we use two variables `initialServerDataLoaded` and\n// `initialServerDataFlushed` to make sure the writer will be closed and\n// `initialServerDataBuffer` will be cleared in the right time.\nfunction nextServerDataRegisterWriter(ctr) {\n    if (initialServerDataBuffer) {\n        initialServerDataBuffer.forEach((val)=>{\n            ctr.enqueue(typeof val === 'string' ? encoder.encode(val) : val);\n        });\n        if (initialServerDataLoaded && !initialServerDataFlushed) {\n            if (isStreamErrorOrUnfinished(ctr)) {\n                ctr.error(Object.defineProperty(new Error('The connection to the page was unexpectedly closed, possibly due to the stop button being clicked, loss of Wi-Fi, or an unstable internet connection.'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E117\",\n                    enumerable: false,\n                    configurable: true\n                }));\n            } else {\n                ctr.close();\n            }\n            initialServerDataFlushed = true;\n            initialServerDataBuffer = undefined;\n        }\n    }\n    initialServerDataWriter = ctr;\n}\n// When `DOMContentLoaded`, we can close all pending writers to finish hydration.\nconst DOMContentLoaded = function() {\n    if (initialServerDataWriter && !initialServerDataFlushed) {\n        initialServerDataWriter.close();\n        initialServerDataFlushed = true;\n        initialServerDataBuffer = undefined;\n    }\n    initialServerDataLoaded = true;\n};\n_c = DOMContentLoaded;\n// It's possible that the DOM is already loaded.\nif (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', DOMContentLoaded, false);\n} else {\n    // Delayed in marco task to ensure it's executed later than hydration\n    setTimeout(DOMContentLoaded);\n}\nconst nextServerDataLoadingGlobal = self.__next_f = self.__next_f || [];\nnextServerDataLoadingGlobal.forEach(nextServerDataCallback);\nnextServerDataLoadingGlobal.push = nextServerDataCallback;\nconst readable = new ReadableStream({\n    start (controller) {\n        nextServerDataRegisterWriter(controller);\n    }\n});\nconst initialServerResponse = createFromReadableStream(readable, {\n    callServer: _appcallserver.callServer,\n    findSourceMapURL: _appfindsourcemapurl.findSourceMapURL\n});\nfunction ServerRoot(param) {\n    let { pendingActionQueue } = param;\n    const initialRSCPayload = (0, _react.use)(initialServerResponse);\n    const actionQueue = (0, _react.use)(pendingActionQueue);\n    const router = /*#__PURE__*/ (0, _jsxruntime.jsx)(_approuter.default, {\n        gracefullyDegrade: (0, _isbot.isBot)(window.navigator.userAgent),\n        actionQueue: actionQueue,\n        globalErrorState: initialRSCPayload.G,\n        assetPrefix: initialRSCPayload.p\n    });\n    if ( true && initialRSCPayload.m) {\n        // We provide missing slot information in a context provider only during development\n        // as we log some additional information about the missing slots in the console.\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.MissingSlotContext, {\n            value: initialRSCPayload.m,\n            children: router\n        });\n    }\n    return router;\n}\n_c1 = ServerRoot;\nconst StrictModeIfEnabled =  true ? _react.default.StrictMode : 0;\nfunction Root(param) {\n    let { children } = param;\n    if (false) {}\n    return children;\n}\n_c2 = Root;\nfunction onDefaultTransitionIndicator() {\n    // TODO: Compose default with user-configureable (e.g. nprogress)\n    // TODO: Use React's default once we figure out hanging indicators: https://codesandbox.io/p/sandbox/charming-moon-hktkp6?file=%2Fsrc%2Findex.js%3A106%2C30\n    return ()=>{};\n}\nconst reactRootOptions = {\n    onDefaultTransitionIndicator: onDefaultTransitionIndicator,\n    onRecoverableError: _onrecoverableerror.onRecoverableError,\n    onCaughtError: _errorboundarycallbacks.onCaughtError,\n    onUncaughtError: _errorboundarycallbacks.onUncaughtError\n};\nfunction hydrate(instrumentationHooks) {\n    // React overrides `.then` and doesn't return a new promise chain,\n    // so we wrap the action queue in a promise to ensure that its value\n    // is defined when the promise resolves.\n    // https://github.com/facebook/react/blob/163365a07872337e04826c4f501565d43dbd2fd4/packages/react-client/src/ReactFlightClient.js#L189-L190\n    const pendingActionQueue = new Promise((resolve, reject)=>{\n        initialServerResponse.then((initialRSCPayload)=>{\n            // setAppBuildId should be called only once, during JS initialization\n            // and before any components have hydrated.\n            (0, _appbuildid.setAppBuildId)(initialRSCPayload.b);\n            const initialTimestamp = Date.now();\n            resolve((0, _approuterinstance.createMutableActionQueue)((0, _createinitialrouterstate.createInitialRouterState)({\n                navigatedAt: initialTimestamp,\n                initialFlightData: initialRSCPayload.f,\n                initialCanonicalUrlParts: initialRSCPayload.c,\n                initialParallelRoutes: new Map(),\n                location: window.location,\n                couldBeIntercepted: initialRSCPayload.i,\n                postponed: initialRSCPayload.s,\n                prerendered: initialRSCPayload.S\n            }), instrumentationHooks));\n        }, (err)=>reject(err));\n    });\n    const reactEl = /*#__PURE__*/ (0, _jsxruntime.jsx)(StrictModeIfEnabled, {\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_headmanagercontextsharedruntime.HeadManagerContext.Provider, {\n            value: {\n                appDir: true\n            },\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Root, {\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(ServerRoot, {\n                    pendingActionQueue: pendingActionQueue\n                })\n            })\n        })\n    });\n    if (document.documentElement.id === '__next_error__') {\n        let element = reactEl;\n        // Server rendering failed, fall back to client-side rendering\n        if (true) {\n            const { createRootLevelDevOverlayElement } = __webpack_require__(/*! ../next-devtools/userspace/app/client-entry */ \"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/client-entry.js\");\n            // Note this won't cause hydration mismatch because we are doing CSR w/o hydration\n            element = createRootLevelDevOverlayElement(element);\n        }\n        _client.default.createRoot(appElement, reactRootOptions).render(element);\n    } else {\n        _react.default.startTransition(()=>{\n            _client.default.hydrateRoot(appElement, reactEl, {\n                ...reactRootOptions,\n                formState: initialFormStateData\n            });\n        });\n    }\n    // TODO-APP: Remove this logic when Float has GC built-in in development.\n    if (true) {\n        const { linkGc } = __webpack_require__(/*! ./app-link-gc */ \"(app-pages-browser)/./node_modules/next/dist/client/app-link-gc.js\");\n        linkGc();\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-index.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"DOMContentLoaded\");\n$RefreshReg$(_c1, \"ServerRoot\");\n$RefreshReg$(_c2, \"Root\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router-instance.js ***!
  \*************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createMutableActionQueue: function() {\n        return createMutableActionQueue;\n    },\n    dispatchNavigateAction: function() {\n        return dispatchNavigateAction;\n    },\n    dispatchTraverseAction: function() {\n        return dispatchTraverseAction;\n    },\n    getCurrentAppRouterState: function() {\n        return getCurrentAppRouterState;\n    },\n    publicAppRouterInstance: function() {\n        return publicAppRouterInstance;\n    }\n});\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _routerreducer = __webpack_require__(/*! ./router-reducer/router-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _isthenable = __webpack_require__(/*! ../../shared/lib/is-thenable */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/is-thenable.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\");\nconst _useactionqueue = __webpack_require__(/*! ./use-action-queue */ \"(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _approuter = __webpack_require__(/*! ./app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./router-reducer/reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst _links = __webpack_require__(/*! ./links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nfunction runRemainingActions(actionQueue, setState) {\n    if (actionQueue.pending !== null) {\n        actionQueue.pending = actionQueue.pending.next;\n        if (actionQueue.pending !== null) {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            runAction({\n                actionQueue,\n                action: actionQueue.pending,\n                setState\n            });\n        } else {\n            // No more actions are pending, check if a refresh is needed\n            if (actionQueue.needsRefresh) {\n                actionQueue.needsRefresh = false;\n                actionQueue.dispatch({\n                    type: _routerreducertypes.ACTION_REFRESH,\n                    origin: window.location.origin\n                }, setState);\n            }\n        }\n    }\n}\nasync function runAction(param) {\n    let { actionQueue, action, setState } = param;\n    const prevState = actionQueue.state;\n    actionQueue.pending = action;\n    const payload = action.payload;\n    const actionResult = actionQueue.action(prevState, payload);\n    function handleResult(nextState) {\n        // if we discarded this action, the state should also be discarded\n        if (action.discarded) {\n            return;\n        }\n        actionQueue.state = nextState;\n        runRemainingActions(actionQueue, setState);\n        action.resolve(nextState);\n    }\n    // if the action is a promise, set up a callback to resolve it\n    if ((0, _isthenable.isThenable)(actionResult)) {\n        actionResult.then(handleResult, (err)=>{\n            runRemainingActions(actionQueue, setState);\n            action.reject(err);\n        });\n    } else {\n        handleResult(actionResult);\n    }\n}\nfunction dispatchAction(actionQueue, payload, setState) {\n    let resolvers = {\n        resolve: setState,\n        reject: ()=>{}\n    };\n    // most of the action types are async with the exception of restore\n    // it's important that restore is handled quickly since it's fired on the popstate event\n    // and we don't want to add any delay on a back/forward nav\n    // this only creates a promise for the async actions\n    if (payload.type !== _routerreducertypes.ACTION_RESTORE) {\n        // Create the promise and assign the resolvers to the object.\n        const deferredPromise = new Promise((resolve, reject)=>{\n            resolvers = {\n                resolve,\n                reject\n            };\n        });\n        (0, _react.startTransition)(()=>{\n            // we immediately notify React of the pending promise -- the resolver is attached to the action node\n            // and will be called when the associated action promise resolves\n            setState(deferredPromise);\n        });\n    }\n    const newAction = {\n        payload,\n        next: null,\n        resolve: resolvers.resolve,\n        reject: resolvers.reject\n    };\n    // Check if the queue is empty\n    if (actionQueue.pending === null) {\n        // The queue is empty, so add the action and start it immediately\n        // Mark this action as the last in the queue\n        actionQueue.last = newAction;\n        runAction({\n            actionQueue,\n            action: newAction,\n            setState\n        });\n    } else if (payload.type === _routerreducertypes.ACTION_NAVIGATE || payload.type === _routerreducertypes.ACTION_RESTORE) {\n        // Navigations (including back/forward) take priority over any pending actions.\n        // Mark the pending action as discarded (so the state is never applied) and start the navigation action immediately.\n        actionQueue.pending.discarded = true;\n        // The rest of the current queue should still execute after this navigation.\n        // (Note that it can't contain any earlier navigations, because we always put those into `actionQueue.pending` by calling `runAction`)\n        newAction.next = actionQueue.pending.next;\n        // if the pending action was a server action, mark the queue as needing a refresh once events are processed\n        if (actionQueue.pending.payload.type === _routerreducertypes.ACTION_SERVER_ACTION) {\n            actionQueue.needsRefresh = true;\n        }\n        runAction({\n            actionQueue,\n            action: newAction,\n            setState\n        });\n    } else {\n        // The queue is not empty, so add the action to the end of the queue\n        // It will be started by runRemainingActions after the previous action finishes\n        if (actionQueue.last !== null) {\n            actionQueue.last.next = newAction;\n        }\n        actionQueue.last = newAction;\n    }\n}\nlet globalActionQueue = null;\nfunction createMutableActionQueue(initialState, instrumentationHooks) {\n    const actionQueue = {\n        state: initialState,\n        dispatch: (payload, setState)=>dispatchAction(actionQueue, payload, setState),\n        action: async (state, action)=>{\n            const result = (0, _routerreducer.reducer)(state, action);\n            return result;\n        },\n        pending: null,\n        last: null,\n        onRouterTransitionStart: instrumentationHooks !== null && typeof instrumentationHooks.onRouterTransitionStart === 'function' ? instrumentationHooks.onRouterTransitionStart : null\n    };\n    if (true) {\n        // The action queue is lazily created on hydration, but after that point\n        // it doesn't change. So we can store it in a global rather than pass\n        // it around everywhere via props/context.\n        if (globalActionQueue !== null) {\n            throw Object.defineProperty(new Error('Internal Next.js Error: createMutableActionQueue was called more ' + 'than once'), \"__NEXT_ERROR_CODE\", {\n                value: \"E624\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        globalActionQueue = actionQueue;\n    }\n    return actionQueue;\n}\nfunction getCurrentAppRouterState() {\n    return globalActionQueue !== null ? globalActionQueue.state : null;\n}\nfunction getAppRouterActionQueue() {\n    if (globalActionQueue === null) {\n        throw Object.defineProperty(new Error('Internal Next.js error: Router action dispatched before initialization.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E668\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    return globalActionQueue;\n}\nfunction getProfilingHookForOnNavigationStart() {\n    if (globalActionQueue !== null) {\n        return globalActionQueue.onRouterTransitionStart;\n    }\n    return null;\n}\nfunction dispatchNavigateAction(href, navigateType, shouldScroll, linkInstanceRef) {\n    // TODO: This stuff could just go into the reducer. Leaving as-is for now\n    // since we're about to rewrite all the router reducer stuff anyway.\n    const url = new URL((0, _addbasepath.addBasePath)(href), location.href);\n    if (false) {}\n    (0, _links.setLinkForCurrentNavigation)(linkInstanceRef);\n    const onRouterTransitionStart = getProfilingHookForOnNavigationStart();\n    if (onRouterTransitionStart !== null) {\n        onRouterTransitionStart(href, navigateType);\n    }\n    (0, _useactionqueue.dispatchAppRouterAction)({\n        type: _routerreducertypes.ACTION_NAVIGATE,\n        url,\n        isExternalUrl: (0, _approuter.isExternalURL)(url),\n        locationSearch: location.search,\n        shouldScroll,\n        navigateType,\n        allowAliasing: true\n    });\n}\nfunction dispatchTraverseAction(href, tree) {\n    const onRouterTransitionStart = getProfilingHookForOnNavigationStart();\n    if (onRouterTransitionStart !== null) {\n        onRouterTransitionStart(href, 'traverse');\n    }\n    (0, _useactionqueue.dispatchAppRouterAction)({\n        type: _routerreducertypes.ACTION_RESTORE,\n        url: new URL(href),\n        tree\n    });\n}\nconst publicAppRouterInstance = {\n    back: ()=>window.history.back(),\n    forward: ()=>window.history.forward(),\n    prefetch:  false ? // cache. So we don't need to dispatch an action.\n    0 : (href, options)=>{\n        // Use the old prefetch implementation.\n        const actionQueue = getAppRouterActionQueue();\n        const url = (0, _approuter.createPrefetchURL)(href);\n        if (url !== null) {\n            var _options_kind;\n            // The prefetch reducer doesn't actually update any state or\n            // trigger a rerender. It just writes to a mutable cache. So we\n            // shouldn't bother calling setState/dispatch; we can just re-run\n            // the reducer directly using the current state.\n            // TODO: Refactor this away from a \"reducer\" so it's\n            // less confusing.\n            (0, _prefetchreducer.prefetchReducer)(actionQueue.state, {\n                type: _routerreducertypes.ACTION_PREFETCH,\n                url,\n                kind: (_options_kind = options == null ? void 0 : options.kind) != null ? _options_kind : _routerreducertypes.PrefetchKind.FULL\n            });\n        }\n    },\n    replace: (href, options)=>{\n        (0, _react.startTransition)(()=>{\n            var _options_scroll;\n            dispatchNavigateAction(href, 'replace', (_options_scroll = options == null ? void 0 : options.scroll) != null ? _options_scroll : true, null);\n        });\n    },\n    push: (href, options)=>{\n        (0, _react.startTransition)(()=>{\n            var _options_scroll;\n            dispatchNavigateAction(href, 'push', (_options_scroll = options == null ? void 0 : options.scroll) != null ? _options_scroll : true, null);\n        });\n    },\n    refresh: ()=>{\n        (0, _react.startTransition)(()=>{\n            (0, _useactionqueue.dispatchAppRouterAction)({\n                type: _routerreducertypes.ACTION_REFRESH,\n                origin: window.location.origin\n            });\n        });\n    },\n    hmrRefresh: ()=>{\n        if (false) {} else {\n            (0, _react.startTransition)(()=>{\n                (0, _useactionqueue.dispatchAppRouterAction)({\n                    type: _routerreducertypes.ACTION_HMR_REFRESH,\n                    origin: window.location.origin\n                });\n            });\n        }\n    }\n};\n// Exists for debugging purposes. Don't use in application code.\nif ( true && window.next) {\n    window.next.router = publicAppRouterInstance;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router-instance.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router.js ***!
  \****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createEmptyCacheNode: function() {\n        return createEmptyCacheNode;\n    },\n    createPrefetchURL: function() {\n        return createPrefetchURL;\n    },\n    default: function() {\n        return AppRouter;\n    },\n    isExternalURL: function() {\n        return isExternalURL;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _createhreffromurl = __webpack_require__(/*! ./router-reducer/create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _hooksclientcontextsharedruntime = __webpack_require__(/*! ../../shared/lib/hooks-client-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\");\nconst _useactionqueue = __webpack_require__(/*! ./use-action-queue */ \"(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js\");\nconst _errorboundary = __webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\");\nconst _globalerror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./builtin/global-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js\"));\nconst _isbot = __webpack_require__(/*! ../../shared/lib/router/utils/is-bot */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _approuterannouncer = __webpack_require__(/*! ./app-router-announcer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-announcer.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _findheadincache = __webpack_require__(/*! ./router-reducer/reducers/find-head-in-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/find-head-in-cache.js\");\nconst _unresolvedthenable = __webpack_require__(/*! ./unresolved-thenable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/unresolved-thenable.js\");\nconst _removebasepath = __webpack_require__(/*! ../remove-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/remove-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../has-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\");\nconst _computechangedpath = __webpack_require__(/*! ./router-reducer/compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _navfailurehandler = __webpack_require__(/*! ./nav-failure-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/nav-failure-handler.js\");\nconst _approuterinstance = __webpack_require__(/*! ./app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _redirect = __webpack_require__(/*! ./redirect */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect.js\");\nconst _redirecterror = __webpack_require__(/*! ./redirect-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-error.js\");\nconst _links = __webpack_require__(/*! ./links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nconst _gracefuldegradeboundary = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./errors/graceful-degrade-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/graceful-degrade-boundary.js\"));\nconst globalMutable = {};\nfunction isExternalURL(url) {\n    return url.origin !== window.location.origin;\n}\nfunction createPrefetchURL(href) {\n    // Don't prefetch for bots as they don't navigate.\n    if ((0, _isbot.isBot)(window.navigator.userAgent)) {\n        return null;\n    }\n    let url;\n    try {\n        url = new URL((0, _addbasepath.addBasePath)(href), window.location.href);\n    } catch (_) {\n        // TODO: Does this need to throw or can we just console.error instead? Does\n        // anyone rely on this throwing? (Seems unlikely.)\n        throw Object.defineProperty(new Error(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\"), \"__NEXT_ERROR_CODE\", {\n            value: \"E234\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    // Don't prefetch during development (improves compilation performance)\n    if (true) {\n        return null;\n    }\n    // External urls can't be prefetched in the same way.\n    if (isExternalURL(url)) {\n        return null;\n    }\n    return url;\n}\nfunction HistoryUpdater(param) {\n    let { appRouterState } = param;\n    (0, _react.useInsertionEffect)(()=>{\n        if (false) {}\n        const { tree, pushRef, canonicalUrl } = appRouterState;\n        const historyState = {\n            ...pushRef.preserveCustomHistoryState ? window.history.state : {},\n            // Identifier is shortened intentionally.\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            // __N is used to identify if the history entry can be handled by the old router.\n            __NA: true,\n            __PRIVATE_NEXTJS_INTERNALS_TREE: tree\n        };\n        if (pushRef.pendingPush && // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n        // This mirrors the browser behavior for normal navigation.\n        (0, _createhreffromurl.createHrefFromUrl)(new URL(window.location.href)) !== canonicalUrl) {\n            // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n            pushRef.pendingPush = false;\n            window.history.pushState(historyState, '', canonicalUrl);\n        } else {\n            window.history.replaceState(historyState, '', canonicalUrl);\n        }\n    }, [\n        appRouterState\n    ]);\n    (0, _react.useEffect)(()=>{\n        // The Next-Url and the base tree may affect the result of a prefetch\n        // task. Re-prefetch all visible links with the updated values. In most\n        // cases, this will not result in any new network requests, only if\n        // the prefetch result actually varies on one of these inputs.\n        if (false) {}\n    }, [\n        appRouterState.nextUrl,\n        appRouterState.tree\n    ]);\n    return null;\n}\n_c = HistoryUpdater;\nfunction createEmptyCacheNode() {\n    return {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1\n    };\n}\nfunction copyNextJsInternalHistoryState(data) {\n    if (data == null) data = {};\n    const currentState = window.history.state;\n    const __NA = currentState == null ? void 0 : currentState.__NA;\n    if (__NA) {\n        data.__NA = __NA;\n    }\n    const __PRIVATE_NEXTJS_INTERNALS_TREE = currentState == null ? void 0 : currentState.__PRIVATE_NEXTJS_INTERNALS_TREE;\n    if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n        data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE;\n    }\n    return data;\n}\nfunction Head(param) {\n    let { headCacheNode } = param;\n    // If this segment has a `prefetchHead`, it's the statically prefetched data.\n    // We should use that on initial render instead of `head`. Then we'll switch\n    // to `head` when the dynamic response streams in.\n    const head = headCacheNode !== null ? headCacheNode.head : null;\n    const prefetchHead = headCacheNode !== null ? headCacheNode.prefetchHead : null;\n    // If no prefetch data is available, then we go straight to rendering `head`.\n    const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head;\n    // We use `useDeferredValue` to handle switching between the prefetched and\n    // final values. The second argument is returned on initial render, then it\n    // re-renders with the first argument.\n    return (0, _react.useDeferredValue)(head, resolvedPrefetchRsc);\n}\n_c1 = Head;\n/**\n * The global router that wraps the application components.\n */ function Router(param) {\n    let { actionQueue, assetPrefix, globalError, gracefullyDegrade } = param;\n    const state = (0, _useactionqueue.useActionQueue)(actionQueue);\n    const { canonicalUrl } = state;\n    // Add memoized pathname/query for useSearchParams and usePathname.\n    const { searchParams, pathname } = (0, _react.useMemo)(()=>{\n        const url = new URL(canonicalUrl,  false ? 0 : window.location.href);\n        return {\n            // This is turned into a readonly class in `useSearchParams`\n            searchParams: url.searchParams,\n            pathname: (0, _hasbasepath.hasBasePath)(url.pathname) ? (0, _removebasepath.removeBasePath)(url.pathname) : url.pathname\n        };\n    }, [\n        canonicalUrl\n    ]);\n    if (true) {\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const { cache, prefetchCache, tree } = state;\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            // Add `window.nd` for debugging purposes.\n            // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n            // @ts-ignore this is for debugging\n            window.nd = {\n                router: _approuterinstance.publicAppRouterInstance,\n                cache,\n                prefetchCache,\n                tree\n            };\n        }, [\n            cache,\n            prefetchCache,\n            tree\n        ]);\n    }\n    (0, _react.useEffect)(()=>{\n        // If the app is restored from bfcache, it's possible that\n        // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n        // would trigger the mpa navigation logic again from the lines below.\n        // This will restore the router to the initial state in the event that the app is restored from bfcache.\n        function handlePageShow(event) {\n            var _window_history_state;\n            if (!event.persisted || !((_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE)) {\n                return;\n            }\n            // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n            // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n            // of the last MPA navigation.\n            globalMutable.pendingMpaPath = undefined;\n            (0, _useactionqueue.dispatchAppRouterAction)({\n                type: _routerreducertypes.ACTION_RESTORE,\n                url: new URL(window.location.href),\n                tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n            });\n        }\n        window.addEventListener('pageshow', handlePageShow);\n        return ()=>{\n            window.removeEventListener('pageshow', handlePageShow);\n        };\n    }, []);\n    (0, _react.useEffect)(()=>{\n        // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n        // are caught and handled by the router.\n        function handleUnhandledRedirect(event) {\n            const error = 'reason' in event ? event.reason : event.error;\n            if ((0, _redirecterror.isRedirectError)(error)) {\n                event.preventDefault();\n                const url = (0, _redirect.getURLFromRedirectError)(error);\n                const redirectType = (0, _redirect.getRedirectTypeFromError)(error);\n                // TODO: This should access the router methods directly, rather than\n                // go through the public interface.\n                if (redirectType === _redirecterror.RedirectType.push) {\n                    _approuterinstance.publicAppRouterInstance.push(url, {});\n                } else {\n                    _approuterinstance.publicAppRouterInstance.replace(url, {});\n                }\n            }\n        }\n        window.addEventListener('error', handleUnhandledRedirect);\n        window.addEventListener('unhandledrejection', handleUnhandledRedirect);\n        return ()=>{\n            window.removeEventListener('error', handleUnhandledRedirect);\n            window.removeEventListener('unhandledrejection', handleUnhandledRedirect);\n        };\n    }, []);\n    // When mpaNavigation flag is set do a hard navigation to the new url.\n    // Infinitely suspend because we don't actually want to rerender any child\n    // components with the new URL and any entangled state updates shouldn't\n    // commit either (eg: useTransition isPending should stay true until the page\n    // unloads).\n    //\n    // This is a side effect in render. Don't try this at home, kids. It's\n    // probably safe because we know this is a singleton component and it's never\n    // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n    // but that's... fine?)\n    const { pushRef } = state;\n    if (pushRef.mpaNavigation) {\n        // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n        if (globalMutable.pendingMpaPath !== canonicalUrl) {\n            const location = window.location;\n            if (pushRef.pendingPush) {\n                location.assign(canonicalUrl);\n            } else {\n                location.replace(canonicalUrl);\n            }\n            globalMutable.pendingMpaPath = canonicalUrl;\n        }\n        // TODO-APP: Should we listen to navigateerror here to catch failed\n        // navigations somehow? And should we call window.stop() if a SPA navigation\n        // should interrupt an MPA one?\n        // NOTE: This is intentionally using `throw` instead of `use` because we're\n        // inside an externally mutable condition (pushRef.mpaNavigation), which\n        // violates the rules of hooks.\n        throw _unresolvedthenable.unresolvedThenable;\n    }\n    (0, _react.useEffect)(()=>{\n        const originalPushState = window.history.pushState.bind(window.history);\n        const originalReplaceState = window.history.replaceState.bind(window.history);\n        // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n        const applyUrlFromHistoryPushReplace = (url)=>{\n            var _window_history_state;\n            const href = window.location.href;\n            const tree = (_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE;\n            (0, _react.startTransition)(()=>{\n                (0, _useactionqueue.dispatchAppRouterAction)({\n                    type: _routerreducertypes.ACTION_RESTORE,\n                    url: new URL(url != null ? url : href, href),\n                    tree\n                });\n            });\n        };\n        /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.pushState = function pushState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalPushState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalPushState(data, _unused, url);\n        };\n        /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.replaceState = function replaceState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalReplaceState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalReplaceState(data, _unused, url);\n        };\n        /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */ const onPopState = (event)=>{\n            if (!event.state) {\n                // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n                return;\n            }\n            // This case happens when the history entry was pushed by the `pages` router.\n            if (!event.state.__NA) {\n                window.location.reload();\n                return;\n            }\n            // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n            // Without startTransition works if the cache is there for this path\n            (0, _react.startTransition)(()=>{\n                (0, _approuterinstance.dispatchTraverseAction)(window.location.href, event.state.__PRIVATE_NEXTJS_INTERNALS_TREE);\n            });\n        };\n        // Register popstate event to call onPopstate.\n        window.addEventListener('popstate', onPopState);\n        return ()=>{\n            window.history.pushState = originalPushState;\n            window.history.replaceState = originalReplaceState;\n            window.removeEventListener('popstate', onPopState);\n        };\n    }, []);\n    const { cache, tree, nextUrl, focusAndScrollRef } = state;\n    const matchingHead = (0, _react.useMemo)(()=>{\n        return (0, _findheadincache.findHeadInCache)(cache, tree[1]);\n    }, [\n        cache,\n        tree\n    ]);\n    // Add memoized pathParams for useParams.\n    const pathParams = (0, _react.useMemo)(()=>{\n        return (0, _computechangedpath.getSelectedParams)(tree);\n    }, [\n        tree\n    ]);\n    const layoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            parentTree: tree,\n            parentCacheNode: cache,\n            parentSegmentPath: null,\n            // Root node always has `url`\n            // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n            url: canonicalUrl\n        };\n    }, [\n        tree,\n        cache,\n        canonicalUrl\n    ]);\n    const globalLayoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            tree,\n            focusAndScrollRef,\n            nextUrl\n        };\n    }, [\n        tree,\n        focusAndScrollRef,\n        nextUrl\n    ]);\n    let head;\n    if (matchingHead !== null) {\n        // The head is wrapped in an extra component so we can use\n        // `useDeferredValue` to swap between the prefetched and final versions of\n        // the head. (This is what LayoutRouter does for segment data, too.)\n        //\n        // The `key` is used to remount the component whenever the head moves to\n        // a different segment.\n        const [headCacheNode, headKey] = matchingHead;\n        head = /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {\n            headCacheNode: headCacheNode\n        }, headKey);\n    } else {\n        head = null;\n    }\n    let content = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_redirectboundary.RedirectBoundary, {\n        children: [\n            head,\n            cache.rsc,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_approuterannouncer.AppRouterAnnouncer, {\n                tree: tree\n            })\n        ]\n    });\n    if (true) {\n        // In development, we apply few error boundaries and hot-reloader:\n        // - DevRootHTTPAccessFallbackBoundary: avoid using navigation API like notFound() in root layout\n        // - HotReloader:\n        //  - hot-reload the app when the code changes\n        //  - render dev overlay\n        //  - catch runtime errors and display global-error when necessary\n        if (true) {\n            const { DevRootHTTPAccessFallbackBoundary } = __webpack_require__(/*! ./dev-root-http-access-fallback-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js\");\n            content = /*#__PURE__*/ (0, _jsxruntime.jsx)(DevRootHTTPAccessFallbackBoundary, {\n                children: content\n            });\n        }\n        const HotReloader = (__webpack_require__(/*! ../dev/hot-reloader/app/hot-reloader-app */ \"(app-pages-browser)/./node_modules/next/dist/client/dev/hot-reloader/app/hot-reloader-app.js\")[\"default\"]);\n        content = /*#__PURE__*/ (0, _jsxruntime.jsx)(HotReloader, {\n            assetPrefix: assetPrefix,\n            globalError: globalError,\n            children: content\n        });\n    } else {}\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(HistoryUpdater, {\n                appRouterState: state\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(RuntimeStyles, {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathParamsContext.Provider, {\n                value: pathParams,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathnameContext.Provider, {\n                    value: pathname,\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.SearchParamsContext.Provider, {\n                        value: searchParams,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.GlobalLayoutRouterContext.Provider, {\n                            value: globalLayoutRouterContext,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.AppRouterContext.Provider, {\n                                value: _approuterinstance.publicAppRouterInstance,\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n                                    value: layoutRouterContext,\n                                    children: content\n                                })\n                            })\n                        })\n                    })\n                })\n            })\n        ]\n    });\n}\n_c2 = Router;\nfunction AppRouter(param) {\n    let { actionQueue, globalErrorState, assetPrefix, gracefullyDegrade } = param;\n    (0, _navfailurehandler.useNavFailureHandler)();\n    const router = /*#__PURE__*/ (0, _jsxruntime.jsx)(Router, {\n        actionQueue: actionQueue,\n        assetPrefix: assetPrefix,\n        globalError: globalErrorState,\n        gracefullyDegrade: gracefullyDegrade\n    });\n    if (gracefullyDegrade) {\n        return router;\n    } else {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary.ErrorBoundary, {\n            // At the very top level, use the default GlobalError component as the final fallback.\n            // When the app router itself fails, which means the framework itself fails, we show the default error.\n            errorComponent: _globalerror.default,\n            children: router\n        });\n    }\n}\n_c3 = AppRouter;\nconst runtimeStyles = new Set();\nlet runtimeStyleChanged = new Set();\nglobalThis._N_E_STYLE_LOAD = function(href) {\n    let len = runtimeStyles.size;\n    runtimeStyles.add(href);\n    if (runtimeStyles.size !== len) {\n        runtimeStyleChanged.forEach((cb)=>cb());\n    }\n    // TODO figure out how to get a promise here\n    // But maybe it's not necessary as react would block rendering until it's loaded\n    return Promise.resolve();\n};\nfunction RuntimeStyles() {\n    _s();\n    const [, forceUpdate] = _react.default.useState(0);\n    const renderedStylesSize = runtimeStyles.size;\n    (0, _react.useEffect)(()=>{\n        const changed = ()=>forceUpdate((c)=>c + 1);\n        runtimeStyleChanged.add(changed);\n        if (renderedStylesSize !== runtimeStyles.size) {\n            changed();\n        }\n        return ()=>{\n            runtimeStyleChanged.delete(changed);\n        };\n    }, [\n        renderedStylesSize,\n        forceUpdate\n    ]);\n    const dplId =  false ? 0 : '';\n    return [\n        ...runtimeStyles\n    ].map((href, i)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n            rel: \"stylesheet\",\n            href: \"\" + href + dplId,\n            // @ts-ignore\n            precedence: \"next\"\n        }, i));\n}\n_s(RuntimeStyles, \"Eht7Kgdrrgt5B4LSklQ7qDPo8Aw=\");\n_c4 = RuntimeStyles;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router.js.map\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HistoryUpdater\");\n$RefreshReg$(_c1, \"Head\");\n$RefreshReg$(_c2, \"Router\");\n$RefreshReg$(_c3, \"AppRouter\");\n$RefreshReg$(_c4, \"RuntimeStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"HTTPAccessFallbackBoundary\", ({\n    enumerable: true,\n    get: function() {\n        return HTTPAccessFallbackBoundary;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _navigationuntracked = __webpack_require__(/*! ../navigation-untracked */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation-untracked.js\");\nconst _httpaccessfallback = __webpack_require__(/*! ./http-access-fallback */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/http-access-fallback.js\");\nconst _warnonce = __webpack_require__(/*! ../../../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nclass HTTPAccessFallbackErrorBoundary extends _react.default.Component {\n    componentDidCatch() {\n        if ( true && this.props.missingSlots && this.props.missingSlots.size > 0 && // A missing children slot is the typical not-found case, so no need to warn\n        !this.props.missingSlots.has('children')) {\n            let warningMessage = 'No default component was found for a parallel route rendered on this page. Falling back to nearest NotFound boundary.\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/routing/parallel-routes#defaultjs\\n\\n';\n            const formattedSlots = Array.from(this.props.missingSlots).sort((a, b)=>a.localeCompare(b)).map((slot)=>\"@\" + slot).join(', ');\n            warningMessage += 'Missing slots: ' + formattedSlots;\n            (0, _warnonce.warnOnce)(warningMessage);\n        }\n    }\n    static getDerivedStateFromError(error) {\n        if ((0, _httpaccessfallback.isHTTPAccessFallbackError)(error)) {\n            const httpStatus = (0, _httpaccessfallback.getAccessFallbackHTTPStatus)(error);\n            return {\n                triggeredStatus: httpStatus\n            };\n        }\n        // Re-throw if error is not for 404\n        throw error;\n    }\n    static getDerivedStateFromProps(props, state) {\n        /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */ if (props.pathname !== state.previousPathname && state.triggeredStatus) {\n            return {\n                triggeredStatus: undefined,\n                previousPathname: props.pathname\n            };\n        }\n        return {\n            triggeredStatus: state.triggeredStatus,\n            previousPathname: props.pathname\n        };\n    }\n    render() {\n        const { notFound, forbidden, unauthorized, children } = this.props;\n        const { triggeredStatus } = this.state;\n        const errorComponents = {\n            [_httpaccessfallback.HTTPAccessErrorStatus.NOT_FOUND]: notFound,\n            [_httpaccessfallback.HTTPAccessErrorStatus.FORBIDDEN]: forbidden,\n            [_httpaccessfallback.HTTPAccessErrorStatus.UNAUTHORIZED]: unauthorized\n        };\n        if (triggeredStatus) {\n            const isNotFound = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.NOT_FOUND && notFound;\n            const isForbidden = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.FORBIDDEN && forbidden;\n            const isUnauthorized = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.UNAUTHORIZED && unauthorized;\n            // If there's no matched boundary in this layer, keep throwing the error by rendering the children\n            if (!(isNotFound || isForbidden || isUnauthorized)) {\n                return children;\n            }\n            return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                        name: \"robots\",\n                        content: \"noindex\"\n                    }),\n                     true && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                        name: \"boundary-next-error\",\n                        content: (0, _httpaccessfallback.getAccessFallbackErrorTypeByStatus)(triggeredStatus)\n                    }),\n                    errorComponents[triggeredStatus]\n                ]\n            });\n        }\n        return children;\n    }\n    constructor(props){\n        super(props);\n        this.state = {\n            triggeredStatus: undefined,\n            previousPathname: props.pathname\n        };\n    }\n}\nfunction HTTPAccessFallbackBoundary(param) {\n    let { notFound, forbidden, unauthorized, children } = param;\n    // When we're rendering the missing params shell, this will return null. This\n    // is because we won't be rendering any not found boundaries or error\n    // boundaries for the missing params shell. When this runs on the client\n    // (where these error can occur), we will get the correct pathname.\n    const pathname = (0, _navigationuntracked.useUntrackedPathname)();\n    const missingSlots = (0, _react.useContext)(_approutercontextsharedruntime.MissingSlotContext);\n    const hasErrorFallback = !!(notFound || forbidden || unauthorized);\n    if (hasErrorFallback) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(HTTPAccessFallbackErrorBoundary, {\n            pathname: pathname,\n            notFound: notFound,\n            forbidden: forbidden,\n            unauthorized: unauthorized,\n            missingSlots: missingSlots,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: children\n    });\n}\n_c = HTTPAccessFallbackBoundary;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-boundary.js.map\nvar _c;\n$RefreshReg$(_c, \"HTTPAccessFallbackBoundary\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/links.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/components/links.js ***!
  \***********************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    IDLE_LINK_STATUS: function() {\n        return IDLE_LINK_STATUS;\n    },\n    PENDING_LINK_STATUS: function() {\n        return PENDING_LINK_STATUS;\n    },\n    mountFormInstance: function() {\n        return mountFormInstance;\n    },\n    mountLinkInstance: function() {\n        return mountLinkInstance;\n    },\n    onLinkVisibilityChanged: function() {\n        return onLinkVisibilityChanged;\n    },\n    onNavigationIntent: function() {\n        return onNavigationIntent;\n    },\n    pingVisibleLinks: function() {\n        return pingVisibleLinks;\n    },\n    setLinkForCurrentNavigation: function() {\n        return setLinkForCurrentNavigation;\n    },\n    unmountLinkForCurrentNavigation: function() {\n        return unmountLinkForCurrentNavigation;\n    },\n    unmountPrefetchableInstance: function() {\n        return unmountPrefetchableInstance;\n    }\n});\nconst _approuterinstance = __webpack_require__(/*! ./app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _approuter = __webpack_require__(/*! ./app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n// Tracks the most recently navigated link instance. When null, indicates\n// the current navigation was not initiated by a link click.\nlet linkForMostRecentNavigation = null;\nconst PENDING_LINK_STATUS = {\n    pending: true\n};\nconst IDLE_LINK_STATUS = {\n    pending: false\n};\nfunction setLinkForCurrentNavigation(link) {\n    (0, _react.startTransition)(()=>{\n        linkForMostRecentNavigation == null ? void 0 : linkForMostRecentNavigation.setOptimisticLinkStatus(IDLE_LINK_STATUS);\n        link == null ? void 0 : link.setOptimisticLinkStatus(PENDING_LINK_STATUS);\n        linkForMostRecentNavigation = link;\n    });\n}\nfunction unmountLinkForCurrentNavigation(link) {\n    if (linkForMostRecentNavigation === link) {\n        linkForMostRecentNavigation = null;\n    }\n}\n// Use a WeakMap to associate a Link instance with its DOM element. This is\n// used by the IntersectionObserver to track the link's visibility.\nconst prefetchable = typeof WeakMap === 'function' ? new WeakMap() : new Map();\n// A Set of the currently visible links. We re-prefetch visible links after a\n// cache invalidation, or when the current URL changes. It's a separate data\n// structure from the WeakMap above because only the visible links need to\n// be enumerated.\nconst prefetchableAndVisible = new Set();\n// A single IntersectionObserver instance shared by all <Link> components.\nconst observer = typeof IntersectionObserver === 'function' ? new IntersectionObserver(handleIntersect, {\n    rootMargin: '200px'\n}) : null;\nfunction observeVisibility(element, instance) {\n    const existingInstance = prefetchable.get(element);\n    if (existingInstance !== undefined) {\n        // This shouldn't happen because each <Link> component should have its own\n        // anchor tag instance, but it's defensive coding to avoid a memory leak in\n        // case there's a logical error somewhere else.\n        unmountPrefetchableInstance(element);\n    }\n    // Only track prefetchable links that have a valid prefetch URL\n    prefetchable.set(element, instance);\n    if (observer !== null) {\n        observer.observe(element);\n    }\n}\nfunction coercePrefetchableUrl(href) {\n    try {\n        return (0, _approuter.createPrefetchURL)(href);\n    } catch (e) {\n        // createPrefetchURL sometimes throws an error if an invalid URL is\n        // provided, though I'm not sure if it's actually necessary.\n        // TODO: Consider removing the throw from the inner function, or change it\n        // to reportError. Or maybe the error isn't even necessary for automatic\n        // prefetches, just navigations.\n        const reportErrorFn = typeof reportError === 'function' ? reportError : console.error;\n        reportErrorFn(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\");\n        return null;\n    }\n}\nfunction mountLinkInstance(element, href, router, kind, prefetchEnabled, setOptimisticLinkStatus) {\n    if (prefetchEnabled) {\n        const prefetchURL = coercePrefetchableUrl(href);\n        if (prefetchURL !== null) {\n            const instance = {\n                router,\n                kind,\n                isVisible: false,\n                prefetchTask: null,\n                prefetchHref: prefetchURL.href,\n                setOptimisticLinkStatus\n            };\n            // We only observe the link's visibility if it's prefetchable. For\n            // example, this excludes links to external URLs.\n            observeVisibility(element, instance);\n            return instance;\n        }\n    }\n    // If the link is not prefetchable, we still create an instance so we can\n    // track its optimistic state (i.e. useLinkStatus).\n    const instance = {\n        router,\n        kind,\n        isVisible: false,\n        prefetchTask: null,\n        prefetchHref: null,\n        setOptimisticLinkStatus\n    };\n    return instance;\n}\nfunction mountFormInstance(element, href, router, kind) {\n    const prefetchURL = coercePrefetchableUrl(href);\n    if (prefetchURL === null) {\n        // This href is not prefetchable, so we don't track it.\n        // TODO: We currently observe/unobserve a form every time its href changes.\n        // For Links, this isn't a big deal because the href doesn't usually change,\n        // but for forms it's extremely common. We should optimize this.\n        return;\n    }\n    const instance = {\n        router,\n        kind,\n        isVisible: false,\n        prefetchTask: null,\n        prefetchHref: prefetchURL.href,\n        setOptimisticLinkStatus: null\n    };\n    observeVisibility(element, instance);\n}\nfunction unmountPrefetchableInstance(element) {\n    const instance = prefetchable.get(element);\n    if (instance !== undefined) {\n        prefetchable.delete(element);\n        prefetchableAndVisible.delete(instance);\n        const prefetchTask = instance.prefetchTask;\n        if (prefetchTask !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(prefetchTask);\n        }\n    }\n    if (observer !== null) {\n        observer.unobserve(element);\n    }\n}\nfunction handleIntersect(entries) {\n    for (const entry of entries){\n        // Some extremely old browsers or polyfills don't reliably support\n        // isIntersecting so we check intersectionRatio instead. (Do we care? Not\n        // really. But whatever this is fine.)\n        const isVisible = entry.intersectionRatio > 0;\n        onLinkVisibilityChanged(entry.target, isVisible);\n    }\n}\nfunction onLinkVisibilityChanged(element, isVisible) {\n    if (true) {\n        // Prefetching on viewport is disabled in development for performance\n        // reasons, because it requires compiling the target page.\n        // TODO: Investigate re-enabling this.\n        return;\n    }\n    const instance = prefetchable.get(element);\n    if (instance === undefined) {\n        return;\n    }\n    instance.isVisible = isVisible;\n    if (isVisible) {\n        prefetchableAndVisible.add(instance);\n    } else {\n        prefetchableAndVisible.delete(instance);\n    }\n    rescheduleLinkPrefetch(instance, _segmentcache.PrefetchPriority.Default);\n}\nfunction onNavigationIntent(element, unstable_upgradeToDynamicPrefetch) {\n    const instance = prefetchable.get(element);\n    if (instance === undefined) {\n        return;\n    }\n    // Prefetch the link on hover/touchstart.\n    if (instance !== undefined) {\n        if (false) {}\n        rescheduleLinkPrefetch(instance, _segmentcache.PrefetchPriority.Intent);\n    }\n}\nfunction rescheduleLinkPrefetch(instance, priority) {\n    const existingPrefetchTask = instance.prefetchTask;\n    if (!instance.isVisible) {\n        // Cancel any in-progress prefetch task. (If it already finished then this\n        // is a no-op.)\n        if (existingPrefetchTask !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(existingPrefetchTask);\n        }\n        // We don't need to reset the prefetchTask to null upon cancellation; an\n        // old task object can be rescheduled with reschedulePrefetchTask. This is a\n        // micro-optimization but also makes the code simpler (don't need to\n        // worry about whether an old task object is stale).\n        return;\n    }\n    if (true) {\n        // The old prefetch implementation does not have different priority levels.\n        // Just schedule a new prefetch task.\n        prefetchWithOldCacheImplementation(instance);\n        return;\n    }\n    const appRouterState = (0, _approuterinstance.getCurrentAppRouterState)();\n    if (appRouterState !== null) {\n        const treeAtTimeOfPrefetch = appRouterState.tree;\n        if (existingPrefetchTask === null) {\n            // Initiate a prefetch task.\n            const nextUrl = appRouterState.nextUrl;\n            const cacheKey = (0, _segmentcache.createCacheKey)(instance.prefetchHref, nextUrl);\n            instance.prefetchTask = (0, _segmentcache.schedulePrefetchTask)(cacheKey, treeAtTimeOfPrefetch, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority, null);\n        } else {\n            // We already have an old task object that we can reschedule. This is\n            // effectively the same as canceling the old task and creating a new one.\n            (0, _segmentcache.reschedulePrefetchTask)(existingPrefetchTask, treeAtTimeOfPrefetch, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n        }\n    }\n}\nfunction pingVisibleLinks(nextUrl, tree) {\n    // For each currently visible link, cancel the existing prefetch task (if it\n    // exists) and schedule a new one. This is effectively the same as if all the\n    // visible links left and then re-entered the viewport.\n    //\n    // This is called when the Next-Url or the base tree changes, since those\n    // may affect the result of a prefetch task. It's also called after a\n    // cache invalidation.\n    for (const instance of prefetchableAndVisible){\n        const task = instance.prefetchTask;\n        if (task !== null && !(0, _segmentcache.isPrefetchTaskDirty)(task, nextUrl, tree)) {\n            continue;\n        }\n        // Something changed. Cancel the existing prefetch task and schedule a\n        // new one.\n        if (task !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(task);\n        }\n        const cacheKey = (0, _segmentcache.createCacheKey)(instance.prefetchHref, nextUrl);\n        instance.prefetchTask = (0, _segmentcache.schedulePrefetchTask)(cacheKey, tree, instance.kind === _routerreducertypes.PrefetchKind.FULL, _segmentcache.PrefetchPriority.Default, null);\n    }\n}\nfunction prefetchWithOldCacheImplementation(instance) {\n    // This is the path used when the Segment Cache is not enabled.\n    if (false) {}\n    const doPrefetch = async ()=>{\n        // note that `appRouter.prefetch()` is currently sync,\n        // so we have to wrap this call in an async function to be able to catch() errors below.\n        return instance.router.prefetch(instance.prefetchHref, {\n            kind: instance.kind\n        });\n    };\n    // Prefetch the page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    doPrefetch().catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=links.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/links.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js ***!
  \************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createInitialRouterState\", ({\n    enumerable: true,\n    get: function() {\n        return createInitialRouterState;\n    }\n}));\nconst _createhreffromurl = __webpack_require__(/*! ./create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _filllazyitemstillleafwithhead = __webpack_require__(/*! ./fill-lazy-items-till-leaf-with-head */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.js\");\nconst _computechangedpath = __webpack_require__(/*! ./compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _prefetchcacheutils = __webpack_require__(/*! ./prefetch-cache-utils */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _refetchinactiveparallelsegments = __webpack_require__(/*! ./refetch-inactive-parallel-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js\");\nconst _flightdatahelpers = __webpack_require__(/*! ../../flight-data-helpers */ \"(app-pages-browser)/./node_modules/next/dist/client/flight-data-helpers.js\");\nfunction createInitialRouterState(param) {\n    let { navigatedAt, initialFlightData, initialCanonicalUrlParts, initialParallelRoutes, location, couldBeIntercepted, postponed, prerendered } = param;\n    // When initialized on the server, the canonical URL is provided as an array of parts.\n    // This is to ensure that when the RSC payload streamed to the client, crawlers don't interpret it\n    // as a URL that should be crawled.\n    const initialCanonicalUrl = initialCanonicalUrlParts.join('/');\n    const normalizedFlightData = (0, _flightdatahelpers.getFlightDataPartsFromPath)(initialFlightData[0]);\n    const { tree: initialTree, seedData: initialSeedData, head: initialHead } = normalizedFlightData;\n    // For the SSR render, seed data should always be available (we only send back a `null` response\n    // in the case of a `loading` segment, pre-PPR.)\n    const rsc = initialSeedData == null ? void 0 : initialSeedData[1];\n    var _initialSeedData_;\n    const loading = (_initialSeedData_ = initialSeedData == null ? void 0 : initialSeedData[3]) != null ? _initialSeedData_ : null;\n    const cache = {\n        lazyData: null,\n        rsc,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        // The cache gets seeded during the first render. `initialParallelRoutes` ensures the cache from the first render is there during the second render.\n        parallelRoutes: initialParallelRoutes,\n        loading,\n        navigatedAt\n    };\n    const canonicalUrl = // This is safe to do as canonicalUrl can't be rendered, it's only used to control the history updates in the useEffect further down in this file.\n    location ? (0, _createhreffromurl.createHrefFromUrl)(location) : initialCanonicalUrl;\n    (0, _refetchinactiveparallelsegments.addRefreshMarkerToActiveParallelSegments)(initialTree, canonicalUrl);\n    const prefetchCache = new Map();\n    // When the cache hasn't been seeded yet we fill the cache with the head.\n    if (initialParallelRoutes === null || initialParallelRoutes.size === 0) {\n        (0, _filllazyitemstillleafwithhead.fillLazyItemsTillLeafWithHead)(navigatedAt, cache, undefined, initialTree, initialSeedData, initialHead, undefined);\n    }\n    var _ref;\n    const initialState = {\n        tree: initialTree,\n        cache,\n        prefetchCache,\n        pushRef: {\n            pendingPush: false,\n            mpaNavigation: false,\n            // First render needs to preserve the previous window.history.state\n            // to avoid it being overwritten on navigation back/forward with MPA Navigation.\n            preserveCustomHistoryState: true\n        },\n        focusAndScrollRef: {\n            apply: false,\n            onlyHashChange: false,\n            hashFragment: null,\n            segmentPaths: []\n        },\n        canonicalUrl,\n        nextUrl: (_ref = (0, _computechangedpath.extractPathFromFlightRouterState)(initialTree) || (location == null ? void 0 : location.pathname)) != null ? _ref : null\n    };\n    if (false) {}\n    return initialState;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=create-initial-router-state.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js ***!
  \******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createFetch: function() {\n        return createFetch;\n    },\n    createFromNextReadableStream: function() {\n        return createFromNextReadableStream;\n    },\n    fetchServerResponse: function() {\n        return fetchServerResponse;\n    },\n    urlToUrlWithoutFlightMarker: function() {\n        return urlToUrlWithoutFlightMarker;\n    }\n});\nconst _client = __webpack_require__(/*! react-server-dom-webpack/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/client.browser.js\");\nconst _approuterheaders = __webpack_require__(/*! ../app-router-headers */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-headers.js\");\nconst _appcallserver = __webpack_require__(/*! ../../app-call-server */ \"(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js\");\nconst _appfindsourcemapurl = __webpack_require__(/*! ../../app-find-source-map-url */ \"(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _flightdatahelpers = __webpack_require__(/*! ../../flight-data-helpers */ \"(app-pages-browser)/./node_modules/next/dist/client/flight-data-helpers.js\");\nconst _appbuildid = __webpack_require__(/*! ../../app-build-id */ \"(app-pages-browser)/./node_modules/next/dist/client/app-build-id.js\");\nconst _setcachebustingsearchparam = __webpack_require__(/*! ./set-cache-busting-search-param */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/set-cache-busting-search-param.js\");\nconst createFromReadableStream = _client.createFromReadableStream;\nfunction urlToUrlWithoutFlightMarker(url) {\n    const urlWithoutFlightParameters = new URL(url, location.origin);\n    urlWithoutFlightParameters.searchParams.delete(_approuterheaders.NEXT_RSC_UNION_QUERY);\n    if (false) {}\n    return urlWithoutFlightParameters;\n}\nfunction doMpaNavigation(url) {\n    return {\n        flightData: urlToUrlWithoutFlightMarker(url).toString(),\n        canonicalUrl: undefined,\n        couldBeIntercepted: false,\n        prerendered: false,\n        postponed: false,\n        staleTime: -1\n    };\n}\nlet abortController = new AbortController();\nif (true) {\n    // Abort any in-flight requests when the page is unloaded, e.g. due to\n    // reloading the page or performing hard navigations. This allows us to ignore\n    // what would otherwise be a thrown TypeError when the browser cancels the\n    // requests.\n    window.addEventListener('pagehide', ()=>{\n        abortController.abort();\n    });\n    // Use a fresh AbortController instance on pageshow, e.g. when navigating back\n    // and the JavaScript execution context is restored by the browser.\n    window.addEventListener('pageshow', ()=>{\n        abortController = new AbortController();\n    });\n}\nasync function fetchServerResponse(url, options) {\n    const { flightRouterState, nextUrl, prefetchKind } = options;\n    const headers = {\n        // Enable flight response\n        [_approuterheaders.RSC_HEADER]: '1',\n        // Provide the current router state\n        [_approuterheaders.NEXT_ROUTER_STATE_TREE_HEADER]: (0, _flightdatahelpers.prepareFlightRouterStateForRequest)(flightRouterState, options.isHmrRefresh)\n    };\n    /**\n   * Three cases:\n   * - `prefetchKind` is `undefined`, it means it's a normal navigation, so we want to prefetch the page data fully\n   * - `prefetchKind` is `full` - we want to prefetch the whole page so same as above\n   * - `prefetchKind` is `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully\n   */ if (prefetchKind === _routerreducertypes.PrefetchKind.AUTO) {\n        headers[_approuterheaders.NEXT_ROUTER_PREFETCH_HEADER] = '1';\n    }\n    if ( true && options.isHmrRefresh) {\n        headers[_approuterheaders.NEXT_HMR_REFRESH_HEADER] = '1';\n    }\n    if (nextUrl) {\n        headers[_approuterheaders.NEXT_URL] = nextUrl;\n    }\n    try {\n        var _res_headers_get;\n        // When creating a \"temporary\" prefetch (the \"on-demand\" prefetch that gets created on navigation, if one doesn't exist)\n        // we send the request with a \"high\" priority as it's in response to a user interaction that could be blocking a transition.\n        // Otherwise, all other prefetches are sent with a \"low\" priority.\n        // We use \"auto\" for in all other cases to match the existing default, as this function is shared outside of prefetching.\n        const fetchPriority = prefetchKind ? prefetchKind === _routerreducertypes.PrefetchKind.TEMPORARY ? 'high' : 'low' : 'auto';\n        if (false) {}\n        const res = await createFetch(url, headers, fetchPriority, abortController.signal);\n        const responseUrl = urlToUrlWithoutFlightMarker(res.url);\n        const canonicalUrl = res.redirected ? responseUrl : undefined;\n        const contentType = res.headers.get('content-type') || '';\n        const interception = !!((_res_headers_get = res.headers.get('vary')) == null ? void 0 : _res_headers_get.includes(_approuterheaders.NEXT_URL));\n        const postponed = !!res.headers.get(_approuterheaders.NEXT_DID_POSTPONE_HEADER);\n        const staleTimeHeaderSeconds = res.headers.get(_approuterheaders.NEXT_ROUTER_STALE_TIME_HEADER);\n        const staleTime = staleTimeHeaderSeconds !== null ? parseInt(staleTimeHeaderSeconds, 10) * 1000 : -1;\n        let isFlightResponse = contentType.startsWith(_approuterheaders.RSC_CONTENT_TYPE_HEADER);\n        if (false) {}\n        // If fetch returns something different than flight response handle it like a mpa navigation\n        // If the fetch was not 200, we also handle it like a mpa navigation\n        if (!isFlightResponse || !res.ok || !res.body) {\n            // in case the original URL came with a hash, preserve it before redirecting to the new URL\n            if (url.hash) {\n                responseUrl.hash = url.hash;\n            }\n            return doMpaNavigation(responseUrl.toString());\n        }\n        // We may navigate to a page that requires a different Webpack runtime.\n        // In prod, every page will have the same Webpack runtime.\n        // In dev, the Webpack runtime is minimal for each page.\n        // We need to ensure the Webpack runtime is updated before executing client-side JS of the new page.\n        if (true) {\n            await (__webpack_require__(/*! ../../dev/hot-reloader/app/hot-reloader-app */ \"(app-pages-browser)/./node_modules/next/dist/client/dev/hot-reloader/app/hot-reloader-app.js\").waitForWebpackRuntimeHotUpdate)();\n        }\n        // Handle the `fetch` readable stream that can be unwrapped by `React.use`.\n        const flightStream = postponed ? createUnclosingPrefetchStream(res.body) : res.body;\n        const response = await createFromNextReadableStream(flightStream);\n        if ((0, _appbuildid.getAppBuildId)() !== response.b) {\n            return doMpaNavigation(res.url);\n        }\n        return {\n            flightData: (0, _flightdatahelpers.normalizeFlightData)(response.f),\n            canonicalUrl: canonicalUrl,\n            couldBeIntercepted: interception,\n            prerendered: response.S,\n            postponed,\n            staleTime\n        };\n    } catch (err) {\n        if (!abortController.signal.aborted) {\n            console.error(\"Failed to fetch RSC payload for \" + url + \". Falling back to browser navigation.\", err);\n        }\n        // If fetch fails handle it like a mpa navigation\n        // TODO-APP: Add a test for the case where a CORS request fails, e.g. external url redirect coming from the response.\n        // See https://github.com/vercel/next.js/issues/43605#issuecomment-1451617521 for a reproduction.\n        return {\n            flightData: url.toString(),\n            canonicalUrl: undefined,\n            couldBeIntercepted: false,\n            prerendered: false,\n            postponed: false,\n            staleTime: -1\n        };\n    }\n}\nasync function createFetch(url, headers, fetchPriority, signal) {\n    // TODO: In output: \"export\" mode, the headers do nothing. Omit them (and the\n    // cache busting search param) from the request so they're\n    // maximally cacheable.\n    if (false) {}\n    if (false) {}\n    const fetchOptions = {\n        // Backwards compat for older browsers. `same-origin` is the default in modern browsers.\n        credentials: 'same-origin',\n        headers,\n        priority: fetchPriority || undefined,\n        signal\n    };\n    // `fetchUrl` is slightly different from `url` because we add a cache-busting\n    // search param to it. This should not leak outside of this function, so we\n    // track them separately.\n    let fetchUrl = new URL(url);\n    (0, _setcachebustingsearchparam.setCacheBustingSearchParam)(fetchUrl, headers);\n    let browserResponse = await fetch(fetchUrl, fetchOptions);\n    // If the server responds with a redirect (e.g. 307), and the redirected\n    // location does not contain the cache busting search param set in the\n    // original request, the response is likely invalid — when following the\n    // redirect, the browser forwards the request headers, but since the cache\n    // busting search param is missing, the server will reject the request due to\n    // a mismatch.\n    //\n    // Ideally, we would be able to intercept the redirect response and perform it\n    // manually, instead of letting the browser automatically follow it, but this\n    // is not allowed by the fetch API.\n    //\n    // So instead, we must \"replay\" the redirect by fetching the new location\n    // again, but this time we'll append the cache busting search param to prevent\n    // a mismatch.\n    //\n    // TODO: We can optimize Next.js's built-in middleware APIs by returning a\n    // custom status code, to prevent the browser from automatically following it.\n    //\n    // This does not affect Server Action-based redirects; those are encoded\n    // differently, as part of the Flight body. It only affects redirects that\n    // occur in a middleware or a third-party proxy.\n    let redirected = browserResponse.redirected;\n    if (false) {}\n    // Remove the cache busting search param from the response URL, to prevent it\n    // from leaking outside of this function.\n    const responseUrl = new URL(browserResponse.url, fetchUrl);\n    responseUrl.searchParams.delete(_approuterheaders.NEXT_RSC_UNION_QUERY);\n    const rscResponse = {\n        url: responseUrl.href,\n        // This is true if any redirects occurred, either automatically by the\n        // browser, or manually by us. So it's different from\n        // `browserResponse.redirected`, which only tells us whether the browser\n        // followed a redirect, and only for the last response in the chain.\n        redirected,\n        // These can be copied from the last browser response we received. We\n        // intentionally only expose the subset of fields that are actually used\n        // elsewhere in the codebase.\n        ok: browserResponse.ok,\n        headers: browserResponse.headers,\n        body: browserResponse.body,\n        status: browserResponse.status\n    };\n    return rscResponse;\n}\nfunction createFromNextReadableStream(flightStream) {\n    return createFromReadableStream(flightStream, {\n        callServer: _appcallserver.callServer,\n        findSourceMapURL: _appfindsourcemapurl.findSourceMapURL\n    });\n}\nfunction createUnclosingPrefetchStream(originalFlightStream) {\n    // When PPR is enabled, prefetch streams may contain references that never\n    // resolve, because that's how we encode dynamic data access. In the decoded\n    // object returned by the Flight client, these are reified into hanging\n    // promises that suspend during render, which is effectively what we want.\n    // The UI resolves when it switches to the dynamic data stream\n    // (via useDeferredValue(dynamic, static)).\n    //\n    // However, the Flight implementation currently errors if the server closes\n    // the response before all the references are resolved. As a cheat to work\n    // around this, we wrap the original stream in a new stream that never closes,\n    // and therefore doesn't error.\n    const reader = originalFlightStream.getReader();\n    return new ReadableStream({\n        async pull (controller) {\n            while(true){\n                const { done, value } = await reader.read();\n                if (!done) {\n                    // Pass to the target stream and keep consuming the Flight response\n                    // from the server.\n                    controller.enqueue(value);\n                    continue;\n                }\n                // The server stream has closed. Exit, but intentionally do not close\n                // the target stream.\n                return;\n            }\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=fetch-server-response.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js ***!
  \*************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hmrRefreshReducer\", ({\n    enumerable: true,\n    get: function() {\n        return hmrRefreshReducer;\n    }\n}));\nconst _fetchserverresponse = __webpack_require__(/*! ../fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _applyrouterstatepatchtotree = __webpack_require__(/*! ../apply-router-state-patch-to-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-router-state-patch-to-tree.js\");\nconst _isnavigatingtonewrootlayout = __webpack_require__(/*! ../is-navigating-to-new-root-layout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js\");\nconst _navigatereducer = __webpack_require__(/*! ./navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nconst _handlemutable = __webpack_require__(/*! ../handle-mutable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-mutable.js\");\nconst _applyflightdata = __webpack_require__(/*! ../apply-flight-data */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-flight-data.js\");\nconst _approuter = __webpack_require__(/*! ../../app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _handlesegmentmismatch = __webpack_require__(/*! ../handle-segment-mismatch */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js\");\nconst _hasinterceptionrouteincurrenttree = __webpack_require__(/*! ./has-interception-route-in-current-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\");\n// A version of refresh reducer that keeps the cache around instead of wiping all of it.\nfunction hmrRefreshReducerImpl(state, action) {\n    const { origin } = action;\n    const mutable = {};\n    const href = state.canonicalUrl;\n    mutable.preserveCustomHistoryState = false;\n    const cache = (0, _approuter.createEmptyCacheNode)();\n    // If the current tree was intercepted, the nextUrl should be included in the request.\n    // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n    const includeNextUrl = (0, _hasinterceptionrouteincurrenttree.hasInterceptionRouteInCurrentTree)(state.tree);\n    // TODO-APP: verify that `href` is not an external url.\n    // Fetch data from the root of the tree.\n    const navigatedAt = Date.now();\n    cache.lazyData = (0, _fetchserverresponse.fetchServerResponse)(new URL(href, origin), {\n        flightRouterState: [\n            state.tree[0],\n            state.tree[1],\n            state.tree[2],\n            'refetch'\n        ],\n        nextUrl: includeNextUrl ? state.nextUrl : null,\n        isHmrRefresh: true\n    });\n    return cache.lazyData.then((param)=>{\n        let { flightData, canonicalUrl: canonicalUrlOverride } = param;\n        // Handle case when navigating to page in `pages` from `app`\n        if (typeof flightData === 'string') {\n            return (0, _navigatereducer.handleExternalUrl)(state, mutable, flightData, state.pushRef.pendingPush);\n        }\n        // Remove cache.lazyData as it has been resolved at this point.\n        cache.lazyData = null;\n        let currentTree = state.tree;\n        let currentCache = state.cache;\n        for (const normalizedFlightData of flightData){\n            const { tree: treePatch, isRootRender } = normalizedFlightData;\n            if (!isRootRender) {\n                // TODO-APP: handle this case better\n                console.log('REFRESH FAILED');\n                return state;\n            }\n            const newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)([\n                ''\n            ], currentTree, treePatch, state.canonicalUrl);\n            if (newTree === null) {\n                return (0, _handlesegmentmismatch.handleSegmentMismatch)(state, action, treePatch);\n            }\n            if ((0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(currentTree, newTree)) {\n                return (0, _navigatereducer.handleExternalUrl)(state, mutable, href, state.pushRef.pendingPush);\n            }\n            const canonicalUrlOverrideHref = canonicalUrlOverride ? (0, _createhreffromurl.createHrefFromUrl)(canonicalUrlOverride) : undefined;\n            if (canonicalUrlOverride) {\n                mutable.canonicalUrl = canonicalUrlOverrideHref;\n            }\n            const applied = (0, _applyflightdata.applyFlightData)(navigatedAt, currentCache, cache, normalizedFlightData);\n            if (applied) {\n                mutable.cache = cache;\n                currentCache = cache;\n            }\n            mutable.patchedTree = newTree;\n            mutable.canonicalUrl = href;\n            currentTree = newTree;\n        }\n        return (0, _handlemutable.handleMutable)(state, mutable);\n    }, ()=>state);\n}\nfunction hmrRefreshReducerNoop(state, _action) {\n    return state;\n}\nconst hmrRefreshReducer =  false ? 0 : hmrRefreshReducerImpl;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hmr-refresh-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js ***!
  \******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// This file is only used in app router due to the specific error state handling.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    onCaughtError: function() {\n        return onCaughtError;\n    },\n    onUncaughtError: function() {\n        return onUncaughtError;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _isnextroutererror = __webpack_require__(/*! ../components/is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _bailouttocsr = __webpack_require__(/*! ../../shared/lib/lazy-dynamic/bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nconst _reportglobalerror = __webpack_require__(/*! ./report-global-error */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/report-global-error.js\");\nconst _errorboundary = __webpack_require__(/*! ../components/error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\");\nconst _globalerror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../components/builtin/global-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js\"));\nconst devToolErrorMod =  true ? __webpack_require__(/*! ../../next-devtools/userspace/app/errors */ \"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/errors/index.js\") : 0;\nfunction onCaughtError(thrownValue, errorInfo) {\n    var _errorInfo_errorBoundary;\n    const errorBoundaryComponent = (_errorInfo_errorBoundary = errorInfo.errorBoundary) == null ? void 0 : _errorInfo_errorBoundary.constructor;\n    let isImplicitErrorBoundary;\n    if (true) {\n        const { AppDevOverlayErrorBoundary } = __webpack_require__(/*! ../../next-devtools/userspace/app/app-dev-overlay-error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/app-dev-overlay-error-boundary.js\");\n        isImplicitErrorBoundary = errorBoundaryComponent === AppDevOverlayErrorBoundary;\n    }\n    isImplicitErrorBoundary = isImplicitErrorBoundary || errorBoundaryComponent === _errorboundary.ErrorBoundaryHandler && errorInfo.errorBoundary.props.errorComponent === _globalerror.default;\n    // Skip the segment explorer triggered error\n    if (true) {\n        const { SEGMENT_EXPLORER_SIMULATED_ERROR_MESSAGE } = __webpack_require__(/*! ../../next-devtools/userspace/app/segment-explorer-node */ \"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\");\n        if (thrownValue instanceof Error && thrownValue.message === SEGMENT_EXPLORER_SIMULATED_ERROR_MESSAGE) {\n            return;\n        }\n    }\n    if (isImplicitErrorBoundary) {\n        // We don't consider errors caught unless they're caught by an explicit error\n        // boundary. The built-in ones are considered implicit.\n        // This mimics how the same app would behave without Next.js.\n        return onUncaughtError(thrownValue, errorInfo);\n    }\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(thrownValue) || (0, _isnextroutererror.isNextRouterError)(thrownValue)) return;\n    if (true) {\n        var _errorInfo_componentStack;\n        const errorBoundaryName = (errorBoundaryComponent == null ? void 0 : errorBoundaryComponent.displayName) || (errorBoundaryComponent == null ? void 0 : errorBoundaryComponent.name) || 'Unknown';\n        const componentThatErroredFrame = errorInfo == null ? void 0 : (_errorInfo_componentStack = errorInfo.componentStack) == null ? void 0 : _errorInfo_componentStack.split('\\n')[1];\n        var // example 1: at Page (http://localhost:3000/_next/static/chunks/pages/index.js?ts=1631600000000:2:1)\n        // example 2: Page@http://localhost:3000/_next/static/chunks/pages/index.js?ts=1631600000000:2:1\n        _componentThatErroredFrame_match;\n        // Match chrome or safari stack trace\n        const matches = (_componentThatErroredFrame_match = componentThatErroredFrame == null ? void 0 : componentThatErroredFrame.match(/\\s+at (\\w+)\\s+|(\\w+)@/)) != null ? _componentThatErroredFrame_match : [];\n        const componentThatErroredName = matches[1] || matches[2] || 'Unknown';\n        // Create error location with errored component and error boundary, to match the behavior of default React onCaughtError handler.\n        const errorBoundaryMessage = \"It was handled by the <\" + errorBoundaryName + \"> error boundary.\";\n        const componentErrorMessage = componentThatErroredName ? \"The above error occurred in the <\" + componentThatErroredName + \"> component.\" : \"The above error occurred in one of your components.\";\n        const errorLocation = componentErrorMessage + \" \" + errorBoundaryMessage;\n        const error = devToolErrorMod.decorateDevError(thrownValue, errorInfo);\n        // Log and report the error with location but without modifying the error stack\n        devToolErrorMod.originConsoleError('%o\\n\\n%s', thrownValue, errorLocation);\n        devToolErrorMod.handleClientError(error);\n    } else {}\n}\nfunction onUncaughtError(thrownValue, errorInfo) {\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(thrownValue) || (0, _isnextroutererror.isNextRouterError)(thrownValue)) return;\n    if (true) {\n        const error = devToolErrorMod.decorateDevError(thrownValue, errorInfo);\n        // TODO: Add an adendum to the overlay telling people about custom error boundaries.\n        (0, _reportglobalerror.reportGlobalError)(error);\n    } else {}\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-boundary-callbacks.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js ***!
  \**************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// This module can be shared between both pages router and app router\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    isRecoverableError: function() {\n        return isRecoverableError;\n    },\n    onRecoverableError: function() {\n        return onRecoverableError;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _bailouttocsr = __webpack_require__(/*! ../../shared/lib/lazy-dynamic/bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst _reportglobalerror = __webpack_require__(/*! ./report-global-error */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/report-global-error.js\");\nconst recoverableErrors = new WeakSet();\nfunction isRecoverableError(error) {\n    return recoverableErrors.has(error);\n}\nconst onRecoverableError = (error, errorInfo)=>{\n    // x-ref: https://github.com/facebook/react/pull/28736\n    let cause = (0, _iserror.default)(error) && 'cause' in error ? error.cause : error;\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(cause)) return;\n    if (true) {\n        const { decorateDevError } = __webpack_require__(/*! ../../next-devtools/userspace/app/errors/stitched-error */ \"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/errors/stitched-error.js\");\n        const causeError = decorateDevError(cause, errorInfo);\n        recoverableErrors.add(causeError);\n        cause = causeError;\n    }\n    (0, _reportglobalerror.reportGlobalError)(cause);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=on-recoverable-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L3JlYWN0LWNsaWVudC1jYWxsYmFja3Mvb24tcmVjb3ZlcmFibGUtZXJyb3IuanMiLCJtYXBwaW5ncyI6IkFBQUEscUVBQXFFOzs7Ozs7Ozs7Ozs7O0lBU3JEQSxrQkFBa0I7ZUFBbEJBOztJQUlIQyxrQkFBa0I7ZUFBbEJBOzs7OzBDQVZ1Qjs4RUFDaEI7K0NBQ2M7QUFFbEMsTUFBTUMsb0JBQW9CLElBQUlDO0FBRXZCLFNBQVNILG1CQUFtQkksS0FBWTtJQUM3QyxPQUFPRixrQkFBa0JHLEdBQUcsQ0FBQ0Q7QUFDL0I7QUFFTyxNQUFNSCxxQkFBNkQsQ0FDeEVHLE9BQ0FFO0lBRUEsc0RBQXNEO0lBQ3RELElBQUlDLFFBQVFDLENBQUFBLEdBQUFBLFNBQUFBLE9BQUFBLEVBQVFKLFVBQVUsV0FBV0EsUUFBUUEsTUFBTUcsS0FBSyxHQUFHSDtJQUMvRCw2RUFBNkU7SUFDN0UsSUFBSUssQ0FBQUEsR0FBQUEsY0FBQUEsbUJBQUFBLEVBQW9CRixRQUFRO0lBRWhDLElBQUlHLElBQW9CLEVBQW1CO1FBQ3pDLE1BQU0sRUFBRUcsZ0JBQWdCLEVBQUUsR0FDeEJDLG1CQUFPQSxDQUFDLGtLQUF5RDtRQUNuRSxNQUFNQyxhQUFhRixpQkFBaUJOLE9BQU9EO1FBQzNDSixrQkFBa0JjLEdBQUcsQ0FBQ0Q7UUFDdEJSLFFBQVFRO0lBQ1Y7SUFFQUUsQ0FBQUEsR0FBQUEsbUJBQUFBLGlCQUFBQSxFQUFrQlY7QUFDcEIiLCJzb3VyY2VzIjpbIkU6XFwyLUFJcHJvZ3JhbVxcc3JjXFxjbGllbnRcXHJlYWN0LWNsaWVudC1jYWxsYmFja3NcXG9uLXJlY292ZXJhYmxlLWVycm9yLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRoaXMgbW9kdWxlIGNhbiBiZSBzaGFyZWQgYmV0d2VlbiBib3RoIHBhZ2VzIHJvdXRlciBhbmQgYXBwIHJvdXRlclxuXG5pbXBvcnQgdHlwZSB7IEh5ZHJhdGlvbk9wdGlvbnMgfSBmcm9tICdyZWFjdC1kb20vY2xpZW50J1xuaW1wb3J0IHsgaXNCYWlsb3V0VG9DU1JFcnJvciB9IGZyb20gJy4uLy4uL3NoYXJlZC9saWIvbGF6eS1keW5hbWljL2JhaWxvdXQtdG8tY3NyJ1xuaW1wb3J0IGlzRXJyb3IgZnJvbSAnLi4vLi4vbGliL2lzLWVycm9yJ1xuaW1wb3J0IHsgcmVwb3J0R2xvYmFsRXJyb3IgfSBmcm9tICcuL3JlcG9ydC1nbG9iYWwtZXJyb3InXG5cbmNvbnN0IHJlY292ZXJhYmxlRXJyb3JzID0gbmV3IFdlYWtTZXQ8RXJyb3I+KClcblxuZXhwb3J0IGZ1bmN0aW9uIGlzUmVjb3ZlcmFibGVFcnJvcihlcnJvcjogRXJyb3IpOiBib29sZWFuIHtcbiAgcmV0dXJuIHJlY292ZXJhYmxlRXJyb3JzLmhhcyhlcnJvcilcbn1cblxuZXhwb3J0IGNvbnN0IG9uUmVjb3ZlcmFibGVFcnJvcjogSHlkcmF0aW9uT3B0aW9uc1snb25SZWNvdmVyYWJsZUVycm9yJ10gPSAoXG4gIGVycm9yLFxuICBlcnJvckluZm9cbikgPT4ge1xuICAvLyB4LXJlZjogaHR0cHM6Ly9naXRodWIuY29tL2ZhY2Vib29rL3JlYWN0L3B1bGwvMjg3MzZcbiAgbGV0IGNhdXNlID0gaXNFcnJvcihlcnJvcikgJiYgJ2NhdXNlJyBpbiBlcnJvciA/IGVycm9yLmNhdXNlIDogZXJyb3JcbiAgLy8gU2tpcCBjZXJ0YWluIGN1c3RvbSBlcnJvcnMgd2hpY2ggYXJlIG5vdCBleHBlY3RlZCB0byBiZSByZXBvcnRlZCBvbiBjbGllbnRcbiAgaWYgKGlzQmFpbG91dFRvQ1NSRXJyb3IoY2F1c2UpKSByZXR1cm5cblxuICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICAgIGNvbnN0IHsgZGVjb3JhdGVEZXZFcnJvciB9ID1cbiAgICAgIHJlcXVpcmUoJy4uLy4uL25leHQtZGV2dG9vbHMvdXNlcnNwYWNlL2FwcC9lcnJvcnMvc3RpdGNoZWQtZXJyb3InKSBhcyB0eXBlb2YgaW1wb3J0KCcuLi8uLi9uZXh0LWRldnRvb2xzL3VzZXJzcGFjZS9hcHAvZXJyb3JzL3N0aXRjaGVkLWVycm9yJylcbiAgICBjb25zdCBjYXVzZUVycm9yID0gZGVjb3JhdGVEZXZFcnJvcihjYXVzZSwgZXJyb3JJbmZvKVxuICAgIHJlY292ZXJhYmxlRXJyb3JzLmFkZChjYXVzZUVycm9yKVxuICAgIGNhdXNlID0gY2F1c2VFcnJvclxuICB9XG5cbiAgcmVwb3J0R2xvYmFsRXJyb3IoY2F1c2UpXG59XG4iXSwibmFtZXMiOlsiaXNSZWNvdmVyYWJsZUVycm9yIiwib25SZWNvdmVyYWJsZUVycm9yIiwicmVjb3ZlcmFibGVFcnJvcnMiLCJXZWFrU2V0IiwiZXJyb3IiLCJoYXMiLCJlcnJvckluZm8iLCJjYXVzZSIsImlzRXJyb3IiLCJpc0JhaWxvdXRUb0NTUkVycm9yIiwicHJvY2VzcyIsImVudiIsIk5PREVfRU5WIiwiZGVjb3JhdGVEZXZFcnJvciIsInJlcXVpcmUiLCJjYXVzZUVycm9yIiwiYWRkIiwicmVwb3J0R2xvYmFsRXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js ***!
  \********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    AppRouterContext: function() {\n        return AppRouterContext;\n    },\n    GlobalLayoutRouterContext: function() {\n        return GlobalLayoutRouterContext;\n    },\n    LayoutRouterContext: function() {\n        return LayoutRouterContext;\n    },\n    MissingSlotContext: function() {\n        return MissingSlotContext;\n    },\n    TemplateContext: function() {\n        return TemplateContext;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst AppRouterContext = _react.default.createContext(null);\nconst LayoutRouterContext = _react.default.createContext(null);\nconst GlobalLayoutRouterContext = _react.default.createContext(null);\nconst TemplateContext = _react.default.createContext(null);\nif (true) {\n    AppRouterContext.displayName = 'AppRouterContext';\n    LayoutRouterContext.displayName = 'LayoutRouterContext';\n    GlobalLayoutRouterContext.displayName = 'GlobalLayoutRouterContext';\n    TemplateContext.displayName = 'TemplateContext';\n}\nconst MissingSlotContext = _react.default.createContext(new Set()); //# sourceMappingURL=app-router-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\n"));

/***/ })

});