/**
 * FNV-1a Hash implementation
 * <AUTHOR> (tjwebb) <<EMAIL>>
 *
 * Ported from https://github.com/tjwebb/fnv-plus/blob/master/index.js
 *
 * Simplified, optimized and add modified for 52 bit, which provides a larger hash space
 * and still making use of Javascript's 53-bit integer space.
 */
export declare const fnv1a52: (str: string) => number;
export declare const generateETag: (payload: string, weak?: boolean) => string;
