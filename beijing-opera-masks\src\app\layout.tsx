import type { Metadata } from "next";
import { PerformanceMonitor } from "@/components/performance/PerformanceMonitor";
import { ThemeProvider } from "@/components/providers/ThemeProvider";
import { AuthProvider } from "@/contexts/AuthContext";
// import "./globals.css";

// 使用CSS导入Google字体以避免Turbopack兼容性问题
// 字体将通过CSS @import或link标签加载

export const metadata: Metadata = {
  title: "京剧脸谱文化展示平台 - 探索中国传统戏曲艺术",
  description: "深入了解京剧脸谱的文化内涵，探索生旦净丑四大行当的艺术魅力。包含15个经典脸谱的详细介绍、绘制动画演示和文化背景解析。",
  keywords: "京剧,脸谱,中国传统文化,戏曲,生旦净丑,文化艺术,传统艺术",
  authors: [{ name: "京剧脸谱文化展示平台" }],
  creator: "京剧脸谱文化展示平台",
  publisher: "京剧脸谱文化展示平台",
  robots: "index, follow",
  openGraph: {
    title: "京剧脸谱文化展示平台",
    description: "探索中国传统京剧脸谱艺术的文化魅力",
    type: "website",
    locale: "zh_CN",
    siteName: "京剧脸谱文化展示平台"
  },
  twitter: {
    card: "summary_large_image",
    title: "京剧脸谱文化展示平台",
    description: "探索中国传统京剧脸谱艺术的文化魅力"
  },
  viewport: "width=device-width, initial-scale=1",
  themeColor: "#B91C1C"
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="https://via.placeholder.com" />
        <link
          href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700;900&family=Noto+Sans+SC:wght@400;500;600;700&family=Ma+Shan+Zheng:wght@400&display=swap"
          rel="stylesheet"
        />
      </head>
      <body
        className="antialiased"
        style={{
          fontFamily: '"Noto Serif SC", "SimSun", "宋体", serif',
          fontSize: '16px',
          lineHeight: '1.6'
        }}
      >
        <PerformanceMonitor />
        <ThemeProvider>
          <AuthProvider>
            {children}
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
