"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useLocalStorage.ts":
/*!**************************************!*\
  !*** ./src/hooks/useLocalStorage.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFavorites: () => (/* binding */ useFavorites),\n/* harmony export */   useLocalStorage: () => (/* binding */ useLocalStorage),\n/* harmony export */   useRecentlyViewed: () => (/* binding */ useRecentlyViewed)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_useEffect_useState_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=useEffect,useState!=!react */ \"(app-pages-browser)/__barrel_optimize__?names=useEffect,useState!=!./node_modules/next/dist/compiled/react/index.js\");\n\n// 通用的localStorage hook\nfunction useLocalStorage(key, initialValue) {\n    // 添加客户端渲染状态\n    const [isClient, setIsClient] = (0,_barrel_optimize_names_useEffect_useState_react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // 获取初始值 - 服务器端始终返回初始值\n    const [storedValue, setStoredValue] = (0,_barrel_optimize_names_useEffect_useState_react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialValue);\n    // 客户端挂载后读取localStorage\n    (0,_barrel_optimize_names_useEffect_useState_react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useLocalStorage.useEffect\": ()=>{\n            setIsClient(true);\n            try {\n                const item = window.localStorage.getItem(key);\n                if (item) {\n                    setStoredValue(JSON.parse(item));\n                }\n            } catch (error) {\n                console.error('Error reading localStorage key \"'.concat(key, '\":'), error);\n            }\n        }\n    }[\"useLocalStorage.useEffect\"], [\n        key\n    ]);\n    // 设置值的函数\n    const setValue = (value)=>{\n        try {\n            const valueToStore = value instanceof Function ? value(storedValue) : value;\n            setStoredValue(valueToStore);\n            if (true) {\n                window.localStorage.setItem(key, JSON.stringify(valueToStore));\n            }\n        } catch (error) {\n            console.error('Error setting localStorage key \"'.concat(key, '\":'), error);\n        }\n    };\n    return [\n        storedValue,\n        setValue\n    ];\n}\n// 收藏功能hook\nfunction useFavorites() {\n    const [favorites, setFavorites] = useLocalStorage('opera-mask-favorites', []);\n    const toggleFavorite = (maskId)=>{\n        setFavorites((prev)=>{\n            if (prev.includes(maskId)) {\n                return prev.filter((id)=>id !== maskId);\n            } else {\n                return [\n                    ...prev,\n                    maskId\n                ];\n            }\n        });\n    };\n    const isFavorite = (maskId)=>{\n        return favorites.includes(maskId);\n    };\n    return {\n        favorites,\n        toggleFavorite,\n        isFavorite\n    };\n}\n// 最近浏览功能hook\nfunction useRecentlyViewed() {\n    const [recentlyViewed, setRecentlyViewed] = useLocalStorage('opera-mask-recent', []);\n    const addToRecentlyViewed = (maskId)=>{\n        setRecentlyViewed((prev)=>{\n            // 移除已存在的项目\n            const filtered = prev.filter((id)=>id !== maskId);\n            // 添加到开头，限制最多10个\n            return [\n                maskId,\n                ...filtered\n            ].slice(0, 10);\n        });\n    };\n    return {\n        recentlyViewed,\n        addToRecentlyViewed\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useLocalStorage.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/__barrel_optimize__?names=useEffect,useState!=!./node_modules/next/dist/compiled/react/index.js":
/*!*******************************************************************************************************!*\
  !*** __barrel_optimize__?names=useEffect,useState!=!./node_modules/next/dist/compiled/react/index.js ***!
  \*******************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var E_2_AIprogram_3_Augment_1_test1_beijing_opera_masks_node_modules_next_dist_compiled_react_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/react/index.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var E_2_AIprogram_3_Augment_1_test1_beijing_opera_masks_node_modules_next_dist_compiled_react_index_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(E_2_AIprogram_3_Augment_1_test1_beijing_opera_masks_node_modules_next_dist_compiled_react_index_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in E_2_AIprogram_3_Augment_1_test1_beijing_opera_masks_node_modules_next_dist_compiled_react_index_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => E_2_AIprogram_3_Augment_1_test1_beijing_opera_masks_node_modules_next_dist_compiled_react_index_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/__barrel_optimize__?names=useEffect,useState!=!./node_modules/next/dist/compiled/react/index.js\n"));

/***/ })

});