"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _data_masks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/masks */ \"(app-pages-browser)/./src/data/masks.ts\");\n/* harmony import */ var _services_maskService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/maskService */ \"(app-pages-browser)/./src/services/maskService.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/providers/ThemeProvider */ \"(app-pages-browser)/./src/components/providers/ThemeProvider.tsx\");\n/* harmony import */ var _components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/navigation/SimpleNavbar */ \"(app-pages-browser)/./src/components/navigation/SimpleNavbar.tsx\");\n/* harmony import */ var _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useLocalStorage */ \"(app-pages-browser)/./src/hooks/useLocalStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [masks, setMasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_data_masks__WEBPACK_IMPORTED_MODULE_3__.operaMasks);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedRole, setSelectedRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [showFavoritesOnly, setShowFavoritesOnly] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { colors } = (0,_components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const { favorites, toggleFavorite, isFavorite, isClient: favoritesClient } = (0,_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useFavorites)();\n    const { recentlyViewed, isClient: recentClient } = (0,_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useRecentlyViewed)();\n    // 加载脸谱数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const loadMasks = {\n                \"Home.useEffect.loadMasks\": async ()=>{\n                    if (!(0,_lib_supabase__WEBPACK_IMPORTED_MODULE_5__.isSupabaseConfigured)()) {\n                        console.log('Using static mask data');\n                        return;\n                    }\n                    setLoading(true);\n                    try {\n                        const maskData = await _services_maskService__WEBPACK_IMPORTED_MODULE_4__.MaskService.getAllApprovedMasks();\n                        setMasks(maskData);\n                    } catch (error) {\n                        console.error('Error loading masks:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"Home.useEffect.loadMasks\"];\n            loadMasks();\n        }\n    }[\"Home.useEffect\"], []);\n    // 筛选脸谱\n    const filteredMasks = masks.filter((mask)=>{\n        // 搜索筛选\n        if (searchTerm) {\n            const searchLower = searchTerm.toLowerCase();\n            const matchesName = mask.name.toLowerCase().includes(searchLower);\n            const matchesCharacter = mask.character.toLowerCase().includes(searchLower);\n            if (!matchesName && !matchesCharacter) return false;\n        }\n        // 角色筛选\n        if (selectedRole !== 'all') {\n            if (mask.roleCategory !== selectedRole) return false;\n        }\n        // 收藏筛选\n        if (showFavoritesOnly) {\n            if (!isFavorite(mask.id)) return false;\n        }\n        return true;\n    });\n    const handleMaskClick = (mask)=>{\n        // 导航到详情页面\n        router.push(\"/mask/\".concat(mask.id));\n    };\n    const handleFavoriteClick = (e, maskId)=>{\n        e.stopPropagation(); // 防止触发卡片点击\n        toggleFavorite(maskId);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            backgroundColor: colors.background,\n            color: colors.textPrimary\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__.SimpleNavbar, {}, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '2rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            marginBottom: '3rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontSize: '2.5rem',\n                                    fontWeight: 'bold',\n                                    marginBottom: '1rem',\n                                    background: 'linear-gradient(135deg, #DC2626, #B91C1C)',\n                                    WebkitBackgroundClip: 'text',\n                                    WebkitTextFillColor: 'transparent',\n                                    fontFamily: '\"Ma Shan Zheng\", cursive'\n                                },\n                                children: \"\\uD83C\\uDFAD 京剧脸谱文化展示\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: '1.125rem',\n                                    color: colors.textSecondary,\n                                    maxWidth: '600px',\n                                    margin: '0 auto'\n                                },\n                                children: \"探索中国传统京剧脸谱艺术的魅力，了解每个角色背后的文化内涵\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            maxWidth: '1200px',\n                            margin: '0 auto 3rem auto',\n                            padding: '0 2rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '1rem',\n                                    flexWrap: 'wrap',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    marginBottom: '2rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"搜索脸谱名称或角色...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        style: {\n                                            padding: '0.75rem 1rem',\n                                            borderRadius: '0.5rem',\n                                            border: \"1px solid \".concat(colors.border),\n                                            backgroundColor: colors.backgroundSecondary,\n                                            color: colors.textPrimary,\n                                            fontSize: '1rem',\n                                            minWidth: '250px'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedRole,\n                                        onChange: (e)=>setSelectedRole(e.target.value),\n                                        style: {\n                                            padding: '0.75rem 1rem',\n                                            borderRadius: '0.5rem',\n                                            border: \"1px solid \".concat(colors.border),\n                                            backgroundColor: colors.backgroundSecondary,\n                                            color: colors.textPrimary,\n                                            fontSize: '1rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"所有角色\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"生\",\n                                                children: \"生角\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"旦\",\n                                                children: \"旦角\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"净\",\n                                                children: \"净角\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"丑\",\n                                                children: \"丑角\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.5rem',\n                                            cursor: 'pointer',\n                                            fontSize: '1rem',\n                                            color: colors.textPrimary\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: showFavoritesOnly,\n                                                onChange: (e)=>setShowFavoritesOnly(e.target.checked),\n                                                style: {\n                                                    width: '18px',\n                                                    height: '18px',\n                                                    cursor: 'pointer'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 13\n                                            }, this),\n                                            \"仅显示收藏 (\",\n                                            favoritesClient ? favorites.length : 0,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center',\n                                    color: colors.textSecondary,\n                                    marginBottom: '1rem'\n                                },\n                                children: [\n                                    \"找到 \",\n                                    filteredMasks.length,\n                                    \" 个脸谱\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 9\n                            }, this),\n                            recentClient && recentlyViewed.length > 0 && !showFavoritesOnly && !searchTerm && selectedRole === 'all' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: colors.backgroundSecondary,\n                                    borderRadius: '12px',\n                                    padding: '1.5rem',\n                                    marginBottom: '2rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            fontSize: '1.125rem',\n                                            fontWeight: 'bold',\n                                            color: colors.textPrimary,\n                                            marginBottom: '1rem',\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.5rem'\n                                        },\n                                        children: \"\\uD83D\\uDD52 最近浏览\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '1rem',\n                                            overflowX: 'auto',\n                                            paddingBottom: '0.5rem'\n                                        },\n                                        children: recentlyViewed.slice(0, 5).map((maskId)=>{\n                                            var _recentMask_images, _recentMask_images1;\n                                            const recentMask = masks.find((m)=>m.id === maskId);\n                                            if (!recentMask) return null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                onClick: ()=>handleMaskClick(recentMask),\n                                                style: {\n                                                    minWidth: '120px',\n                                                    cursor: 'pointer',\n                                                    textAlign: 'center',\n                                                    transition: 'transform 0.2s ease'\n                                                },\n                                                onMouseEnter: (e)=>{\n                                                    e.currentTarget.style.transform = 'scale(1.05)';\n                                                },\n                                                onMouseLeave: (e)=>{\n                                                    e.currentTarget.style.transform = 'scale(1)';\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: ((_recentMask_images = recentMask.images) === null || _recentMask_images === void 0 ? void 0 : _recentMask_images.fullSize) || ((_recentMask_images1 = recentMask.images) === null || _recentMask_images1 === void 0 ? void 0 : _recentMask_images1.thumbnail),\n                                                        alt: recentMask.name,\n                                                        style: {\n                                                            width: '80px',\n                                                            height: '80px',\n                                                            borderRadius: '50%',\n                                                            objectFit: 'cover',\n                                                            marginBottom: '0.5rem',\n                                                            border: \"2px solid \".concat(colors.border)\n                                                        },\n                                                        onError: (e)=>{\n                                                            const target = e.target;\n                                                            target.src = \"https://via.placeholder.com/80x80/DC143C/FFFFFF?text=\".concat(encodeURIComponent(recentMask.name.charAt(0)));\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: '0.75rem',\n                                                            color: colors.textSecondary,\n                                                            whiteSpace: 'nowrap',\n                                                            overflow: 'hidden',\n                                                            textOverflow: 'ellipsis'\n                                                        },\n                                                        children: recentMask.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, maskId, true, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 7\n                    }, this),\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            padding: '2rem',\n                            color: colors.textSecondary\n                        },\n                        children: \"正在加载脸谱数据...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'grid',\n                            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                            gap: '2rem',\n                            maxWidth: '1200px',\n                            margin: '0 auto'\n                        },\n                        children: filteredMasks.map((mask)=>{\n                            var _mask_images, _mask_images1;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>handleMaskClick(mask),\n                                style: {\n                                    backgroundColor: colors.backgroundSecondary,\n                                    borderRadius: '12px',\n                                    padding: '1.5rem',\n                                    cursor: 'pointer',\n                                    transition: 'all 0.3s ease',\n                                    border: \"1px solid \".concat(colors.border),\n                                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n                                    position: 'relative'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>handleFavoriteClick(e, mask.id),\n                                        style: {\n                                            position: 'absolute',\n                                            top: '1rem',\n                                            right: '1rem',\n                                            backgroundColor: 'transparent',\n                                            border: 'none',\n                                            fontSize: '1.5rem',\n                                            cursor: 'pointer',\n                                            padding: '0.25rem',\n                                            borderRadius: '50%',\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            justifyContent: 'center',\n                                            transition: 'all 0.2s ease'\n                                        },\n                                        title: isFavorite(mask.id) ? '取消收藏' : '添加收藏',\n                                        children: isFavorite(mask.id) ? '❤️' : '🤍'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: '100%',\n                                            height: '200px',\n                                            borderRadius: '8px',\n                                            overflow: 'hidden',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: ((_mask_images = mask.images) === null || _mask_images === void 0 ? void 0 : _mask_images.fullSize) || mask.imageUrl || ((_mask_images1 = mask.images) === null || _mask_images1 === void 0 ? void 0 : _mask_images1.thumbnail),\n                                            alt: mask.name,\n                                            style: {\n                                                width: '100%',\n                                                height: '100%',\n                                                objectFit: 'cover'\n                                            },\n                                            onError: (e)=>{\n                                                // 图片加载失败时的备用处理\n                                                const target = e.target;\n                                                target.src = \"https://via.placeholder.com/300x300/DC143C/FFFFFF?text=\".concat(encodeURIComponent(mask.name));\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            fontSize: '1.25rem',\n                                            fontWeight: 'bold',\n                                            marginBottom: '0.5rem',\n                                            color: colors.textPrimary\n                                        },\n                                        children: mask.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            fontSize: '0.875rem',\n                                            color: colors.textSecondary,\n                                            marginBottom: '1rem'\n                                        },\n                                        children: [\n                                            \"角色: \",\n                                            mask.character\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 13\n                                    }, this),\n                                    (mask.colorTheme || mask.mainColors) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '0.5rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: (mask.colorTheme || mask.mainColors || []).slice(0, 3).map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: '20px',\n                                                    height: '20px',\n                                                    borderRadius: '50%',\n                                                    backgroundColor: color,\n                                                    border: '1px solid rgba(0,0,0,0.1)'\n                                                },\n                                                title: color\n                                            }, index, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this),\n                                    (mask.personalityTraits || mask.tags) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            flexWrap: 'wrap',\n                                            gap: '0.5rem'\n                                        },\n                                        children: (mask.personalityTraits || mask.tags || []).slice(0, 3).map((trait, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.75rem',\n                                                    padding: '0.25rem 0.5rem',\n                                                    backgroundColor: colors.primary + '20',\n                                                    color: colors.primary,\n                                                    borderRadius: '12px'\n                                                },\n                                                children: trait\n                                            }, index, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, mask.id, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"Rh5M2v2fUtdFaR9gDEv9eH0ssCg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__.useTheme,\n        _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useFavorites,\n        _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useRecentlyViewed\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});