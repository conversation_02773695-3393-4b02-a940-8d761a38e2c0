"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/mask/[id]/page",{

/***/ "(app-pages-browser)/./src/app/mask/[id]/page.tsx":
/*!************************************!*\
  !*** ./src/app/mask/[id]/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MaskDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _data_masks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/masks */ \"(app-pages-browser)/./src/data/masks.ts\");\n/* harmony import */ var _services_maskService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/maskService */ \"(app-pages-browser)/./src/services/maskService.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/providers/ThemeProvider */ \"(app-pages-browser)/./src/components/providers/ThemeProvider.tsx\");\n/* harmony import */ var _components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/navigation/SimpleNavbar */ \"(app-pages-browser)/./src/components/navigation/SimpleNavbar.tsx\");\n/* harmony import */ var _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useLocalStorage */ \"(app-pages-browser)/./src/hooks/useLocalStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction MaskDetailPage() {\n    var _mask_images, _mask_images1;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const maskId = params.id;\n    const [mask, setMask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAnimation, setShowAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { colors } = (0,_components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const { toggleFavorite, isFavorite } = (0,_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useFavorites)();\n    const { addToRecentlyViewed } = (0,_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useRecentlyViewed)();\n    // 加载脸谱数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MaskDetailPage.useEffect\": ()=>{\n            const loadMask = {\n                \"MaskDetailPage.useEffect.loadMask\": async ()=>{\n                    try {\n                        let maskData = _data_masks__WEBPACK_IMPORTED_MODULE_3__.operaMasks; // 默认使用静态数据\n                        if ((0,_lib_supabase__WEBPACK_IMPORTED_MODULE_5__.isSupabaseConfigured)()) {\n                            try {\n                                maskData = await _services_maskService__WEBPACK_IMPORTED_MODULE_4__.MaskService.getAllApprovedMasks();\n                            } catch (error) {\n                                console.error('Error loading masks from database:', error);\n                            }\n                        }\n                        const foundMask = maskData.find({\n                            \"MaskDetailPage.useEffect.loadMask.foundMask\": (m)=>m.id === maskId\n                        }[\"MaskDetailPage.useEffect.loadMask.foundMask\"]);\n                        setMask(foundMask || null);\n                        // 添加到最近浏览记录\n                        if (foundMask) {\n                            addToRecentlyViewed(foundMask.id);\n                        }\n                    } catch (error) {\n                        console.error('Error loading mask:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"MaskDetailPage.useEffect.loadMask\"];\n            loadMask();\n        }\n    }[\"MaskDetailPage.useEffect\"], [\n        maskId,\n        addToRecentlyViewed\n    ]);\n    // 加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                backgroundColor: colors.background,\n                color: colors.textPrimary\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__.SimpleNavbar, {\n                    showBackButton: true,\n                    title: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        minHeight: 'calc(100vh - 80px)'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '3rem',\n                                    marginBottom: '1rem',\n                                    animation: 'pulse 2s infinite'\n                                },\n                                children: \"\\uD83C\\uDFAD\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"正在加载脸谱信息...\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this);\n    }\n    // 脸谱未找到\n    if (!mask) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                backgroundColor: colors.background,\n                color: colors.textPrimary\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__.SimpleNavbar, {\n                    showBackButton: true,\n                    title: \"脸谱未找到\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        minHeight: 'calc(100vh - 80px)'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontSize: '2rem',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"脸谱未找到\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    color: colors.textSecondary,\n                                    marginBottom: '2rem'\n                                },\n                                children: \"抱歉，您访问的脸谱不存在。\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/'),\n                                style: {\n                                    backgroundColor: colors.primary,\n                                    color: 'white',\n                                    padding: '0.75rem 1.5rem',\n                                    borderRadius: '0.5rem',\n                                    border: 'none',\n                                    cursor: 'pointer',\n                                    fontSize: '1rem'\n                                },\n                                children: \"返回首页\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            backgroundColor: colors.background,\n            color: colors.textPrimary\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__.SimpleNavbar, {\n                showBackButton: true,\n                title: mask.name\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '1200px',\n                    margin: '0 auto',\n                    padding: '2rem'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'grid',\n                        gridTemplateColumns: '1fr 1fr',\n                        gap: '3rem',\n                        '@media (max-width: 768px)': {\n                            gridTemplateColumns: '1fr'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: colors.backgroundSecondary,\n                                    borderRadius: '12px',\n                                    padding: '2rem',\n                                    textAlign: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: ((_mask_images = mask.images) === null || _mask_images === void 0 ? void 0 : _mask_images.fullSize) || mask.imageUrl || ((_mask_images1 = mask.images) === null || _mask_images1 === void 0 ? void 0 : _mask_images1.thumbnail),\n                                        alt: mask.name,\n                                        style: {\n                                            width: '100%',\n                                            maxWidth: '400px',\n                                            height: 'auto',\n                                            borderRadius: '8px',\n                                            marginBottom: '1rem'\n                                        },\n                                        onError: (e)=>{\n                                            const target = e.target;\n                                            target.src = \"https://via.placeholder.com/400x400/DC143C/FFFFFF?text=\".concat(encodeURIComponent(mask.name));\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '1rem',\n                                            marginTop: '1rem',\n                                            justifyContent: 'center'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowAnimation(!showAnimation),\n                                                style: {\n                                                    backgroundColor: colors.primary,\n                                                    color: 'white',\n                                                    padding: '0.75rem 1.5rem',\n                                                    borderRadius: '0.5rem',\n                                                    border: 'none',\n                                                    cursor: 'pointer',\n                                                    fontSize: '1rem'\n                                                },\n                                                children: showAnimation ? '隐藏绘制过程' : '观看绘制过程'\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleFavorite(mask.id),\n                                                style: {\n                                                    backgroundColor: isFavorite(mask.id) ? colors.primary : 'transparent',\n                                                    color: isFavorite(mask.id) ? 'white' : colors.primary,\n                                                    border: \"2px solid \".concat(colors.primary),\n                                                    padding: '0.75rem 1.5rem',\n                                                    borderRadius: '0.5rem',\n                                                    cursor: 'pointer',\n                                                    fontSize: '1rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '0.5rem'\n                                                },\n                                                children: isFavorite(mask.id) ? '❤️ 已收藏' : '🤍 收藏'\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: colors.backgroundSecondary,\n                                        borderRadius: '12px',\n                                        padding: '2rem',\n                                        marginBottom: '2rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            style: {\n                                                fontSize: '1.5rem',\n                                                fontWeight: 'bold',\n                                                marginBottom: '1rem',\n                                                color: colors.textPrimary\n                                            },\n                                            children: \"基本信息\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                marginBottom: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"角色：\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" \",\n                                                mask.character\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                marginBottom: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"行当：\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" \",\n                                                mask.roleCategory\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                marginBottom: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"色彩分类：\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" \",\n                                                mask.colorCategory\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this),\n                                        mask.mainColors && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                marginBottom: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"主要色彩：\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        gap: '0.5rem',\n                                                        marginTop: '0.5rem'\n                                                    },\n                                                    children: mask.mainColors.map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                width: '30px',\n                                                                height: '30px',\n                                                                borderRadius: '50%',\n                                                                backgroundColor: color,\n                                                                border: '2px solid rgba(0,0,0,0.1)'\n                                                            },\n                                                            title: color\n                                                        }, index, false, {\n                                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: colors.backgroundSecondary,\n                                        borderRadius: '12px',\n                                        padding: '2rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            style: {\n                                                fontSize: '1.5rem',\n                                                fontWeight: 'bold',\n                                                marginBottom: '1rem',\n                                                color: colors.textPrimary\n                                            },\n                                            children: \"文化背景\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                lineHeight: '1.6'\n                                            },\n                                            children: typeof mask.culturalBackground === 'string' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: mask.culturalBackground\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '1rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"历史起源：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                style: {\n                                                                    marginTop: '0.5rem'\n                                                                },\n                                                                children: mask.culturalBackground.origin\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '1rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"性格特点：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                style: {\n                                                                    marginTop: '0.5rem'\n                                                                },\n                                                                children: mask.culturalBackground.personality\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '1rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"象征意义：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                style: {\n                                                                    marginTop: '0.5rem'\n                                                                },\n                                                                children: mask.culturalBackground.symbolism\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\n_s(MaskDetailPage, \"1OydzzeE+F7DM5O1RwNKSjUnD/s=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__.useTheme,\n        _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useFavorites,\n        _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useRecentlyViewed\n    ];\n});\n_c = MaskDetailPage;\nvar _c;\n$RefreshReg$(_c, \"MaskDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbWFzay9baWRdL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFbUQ7QUFDSTtBQUNiO0FBRVc7QUFDQztBQUNVO0FBQ0k7QUFDTTtBQUUzRCxTQUFTWTtRQWtKSEMsY0FBMENBOztJQWpKN0QsTUFBTUMsU0FBU1gsMERBQVNBO0lBQ3hCLE1BQU1ZLFNBQVNYLDBEQUFTQTtJQUN4QixNQUFNWSxTQUFTRixPQUFPRyxFQUFFO0lBQ3hCLE1BQU0sQ0FBQ0osTUFBTUssUUFBUSxHQUFHakIsK0NBQVFBLENBQW1CO0lBQ25ELE1BQU0sQ0FBQ2tCLFNBQVNDLFdBQVcsR0FBR25CLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ29CLGVBQWVDLGlCQUFpQixHQUFHckIsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxFQUFFc0IsTUFBTSxFQUFFLEdBQUdmLDZFQUFRQTtJQUMzQixNQUFNLEVBQUVnQixjQUFjLEVBQUVDLFVBQVUsRUFBRSxHQUFHZixvRUFBWUE7SUFDbkQsTUFBTSxFQUFFZ0IsbUJBQW1CLEVBQUUsR0FBR2YseUVBQWlCQTtJQUVqRCxTQUFTO0lBQ1RULGdEQUFTQTtvQ0FBQztZQUNSLE1BQU15QjtxREFBVztvQkFDZixJQUFJO3dCQUNGLElBQUlDLFdBQVd2QixtREFBVUEsRUFBRSxXQUFXO3dCQUV0QyxJQUFJRSxtRUFBb0JBLElBQUk7NEJBQzFCLElBQUk7Z0NBQ0ZxQixXQUFXLE1BQU10Qiw4REFBV0EsQ0FBQ3VCLG1CQUFtQjs0QkFDbEQsRUFBRSxPQUFPQyxPQUFPO2dDQUNkQyxRQUFRRCxLQUFLLENBQUMsc0NBQXNDQTs0QkFDdEQ7d0JBQ0Y7d0JBRUEsTUFBTUUsWUFBWUosU0FBU0ssSUFBSTsyRUFBQ0MsQ0FBQUEsSUFBS0EsRUFBRWpCLEVBQUUsS0FBS0Q7O3dCQUM5Q0UsUUFBUWMsYUFBYTt3QkFFckIsWUFBWTt3QkFDWixJQUFJQSxXQUFXOzRCQUNiTixvQkFBb0JNLFVBQVVmLEVBQUU7d0JBQ2xDO29CQUNGLEVBQUUsT0FBT2EsT0FBTzt3QkFDZEMsUUFBUUQsS0FBSyxDQUFDLHVCQUF1QkE7b0JBQ3ZDLFNBQVU7d0JBQ1JWLFdBQVc7b0JBQ2I7Z0JBQ0Y7O1lBRUFPO1FBQ0Y7bUNBQUc7UUFBQ1g7UUFBUVU7S0FBb0I7SUFFaEMsT0FBTztJQUNQLElBQUlQLFNBQVM7UUFDWCxxQkFDRSw4REFBQ2dCO1lBQUlDLE9BQU87Z0JBQ1ZDLFdBQVc7Z0JBQ1hDLGlCQUFpQmYsT0FBT2dCLFVBQVU7Z0JBQ2xDQyxPQUFPakIsT0FBT2tCLFdBQVc7WUFDM0I7OzhCQUNFLDhEQUFDaEMsNkVBQVlBO29CQUFDaUMsY0FBYztvQkFBQ0MsT0FBTTs7Ozs7OzhCQUNuQyw4REFBQ1I7b0JBQUlDLE9BQU87d0JBQ1ZRLFNBQVM7d0JBQ1RDLFlBQVk7d0JBQ1pDLGdCQUFnQjt3QkFDaEJULFdBQVc7b0JBQ2I7OEJBQ0UsNEVBQUNGO3dCQUFJQyxPQUFPOzRCQUFFVyxXQUFXO3dCQUFTOzswQ0FDaEMsOERBQUNaO2dDQUFJQyxPQUFPO29DQUNWWSxVQUFVO29DQUNWQyxjQUFjO29DQUNkQyxXQUFXO2dDQUNiOzBDQUFHOzs7Ozs7MENBR0gsOERBQUNDOzBDQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUtiO0lBRUEsUUFBUTtJQUNSLElBQUksQ0FBQ3RDLE1BQU07UUFDVCxxQkFDRSw4REFBQ3NCO1lBQUlDLE9BQU87Z0JBQ1ZDLFdBQVc7Z0JBQ1hDLGlCQUFpQmYsT0FBT2dCLFVBQVU7Z0JBQ2xDQyxPQUFPakIsT0FBT2tCLFdBQVc7WUFDM0I7OzhCQUNFLDhEQUFDaEMsNkVBQVlBO29CQUFDaUMsY0FBYztvQkFBQ0MsT0FBTTs7Ozs7OzhCQUNuQyw4REFBQ1I7b0JBQUlDLE9BQU87d0JBQ1ZRLFNBQVM7d0JBQ1RDLFlBQVk7d0JBQ1pDLGdCQUFnQjt3QkFDaEJULFdBQVc7b0JBQ2I7OEJBQ0UsNEVBQUNGO3dCQUFJQyxPQUFPOzRCQUFFVyxXQUFXO3dCQUFTOzswQ0FDaEMsOERBQUNLO2dDQUFHaEIsT0FBTztvQ0FBRVksVUFBVTtvQ0FBUUMsY0FBYztnQ0FBTzswQ0FBRzs7Ozs7OzBDQUd2RCw4REFBQ0U7Z0NBQUVmLE9BQU87b0NBQUVJLE9BQU9qQixPQUFPOEIsYUFBYTtvQ0FBRUosY0FBYztnQ0FBTzswQ0FBRzs7Ozs7OzBDQUdqRSw4REFBQ0s7Z0NBQ0NDLFNBQVMsSUFBTXhDLE9BQU95QyxJQUFJLENBQUM7Z0NBQzNCcEIsT0FBTztvQ0FDTEUsaUJBQWlCZixPQUFPa0MsT0FBTztvQ0FDL0JqQixPQUFPO29DQUNQa0IsU0FBUztvQ0FDVEMsY0FBYztvQ0FDZEMsUUFBUTtvQ0FDUkMsUUFBUTtvQ0FDUmIsVUFBVTtnQ0FDWjswQ0FDRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFPWDtJQUVBLHFCQUNFLDhEQUFDYjtRQUFJQyxPQUFPO1lBQ1ZDLFdBQVc7WUFDWEMsaUJBQWlCZixPQUFPZ0IsVUFBVTtZQUNsQ0MsT0FBT2pCLE9BQU9rQixXQUFXO1FBQzNCOzswQkFFRSw4REFBQ2hDLDZFQUFZQTtnQkFBQ2lDLGNBQWM7Z0JBQUNDLE9BQU85QixLQUFLaUQsSUFBSTs7Ozs7OzBCQUc3Qyw4REFBQzNCO2dCQUFJQyxPQUFPO29CQUNWMkIsVUFBVTtvQkFDVkMsUUFBUTtvQkFDUk4sU0FBUztnQkFDWDswQkFDRSw0RUFBQ3ZCO29CQUFJQyxPQUFPO3dCQUNWUSxTQUFTO3dCQUNUcUIscUJBQXFCO3dCQUNyQkMsS0FBSzt3QkFDTCw2QkFBNkI7NEJBQzNCRCxxQkFBcUI7d0JBQ3ZCO29CQUNGOztzQ0FFRSw4REFBQzlCO3NDQUNDLDRFQUFDQTtnQ0FBSUMsT0FBTztvQ0FDVkUsaUJBQWlCZixPQUFPNEMsbUJBQW1CO29DQUMzQ1IsY0FBYztvQ0FDZEQsU0FBUztvQ0FDVFgsV0FBVztnQ0FDYjs7a0RBQ0UsOERBQUNxQjt3Q0FDQ0MsS0FBS3hELEVBQUFBLGVBQUFBLEtBQUt5RCxNQUFNLGNBQVh6RCxtQ0FBQUEsYUFBYTBELFFBQVEsS0FBSTFELEtBQUsyRCxRQUFRLE1BQUkzRCxnQkFBQUEsS0FBS3lELE1BQU0sY0FBWHpELG9DQUFBQSxjQUFhNEQsU0FBUzt3Q0FDckVDLEtBQUs3RCxLQUFLaUQsSUFBSTt3Q0FDZDFCLE9BQU87NENBQ0x1QyxPQUFPOzRDQUNQWixVQUFVOzRDQUNWYSxRQUFROzRDQUNSakIsY0FBYzs0Q0FDZFYsY0FBYzt3Q0FDaEI7d0NBQ0E0QixTQUFTLENBQUNDOzRDQUNSLE1BQU1DLFNBQVNELEVBQUVDLE1BQU07NENBQ3ZCQSxPQUFPVixHQUFHLEdBQUcsMERBQXdGLE9BQTlCVyxtQkFBbUJuRSxLQUFLaUQsSUFBSTt3Q0FDckc7Ozs7OztrREFJRiw4REFBQzNCO3dDQUFJQyxPQUFPOzRDQUNWUSxTQUFTOzRDQUNUc0IsS0FBSzs0Q0FDTGUsV0FBVzs0Q0FDWG5DLGdCQUFnQjt3Q0FDbEI7OzBEQUNFLDhEQUFDUTtnREFDQ0MsU0FBUyxJQUFNakMsaUJBQWlCLENBQUNEO2dEQUNqQ2UsT0FBTztvREFDTEUsaUJBQWlCZixPQUFPa0MsT0FBTztvREFDL0JqQixPQUFPO29EQUNQa0IsU0FBUztvREFDVEMsY0FBYztvREFDZEMsUUFBUTtvREFDUkMsUUFBUTtvREFDUmIsVUFBVTtnREFDWjswREFFQzNCLGdCQUFnQixXQUFXOzs7Ozs7MERBRzlCLDhEQUFDaUM7Z0RBQ0NDLFNBQVMsSUFBTS9CLGVBQWVYLEtBQUtJLEVBQUU7Z0RBQ3JDbUIsT0FBTztvREFDTEUsaUJBQWlCYixXQUFXWixLQUFLSSxFQUFFLElBQUlNLE9BQU9rQyxPQUFPLEdBQUc7b0RBQ3hEakIsT0FBT2YsV0FBV1osS0FBS0ksRUFBRSxJQUFJLFVBQVVNLE9BQU9rQyxPQUFPO29EQUNyREcsUUFBUSxhQUE0QixPQUFmckMsT0FBT2tDLE9BQU87b0RBQ25DQyxTQUFTO29EQUNUQyxjQUFjO29EQUNkRSxRQUFRO29EQUNSYixVQUFVO29EQUNWSixTQUFTO29EQUNUQyxZQUFZO29EQUNacUIsS0FBSztnREFDUDswREFFQ3pDLFdBQVdaLEtBQUtJLEVBQUUsSUFBSSxXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPMUMsOERBQUNrQjs7OENBQ0MsOERBQUNBO29DQUFJQyxPQUFPO3dDQUNWRSxpQkFBaUJmLE9BQU80QyxtQkFBbUI7d0NBQzNDUixjQUFjO3dDQUNkRCxTQUFTO3dDQUNUVCxjQUFjO29DQUNoQjs7c0RBQ0UsOERBQUNpQzs0Q0FBRzlDLE9BQU87Z0RBQ1RZLFVBQVU7Z0RBQ1ZtQyxZQUFZO2dEQUNabEMsY0FBYztnREFDZFQsT0FBT2pCLE9BQU9rQixXQUFXOzRDQUMzQjtzREFBRzs7Ozs7O3NEQUlILDhEQUFDTjs0Q0FBSUMsT0FBTztnREFBRWEsY0FBYzs0Q0FBTzs7OERBQ2pDLDhEQUFDbUM7OERBQU87Ozs7OztnREFBWTtnREFBRXZFLEtBQUt3RSxTQUFTOzs7Ozs7O3NEQUd0Qyw4REFBQ2xEOzRDQUFJQyxPQUFPO2dEQUFFYSxjQUFjOzRDQUFPOzs4REFDakMsOERBQUNtQzs4REFBTzs7Ozs7O2dEQUFZO2dEQUFFdkUsS0FBS3lFLFlBQVk7Ozs7Ozs7c0RBR3pDLDhEQUFDbkQ7NENBQUlDLE9BQU87Z0RBQUVhLGNBQWM7NENBQU87OzhEQUNqQyw4REFBQ21DOzhEQUFPOzs7Ozs7Z0RBQWM7Z0RBQUV2RSxLQUFLMEUsYUFBYTs7Ozs7Ozt3Q0FJM0MxRSxLQUFLMkUsVUFBVSxrQkFDZCw4REFBQ3JEOzRDQUFJQyxPQUFPO2dEQUFFYSxjQUFjOzRDQUFPOzs4REFDakMsOERBQUNtQzs4REFBTzs7Ozs7OzhEQUNSLDhEQUFDakQ7b0RBQUlDLE9BQU87d0RBQUVRLFNBQVM7d0RBQVFzQixLQUFLO3dEQUFVZSxXQUFXO29EQUFTOzhEQUMvRHBFLEtBQUsyRSxVQUFVLENBQUNDLEdBQUcsQ0FBQyxDQUFDakQsT0FBT2tELHNCQUMzQiw4REFBQ3ZEOzREQUVDQyxPQUFPO2dFQUNMdUMsT0FBTztnRUFDUEMsUUFBUTtnRUFDUmpCLGNBQWM7Z0VBQ2RyQixpQkFBaUJFO2dFQUNqQm9CLFFBQVE7NERBQ1Y7NERBQ0FqQixPQUFPSDsyREFSRmtEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQWlCakIsOERBQUN2RDtvQ0FBSUMsT0FBTzt3Q0FDVkUsaUJBQWlCZixPQUFPNEMsbUJBQW1CO3dDQUMzQ1IsY0FBYzt3Q0FDZEQsU0FBUztvQ0FDWDs7c0RBQ0UsOERBQUN3Qjs0Q0FBRzlDLE9BQU87Z0RBQ1RZLFVBQVU7Z0RBQ1ZtQyxZQUFZO2dEQUNabEMsY0FBYztnREFDZFQsT0FBT2pCLE9BQU9rQixXQUFXOzRDQUMzQjtzREFBRzs7Ozs7O3NEQUlILDhEQUFDTjs0Q0FBSUMsT0FBTztnREFBRXVELFlBQVk7NENBQU07c0RBQzdCLE9BQU85RSxLQUFLK0Usa0JBQWtCLEtBQUsseUJBQ2xDLDhEQUFDekM7MERBQUd0QyxLQUFLK0Usa0JBQWtCOzs7OztxRUFFM0I7O2tFQUNFLDhEQUFDekQ7d0RBQUlDLE9BQU87NERBQUVhLGNBQWM7d0RBQU87OzBFQUNqQyw4REFBQ21DOzBFQUFPOzs7Ozs7MEVBQ1IsOERBQUNqQztnRUFBRWYsT0FBTztvRUFBRTZDLFdBQVc7Z0VBQVM7MEVBQUlwRSxLQUFLK0Usa0JBQWtCLENBQUNDLE1BQU07Ozs7Ozs7Ozs7OztrRUFHcEUsOERBQUMxRDt3REFBSUMsT0FBTzs0REFBRWEsY0FBYzt3REFBTzs7MEVBQ2pDLDhEQUFDbUM7MEVBQU87Ozs7OzswRUFDUiw4REFBQ2pDO2dFQUFFZixPQUFPO29FQUFFNkMsV0FBVztnRUFBUzswRUFBSXBFLEtBQUsrRSxrQkFBa0IsQ0FBQ0UsV0FBVzs7Ozs7Ozs7Ozs7O2tFQUd6RSw4REFBQzNEO3dEQUFJQyxPQUFPOzREQUFFYSxjQUFjO3dEQUFPOzswRUFDakMsOERBQUNtQzswRUFBTzs7Ozs7OzBFQUNSLDhEQUFDakM7Z0VBQUVmLE9BQU87b0VBQUU2QyxXQUFXO2dFQUFTOzBFQUFJcEUsS0FBSytFLGtCQUFrQixDQUFDRyxTQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFXM0Y7R0EzU3dCbkY7O1FBQ1BULHNEQUFTQTtRQUNUQyxzREFBU0E7UUFLTEkseUVBQVFBO1FBQ1lFLGdFQUFZQTtRQUNuQkMscUVBQWlCQTs7O0tBVDNCQyIsInNvdXJjZXMiOlsiRTpcXDItQUlwcm9ncmFtXFwzLUF1Z21lbnRcXDEtdGVzdDFcXGJlaWppbmctb3BlcmEtbWFza3NcXHNyY1xcYXBwXFxtYXNrXFxbaWRdXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUGFyYW1zLCB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xuaW1wb3J0IHsgb3BlcmFNYXNrcyB9IGZyb20gJ0AvZGF0YS9tYXNrcyc7XG5pbXBvcnQgeyBPcGVyYU1hc2sgfSBmcm9tICdAL3R5cGVzL21hc2snO1xuaW1wb3J0IHsgTWFza1NlcnZpY2UgfSBmcm9tICdAL3NlcnZpY2VzL21hc2tTZXJ2aWNlJztcbmltcG9ydCB7IGlzU3VwYWJhc2VDb25maWd1cmVkIH0gZnJvbSAnQC9saWIvc3VwYWJhc2UnO1xuaW1wb3J0IHsgdXNlVGhlbWUgfSBmcm9tICdAL2NvbXBvbmVudHMvcHJvdmlkZXJzL1RoZW1lUHJvdmlkZXInO1xuaW1wb3J0IHsgU2ltcGxlTmF2YmFyIH0gZnJvbSAnQC9jb21wb25lbnRzL25hdmlnYXRpb24vU2ltcGxlTmF2YmFyJztcbmltcG9ydCB7IHVzZUZhdm9yaXRlcywgdXNlUmVjZW50bHlWaWV3ZWQgfSBmcm9tICdAL2hvb2tzL3VzZUxvY2FsU3RvcmFnZSc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE1hc2tEZXRhaWxQYWdlKCkge1xuICBjb25zdCBwYXJhbXMgPSB1c2VQYXJhbXMoKTtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IG1hc2tJZCA9IHBhcmFtcy5pZCBhcyBzdHJpbmc7XG4gIGNvbnN0IFttYXNrLCBzZXRNYXNrXSA9IHVzZVN0YXRlPE9wZXJhTWFzayB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW3Nob3dBbmltYXRpb24sIHNldFNob3dBbmltYXRpb25dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCB7IGNvbG9ycyB9ID0gdXNlVGhlbWUoKTtcbiAgY29uc3QgeyB0b2dnbGVGYXZvcml0ZSwgaXNGYXZvcml0ZSB9ID0gdXNlRmF2b3JpdGVzKCk7XG4gIGNvbnN0IHsgYWRkVG9SZWNlbnRseVZpZXdlZCB9ID0gdXNlUmVjZW50bHlWaWV3ZWQoKTtcblxuICAvLyDliqDovb3ohLjosLHmlbDmja5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBsb2FkTWFzayA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGxldCBtYXNrRGF0YSA9IG9wZXJhTWFza3M7IC8vIOm7mOiupOS9v+eUqOmdmeaAgeaVsOaNrlxuICAgICAgICBcbiAgICAgICAgaWYgKGlzU3VwYWJhc2VDb25maWd1cmVkKCkpIHtcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgbWFza0RhdGEgPSBhd2FpdCBNYXNrU2VydmljZS5nZXRBbGxBcHByb3ZlZE1hc2tzKCk7XG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgbWFza3MgZnJvbSBkYXRhYmFzZTonLCBlcnJvcik7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICBjb25zdCBmb3VuZE1hc2sgPSBtYXNrRGF0YS5maW5kKG0gPT4gbS5pZCA9PT0gbWFza0lkKTtcbiAgICAgICAgc2V0TWFzayhmb3VuZE1hc2sgfHwgbnVsbCk7XG5cbiAgICAgICAgLy8g5re75Yqg5Yiw5pyA6L+R5rWP6KeI6K6w5b2VXG4gICAgICAgIGlmIChmb3VuZE1hc2spIHtcbiAgICAgICAgICBhZGRUb1JlY2VudGx5Vmlld2VkKGZvdW5kTWFzay5pZCk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgbWFzazonLCBlcnJvcik7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgbG9hZE1hc2soKTtcbiAgfSwgW21hc2tJZCwgYWRkVG9SZWNlbnRseVZpZXdlZF0pO1xuXG4gIC8vIOWKoOi9veeKtuaAgVxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgIG1pbkhlaWdodDogJzEwMHZoJyxcbiAgICAgICAgYmFja2dyb3VuZENvbG9yOiBjb2xvcnMuYmFja2dyb3VuZCxcbiAgICAgICAgY29sb3I6IGNvbG9ycy50ZXh0UHJpbWFyeVxuICAgICAgfX0+XG4gICAgICAgIDxTaW1wbGVOYXZiYXIgc2hvd0JhY2tCdXR0b24gdGl0bGU9XCLliqDovb3kuK0uLi5cIiAvPlxuICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgICBtaW5IZWlnaHQ6ICdjYWxjKDEwMHZoIC0gODBweCknXG4gICAgICAgIH19PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgdGV4dEFsaWduOiAnY2VudGVyJyB9fT5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgXG4gICAgICAgICAgICAgIGZvbnRTaXplOiAnM3JlbScsIFxuICAgICAgICAgICAgICBtYXJnaW5Cb3R0b206ICcxcmVtJyxcbiAgICAgICAgICAgICAgYW5pbWF0aW9uOiAncHVsc2UgMnMgaW5maW5pdGUnXG4gICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAg8J+OrVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8cD7mraPlnKjliqDovb3ohLjosLHkv6Hmga8uLi48L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIC8vIOiEuOiwseacquaJvuWIsFxuICBpZiAoIW1hc2spIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICBtaW5IZWlnaHQ6ICcxMDB2aCcsXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogY29sb3JzLmJhY2tncm91bmQsXG4gICAgICAgIGNvbG9yOiBjb2xvcnMudGV4dFByaW1hcnlcbiAgICAgIH19PlxuICAgICAgICA8U2ltcGxlTmF2YmFyIHNob3dCYWNrQnV0dG9uIHRpdGxlPVwi6IS46LCx5pyq5om+5YiwXCIgLz5cbiAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgICAgbWluSGVpZ2h0OiAnY2FsYygxMDB2aCAtIDgwcHgpJ1xuICAgICAgICB9fT5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IHRleHRBbGlnbjogJ2NlbnRlcicgfX0+XG4gICAgICAgICAgICA8aDEgc3R5bGU9e3sgZm9udFNpemU6ICcycmVtJywgbWFyZ2luQm90dG9tOiAnMXJlbScgfX0+XG4gICAgICAgICAgICAgIOiEuOiwseacquaJvuWIsFxuICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgIDxwIHN0eWxlPXt7IGNvbG9yOiBjb2xvcnMudGV4dFNlY29uZGFyeSwgbWFyZ2luQm90dG9tOiAnMnJlbScgfX0+XG4gICAgICAgICAgICAgIOaKseatie+8jOaCqOiuv+mXrueahOiEuOiwseS4jeWtmOWcqOOAglxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaCgnLycpfVxuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogY29sb3JzLnByaW1hcnksXG4gICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgcGFkZGluZzogJzAuNzVyZW0gMS41cmVtJyxcbiAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcwLjVyZW0nLFxuICAgICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMXJlbSdcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg6L+U5Zue6aaW6aG1XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICBtaW5IZWlnaHQ6ICcxMDB2aCcsXG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6IGNvbG9ycy5iYWNrZ3JvdW5kLFxuICAgICAgY29sb3I6IGNvbG9ycy50ZXh0UHJpbWFyeVxuICAgIH19PlxuICAgICAgey8qIOWvvOiIquagjyAqL31cbiAgICAgIDxTaW1wbGVOYXZiYXIgc2hvd0JhY2tCdXR0b24gdGl0bGU9e21hc2submFtZX0gLz5cblxuICAgICAgey8qIOS4u+imgeWGheWuuSAqL31cbiAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgbWF4V2lkdGg6ICcxMjAwcHgnLFxuICAgICAgICBtYXJnaW46ICcwIGF1dG8nLFxuICAgICAgICBwYWRkaW5nOiAnMnJlbSdcbiAgICAgIH19PlxuICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgZGlzcGxheTogJ2dyaWQnLFxuICAgICAgICAgIGdyaWRUZW1wbGF0ZUNvbHVtbnM6ICcxZnIgMWZyJyxcbiAgICAgICAgICBnYXA6ICczcmVtJyxcbiAgICAgICAgICAnQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSc6IHtcbiAgICAgICAgICAgIGdyaWRUZW1wbGF0ZUNvbHVtbnM6ICcxZnInXG4gICAgICAgICAgfVxuICAgICAgICB9fT5cbiAgICAgICAgICB7Lyog5bem5L6n77ya6IS46LCx5Zu+54mHICovfVxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogY29sb3JzLmJhY2tncm91bmRTZWNvbmRhcnksXG4gICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzEycHgnLFxuICAgICAgICAgICAgICBwYWRkaW5nOiAnMnJlbScsXG4gICAgICAgICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcidcbiAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgc3JjPXttYXNrLmltYWdlcz8uZnVsbFNpemUgfHwgbWFzay5pbWFnZVVybCB8fCBtYXNrLmltYWdlcz8udGh1bWJuYWlsfVxuICAgICAgICAgICAgICAgIGFsdD17bWFzay5uYW1lfVxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgICAgICAgICAgbWF4V2lkdGg6ICc0MDBweCcsXG4gICAgICAgICAgICAgICAgICBoZWlnaHQ6ICdhdXRvJyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzhweCcsXG4gICAgICAgICAgICAgICAgICBtYXJnaW5Cb3R0b206ICcxcmVtJ1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgb25FcnJvcj17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgIGNvbnN0IHRhcmdldCA9IGUudGFyZ2V0IGFzIEhUTUxJbWFnZUVsZW1lbnQ7XG4gICAgICAgICAgICAgICAgICB0YXJnZXQuc3JjID0gYGh0dHBzOi8vdmlhLnBsYWNlaG9sZGVyLmNvbS80MDB4NDAwL0RDMTQzQy9GRkZGRkY/dGV4dD0ke2VuY29kZVVSSUNvbXBvbmVudChtYXNrLm5hbWUpfWA7XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIHsvKiDliqjnlLvmjInpkq7lkozmlLbol4/mjInpkq4gKi99XG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgZ2FwOiAnMXJlbScsXG4gICAgICAgICAgICAgICAgbWFyZ2luVG9wOiAnMXJlbScsXG4gICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInXG4gICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dBbmltYXRpb24oIXNob3dBbmltYXRpb24pfVxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBjb2xvcnMucHJpbWFyeSxcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcwLjc1cmVtIDEuNXJlbScsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzAuNXJlbScsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxcmVtJ1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7c2hvd0FuaW1hdGlvbiA/ICfpmpDol4/nu5jliLbov4fnqIsnIDogJ+ingueci+e7mOWItui/h+eoiyd9XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB0b2dnbGVGYXZvcml0ZShtYXNrLmlkKX1cbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogaXNGYXZvcml0ZShtYXNrLmlkKSA/IGNvbG9ycy5wcmltYXJ5IDogJ3RyYW5zcGFyZW50JyxcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6IGlzRmF2b3JpdGUobWFzay5pZCkgPyAnd2hpdGUnIDogY29sb3JzLnByaW1hcnksXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlcjogYDJweCBzb2xpZCAke2NvbG9ycy5wcmltYXJ5fWAsXG4gICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcwLjc1cmVtIDEuNXJlbScsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzAuNXJlbScsXG4gICAgICAgICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzFyZW0nLFxuICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgICBnYXA6ICcwLjVyZW0nXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHtpc0Zhdm9yaXRlKG1hc2suaWQpID8gJ+KdpO+4jyDlt7LmlLbol48nIDogJ/CfpI0g5pS26JePJ31cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiDlj7PkvqfvvJrohLjosLHkv6Hmga8gKi99XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBjb2xvcnMuYmFja2dyb3VuZFNlY29uZGFyeSxcbiAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTJweCcsXG4gICAgICAgICAgICAgIHBhZGRpbmc6ICcycmVtJyxcbiAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnMnJlbSdcbiAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICA8aDIgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEuNXJlbScsXG4gICAgICAgICAgICAgICAgZm9udFdlaWdodDogJ2JvbGQnLFxuICAgICAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzFyZW0nLFxuICAgICAgICAgICAgICAgIGNvbG9yOiBjb2xvcnMudGV4dFByaW1hcnlcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAg5Z+65pys5L+h5oGvXG4gICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PlxuICAgICAgICAgICAgICAgIDxzdHJvbmc+6KeS6Imy77yaPC9zdHJvbmc+IHttYXNrLmNoYXJhY3Rlcn1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PlxuICAgICAgICAgICAgICAgIDxzdHJvbmc+6KGM5b2T77yaPC9zdHJvbmc+IHttYXNrLnJvbGVDYXRlZ29yeX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PlxuICAgICAgICAgICAgICAgIDxzdHJvbmc+6Imy5b2p5YiG57G777yaPC9zdHJvbmc+IHttYXNrLmNvbG9yQ2F0ZWdvcnl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiDkuLvopoHpopzoibIgKi99XG4gICAgICAgICAgICAgIHttYXNrLm1haW5Db2xvcnMgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgbWFyZ2luQm90dG9tOiAnMXJlbScgfX0+XG4gICAgICAgICAgICAgICAgICA8c3Ryb25nPuS4u+imgeiJsuW9qe+8mjwvc3Ryb25nPlxuICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGdhcDogJzAuNXJlbScsIG1hcmdpblRvcDogJzAuNXJlbScgfX0+XG4gICAgICAgICAgICAgICAgICAgIHttYXNrLm1haW5Db2xvcnMubWFwKChjb2xvciwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6ICczMHB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnMzBweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzUwJScsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogY29sb3IsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcjogJzJweCBzb2xpZCByZ2JhKDAsMCwwLDAuMSknXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9e2NvbG9yfVxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIOaWh+WMluiDjOaZryAqL31cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBjb2xvcnMuYmFja2dyb3VuZFNlY29uZGFyeSxcbiAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTJweCcsXG4gICAgICAgICAgICAgIHBhZGRpbmc6ICcycmVtJ1xuICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgIDxoMiBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMS41cmVtJyxcbiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCcsXG4gICAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnMXJlbScsXG4gICAgICAgICAgICAgICAgY29sb3I6IGNvbG9ycy50ZXh0UHJpbWFyeVxuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICDmlofljJbog4zmma9cbiAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgbGluZUhlaWdodDogJzEuNicgfX0+XG4gICAgICAgICAgICAgICAge3R5cGVvZiBtYXNrLmN1bHR1cmFsQmFja2dyb3VuZCA9PT0gJ3N0cmluZycgPyAoXG4gICAgICAgICAgICAgICAgICA8cD57bWFzay5jdWx0dXJhbEJhY2tncm91bmR9PC9wPlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PlxuICAgICAgICAgICAgICAgICAgICAgIDxzdHJvbmc+5Y6G5Y+y6LW35rqQ77yaPC9zdHJvbmc+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgc3R5bGU9e3sgbWFyZ2luVG9wOiAnMC41cmVtJyB9fT57bWFzay5jdWx0dXJhbEJhY2tncm91bmQub3JpZ2lufTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PlxuICAgICAgICAgICAgICAgICAgICAgIDxzdHJvbmc+5oCn5qC854m554K577yaPC9zdHJvbmc+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgc3R5bGU9e3sgbWFyZ2luVG9wOiAnMC41cmVtJyB9fT57bWFzay5jdWx0dXJhbEJhY2tncm91bmQucGVyc29uYWxpdHl9PC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgbWFyZ2luQm90dG9tOiAnMXJlbScgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgPHN0cm9uZz7osaHlvoHmhI/kuYnvvJo8L3N0cm9uZz5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBzdHlsZT17eyBtYXJnaW5Ub3A6ICcwLjVyZW0nIH19PnttYXNrLmN1bHR1cmFsQmFja2dyb3VuZC5zeW1ib2xpc219PC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVBhcmFtcyIsInVzZVJvdXRlciIsIm9wZXJhTWFza3MiLCJNYXNrU2VydmljZSIsImlzU3VwYWJhc2VDb25maWd1cmVkIiwidXNlVGhlbWUiLCJTaW1wbGVOYXZiYXIiLCJ1c2VGYXZvcml0ZXMiLCJ1c2VSZWNlbnRseVZpZXdlZCIsIk1hc2tEZXRhaWxQYWdlIiwibWFzayIsInBhcmFtcyIsInJvdXRlciIsIm1hc2tJZCIsImlkIiwic2V0TWFzayIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwic2hvd0FuaW1hdGlvbiIsInNldFNob3dBbmltYXRpb24iLCJjb2xvcnMiLCJ0b2dnbGVGYXZvcml0ZSIsImlzRmF2b3JpdGUiLCJhZGRUb1JlY2VudGx5Vmlld2VkIiwibG9hZE1hc2siLCJtYXNrRGF0YSIsImdldEFsbEFwcHJvdmVkTWFza3MiLCJlcnJvciIsImNvbnNvbGUiLCJmb3VuZE1hc2siLCJmaW5kIiwibSIsImRpdiIsInN0eWxlIiwibWluSGVpZ2h0IiwiYmFja2dyb3VuZENvbG9yIiwiYmFja2dyb3VuZCIsImNvbG9yIiwidGV4dFByaW1hcnkiLCJzaG93QmFja0J1dHRvbiIsInRpdGxlIiwiZGlzcGxheSIsImFsaWduSXRlbXMiLCJqdXN0aWZ5Q29udGVudCIsInRleHRBbGlnbiIsImZvbnRTaXplIiwibWFyZ2luQm90dG9tIiwiYW5pbWF0aW9uIiwicCIsImgxIiwidGV4dFNlY29uZGFyeSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJwdXNoIiwicHJpbWFyeSIsInBhZGRpbmciLCJib3JkZXJSYWRpdXMiLCJib3JkZXIiLCJjdXJzb3IiLCJuYW1lIiwibWF4V2lkdGgiLCJtYXJnaW4iLCJncmlkVGVtcGxhdGVDb2x1bW5zIiwiZ2FwIiwiYmFja2dyb3VuZFNlY29uZGFyeSIsImltZyIsInNyYyIsImltYWdlcyIsImZ1bGxTaXplIiwiaW1hZ2VVcmwiLCJ0aHVtYm5haWwiLCJhbHQiLCJ3aWR0aCIsImhlaWdodCIsIm9uRXJyb3IiLCJlIiwidGFyZ2V0IiwiZW5jb2RlVVJJQ29tcG9uZW50IiwibWFyZ2luVG9wIiwiaDIiLCJmb250V2VpZ2h0Iiwic3Ryb25nIiwiY2hhcmFjdGVyIiwicm9sZUNhdGVnb3J5IiwiY29sb3JDYXRlZ29yeSIsIm1haW5Db2xvcnMiLCJtYXAiLCJpbmRleCIsImxpbmVIZWlnaHQiLCJjdWx0dXJhbEJhY2tncm91bmQiLCJvcmlnaW4iLCJwZXJzb25hbGl0eSIsInN5bWJvbGlzbSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/mask/[id]/page.tsx\n"));

/***/ })

});