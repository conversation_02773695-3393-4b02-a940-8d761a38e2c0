'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { MaskService } from '@/services/maskService';

interface AddMaskFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

const ROLE_TYPES = [
  { value: 'sheng', label: '生角', description: '男性正面角色' },
  { value: 'dan', label: '旦角', description: '女性角色' },
  { value: 'jing', label: '净角', description: '性格鲜明的男性角色' },
  { value: 'chou', label: '丑角', description: '滑稽或反面角色' }
];

const COLOR_OPTIONS = [
  '红色', '黑色', '白色', '蓝色', '绿色', '黄色', '紫色', '金色', '银色', '粉色'
];

const PERSONALITY_TRAITS = [
  '忠义', '勇猛', '智慧', '正直', '威武', '仁慈', '狡诈', '机智', '豪爽', '清廉',
  '公正', '刚直', '温和', '善良', '坚韧', '果断', '谨慎', '热情', '冷静', '幽默'
];

export function AddMaskForm({ onSuccess, onCancel }: AddMaskFormProps) {
  const { user } = useAuth();
  const { colors } = useTheme();
  
  const [formData, setFormData] = useState({
    name: '',
    character: '',
    roleType: 'jing' as const,
    colorTheme: [] as string[],
    imageUrl: '',
    culturalBackground: '',
    personalityTraits: [] as string[],
    storyDescription: '',
    dynasty: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [imagePreview, setImagePreview] = useState('');

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleColorToggle = (color: string) => {
    setFormData(prev => ({
      ...prev,
      colorTheme: prev.colorTheme.includes(color)
        ? prev.colorTheme.filter(c => c !== color)
        : [...prev.colorTheme, color]
    }));
  };

  const handleTraitToggle = (trait: string) => {
    setFormData(prev => ({
      ...prev,
      personalityTraits: prev.personalityTraits.includes(trait)
        ? prev.personalityTraits.filter(t => t !== trait)
        : [...prev.personalityTraits, trait]
    }));
  };

  const handleImageUrlChange = (url: string) => {
    setFormData(prev => ({ ...prev, imageUrl: url }));
    setImagePreview(url);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) {
      setError('请先登录');
      return;
    }

    setLoading(true);
    setError('');

    try {
      await MaskService.createMask({
        name: formData.name,
        character: formData.character,
        role_type: formData.roleType,
        color_theme: formData.colorTheme,
        image_url: formData.imageUrl,
        cultural_background: formData.culturalBackground,
        personality_traits: formData.personalityTraits,
        story_description: formData.storyDescription || undefined,
        dynasty: formData.dynasty || undefined,
        created_by: user.id,
        is_official: false,
        is_approved: false // 需要审核
      });

      onSuccess?.();
    } catch (err: any) {
      setError(err.message || '提交失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      backgroundColor: colors.background,
      padding: '2rem',
      borderRadius: '12px',
      maxWidth: '800px',
      margin: '0 auto'
    }}>
      <h2 style={{
        fontSize: '1.5rem',
        fontWeight: 'bold',
        color: colors.text,
        marginBottom: '1.5rem',
        textAlign: 'center'
      }}>
        添加京剧脸谱
      </h2>

      <form onSubmit={handleSubmit}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '1.5rem'
        }}>
          {/* 基本信息 */}
          <div>
            <h3 style={{
              fontSize: '1.125rem',
              fontWeight: '600',
              color: colors.text,
              marginBottom: '1rem'
            }}>
              基本信息
            </h3>

            <div style={{ marginBottom: '1rem' }}>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: colors.text,
                marginBottom: '0.5rem'
              }}>
                脸谱名称 *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: `1px solid ${colors.border}`,
                  borderRadius: '6px',
                  fontSize: '1rem',
                  backgroundColor: colors.background,
                  color: colors.text,
                  boxSizing: 'border-box'
                }}
                placeholder="例如：关羽脸谱"
              />
            </div>

            <div style={{ marginBottom: '1rem' }}>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: colors.text,
                marginBottom: '0.5rem'
              }}>
                角色名称 *
              </label>
              <input
                type="text"
                value={formData.character}
                onChange={(e) => handleInputChange('character', e.target.value)}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: `1px solid ${colors.border}`,
                  borderRadius: '6px',
                  fontSize: '1rem',
                  backgroundColor: colors.background,
                  color: colors.text,
                  boxSizing: 'border-box'
                }}
                placeholder="例如：关羽"
              />
            </div>

            <div style={{ marginBottom: '1rem' }}>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: colors.text,
                marginBottom: '0.5rem'
              }}>
                角色类型 *
              </label>
              <select
                value={formData.roleType}
                onChange={(e) => handleInputChange('roleType', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: `1px solid ${colors.border}`,
                  borderRadius: '6px',
                  fontSize: '1rem',
                  backgroundColor: colors.background,
                  color: colors.text,
                  boxSizing: 'border-box'
                }}
              >
                {ROLE_TYPES.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label} - {type.description}
                  </option>
                ))}
              </select>
            </div>

            <div style={{ marginBottom: '1rem' }}>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: colors.text,
                marginBottom: '0.5rem'
              }}>
                朝代
              </label>
              <input
                type="text"
                value={formData.dynasty}
                onChange={(e) => handleInputChange('dynasty', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: `1px solid ${colors.border}`,
                  borderRadius: '6px',
                  fontSize: '1rem',
                  backgroundColor: colors.background,
                  color: colors.text,
                  boxSizing: 'border-box'
                }}
                placeholder="例如：东汉、北宋"
              />
            </div>
          </div>

          {/* 图片和颜色 */}
          <div>
            <h3 style={{
              fontSize: '1.125rem',
              fontWeight: '600',
              color: colors.text,
              marginBottom: '1rem'
            }}>
              外观设置
            </h3>

            <div style={{ marginBottom: '1rem' }}>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: colors.text,
                marginBottom: '0.5rem'
              }}>
                脸谱图片URL *
              </label>
              <input
                type="url"
                value={formData.imageUrl}
                onChange={(e) => handleImageUrlChange(e.target.value)}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: `1px solid ${colors.border}`,
                  borderRadius: '6px',
                  fontSize: '1rem',
                  backgroundColor: colors.background,
                  color: colors.text,
                  boxSizing: 'border-box'
                }}
                placeholder="请输入图片URL"
              />
            </div>

            {imagePreview && (
              <div style={{ marginBottom: '1rem' }}>
                <img
                  src={imagePreview}
                  alt="预览"
                  style={{
                    width: '100%',
                    maxWidth: '200px',
                    height: 'auto',
                    borderRadius: '8px',
                    border: `1px solid ${colors.border}`
                  }}
                  onError={() => setImagePreview('')}
                />
              </div>
            )}

            <div style={{ marginBottom: '1rem' }}>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: colors.text,
                marginBottom: '0.5rem'
              }}>
                颜色主题
              </label>
              <div style={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: '0.5rem'
              }}>
                {COLOR_OPTIONS.map(color => (
                  <button
                    key={color}
                    type="button"
                    onClick={() => handleColorToggle(color)}
                    style={{
                      padding: '0.5rem 1rem',
                      border: `1px solid ${colors.border}`,
                      borderRadius: '20px',
                      fontSize: '0.875rem',
                      backgroundColor: formData.colorTheme.includes(color) 
                        ? colors.primary 
                        : colors.background,
                      color: formData.colorTheme.includes(color) 
                        ? 'white' 
                        : colors.text,
                      cursor: 'pointer'
                    }}
                  >
                    {color}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 文化背景 */}
        <div style={{ marginTop: '1.5rem' }}>
          <label style={{
            display: 'block',
            fontSize: '0.875rem',
            fontWeight: '500',
            color: colors.text,
            marginBottom: '0.5rem'
          }}>
            文化背景 *
          </label>
          <textarea
            value={formData.culturalBackground}
            onChange={(e) => handleInputChange('culturalBackground', e.target.value)}
            required
            rows={4}
            style={{
              width: '100%',
              padding: '0.75rem',
              border: `1px solid ${colors.border}`,
              borderRadius: '6px',
              fontSize: '1rem',
              backgroundColor: colors.background,
              color: colors.text,
              boxSizing: 'border-box',
              resize: 'vertical'
            }}
            placeholder="请描述角色的历史背景、文化意义等..."
          />
        </div>

        {/* 性格特征 */}
        <div style={{ marginTop: '1.5rem' }}>
          <label style={{
            display: 'block',
            fontSize: '0.875rem',
            fontWeight: '500',
            color: colors.text,
            marginBottom: '0.5rem'
          }}>
            性格特征
          </label>
          <div style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '0.5rem'
          }}>
            {PERSONALITY_TRAITS.map(trait => (
              <button
                key={trait}
                type="button"
                onClick={() => handleTraitToggle(trait)}
                style={{
                  padding: '0.5rem 1rem',
                  border: `1px solid ${colors.border}`,
                  borderRadius: '20px',
                  fontSize: '0.875rem',
                  backgroundColor: formData.personalityTraits.includes(trait) 
                    ? colors.primary 
                    : colors.background,
                  color: formData.personalityTraits.includes(trait) 
                    ? 'white' 
                    : colors.text,
                  cursor: 'pointer'
                }}
              >
                {trait}
              </button>
            ))}
          </div>
        </div>

        {/* 故事描述 */}
        <div style={{ marginTop: '1.5rem' }}>
          <label style={{
            display: 'block',
            fontSize: '0.875rem',
            fontWeight: '500',
            color: colors.text,
            marginBottom: '0.5rem'
          }}>
            故事描述
          </label>
          <textarea
            value={formData.storyDescription}
            onChange={(e) => handleInputChange('storyDescription', e.target.value)}
            rows={3}
            style={{
              width: '100%',
              padding: '0.75rem',
              border: `1px solid ${colors.border}`,
              borderRadius: '6px',
              fontSize: '1rem',
              backgroundColor: colors.background,
              color: colors.text,
              boxSizing: 'border-box',
              resize: 'vertical'
            }}
            placeholder="请描述角色的故事、传说等（可选）..."
          />
        </div>

        {error && (
          <div style={{
            backgroundColor: '#FEE2E2',
            color: '#DC2626',
            padding: '0.75rem',
            borderRadius: '6px',
            fontSize: '0.875rem',
            marginTop: '1rem'
          }}>
            {error}
          </div>
        )}

        <div style={{
          display: 'flex',
          gap: '1rem',
          marginTop: '2rem',
          justifyContent: 'center'
        }}>
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              style={{
                padding: '0.75rem 2rem',
                border: `1px solid ${colors.border}`,
                borderRadius: '6px',
                fontSize: '1rem',
                backgroundColor: colors.background,
                color: colors.text,
                cursor: 'pointer'
              }}
            >
              取消
            </button>
          )}
          <button
            type="submit"
            disabled={loading}
            style={{
              padding: '0.75rem 2rem',
              border: 'none',
              borderRadius: '6px',
              fontSize: '1rem',
              backgroundColor: loading ? colors.textSecondary : colors.primary,
              color: 'white',
              cursor: loading ? 'not-allowed' : 'pointer'
            }}
          >
            {loading ? '提交中...' : '提交审核'}
          </button>
        </div>
      </form>

      <div style={{
        marginTop: '1.5rem',
        padding: '1rem',
        backgroundColor: colors.cardBackground,
        borderRadius: '8px',
        fontSize: '0.875rem',
        color: colors.textSecondary
      }}>
        <p style={{ margin: 0 }}>
          📝 提交说明：您提交的脸谱将进入审核流程，审核通过后将在平台上展示。
          请确保提供的信息准确、图片清晰，并尊重京剧文化的传统和艺术价值。
        </p>
      </div>
    </div>
  );
}
