#!/bin/bash

# Beijing Opera Masks Platform - Production Deployment Script
# Target Domain: milei.dpdns.org

echo "🎭 Beijing Opera Masks Platform - Production Deployment"
echo "Target Domain: milei.dpdns.org"
echo "=================================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js version: $(node --version)"
echo "✅ npm version: $(npm --version)"

# Set production environment
export NODE_ENV=production
export NEXT_PUBLIC_BASE_URL=https://milei.dpdns.org

echo "🔧 Setting up production environment..."
echo "   NODE_ENV: $NODE_ENV"
echo "   BASE_URL: $NEXT_PUBLIC_BASE_URL"

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf .next
rm -rf out
rm -rf node_modules/.cache

# Install dependencies
echo "📦 Installing dependencies..."
npm ci --production=false

# Run production build
echo "🏗️  Building for production..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed. Please check the errors above."
    exit 1
fi

echo "✅ Build completed successfully!"

# Test the build
echo "🧪 Testing production build..."
timeout 10s npm start &
BUILD_PID=$!
sleep 5

# Check if the server started
if kill -0 $BUILD_PID 2>/dev/null; then
    echo "✅ Production server started successfully"
    kill $BUILD_PID
else
    echo "❌ Production server failed to start"
    exit 1
fi

# Display deployment information
echo ""
echo "🚀 Deployment Information"
echo "========================"
echo "✅ Application built for production"
echo "✅ Target domain: milei.dpdns.org"
echo "✅ Real mask images: 9 characters configured"
echo "✅ Image sources: d.bmcx.com, img1.baidu.com"
echo "✅ Features: Homepage, detail pages, modals"
echo "✅ Responsive design enabled"
echo "✅ Performance optimizations applied"

echo ""
echo "📋 Next Steps:"
echo "1. Configure DNS records to point to milei.dpdns.org"
echo "2. Set up SSL certificate for HTTPS"
echo "3. Deploy the .next folder to your server"
echo "4. Start the production server with: npm start"
echo "5. Test all functionality after deployment"

echo ""
echo "🔍 Testing Checklist:"
echo "- [ ] Domain resolves correctly"
echo "- [ ] HTTPS certificate is valid"
echo "- [ ] All 9 mask images load properly"
echo "- [ ] Navigation works between pages"
echo "- [ ] Modal dialogs function correctly"
echo "- [ ] Responsive design works on mobile"
echo "- [ ] External image loading works"

echo ""
echo "🎭 Character Status (9 real masks configured):"
echo "✅ 关羽 (guanyu) - d.bmcx.com"
echo "✅ 包拯 (baogong) - d.bmcx.com"
echo "✅ 曹操 (caocao) - d.bmcx.com"
echo "✅ 张飞 (zhangfei) - d.bmcx.com"
echo "✅ 窦尔敦 (doulujin) - d.bmcx.com"
echo "✅ 杨七郎 (yangqilang) - baidu.com"
echo "✅ 蒋干 (jianggan) - d.bmcx.com"
echo "✅ 刘备 (liubei) - baidu.com"
echo "✅ 孙悟空 (sunwukong) - d.bmcx.com"

echo ""
echo "🎉 Production build completed successfully!"
echo "Ready for deployment to milei.dpdns.org"
