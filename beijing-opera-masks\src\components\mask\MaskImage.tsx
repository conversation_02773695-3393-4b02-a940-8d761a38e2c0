'use client';

import React, { useState, useEffect } from 'react';
import { OperaMask } from '@/types/mask';
import { MaskImageGenerator, maskConfigs } from '@/utils/maskImageGenerator';

interface MaskImageProps {
  mask: OperaMask;
  width?: number;
  height?: number;
  className?: string;
}

// 真实脸谱图片URL映射
// 为特定角色使用真实的京剧脸谱图片
const maskImageUrls: Record<string, string> = {
  // 已添加真实脸谱图片的角色
  'guanyu': 'https://d.bmcx.com/lianpu/d/0072.jpg', // 关羽红脸真实图片
  'baogong': 'https://d.bmcx.com/lianpu/d/0018.jpg', // 包拯黑脸真实图片
  'caocao': 'https://d.bmcx.com/lianpu/d/0028.jpg', // 曹操白脸真实图片
  'zhangfei': 'https://d.bmcx.com/lianpu/d/0324.jpg', // 张飞黑脸真实图片
  'doulujin': 'https://d.bmcx.com/lianpu/d/0056.jpg', // 窦尔敦蓝脸真实图片
  'yangqilang': 'https://img1.baidu.com/it/u=348325659,1868481632&fm=253&fmt=auto&app=138&f=JPEG?w=351&h=441', // 杨七郎真实图片
  'jianggan': 'https://d.bmcx.com/lianpu/d/0117.jpg', // 蒋干白脸真实图片
  'liubei': 'https://img1.baidu.com/it/u=93431987,3113680563&fm=253&fmt=auto&app=138&f=JPEG?w=412&h=502', // 刘备真实图片
  'sunwukong': 'https://d.bmcx.com/lianpu/d/0234.jpg', // 孙悟空金脸真实图片

  // 其他角色暂时使用Canvas生成，后续可以逐步替换
  'huangzhong': '', // 黄忠黄脸 - 使用Canvas生成
  'diaochan': '', // 貂蝉 - 使用Canvas生成
  'dianwei': '', // 典韦 - 使用Canvas生成
  'likui': '', // 李逵 - 使用Canvas生成
  'zhubaijie': '', // 猪八戒 - 使用Canvas生成
  'baigu': '', // 白骨精 - 使用Canvas生成
  'huajiangjun': '', // 花脸将军 - 使用Canvas生成
  'qingyi': '', // 青衣花旦 - 使用Canvas生成
  'xiaosheng': '', // 小生角色 - 使用Canvas生成
  'yangguifei': '', // 杨贵妃 - 使用Canvas生成
  'machao': '' // 马超 - 使用Canvas生成
};

export function MaskImage({ mask, width = 300, height = 300, className }: MaskImageProps) {
  const [canvasImage, setCanvasImage] = useState<string | null>(null);
  const [realImage, setRealImage] = useState<string | null>(null);
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 获取真实脸谱图片URL
  const realImageUrl = maskImageUrls[mask.id];

  // 生成Canvas图片作为后备方案
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const config = maskConfigs[mask.id];
      if (config) {
        const generator = new MaskImageGenerator(width, height);
        const generatedImage = generator.generateMaskImage(config);
        setCanvasImage(generatedImage);
      } else {
        // 如果没有配置，使用默认配置
        const defaultConfig = {
          id: mask.id,
          name: mask.name,
          character: mask.character,
          mainColors: mask.mainColors,
          faceShape: 'oval' as const,
          eyeStyle: 'normal' as const,
          decorations: ['forehead-mark']
        };
        const generator = new MaskImageGenerator(width, height);
        const generatedImage = generator.generateMaskImage(defaultConfig);
        setCanvasImage(generatedImage);
      }
    }
  }, [mask.id, mask.name, mask.character, mask.mainColors, width, height]);

  // 如果有真实图片URL，尝试加载真实图片
  useEffect(() => {
    if (realImageUrl) {
      const img = new Image();
      // 对于外部图片，尝试不设置crossOrigin以避免CORS问题
      if (realImageUrl.startsWith('http')) {
        // 外部图片不设置crossOrigin
      } else {
        img.crossOrigin = 'anonymous';
      }

      // 设置加载超时
      const timeout = setTimeout(() => {
        setImageError(true);
        setRealImage(null);
        setIsLoading(false);
        console.warn(`图片加载超时: ${realImageUrl}`);
      }, 5000); // 5秒超时

      img.onload = () => {
        clearTimeout(timeout);
        setRealImage(realImageUrl);
        setImageError(false);
        setIsLoading(false);
        console.log(`✅ 成功加载真实脸谱图片: ${mask.name} - ${realImageUrl}`);
      };

      img.onerror = (error) => {
        clearTimeout(timeout);
        setImageError(true);
        setRealImage(null);
        setIsLoading(false);
        console.warn(`❌ 真实脸谱图片加载失败，回退到Canvas生成: ${mask.name} - ${realImageUrl}`, error);
      };

      console.log(`🔄 开始加载真实脸谱图片: ${mask.name} - ${realImageUrl}`);
      img.src = realImageUrl;

      // 清理函数
      return () => {
        clearTimeout(timeout);
      };
    } else {
      // 没有真实图片URL，直接使用Canvas生成的图片
      setIsLoading(false);
    }
  }, [realImageUrl, mask.name]);

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return renderLoadingState();
  }

  // 优先使用真实图片，如果加载失败或没有真实图片则使用Canvas生成的图片
  if (realImage && !imageError) {
    return renderRealImage();
  } else if (canvasImage) {
    return renderCanvasImage();
  } else {
    return renderLoadingState();
  }

  // 渲染加载状态
  function renderLoadingState() {
    return (
      <div
        className={className}
        style={{
          width,
          height,
          position: 'relative',
          overflow: 'hidden',
          borderRadius: '0.5rem',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#F3F4F6',
          color: '#9CA3AF'
        }}
      >
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🎭</div>
          <div style={{ fontSize: '0.875rem' }}>生成中...</div>
        </div>
      </div>
    );
  }

  // 真实图片渲染
  function renderRealImage() {
    return (
      <div
        className={className}
        style={{
          width,
          height,
          position: 'relative',
          overflow: 'hidden',
          borderRadius: '0.5rem',
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
          transition: 'transform 0.3s ease, box-shadow 0.3s ease'
        }}
        title={`${mask.name} - 真实脸谱图片`}
      >
        <img
          src={realImage!}
          alt={`${mask.name} - ${mask.character}`}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            display: 'block'
          }}
        />

        {/* 真实图片标识 */}
        <div
          style={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            background: 'rgba(34, 197, 94, 0.8)',
            color: 'white',
            fontSize: '10px',
            padding: '2px 6px',
            borderRadius: '4px',
            fontWeight: 'bold'
          }}
        >
          真实脸谱
        </div>

        {/* 统一的悬停效果 */}
        <div
          style={{
            position: 'absolute',
            inset: 0,
            background: 'transparent',
            transition: 'background 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = 'rgba(0, 0, 0, 0.1)';
            e.currentTarget.parentElement!.style.transform = 'scale(1.02)';
            e.currentTarget.parentElement!.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.2)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'transparent';
            e.currentTarget.parentElement!.style.transform = 'scale(1)';
            e.currentTarget.parentElement!.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
          }}
        />
      </div>
    );
  }

  // Canvas生成的图片渲染
  function renderCanvasImage() {
    return (
      <div
        className={className}
        style={{
          width,
          height,
          position: 'relative',
          overflow: 'hidden',
          borderRadius: '0.5rem',
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
          transition: 'transform 0.3s ease, box-shadow 0.3s ease'
        }}
        title={`${mask.name} - Canvas生成脸谱`}
      >
        <img
          src={canvasImage!}
          alt={`${mask.name} - ${mask.character}`}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            display: 'block'
          }}
        />

        {/* Canvas生成标识 */}
        <div
          style={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            background: 'rgba(59, 130, 246, 0.8)',
            color: 'white',
            fontSize: '10px',
            padding: '2px 6px',
            borderRadius: '4px',
            fontWeight: 'bold'
          }}
        >
          AI生成
        </div>

        {/* 统一的悬停效果 */}
        <div
          style={{
            position: 'absolute',
            inset: 0,
            background: 'transparent',
            transition: 'background 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = 'rgba(0, 0, 0, 0.1)';
            e.currentTarget.parentElement!.style.transform = 'scale(1.02)';
            e.currentTarget.parentElement!.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.2)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'transparent';
            e.currentTarget.parentElement!.style.transform = 'scale(1)';
            e.currentTarget.parentElement!.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
          }}
        />
      </div>
    );
  }

  // SVG后备方案
  function generateFallbackSVG() {
    const baseProps = {
      width,
      height,
      viewBox: "0 0 300 300",
      className,
      style: { borderRadius: '0.5rem' }
    };

    // 简化的SVG后备方案
    return (
      <svg {...baseProps}>
        <defs>
          <radialGradient id={`gradient-${mask.id}`} cx="50%" cy="40%" r="60%">
            <stop offset="0%" stopColor={mask.mainColors[0]} />
            <stop offset="100%" stopColor={mask.mainColors[1] || mask.mainColors[0]} />
          </radialGradient>
        </defs>

        {/* 脸部轮廓 */}
        <ellipse
          cx="150"
          cy="150"
          rx="120"
          ry="140"
          fill={`url(#gradient-${mask.id})`}
          stroke="#333"
          strokeWidth="3"
        />

        {/* 基础五官 */}
        <path d="M 90 110 Q 120 90 150 110" stroke="#000" strokeWidth="4" fill="none"/>
        <path d="M 150 110 Q 180 90 210 110" stroke="#000" strokeWidth="4" fill="none"/>
        <ellipse cx="120" cy="140" rx="15" ry="10" fill="white" stroke="#000" strokeWidth="2"/>
        <ellipse cx="180" cy="140" rx="15" ry="10" fill="white" stroke="#000" strokeWidth="2"/>
        <circle cx="120" cy="140" r="6" fill="#000"/>
        <circle cx="180" cy="140" r="6" fill="#000"/>
        <path d="M 150 160 L 150 180" stroke="#000" strokeWidth="3"/>
        <ellipse cx="150" cy="200" rx="10" ry="6" fill="#000"/>

        {/* 角色名称 */}
        <text
          x="150"
          y="260"
          textAnchor="middle"
          fill="white"
          fontSize="24"
          fontWeight="bold"
          fontFamily="serif"
          stroke="#000"
          strokeWidth="1"
        >
          {mask.character}
        </text>
      </svg>
    );
  }
}
