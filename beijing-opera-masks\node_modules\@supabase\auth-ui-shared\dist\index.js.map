{"version": 3, "sources": ["../src/index.ts", "../src/theming/defaultThemes.ts", "../src/theming/Themes.ts", "../src/constants.ts", "../src/theming/utils.ts", "../src/types.ts", "../src/utils.ts", "../src/localization/en.json"], "sourcesContent": ["export * from './theming'\nexport * from './constants'\nexport * from './types'\nexport * from './utils'\nexport { en } from './localization'\n", "/**\n * Create default theme\n *\n * createStitches()\n * https://stitches.dev/docs/api#theme\n *\n * to add a new theme use  createTheme({})\n * https://stitches.dev/docs/api#theme\n */\n\nimport { Theme } from './../types'\n\n// brand: 'hsl(252 62% 55%)',\n// brandAccent: 'hsl(252 62% 45%)',\n\nexport const ThemeSupa: Theme = {\n  default: {\n    colors: {\n      brand: 'hsl(153 60.0% 53.0%)',\n      brandAccent: 'hsl(154 54.8% 45.1%)',\n      brandButtonText: 'white',\n      defaultButtonBackground: 'white',\n      defaultButtonBackgroundHover: '#eaeaea',\n      defaultButtonBorder: 'lightgray',\n      defaultButtonText: 'gray',\n      dividerBackground: '#eaeaea',\n      inputBackground: 'transparent',\n      inputBorder: 'lightgray',\n      inputBorderHover: 'gray',\n      inputBorderFocus: 'gray',\n      inputText: 'black',\n      inputLabelText: 'gray',\n      inputPlaceholder: 'darkgray',\n      messageText: '#2b805a',\n      messageBackground: '#e7fcf1',\n      messageBorder: '#d0f3e1',\n      messageTextDanger: '#ff6369',\n      messageBackgroundDanger: '#fff8f8',\n      messageBorderDanger: '#822025',\n      anchorTextColor: 'gray',\n      anchorTextHoverColor: 'darkgray',\n    },\n    space: {\n      spaceSmall: '4px',\n      spaceMedium: '8px',\n      spaceLarge: '16px',\n      labelBottomMargin: '8px',\n      anchorBottomMargin: '4px',\n      emailInputSpacing: '4px',\n      socialAuthSpacing: '4px',\n      buttonPadding: '10px 15px',\n      inputPadding: '10px 15px',\n    },\n    fontSizes: {\n      baseBodySize: '13px',\n      baseInputSize: '14px',\n      baseLabelSize: '14px',\n      baseButtonSize: '14px',\n    },\n    fonts: {\n      bodyFontFamily: `ui-sans-serif, sans-serif`,\n      buttonFontFamily: `ui-sans-serif, sans-serif`,\n      inputFontFamily: `ui-sans-serif, sans-serif`,\n      labelFontFamily: `ui-sans-serif, sans-serif`,\n    },\n    // fontWeights: {},\n    // lineHeights: {},\n    // letterSpacings: {},\n    // sizes: {},\n    borderWidths: {\n      buttonBorderWidth: '1px',\n      inputBorderWidth: '1px',\n    },\n    // borderStyles: {},\n    radii: {\n      borderRadiusButton: '4px',\n      buttonBorderRadius: '4px',\n      inputBorderRadius: '4px',\n    },\n    // shadows: {},\n    // zIndices: {},\n    // transitions: {},\n  },\n  dark: {\n    colors: {\n      brandButtonText: 'white',\n      defaultButtonBackground: '#2e2e2e',\n      defaultButtonBackgroundHover: '#3e3e3e',\n      defaultButtonBorder: '#3e3e3e',\n      defaultButtonText: 'white',\n      dividerBackground: '#2e2e2e',\n      inputBackground: '#1e1e1e',\n      inputBorder: '#3e3e3e',\n      inputBorderHover: 'gray',\n      inputBorderFocus: 'gray',\n      inputText: 'white',\n      inputPlaceholder: 'darkgray',\n      messageText: '#85e0b7',\n      messageBackground: '#072719',\n      messageBorder: '#2b805a',\n      messageBackgroundDanger: '#1f1315',\n    },\n  },\n}\n\nexport const ThemeMinimal: Theme = {\n  default: {\n    colors: {\n      brand: 'black',\n      brandAccent: '#333333',\n      brandButtonText: 'white',\n      defaultButtonBackground: 'white',\n      defaultButtonBorder: 'lightgray',\n      defaultButtonText: 'gray',\n      dividerBackground: '#eaeaea',\n      inputBackground: 'transparent',\n      inputBorder: 'lightgray',\n      inputText: 'black',\n      inputPlaceholder: 'darkgray',\n      messageText: '#2b805a',\n      messageBackground: '#e7fcf1',\n      messageBorder: '#d0f3e1',\n      messageTextDanger: '#ff6369',\n      messageBackgroundDanger: '#fff8f8',\n      messageBorderDanger: '#822025',\n    },\n    space: {\n      spaceSmall: '4px',\n      spaceMedium: '8px',\n      spaceLarge: '16px',\n    },\n    fontSizes: {\n      baseInputSize: '14px',\n      baseLabelSize: '12px',\n    },\n    fonts: {\n      bodyFontFamily: '',\n      inputFontFamily: '',\n      buttonFontFamily: '',\n      labelFontFamily: '',\n      // linkFontFamily: '',\n    },\n    // fontWeights: {},\n    // lineHeights: {},\n    // letterSpacings: {},\n    // sizes: {},\n    borderWidths: {},\n    // borderStyles: {},\n    radii: {},\n    // shadows: {},\n    // zIndices: {},\n    // transitions: {},\n  },\n  dark: {\n    colors: {\n      brand: 'white',\n      brandAccent: '#afafaf',\n      brandButtonText: 'black',\n      defaultButtonBackground: '#080808',\n      defaultButtonBorder: 'black',\n      defaultButtonText: 'white',\n      dividerBackground: 'black',\n      inputBackground: 'transparent',\n      inputBorder: 'gray',\n      inputText: 'black',\n      inputPlaceholder: 'darkgray',\n      messageText: '#85e0b7',\n      messageBackground: '#072719',\n      messageBorder: '#2b805a',\n      messageBackgroundDanger: '#1f1315',\n    },\n  },\n}\n", "/**\n * Create default theme\n *\n * createStitches()\n * https://stitches.dev/docs/api#theme\n *\n * to add a new theme use  createTheme({})\n * https://stitches.dev/docs/api#theme\n */\n\nimport { ThemeVariables } from './Types'\n\n// brand: 'hsl(252 62% 55%)',\n// brandAccent: 'hsl(252 62% 45%)',\n\nconst supabase: ThemeVariables = {\n  colors: {\n    brand: 'hsl(153 60.0% 53.0%)',\n    brandAccent: 'hsl(154 54.8% 45.1%)',\n    brandButtonText: 'white',\n    defaultButtonBackground: 'white',\n    defaultButtonBackgroundHover: '#eaeaea',\n    defaultButtonBorder: 'lightgray',\n    defaultButtonText: 'gray',\n    dividerBackground: '#eaeaea',\n    inputBackground: 'transparent',\n    inputBorder: 'lightgray',\n    inputBorderHover: 'gray',\n    inputBorderFocus: 'gray',\n    inputText: 'black',\n    inputLabelText: 'gray',\n    inputPlaceholder: 'darkgray',\n    messageText: 'gray',\n    messageTextDanger: 'red',\n    anchorTextColor: 'gray',\n    anchorTextHoverColor: 'darkgray',\n  },\n  space: {\n    spaceSmall: '4px',\n    spaceMedium: '8px',\n    spaceLarge: '16px',\n    labelBottomMargin: '8px',\n    anchorBottomMargin: '4px',\n    emailInputSpacing: '4px',\n    socialAuthSpacing: '4px',\n    buttonPadding: '10px 15px',\n    inputPadding: '10px 15px',\n  },\n  fontSizes: {\n    baseBodySize: '13px',\n    baseInputSize: '14px',\n    baseLabelSize: '14px',\n    baseButtonSize: '14px',\n  },\n  fonts: {\n    bodyFontFamily: `ui-sans-serif, sans-serif`,\n    buttonFontFamily: `ui-sans-serif, sans-serif`,\n    inputFontFamily: `ui-sans-serif, sans-serif`,\n    labelFontFamily: `ui-sans-serif, sans-serif`,\n  },\n  // fontWeights: {},\n  // lineHeights: {},\n  // letterSpacings: {},\n  // sizes: {},\n  borderWidths: {\n    buttonBorderWidth: '1px',\n    inputBorderWidth: '1px',\n  },\n  // borderStyles: {},\n  radii: {\n    borderRadiusButton: '4px',\n    buttonBorderRadius: '4px',\n    inputBorderRadius: '4px',\n  },\n  // shadows: {},\n  // zIndices: {},\n  // transitions: {},\n}\n\nconst defaultDarkTheme: ThemeVariables = {\n  colors: {\n    brandButtonText: 'white',\n    defaultButtonBackground: '#2e2e2e',\n    defaultButtonBackgroundHover: '#3e3e3e',\n    defaultButtonBorder: '#3e3e3e',\n    defaultButtonText: 'white',\n    dividerBackground: '#2e2e2e',\n    inputBackground: '#1e1e1e',\n    inputBorder: '#3e3e3e',\n    inputBorderHover: 'gray',\n    inputBorderFocus: 'gray',\n    inputText: 'white',\n    inputPlaceholder: 'darkgray',\n  },\n}\n\nconst minimal: ThemeVariables = {\n  colors: {\n    brand: 'black',\n    brandAccent: '#333333',\n    brandButtonText: 'white',\n    defaultButtonBackground: 'white',\n    defaultButtonBorder: 'lightgray',\n    defaultButtonText: 'gray',\n    dividerBackground: '#eaeaea',\n    inputBackground: 'transparent',\n    inputBorder: 'lightgray',\n    inputText: 'black',\n    inputPlaceholder: 'darkgray',\n  },\n  space: {\n    spaceSmall: '4px',\n    spaceMedium: '8px',\n    spaceLarge: '16px',\n  },\n  fontSizes: {\n    baseInputSize: '14px',\n    baseLabelSize: '12px',\n  },\n  fonts: {\n    bodyFontFamily: '',\n    inputFontFamily: '',\n    buttonFontFamily: '',\n    labelFontFamily: '',\n    // linkFontFamily: '',\n  },\n  // fontWeights: {},\n  // lineHeights: {},\n  // letterSpacings: {},\n  // sizes: {},\n  borderWidths: {},\n  // borderStyles: {},\n  radii: {},\n  // shadows: {},\n  // zIndices: {},\n  // transitions: {},\n}\n\nconst minimalDark: ThemeVariables = {\n  colors: {\n    brand: 'white',\n    brandAccent: '#afafaf',\n    brandButtonText: 'black',\n    defaultButtonBackground: '#080808',\n    defaultButtonBorder: 'black',\n    defaultButtonText: 'white',\n    dividerBackground: 'black',\n    inputBackground: 'transparent',\n    inputBorder: 'gray',\n    inputText: 'black',\n    inputPlaceholder: 'darkgray',\n  },\n}\n\nconst darkThemes = {\n  supabase: defaultDarkTheme,\n  minimal: minimalDark,\n}\n\nexport { supabase, minimal, darkThemes }\n", "import { ViewsMap } from './types'\n\nexport const VIEWS: ViewsMap = {\n  SIGN_IN: 'sign_in',\n  SIGN_UP: 'sign_up',\n  FORGOTTEN_PASSWORD: 'forgotten_password',\n  MAGIC_LINK: 'magic_link',\n  UPDATE_PASSWORD: 'update_password',\n  VERIFY_OTP: 'verify_otp',\n}\n\nexport const PREPENDED_CLASS_NAMES = 'supabase-auth-ui'\n\n/**\n * CSS class names\n * used for generating prepended classes\n */\nexport const CLASS_NAMES = {\n  // interfaces\n  ROOT: 'root',\n  SIGN_IN: VIEWS.SIGN_IN,\n  SIGN_UP: VIEWS.SIGN_UP,\n  FORGOTTEN_PASSWORD: VIEWS.FORGOTTEN_PASSWORD,\n  MAGIC_LINK: VIEWS.MAGIC_LINK,\n  UPDATE_PASSWORD: VIEWS.UPDATE_PASSWORD,\n  // ui\n  anchor: 'ui-anchor',\n  button: 'ui-button',\n  container: 'ui-container',\n  divider: 'ui-divider',\n  input: 'ui-input',\n  label: 'ui-label',\n  loader: 'ui-loader',\n  message: 'ui-message',\n}\n", "import { BaseAppearance } from '../types'\nimport { CLASS_NAMES, PREPENDED_CLASS_NAMES } from './../constants'\n\nexport function generateClassNames(\n  /**\n   * name of css class name variable\n   */\n  classNameKey:\n    | 'button'\n    | 'container'\n    | 'anchor'\n    | 'divider'\n    | 'label'\n    | 'input'\n    | 'loader'\n    | 'message',\n  /**\n   * stiches CSS output\n   */\n  defaultStyles: string,\n  /**\n   * appearance variables\n   */\n  appearance?: BaseAppearance\n) {\n  const classNames = []\n  const className = CLASS_NAMES[classNameKey]\n\n  classNames.push(\n    appearance?.prependedClassName\n      ? appearance?.prependedClassName + '_' + className\n      : PREPENDED_CLASS_NAMES + '_' + className\n  )\n\n  if (appearance?.className?.[classNameKey]) {\n    classNames.push(appearance?.className?.[classNameKey])\n  }\n\n  if (appearance?.extend === undefined || appearance?.extend === true) {\n    classNames.push(defaultStyles)\n  }\n\n  return classNames\n}\n", "import { CssComponent } from '@stitches/core/types/styled-component'\nimport {\n  EmailOtpType,\n  MobileOtpType,\n  Provider,\n  SupabaseClient,\n} from '@supabase/supabase-js'\nimport { ThemeVariables } from './theming'\n\nexport interface AnimationTailwindClasses {\n  enter?: string\n  enterFrom?: string\n  enterTo?: string\n  leave?: string\n  leaveFrom?: string\n  leaveTo?: string\n}\n\nexport type AuthProviders = Provider\nexport interface Localization {\n  // [key: string]: I18nVariables\n  ['en']: I18nVariables\n}\n\n// export type SocialLayout = 'horizontal' | 'vertical'\nexport enum SocialLayouts {\n  'horizontal',\n  'vertical',\n}\nexport type SocialLayout = keyof typeof SocialLayouts\nexport type SocialButtonSize = 'tiny' | 'small' | 'medium' | 'large' | 'xlarge'\n\nexport type ViewSignIn = 'sign_in'\nexport type ViewSignUp = 'sign_up'\nexport type ViewMagicLink = 'magic_link'\nexport type ViewForgottenPassword = 'forgotten_password'\nexport type ViewUpdatePassword = 'update_password'\nexport type ViewVerifyOtp = 'verify_otp'\n\nexport type ViewType =\n  | ViewSignIn\n  | ViewSignUp\n  | ViewMagicLink\n  | ViewForgottenPassword\n  | ViewUpdatePassword\n  | ViewVerifyOtp\n\nexport interface ViewsMap {\n  [key: string]: ViewType\n}\n\nexport interface Theme {\n  default: ThemeVariables\n  [key: string]: ThemeVariables\n}\n\nexport type RedirectTo = undefined | string\nexport type OtpType = EmailOtpType | MobileOtpType\n\nexport interface BaseAppearance {\n  theme?: Theme\n  prependedClassName?: string\n  extend?: boolean\n  variables?: {\n    default: ThemeVariables\n    [key: string]: ThemeVariables\n  }\n  className?: {\n    anchor?: string | CssComponent\n    button?: string | CssComponent\n    container?: string | CssComponent\n    divider?: string | CssComponent\n    input?: string | CssComponent\n    label?: string | CssComponent\n    loader?: string | CssComponent\n    message?: string | CssComponent\n  }\n}\n\nexport type ProviderScopes = {\n  [key in Partial<Provider>]: string\n}\n\nexport interface BaseAuth {\n  supabaseClient: SupabaseClient\n  socialLayout?: SocialLayout\n  providers?: Provider[]\n  providerScopes?: Partial<ProviderScopes>\n  queryParams?: { [key: string]: string }\n  view?: ViewType\n  redirectTo?: RedirectTo\n  onlyThirdPartyProviders?: boolean\n  magicLink?: boolean\n  showLinks?: boolean\n  otpType?: OtpType\n  additionalData?: { [key: string]: any }\n\n  /**\n   * This will toggle on the dark variation of the theme\n   */\n  dark?: boolean\n  /**\n   * Override the labels and button text\n   */\n  localization?: {\n    variables?: I18nVariables\n  }\n  theme?: 'default' | string\n}\n\nexport type I18nVariables = {\n  sign_up?: {\n    email_label?: string\n    password_label?: string\n    email_input_placeholder?: string\n    password_input_placeholder?: string\n    button_label?: string\n    loading_button_label?: string\n    social_provider_text?: string\n    link_text?: string\n    confirmation_text?: string\n  }\n  sign_in?: {\n    email_label?: string\n    password_label?: string\n    email_input_placeholder?: string\n    password_input_placeholder?: string\n    button_label?: string\n    loading_button_label?: string\n    social_provider_text?: string\n    link_text?: string\n  }\n  magic_link?: {\n    email_input_label?: string\n    email_input_placeholder?: string\n    button_label?: string\n    loading_button_label?: string\n    link_text?: string\n    confirmation_text?: string\n    empty_email_address?: string\n  }\n  forgotten_password?: {\n    email_label?: string\n    password_label?: string\n    email_input_placeholder?: string\n    button_label?: string\n    loading_button_label?: string\n    link_text?: string\n    confirmation_text?: string\n  }\n  update_password?: {\n    password_label?: string\n    password_input_placeholder?: string\n    button_label?: string\n    loading_button_label?: string\n    confirmation_text?: string\n  }\n  verify_otp?: {\n    email_input_label?: string\n    email_input_placeholder?: string\n    phone_input_label?: string\n    phone_input_placeholder?: string\n    token_input_label?: string\n    token_input_placeholder?: string\n    button_label?: string\n    loading_button_label?: string\n  }\n}\n", "function value(src: any, next: any) {\n  let k\n  if (src && next && typeof src === 'object' && typeof next === 'object') {\n    if (Array.isArray(next)) {\n      for (k = 0; k < next.length; k++) {\n        src[k] = value(src[k], next[k])\n      }\n    } else {\n      for (k in next) {\n        src[k] = value(src[k], next[k])\n      }\n    }\n    return src\n  }\n  return next\n}\n\nexport function merge(target: any, ...args: any) {\n  let len: number = args.length\n  for (let i = 0; i < len; i++) {\n    target = value(target, args[i])\n  }\n  return target\n}\n\n// Source https://stackoverflow.com/a/61634647/811799\nexport function template(string: string, data: Record<string, string>) {\n  return string.replace(\n    /{{(\\w+)}}/g,\n    (placeholderWithDelimiters, placeholderWithoutDelimiters) =>\n      data.hasOwnProperty(placeholderWithoutDelimiters)\n        ? data[placeholderWithoutDelimiters]\n        : placeholderWithDelimiters\n  )\n}\n", "{\n  \"sign_up\": {\n    \"email_label\": \"Email address\",\n    \"password_label\": \"Create a Password\",\n    \"email_input_placeholder\": \"Your email address\",\n    \"password_input_placeholder\": \"Your password\",\n    \"button_label\": \"Sign up\",\n    \"loading_button_label\": \"Signing up ...\",\n    \"social_provider_text\": \"Sign in with {{provider}}\",\n    \"link_text\": \"Don't have an account? Sign up\",\n    \"confirmation_text\": \"Check your email for the confirmation link\"\n  },\n  \"sign_in\": {\n    \"email_label\": \"Email address\",\n    \"password_label\": \"Your Password\",\n    \"email_input_placeholder\": \"Your email address\",\n    \"password_input_placeholder\": \"Your password\",\n    \"button_label\": \"Sign in\",\n    \"loading_button_label\": \"Signing in ...\",\n    \"social_provider_text\": \"Sign in with {{provider}}\",\n    \"link_text\": \"Already have an account? Sign in\"\n  },\n  \"magic_link\": {\n    \"email_input_label\": \"Email address\",\n    \"email_input_placeholder\": \"Your email address\",\n    \"button_label\": \"Send Magic Link\",\n    \"loading_button_label\": \"Sending Magic Link ...\",\n    \"link_text\": \"Send a magic link email\",\n    \"confirmation_text\": \"Check your email for the magic link\"\n  },\n  \"forgotten_password\": {\n    \"email_label\": \"Email address\",\n    \"password_label\": \"Your Password\",\n    \"email_input_placeholder\": \"Your email address\",\n    \"button_label\": \"Send reset password instructions\",\n    \"loading_button_label\": \"Sending reset instructions ...\",\n    \"link_text\": \"Forgot your password?\",\n    \"confirmation_text\": \"Check your email for the password reset link\"\n  },\n  \"update_password\": {\n    \"password_label\": \"New password\",\n    \"password_input_placeholder\": \"Your new password\",\n    \"button_label\": \"Update password\",\n    \"loading_button_label\": \"Updating password ...\",\n    \"confirmation_text\": \"Your password has been updated\"\n  },\n  \"verify_otp\": {\n    \"email_input_label\": \"Email address\",\n    \"email_input_placeholder\": \"Your email address\",\n    \"phone_input_label\": \"Phone number\",\n    \"phone_input_placeholder\": \"Your phone number\",\n    \"token_input_label\": \"Token\",\n    \"token_input_placeholder\": \"Your Otp token\",\n    \"button_label\": \"Verify token\",\n    \"loading_button_label\": \"Signing in ...\"\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACeO,IAAM,YAAmB;AAAA,EAC9B,SAAS;AAAA,IACP,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,yBAAyB;AAAA,MACzB,8BAA8B;AAAA,MAC9B,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,mBAAmB;AAAA,MACnB,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,yBAAyB;AAAA,MACzB,qBAAqB;AAAA,MACrB,iBAAiB;AAAA,MACjB,sBAAsB;AAAA,IACxB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,MACnB,eAAe;AAAA,MACf,cAAc;AAAA,IAChB;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,eAAe;AAAA,MACf,eAAe;AAAA,MACf,gBAAgB;AAAA,IAClB;AAAA,IACA,OAAO;AAAA,MACL,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,IACnB;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,cAAc;AAAA,MACZ,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,IACpB;AAAA;AAAA,IAEA,OAAO;AAAA,MACL,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,IACrB;AAAA;AAAA;AAAA;AAAA,EAIF;AAAA,EACA,MAAM;AAAA,IACJ,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,yBAAyB;AAAA,MACzB,8BAA8B;AAAA,MAC9B,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,mBAAmB;AAAA,MACnB,eAAe;AAAA,MACf,yBAAyB;AAAA,IAC3B;AAAA,EACF;AACF;AAEO,IAAM,eAAsB;AAAA,EACjC,SAAS;AAAA,IACP,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,yBAAyB;AAAA,MACzB,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,mBAAmB;AAAA,MACnB,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,yBAAyB;AAAA,MACzB,qBAAqB;AAAA,IACvB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,YAAY;AAAA,IACd;AAAA,IACA,WAAW;AAAA,MACT,eAAe;AAAA,MACf,eAAe;AAAA,IACjB;AAAA,IACA,OAAO;AAAA,MACL,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA;AAAA,IAEnB;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,cAAc,CAAC;AAAA;AAAA,IAEf,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA,EAIV;AAAA,EACA,MAAM;AAAA,IACJ,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,yBAAyB;AAAA,MACzB,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,mBAAmB;AAAA,MACnB,eAAe;AAAA,MACf,yBAAyB;AAAA,IAC3B;AAAA,EACF;AACF;;;AC7JA,IAAM,WAA2B;AAAA,EAC/B,QAAQ;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,yBAAyB;AAAA,IACzB,8BAA8B;AAAA,IAC9B,qBAAqB;AAAA,IACrB,mBAAmB;AAAA,IACnB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,sBAAsB;AAAA,EACxB;AAAA,EACA,OAAO;AAAA,IACL,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,cAAc;AAAA,EAChB;AAAA,EACA,WAAW;AAAA,IACT,cAAc;AAAA,IACd,eAAe;AAAA,IACf,eAAe;AAAA,IACf,gBAAgB;AAAA,EAClB;AAAA,EACA,OAAO;AAAA,IACL,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AAAA,IACZ,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,EACpB;AAAA;AAAA,EAEA,OAAO;AAAA,IACL,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,EACrB;AAAA;AAAA;AAAA;AAIF;AAEA,IAAM,mBAAmC;AAAA,EACvC,QAAQ;AAAA,IACN,iBAAiB;AAAA,IACjB,yBAAyB;AAAA,IACzB,8BAA8B;AAAA,IAC9B,qBAAqB;AAAA,IACrB,mBAAmB;AAAA,IACnB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,kBAAkB;AAAA,EACpB;AACF;AAEA,IAAM,UAA0B;AAAA,EAC9B,QAAQ;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,yBAAyB;AAAA,IACzB,qBAAqB;AAAA,IACrB,mBAAmB;AAAA,IACnB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,WAAW;AAAA,IACX,kBAAkB;AAAA,EACpB;AAAA,EACA,OAAO;AAAA,IACL,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,EACd;AAAA,EACA,WAAW;AAAA,IACT,eAAe;AAAA,IACf,eAAe;AAAA,EACjB;AAAA,EACA,OAAO;AAAA,IACL,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,iBAAiB;AAAA;AAAA,EAEnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,CAAC;AAAA;AAAA,EAEf,OAAO,CAAC;AAAA;AAAA;AAAA;AAIV;AAEA,IAAM,cAA8B;AAAA,EAClC,QAAQ;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,yBAAyB;AAAA,IACzB,qBAAqB;AAAA,IACrB,mBAAmB;AAAA,IACnB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,WAAW;AAAA,IACX,kBAAkB;AAAA,EACpB;AACF;AAEA,IAAM,aAAa;AAAA,EACjB,UAAU;AAAA,EACV,SAAS;AACX;;;AC3JO,IAAM,QAAkB;AAAA,EAC7B,SAAS;AAAA,EACT,SAAS;AAAA,EACT,oBAAoB;AAAA,EACpB,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,YAAY;AACd;AAEO,IAAM,wBAAwB;AAM9B,IAAM,cAAc;AAAA;AAAA,EAEzB,MAAM;AAAA,EACN,SAAS,MAAM;AAAA,EACf,SAAS,MAAM;AAAA,EACf,oBAAoB,MAAM;AAAA,EAC1B,YAAY,MAAM;AAAA,EAClB,iBAAiB,MAAM;AAAA;AAAA,EAEvB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,SAAS;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AACX;;;AC/BO,SAAS,mBAId,cAYA,eAIA,YACA;AAxBF;AAyBE,QAAM,aAAa,CAAC;AACpB,QAAM,YAAY,YAAY,YAAY;AAE1C,aAAW;AAAA,KACT,yCAAY,uBACR,yCAAY,sBAAqB,MAAM,YACvC,wBAAwB,MAAM;AAAA,EACpC;AAEA,OAAI,8CAAY,cAAZ,mBAAwB,eAAe;AACzC,eAAW,MAAK,8CAAY,cAAZ,mBAAwB,aAAa;AAAA,EACvD;AAEA,OAAI,yCAAY,YAAW,WAAa,yCAAY,YAAW,MAAM;AACnE,eAAW,KAAK,aAAa;AAAA,EAC/B;AAEA,SAAO;AACT;;;AClBO,IAAK,gBAAL,kBAAKA,mBAAL;AACL,EAAAA,8BAAA;AACA,EAAAA,8BAAA;AAFU,SAAAA;AAAA,GAAA;;;ACzBZ,SAAS,MAAM,KAAU,MAAW;AAClC,MAAI;AACJ,MAAI,OAAO,QAAQ,OAAO,QAAQ,YAAY,OAAO,SAAS,UAAU;AACtE,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,WAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAChC,YAAI,CAAC,IAAI,MAAM,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,MAChC;AAAA,IACF,OAAO;AACL,WAAK,KAAK,MAAM;AACd,YAAI,CAAC,IAAI,MAAM,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,MAChC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEO,SAAS,MAAM,WAAgB,MAAW;AAC/C,MAAI,MAAc,KAAK;AACvB,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,aAAS,MAAM,QAAQ,KAAK,CAAC,CAAC;AAAA,EAChC;AACA,SAAO;AACT;AAGO,SAAS,SAAS,QAAgB,MAA8B;AACrE,SAAO,OAAO;AAAA,IACZ;AAAA,IACA,CAAC,2BAA2B,iCAC1B,KAAK,eAAe,4BAA4B,IAC5C,KAAK,4BAA4B,IACjC;AAAA,EACR;AACF;;;AClCA;AAAA,EACE,SAAW;AAAA,IACT,aAAe;AAAA,IACf,gBAAkB;AAAA,IAClB,yBAA2B;AAAA,IAC3B,4BAA8B;AAAA,IAC9B,cAAgB;AAAA,IAChB,sBAAwB;AAAA,IACxB,sBAAwB;AAAA,IACxB,WAAa;AAAA,IACb,mBAAqB;AAAA,EACvB;AAAA,EACA,SAAW;AAAA,IACT,aAAe;AAAA,IACf,gBAAkB;AAAA,IAClB,yBAA2B;AAAA,IAC3B,4BAA8B;AAAA,IAC9B,cAAgB;AAAA,IAChB,sBAAwB;AAAA,IACxB,sBAAwB;AAAA,IACxB,WAAa;AAAA,EACf;AAAA,EACA,YAAc;AAAA,IACZ,mBAAqB;AAAA,IACrB,yBAA2B;AAAA,IAC3B,cAAgB;AAAA,IAChB,sBAAwB;AAAA,IACxB,WAAa;AAAA,IACb,mBAAqB;AAAA,EACvB;AAAA,EACA,oBAAsB;AAAA,IACpB,aAAe;AAAA,IACf,gBAAkB;AAAA,IAClB,yBAA2B;AAAA,IAC3B,cAAgB;AAAA,IAChB,sBAAwB;AAAA,IACxB,WAAa;AAAA,IACb,mBAAqB;AAAA,EACvB;AAAA,EACA,iBAAmB;AAAA,IACjB,gBAAkB;AAAA,IAClB,4BAA8B;AAAA,IAC9B,cAAgB;AAAA,IAChB,sBAAwB;AAAA,IACxB,mBAAqB;AAAA,EACvB;AAAA,EACA,YAAc;AAAA,IACZ,mBAAqB;AAAA,IACrB,yBAA2B;AAAA,IAC3B,mBAAqB;AAAA,IACrB,yBAA2B;AAAA,IAC3B,mBAAqB;AAAA,IACrB,yBAA2B;AAAA,IAC3B,cAAgB;AAAA,IAChB,sBAAwB;AAAA,EAC1B;AACF;", "names": ["SocialLayouts"]}