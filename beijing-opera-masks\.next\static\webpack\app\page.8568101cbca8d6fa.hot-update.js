"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _data_masks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/masks */ \"(app-pages-browser)/./src/data/masks.ts\");\n/* harmony import */ var _services_maskService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/maskService */ \"(app-pages-browser)/./src/services/maskService.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/providers/ThemeProvider */ \"(app-pages-browser)/./src/components/providers/ThemeProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Home() {\n    var _selectedMask_images, _selectedMask_images1, _selectedMask_culturalBackground;\n    _s();\n    const [masks, setMasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_data_masks__WEBPACK_IMPORTED_MODULE_3__.operaMasks);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedMask, setSelectedMask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { colors } = (0,_components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    // 加载脸谱数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const loadMasks = {\n                \"Home.useEffect.loadMasks\": async ()=>{\n                    if (!(0,_lib_supabase__WEBPACK_IMPORTED_MODULE_5__.isSupabaseConfigured)()) {\n                        console.log('Using static mask data');\n                        return;\n                    }\n                    setLoading(true);\n                    try {\n                        const maskData = await _services_maskService__WEBPACK_IMPORTED_MODULE_4__.MaskService.getAllApprovedMasks();\n                        setMasks(maskData);\n                    } catch (error) {\n                        console.error('Error loading masks:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"Home.useEffect.loadMasks\"];\n            loadMasks();\n        }\n    }[\"Home.useEffect\"], []);\n    const handleMaskClick = (mask)=>{\n        setSelectedMask(mask);\n    };\n    const closeModal = ()=>{\n        setSelectedMask(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            backgroundColor: colors.background,\n            color: colors.textPrimary,\n            padding: '2rem'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center',\n                    marginBottom: '3rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            fontSize: '2.5rem',\n                            fontWeight: 'bold',\n                            marginBottom: '1rem',\n                            background: 'linear-gradient(135deg, #DC2626, #B91C1C)',\n                            WebkitBackgroundClip: 'text',\n                            WebkitTextFillColor: 'transparent',\n                            fontFamily: '\"Ma Shan Zheng\", cursive'\n                        },\n                        children: \"\\uD83C\\uDFAD 京剧脸谱文化展示\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            fontSize: '1.125rem',\n                            color: colors.textSecondary,\n                            maxWidth: '600px',\n                            margin: '0 auto'\n                        },\n                        children: \"探索中国传统京剧脸谱艺术的魅力，了解每个角色背后的文化内涵\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center',\n                    padding: '2rem',\n                    color: colors.textSecondary\n                },\n                children: \"正在加载脸谱数据...\"\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                    gap: '2rem',\n                    maxWidth: '1200px',\n                    margin: '0 auto'\n                },\n                children: masks.map((mask)=>{\n                    var _mask_images, _mask_images1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: ()=>handleMaskClick(mask),\n                        style: {\n                            backgroundColor: colors.cardBackground,\n                            borderRadius: '12px',\n                            padding: '1.5rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.3s ease',\n                            border: \"1px solid \".concat(colors.border),\n                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: '100%',\n                                    height: '200px',\n                                    borderRadius: '8px',\n                                    overflow: 'hidden',\n                                    marginBottom: '1rem'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: ((_mask_images = mask.images) === null || _mask_images === void 0 ? void 0 : _mask_images.fullSize) || mask.imageUrl || ((_mask_images1 = mask.images) === null || _mask_images1 === void 0 ? void 0 : _mask_images1.thumbnail),\n                                    alt: mask.name,\n                                    style: {\n                                        width: '100%',\n                                        height: '100%',\n                                        objectFit: 'cover'\n                                    },\n                                    onError: (e)=>{\n                                        // 图片加载失败时的备用处理\n                                        const target = e.target;\n                                        target.src = \"https://via.placeholder.com/300x300/DC143C/FFFFFF?text=\".concat(encodeURIComponent(mask.name));\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    fontSize: '1.25rem',\n                                    fontWeight: 'bold',\n                                    marginBottom: '0.5rem',\n                                    color: colors.textPrimary\n                                },\n                                children: mask.name\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: '0.875rem',\n                                    color: colors.textSecondary,\n                                    marginBottom: '1rem'\n                                },\n                                children: [\n                                    \"角色: \",\n                                    mask.character\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this),\n                            (mask.colorTheme || mask.mainColors) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '0.5rem',\n                                    marginBottom: '1rem'\n                                },\n                                children: (mask.colorTheme || mask.mainColors || []).slice(0, 3).map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: '20px',\n                                            height: '20px',\n                                            borderRadius: '50%',\n                                            backgroundColor: color,\n                                            border: '1px solid rgba(0,0,0,0.1)'\n                                        },\n                                        title: color\n                                    }, index, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 15\n                            }, this),\n                            (mask.personalityTraits || mask.tags) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexWrap: 'wrap',\n                                    gap: '0.5rem'\n                                },\n                                children: (mask.personalityTraits || mask.tags || []).slice(0, 3).map((trait, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: '0.75rem',\n                                            padding: '0.25rem 0.5rem',\n                                            backgroundColor: colors.primary + '20',\n                                            color: colors.primary,\n                                            borderRadius: '12px'\n                                        },\n                                        children: trait\n                                    }, index, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, mask.id, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            selectedMask && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    zIndex: 1000,\n                    padding: '1rem'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: colors.background,\n                        borderRadius: '12px',\n                        padding: '2rem',\n                        maxWidth: '600px',\n                        width: '100%',\n                        maxHeight: '80vh',\n                        overflow: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center',\n                                marginBottom: '1.5rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        fontSize: '1.5rem',\n                                        fontWeight: 'bold',\n                                        color: colors.textPrimary\n                                    },\n                                    children: selectedMask.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: closeModal,\n                                    style: {\n                                        background: 'none',\n                                        border: 'none',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer',\n                                        color: colors.textSecondary\n                                    },\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: ((_selectedMask_images = selectedMask.images) === null || _selectedMask_images === void 0 ? void 0 : _selectedMask_images.fullSize) || selectedMask.imageUrl || ((_selectedMask_images1 = selectedMask.images) === null || _selectedMask_images1 === void 0 ? void 0 : _selectedMask_images1.thumbnail),\n                            alt: selectedMask.name,\n                            style: {\n                                width: '100%',\n                                height: '300px',\n                                objectFit: 'cover',\n                                borderRadius: '8px',\n                                marginBottom: '1.5rem'\n                            },\n                            onError: (e)=>{\n                                const target = e.target;\n                                target.src = \"https://via.placeholder.com/600x300/DC143C/FFFFFF?text=\".concat(encodeURIComponent(selectedMask.name));\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '0.875rem',\n                                color: colors.textSecondary,\n                                lineHeight: '1.6'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"角色:\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 18\n                                        }, this),\n                                        \" \",\n                                        selectedMask.character\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"文化背景:\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 18\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        marginLeft: '1rem',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: typeof selectedMask.culturalBackground === 'string' ? selectedMask.culturalBackground : ((_selectedMask_culturalBackground = selectedMask.culturalBackground) === null || _selectedMask_culturalBackground === void 0 ? void 0 : _selectedMask_culturalBackground.origin) || '传统京剧脸谱艺术'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this),\n                                (selectedMask.personalityTraits || selectedMask.tags) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"特征:\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 22\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                flexWrap: 'wrap',\n                                                gap: '0.5rem',\n                                                marginLeft: '1rem'\n                                            },\n                                            children: (selectedMask.personalityTraits || selectedMask.tags || []).map((trait, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.75rem',\n                                                        padding: '0.25rem 0.5rem',\n                                                        backgroundColor: colors.primary + '20',\n                                                        color: colors.primary,\n                                                        borderRadius: '12px'\n                                                    },\n                                                    children: trait\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"6X7EJKP6sHcAmHtdYakpvUSXzq8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__.useTheme\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});