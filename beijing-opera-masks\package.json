{"name": "beijing-opera-masks", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "node src/__tests__/utils.test.ts", "deploy": "node scripts/deploy.js", "docker:build": "docker build -t beijing-opera-masks .", "docker:run": "docker run -p 3000:3000 beijing-opera-masks", "analyze": "cross-env ANALYZE=true next build", "export": "next export"}, "dependencies": {"@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.52.0", "clsx": "^2.1.1", "next": "15.4.2", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "typescript": "5.8.3"}}