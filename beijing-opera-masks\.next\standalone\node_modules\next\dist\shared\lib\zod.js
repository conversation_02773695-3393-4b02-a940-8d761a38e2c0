"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    formatZodError: null,
    normalizeZodErrors: null,
    reportZodError: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    formatZodError: function() {
        return formatZodError;
    },
    normalizeZodErrors: function() {
        return normalizeZodErrors;
    },
    reportZodError: function() {
        return reportZodError;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _zod = require("next/dist/compiled/zod");
const _zodvalidationerror = require("next/dist/compiled/zod-validation-error");
const _log = /*#__PURE__*/ _interop_require_wildcard._(require("../../build/output/log"));
function processZodErrorMessage(issue) {
    let message = issue.message;
    let path;
    if (issue.path.length > 0) {
        if (issue.path.length === 1) {
            const identifier = issue.path[0];
            if (typeof identifier === 'number') {
                // The first identifier inside path is a number
                path = "index " + identifier;
            } else {
                path = '"' + identifier + '"';
            }
        } else {
            // joined path to be shown in the error message
            path = '"' + issue.path.reduce((acc, cur)=>{
                if (typeof cur === 'number') {
                    // array index
                    return acc + "[" + cur + "]";
                }
                if (cur.includes('"')) {
                    // escape quotes
                    return acc + '["' + cur.replaceAll('"', '\\"') + '"]';
                }
                // dot notation
                const separator = acc.length === 0 ? '' : '.';
                return acc + separator + cur;
            }, '') + '"';
        }
    } else {
        path = '';
    }
    if (issue.code === 'invalid_type' && issue.received === _zod.ZodParsedType.undefined) {
        // Missing key in object.
        return path + " is missing, expected " + issue.expected;
    }
    if (issue.code === 'invalid_enum_value') {
        // Remove "Invalid enum value" prefix from zod default error message
        return "Expected " + _zod.util.joinValues(issue.options) + ", received '" + issue.received + "' at " + path;
    }
    return message + (path ? " at " + path : '');
}
function normalizeZodErrors(error) {
    return error.issues.flatMap((issue)=>{
        const issues = [
            {
                issue,
                message: processZodErrorMessage(issue)
            }
        ];
        if ('unionErrors' in issue) {
            for (const unionError of issue.unionErrors){
                issues.push(...normalizeZodErrors(unionError));
            }
        }
        return issues;
    });
}
function formatZodError(prefix, error) {
    return Object.defineProperty(new Error((0, _zodvalidationerror.fromZodError)(error, {
        prefix: prefix
    }).toString()), "__NEXT_ERROR_CODE", {
        value: "E394",
        enumerable: false,
        configurable: true
    });
}
function reportZodError(prefix, error) {
    _log.error(formatZodError(prefix, error).message);
}

//# sourceMappingURL=zod.js.map