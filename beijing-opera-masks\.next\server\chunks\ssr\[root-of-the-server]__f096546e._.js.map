{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/data/masks.ts"], "sourcesContent": ["import { OperaMask } from '@/types/mask';\n\nexport const operaMasks: OperaMask[] = [\n  {\n    id: 'guanyu',\n    name: '关羽脸谱',\n    character: '关羽',\n    roleCategory: '净',\n    colorCategory: '红脸',\n    mainColors: ['#DC143C', '#FFD700', '#000000'],\n    culturalBackground: {\n      origin: '三国时期蜀汉名将，被尊为武圣',\n      personality: '忠义勇武，刚正不阿，义薄云天',\n      symbolism: '忠诚、正义、勇敢的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '红色': '忠勇正义，赤胆忠心',\n      '金色': '神圣威严，地位崇高',\n      '黑色': '刚毅坚定，不可动摇'\n    },\n    relatedOperas: [\n      { name: '单刀会', description: '关羽单刀赴会的故事', period: '三国' },\n      { name: '华容道', description: '关羽义释曹操', period: '三国' },\n      { name: '走麦城', description: '关羽败走麦城', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=关羽',\n      fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=关羽脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹红色底色', duration: 1000, color: '#DC143C' },\n      { id: 2, name: '眉毛', description: '绘制浓眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1200, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘鼻梁线条', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加金色装饰', duration: 1000, color: '#FFD700' }\n    ],\n    difficulty: 'medium',\n    popularity: 10,\n    tags: ['三国', '武将', '忠义', '经典']\n  },\n  {\n    id: 'baogong',\n    name: '包拯脸谱',\n    character: '包拯',\n    roleCategory: '净',\n    colorCategory: '黑脸',\n    mainColors: ['#000000', '#FFFFFF', '#FFD700'],\n    culturalBackground: {\n      origin: '北宋名臣，以清廉公正著称',\n      personality: '铁面无私，执法如山，清正廉洁',\n      symbolism: '公正执法，清廉为民的象征',\n      historicalPeriod: '北宋时期'\n    },\n    colorMeaning: {\n      '黑色': '公正严明，铁面无私',\n      '白色': '清廉正直，一尘不染',\n      '金色': '威严庄重，地位尊崇'\n    },\n    relatedOperas: [\n      { name: '铡美案', description: '包拯铡驸马陈世美', period: '宋代' },\n      { name: '打龙袍', description: '包拯怒斥宋仁宗', period: '宋代' },\n      { name: '赤桑镇', description: '包拯审案的故事', period: '宋代' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/000000/FFFFFF?text=包拯',\n      fullSize: 'https://via.placeholder.com/600x600/000000/FFFFFF?text=包拯脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹黑色底色', duration: 1000, color: '#000000' },\n      { id: 2, name: '额头', description: '绘制白色月牙', duration: 800, color: '#FFFFFF' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1000, color: '#FFFFFF' },\n      { id: 4, name: '鼻翼', description: '描绘鼻翼线条', duration: 600, color: '#FFFFFF' },\n      { id: 5, name: '装饰', description: '添加金色细节', duration: 800, color: '#FFD700' }\n    ],\n    difficulty: 'easy',\n    popularity: 9,\n    tags: ['宋代', '清官', '正义', '经典']\n  },\n  {\n    id: 'caocao',\n    name: '曹操脸谱',\n    character: '曹操',\n    roleCategory: '净',\n    colorCategory: '白脸',\n    mainColors: ['#FFFFFF', '#000000', '#DC143C'],\n    culturalBackground: {\n      origin: '东汉末年政治家、军事家、文学家',\n      personality: '奸诈狡猾，野心勃勃，但才华横溢',\n      symbolism: '奸诈、权谋、复杂人性的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '白色': '奸诈狡猾，阴险毒辣',\n      '黑色': '深沉城府，心机深重',\n      '红色': '暴戾之气，杀伐果断'\n    },\n    relatedOperas: [\n      { name: '捉放曹', description: '陈宫捉放曹操', period: '三国' },\n      { name: '击鼓骂曹', description: '祢衡击鼓骂曹', period: '三国' },\n      { name: '群英会', description: '曹操与群雄斗智', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/FFFFFF/000000?text=曹操',\n      fullSize: 'https://via.placeholder.com/600x600/FFFFFF/000000?text=曹操脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹白色底色', duration: 1000, color: '#FFFFFF' },\n      { id: 2, name: '眉毛', description: '绘制黑色浓眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1200, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘鼻梁阴影', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加红色细节', duration: 1000, color: '#DC143C' }\n    ],\n    difficulty: 'medium',\n    popularity: 8,\n    tags: ['三国', '奸雄', '复杂', '经典']\n  },\n  {\n    id: 'zhangfei',\n    name: '张飞脸谱',\n    character: '张飞',\n    roleCategory: '净',\n    colorCategory: '黑脸',\n    mainColors: ['#000000', '#FFFFFF', '#DC143C'],\n    culturalBackground: {\n      origin: '三国时期蜀汉名将，刘备义弟',\n      personality: '勇猛粗犷，嫉恶如仇，忠义豪爽',\n      symbolism: '勇猛、正直、豪爽的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '黑色': '刚直勇猛，正气凛然',\n      '白色': '纯真豪爽，心无城府',\n      '红色': '热血沸腾，义气冲天'\n    },\n    relatedOperas: [\n      { name: '长坂坡', description: '张飞大战长坂坡', period: '三国' },\n      { name: '古城会', description: '张飞误会关羽', period: '三国' },\n      { name: '芦花荡', description: '张飞智取芦花荡', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/000000/FFFFFF?text=张飞',\n      fullSize: 'https://via.placeholder.com/600x600/000000/FFFFFF?text=张飞脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹黑色底色', duration: 1000, color: '#000000' },\n      { id: 2, name: '眉毛', description: '绘制白色粗眉', duration: 800, color: '#FFFFFF' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1000, color: '#FFFFFF' },\n      { id: 4, name: '鼻翼', description: '描绘鼻翼线条', duration: 600, color: '#FFFFFF' },\n      { id: 5, name: '装饰', description: '添加红色装饰', duration: 800, color: '#DC143C' }\n    ],\n    difficulty: 'easy',\n    popularity: 8,\n    tags: ['三国', '武将', '勇猛', '豪爽']\n  },\n  {\n    id: 'doulujin',\n    name: '窦尔敦脸谱',\n    character: '窦尔敦',\n    roleCategory: '净',\n    colorCategory: '蓝脸',\n    mainColors: ['#0066CC', '#FFFFFF', '#FFD700'],\n    culturalBackground: {\n      origin: '清代绿林好汉，盗侠传奇人物',\n      personality: '刚强勇猛，侠肝义胆，不畏强权',\n      symbolism: '刚强、勇敢、反抗精神的象征',\n      historicalPeriod: '清代'\n    },\n    colorMeaning: {\n      '蓝色': '刚强勇猛，桀骜不驯',\n      '白色': '正直豪爽，光明磊落',\n      '金色': '英雄气概，不凡身份'\n    },\n    relatedOperas: [\n      { name: '盗御马', description: '窦尔敦盗取御马', period: '清代' },\n      { name: '连环套', description: '窦尔敦中计被擒', period: '清代' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/0066CC/FFFFFF?text=窦尔敦',\n      fullSize: 'https://via.placeholder.com/600x600/0066CC/FFFFFF?text=窦尔敦脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹蓝色底色', duration: 1000, color: '#0066CC' },\n      { id: 2, name: '眉毛', description: '绘制白色眉毛', duration: 800, color: '#FFFFFF' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1000, color: '#FFFFFF' },\n      { id: 4, name: '鼻梁', description: '描绘鼻梁线条', duration: 600, color: '#FFFFFF' },\n      { id: 5, name: '装饰', description: '添加金色装饰', duration: 800, color: '#FFD700' }\n    ],\n    difficulty: 'medium',\n    popularity: 7,\n    tags: ['清代', '绿林', '侠客', '勇猛']\n  },\n  {\n    id: 'yangqilang',\n    name: '杨七郎脸谱',\n    character: '杨七郎',\n    roleCategory: '生',\n    colorCategory: '红脸',\n    mainColors: ['#DC143C', '#FFD700', '#000000'],\n    culturalBackground: {\n      origin: '北宋杨家将中的七子杨延嗣',\n      personality: '英勇善战，忠君爱国，血性男儿',\n      symbolism: '忠勇、牺牲、家国情怀的象征',\n      historicalPeriod: '北宋时期'\n    },\n    colorMeaning: {\n      '红色': '忠勇热血，为国捐躯',\n      '金色': '英雄本色，光耀门第',\n      '黑色': '刚毅果敢，义无反顾'\n    },\n    relatedOperas: [\n      { name: '杨家将', description: '杨家将抗辽的故事', period: '宋代' },\n      { name: '四郎探母', description: '杨四郎探望母亲', period: '宋代' },\n      { name: '穆桂英挂帅', description: '穆桂英率军出征', period: '宋代' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=杨七郎',\n      fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=杨七郎脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹红色底色', duration: 1000, color: '#DC143C' },\n      { id: 2, name: '眉毛', description: '绘制黑色剑眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘鼻梁线条', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加金色装饰', duration: 800, color: '#FFD700' }\n    ],\n    difficulty: 'medium',\n    popularity: 7,\n    tags: ['宋代', '杨家将', '忠勇', '英雄']\n  },\n  {\n    id: 'yangguifei',\n    name: '杨贵妃脸谱',\n    character: '杨贵妃',\n    roleCategory: '旦',\n    colorCategory: '红脸',\n    mainColors: ['#FFB6C1', '#FFD700', '#DC143C'],\n    culturalBackground: {\n      origin: '唐代著名美女，唐玄宗宠妃',\n      personality: '美丽动人，聪慧机敏，但也任性娇纵',\n      symbolism: '美丽、爱情、悲剧的象征',\n      historicalPeriod: '唐代'\n    },\n    colorMeaning: {\n      '粉红色': '娇美动人，温柔如水',\n      '金色': '富贵荣华，地位尊贵',\n      '红色': '热情如火，爱情炽烈'\n    },\n    relatedOperas: [\n      { name: '贵妃醉酒', description: '杨贵妃醉酒的故事', period: '唐代' },\n      { name: '长生殿', description: '唐玄宗与杨贵妃的爱情', period: '唐代' },\n      { name: '马嵬坡', description: '杨贵妃马嵬坡之死', period: '唐代' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/FFB6C1/FFFFFF?text=杨贵妃',\n      fullSize: 'https://via.placeholder.com/600x600/FFB6C1/FFFFFF?text=杨贵妃脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹粉色底色', duration: 1000, color: '#FFB6C1' },\n      { id: 2, name: '眉毛', description: '绘制柳叶眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画凤眼轮廓', duration: 1200, color: '#000000' },\n      { id: 4, name: '唇部', description: '描绘樱桃小口', duration: 600, color: '#DC143C' },\n      { id: 5, name: '装饰', description: '添加金色花钿', duration: 1000, color: '#FFD700' }\n    ],\n    difficulty: 'hard',\n    popularity: 9,\n    tags: ['唐代', '美女', '爱情', '悲剧']\n  },\n  {\n    id: 'wusong',\n    name: '武松脸谱',\n    character: '武松',\n    roleCategory: '净',\n    colorCategory: '红脸',\n    mainColors: ['#DC143C', '#000000', '#FFD700'],\n    culturalBackground: {\n      origin: '水浒传中的英雄好汉，行者武松',\n      personality: '勇猛无畏，嫉恶如仇，义薄云天',\n      symbolism: '正义、勇敢、反抗精神的象征',\n      historicalPeriod: '北宋时期'\n    },\n    colorMeaning: {\n      '红色': '正义凛然，热血沸腾',\n      '黑色': '刚毅果敢，不屈不挠',\n      '金色': '英雄本色，光明磊落'\n    },\n    relatedOperas: [\n      { name: '武松打虎', description: '武松景阳冈打虎', period: '宋代' },\n      { name: '狮子楼', description: '武松杀西门庆', period: '宋代' },\n      { name: '十字坡', description: '武松遇孙二娘', period: '宋代' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=武松',\n      fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=武松脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹红色底色', duration: 1000, color: '#DC143C' },\n      { id: 2, name: '眉毛', description: '绘制浓黑剑眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画虎目轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘挺直鼻梁', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加金色装饰', duration: 800, color: '#FFD700' }\n    ],\n    difficulty: 'medium',\n    popularity: 9,\n    tags: ['水浒', '英雄', '正义', '勇猛']\n  },\n  {\n    id: 'jianggan',\n    name: '蒋干脸谱',\n    character: '蒋干',\n    roleCategory: '丑',\n    colorCategory: '白脸',\n    mainColors: ['#FFFFFF', '#000000', '#808080'],\n    culturalBackground: {\n      origin: '三国时期人物，曹操谋士',\n      personality: '自作聪明，好事多磨，常弄巧成拙',\n      symbolism: '愚蠢、自负、滑稽的象征',\n      historicalPeriod: '三国时期'\n    },\n    colorMeaning: {\n      '白色': '愚蠢无知，自以为是',\n      '黑色': '心机不深，容易上当',\n      '灰色': '平庸无能，不值一提'\n    },\n    relatedOperas: [\n      { name: '群英会', description: '蒋干中计盗书', period: '三国' },\n      { name: '借东风', description: '诸葛亮借东风', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/FFFFFF/000000?text=蒋干',\n      fullSize: 'https://via.placeholder.com/600x600/FFFFFF/000000?text=蒋干脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹白色底色', duration: 1000, color: '#FFFFFF' },\n      { id: 2, name: '眉毛', description: '绘制细眉', duration: 600, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画小眼轮廓', duration: 800, color: '#000000' },\n      { id: 4, name: '鼻部', description: '描绘尖鼻', duration: 400, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加滑稽装饰', duration: 600, color: '#808080' }\n    ],\n    difficulty: 'easy',\n    popularity: 6,\n    tags: ['三国', '丑角', '滑稽', '愚蠢']\n  },\n  {\n    id: 'liubei',\n    name: '刘备脸谱',\n    character: '刘备',\n    roleCategory: '生',\n    colorCategory: '红脸',\n    mainColors: ['#DC143C', '#FFD700', '#000000'],\n    culturalBackground: {\n      origin: '三国时期蜀汉开国皇帝',\n      personality: '仁德宽厚，礼贤下士，志向远大',\n      symbolism: '仁德、理想、领袖风范的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '红色': '仁德之心，爱民如子',\n      '金色': '帝王之相，天命所归',\n      '黑色': '深沉稳重，胸怀大志'\n    },\n    relatedOperas: [\n      { name: '三顾茅庐', description: '刘备三顾茅庐请诸葛亮', period: '三国' },\n      { name: '甘露寺', description: '刘备招亲', period: '三国' },\n      { name: '白帝城', description: '刘备托孤', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=刘备',\n      fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=刘备脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹红色底色', duration: 1000, color: '#DC143C' },\n      { id: 2, name: '眉毛', description: '绘制慈眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画慈目轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '胡须', description: '描绘长须', duration: 1200, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加金色装饰', duration: 800, color: '#FFD700' }\n    ],\n    difficulty: 'medium',\n    popularity: 8,\n    tags: ['三国', '帝王', '仁德', '领袖']\n  },\n  {\n    id: 'huangzhong',\n    name: '黄忠脸谱',\n    character: '黄忠',\n    roleCategory: '净',\n    colorCategory: '黄脸',\n    mainColors: ['#FFD700', '#000000', '#DC143C'],\n    culturalBackground: {\n      origin: '三国时期蜀汉五虎上将之一',\n      personality: '老当益壮，勇猛善射，忠心耿耿',\n      symbolism: '老骥伏枥、壮心不已的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '黄色': '老成持重，经验丰富',\n      '黑色': '刚毅坚定，不服老迈',\n      '红色': '壮心不已，热血依然'\n    },\n    relatedOperas: [\n      { name: '定军山', description: '黄忠定军山斩夏侯渊', period: '三国' },\n      { name: '战长沙', description: '黄忠战关羽', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/FFD700/000000?text=黄忠',\n      fullSize: 'https://via.placeholder.com/600x600/FFD700/000000?text=黄忠脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹黄色底色', duration: 1000, color: '#FFD700' },\n      { id: 2, name: '眉毛', description: '绘制白眉', duration: 800, color: '#FFFFFF' },\n      { id: 3, name: '眼部', description: '勾画老目轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '胡须', description: '描绘白须', duration: 1200, color: '#FFFFFF' },\n      { id: 5, name: '装饰', description: '添加红色装饰', duration: 800, color: '#DC143C' }\n    ],\n    difficulty: 'medium',\n    popularity: 7,\n    tags: ['三国', '老将', '勇猛', '忠诚']\n  },\n  {\n    id: 'machao',\n    name: '马超脸谱',\n    character: '马超',\n    roleCategory: '净',\n    colorCategory: '银脸',\n    mainColors: ['#C0C0C0', '#000000', '#DC143C'],\n    culturalBackground: {\n      origin: '三国时期蜀汉五虎上将之一，西凉马腾之子',\n      personality: '英勇善战，威风凛凛，有万夫不当之勇',\n      symbolism: '英勇、威武、西北豪杰的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '银色': '英武不凡，光芒四射',\n      '黑色': '刚毅果敢，威风凛凛',\n      '红色': '热血沸腾，勇猛无敌'\n    },\n    relatedOperas: [\n      { name: '战渭南', description: '马超大战曹操', period: '三国' },\n      { name: '取成都', description: '马超助刘备取成都', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/C0C0C0/000000?text=马超',\n      fullSize: 'https://via.placeholder.com/600x600/C0C0C0/000000?text=马超脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹银色底色', duration: 1000, color: '#C0C0C0' },\n      { id: 2, name: '眉毛', description: '绘制黑色剑眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画鹰目轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘挺直鼻梁', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加红色装饰', duration: 800, color: '#DC143C' }\n    ],\n    difficulty: 'hard',\n    popularity: 7,\n    tags: ['三国', '西凉', '英武', '威猛']\n  },\n  {\n    id: 'zhaoyun',\n    name: '赵云脸谱',\n    character: '赵云',\n    roleCategory: '生',\n    colorCategory: '白脸',\n    mainColors: ['#FFFFFF', '#000000', '#4169E1'],\n    culturalBackground: {\n      origin: '三国时期蜀汉五虎上将之一，常山赵子龙',\n      personality: '英勇善战，忠心耿耿，智勇双全',\n      symbolism: '忠诚、勇敢、完美武将的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '白色': '纯洁忠诚，品格高尚',\n      '黑色': '刚毅果敢，意志坚定',\n      '蓝色': '冷静睿智，深谋远虑'\n    },\n    relatedOperas: [\n      { name: '长坂坡', description: '赵云长坂坡救阿斗', period: '三国' },\n      { name: '截江夺斗', description: '赵云截江救阿斗', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/FFFFFF/000000?text=赵云',\n      fullSize: 'https://via.placeholder.com/600x600/FFFFFF/000000?text=赵云脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹白色底色', duration: 1000, color: '#FFFFFF' },\n      { id: 2, name: '眉毛', description: '绘制黑色剑眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画英目轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘挺直鼻梁', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加蓝色装饰', duration: 800, color: '#4169E1' }\n    ],\n    difficulty: 'medium',\n    popularity: 9,\n    tags: ['三国', '完美', '忠诚', '英勇']\n  },\n  {\n    id: 'sunwukong',\n    name: '孙悟空脸谱',\n    character: '孙悟空',\n    roleCategory: '净',\n    colorCategory: '金脸',\n    mainColors: ['#FFD700', '#DC143C', '#000000'],\n    culturalBackground: {\n      origin: '西游记中的齐天大圣，花果山美猴王',\n      personality: '机智勇敢，神通广大，桀骜不驯',\n      symbolism: '反抗精神、智慧勇敢的象征',\n      historicalPeriod: '神话传说'\n    },\n    colorMeaning: {\n      '金色': '神通广大，法力无边',\n      '红色': '火眼金睛，热情如火',\n      '黑色': '桀骜不驯，不畏权威'\n    },\n    relatedOperas: [\n      { name: '大闹天宫', description: '孙悟空大闹天宫', period: '神话' },\n      { name: '三打白骨精', description: '孙悟空三打白骨精', period: '神话' },\n      { name: '真假美猴王', description: '真假美猴王大战', period: '神话' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/FFD700/000000?text=孙悟空',\n      fullSize: 'https://via.placeholder.com/600x600/FFD700/000000?text=孙悟空脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹金色底色', duration: 1000, color: '#FFD700' },\n      { id: 2, name: '眉毛', description: '绘制火焰眉', duration: 1000, color: '#DC143C' },\n      { id:3, name: '眼部', description: '勾画火眼金睛', duration: 1200, color: '#DC143C' },\n      { id: 4, name: '鼻部', description: '描绘猴鼻', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加神话装饰', duration: 1000, color: '#000000' }\n    ],\n    difficulty: 'hard',\n    popularity: 10,\n    tags: ['西游记', '神话', '反抗', '智慧']\n  }\n];\n\n// 按分类组织的脸谱数据\nexport const masksByRole = {\n  '生': operaMasks.filter(mask => mask.roleCategory === '生'),\n  '旦': operaMasks.filter(mask => mask.roleCategory === '旦'),\n  '净': operaMasks.filter(mask => mask.roleCategory === '净'),\n  '丑': operaMasks.filter(mask => mask.roleCategory === '丑')\n};\n\nexport const masksByColor = {\n  '红脸': operaMasks.filter(mask => mask.colorCategory === '红脸'),\n  '黑脸': operaMasks.filter(mask => mask.colorCategory === '黑脸'),\n  '白脸': operaMasks.filter(mask => mask.colorCategory === '白脸'),\n  '蓝脸': operaMasks.filter(mask => mask.colorCategory === '蓝脸'),\n  '绿脸': operaMasks.filter(mask => mask.colorCategory === '绿脸'),\n  '黄脸': operaMasks.filter(mask => mask.colorCategory === '黄脸'),\n  '金脸': operaMasks.filter(mask => mask.colorCategory === '金脸'),\n  '银脸': operaMasks.filter(mask => mask.colorCategory === '银脸')\n};\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,aAA0B;IACrC;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAa,QAAQ;YAAK;YACtD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;SACpD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;SAC9E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAY,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;YACpD;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;SACrD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAQ,aAAa;gBAAU,QAAQ;YAAK;YACpD;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;SACrD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;SAC9E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;YACpD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;SACrD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;YACpD;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;SACrD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAY,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAQ,aAAa;gBAAW,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAS,aAAa;gBAAW,QAAQ;YAAK;SACvD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAO;YAAM;SAAK;IACjC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,OAAO;YACP,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAQ,aAAa;gBAAY,QAAQ;YAAK;YACtD;gBAAE,MAAM;gBAAO,aAAa;gBAAc,QAAQ;YAAK;YACvD;gBAAE,MAAM;gBAAO,aAAa;gBAAY,QAAQ;YAAK;SACtD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAS,UAAU;gBAAK,OAAO;YAAU;YAC3E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;SAC9E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAQ,aAAa;gBAAW,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;SACpD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;SACpD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAQ,aAAa;gBAAc,QAAQ;YAAK;YACxD;gBAAE,MAAM;gBAAO,aAAa;gBAAQ,QAAQ;YAAK;YACjD;gBAAE,MAAM;gBAAO,aAAa;gBAAQ,QAAQ;YAAK;SAClD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAM,OAAO;YAAU;YAC3E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAa,QAAQ;YAAK;YACtD;gBAAE,MAAM;gBAAO,aAAa;gBAAS,QAAQ;YAAK;SACnD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAM,OAAO;YAAU;YAC3E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAO,aAAa;gBAAY,QAAQ;YAAK;SACtD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAY,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAQ,aAAa;gBAAW,QAAQ;YAAK;SACtD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAQ,aAAa;gBAAW,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAS,aAAa;gBAAY,QAAQ;YAAK;YACvD;gBAAE,MAAM;gBAAS,aAAa;gBAAW,QAAQ;YAAK;SACvD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAS,UAAU;gBAAM,OAAO;YAAU;YAC5E;gBAAE,IAAG;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;SAC9E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAO;YAAM;YAAM;SAAK;IACjC;CACD;AAGM,MAAM,cAAc;IACzB,KAAK,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,YAAY,KAAK;IACrD,KAAK,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,YAAY,KAAK;IACrD,KAAK,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,YAAY,KAAK;IACrD,KAAK,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,YAAY,KAAK;AACvD;AAEO,MAAM,eAAe;IAC1B,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;AACzD", "debugId": null}}, {"offset": {"line": 1285, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { operaMasks } from '@/data/masks';\nimport { OperaMask } from '@/types/mask';\n\nexport default function Home() {\n  const [selectedMask, setSelectedMask] = useState<OperaMask | null>(null);\n  const router = useRouter();\n\n  const handleMaskClick = (mask: OperaMask) => {\n    setSelectedMask(mask);\n  };\n\n  const handleViewDetails = (mask: OperaMask) => {\n    router.push(`/mask/${mask.id}`);\n  };\n\n  const handleCloseModal = () => {\n    setSelectedMask(null);\n  };\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      backgroundColor: '#F9FAFB',\n      fontFamily: '\"Noto Sans SC\", sans-serif'\n    }}>\n      {/* 头部 */}\n      <header style={{\n        backgroundColor: 'white',\n        borderBottom: '2px solid #F59E0B',\n        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n        position: 'sticky',\n        top: 0,\n        zIndex: 40\n      }}>\n        <div style={{\n          maxWidth: '1200px',\n          margin: '0 auto',\n          padding: '1rem 1.5rem',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        }}>\n          <h1 style={{\n            fontSize: '1.5rem',\n            fontWeight: 'bold',\n            color: '#1F2937',\n            fontFamily: '\"Noto Serif SC\", serif'\n          }}>\n            京剧脸谱文化展示平台\n          </h1>\n        </div>\n      </header>\n\n      {/* 主要内容 */}\n      <main style={{ padding: '2rem 1.5rem' }}>\n        {/* 英雄区域 */}\n        <section style={{\n          textAlign: 'center',\n          padding: '3rem 0',\n          background: 'linear-gradient(135deg, rgba(185, 28, 28, 0.05) 0%, transparent 50%, rgba(245, 158, 11, 0.05) 100%)',\n          borderRadius: '0.75rem',\n          marginBottom: '3rem'\n        }}>\n          <h2 style={{\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#1F2937',\n            fontFamily: '\"Noto Serif SC\", serif',\n            marginBottom: '1rem'\n          }}>\n            探索京剧脸谱的艺术魅力\n          </h2>\n          <p style={{\n            fontSize: '1.125rem',\n            color: '#6B7280',\n            maxWidth: '32rem',\n            margin: '0 auto 2rem'\n          }}>\n            深入了解中国传统戏曲文化，感受脸谱艺术的独特魅力与深厚内涵\n          </p>\n        </section>\n\n        {/* 脸谱网格 */}\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',\n          gap: '2rem',\n          maxWidth: '1200px',\n          margin: '0 auto'\n        }}>\n          {operaMasks.map((mask) => (\n            <div\n              key={mask.id}\n              onClick={() => handleMaskClick(mask)}\n              style={{\n                backgroundColor: 'white',\n                borderRadius: '0.75rem',\n                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',\n                border: '2px solid #F59E0B',\n                overflow: 'hidden',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease',\n                position: 'relative'\n              }}\n              onMouseEnter={(e) => {\n                e.currentTarget.style.transform = 'scale(1.05)';\n                e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)';\n              }}\n              onMouseLeave={(e) => {\n                e.currentTarget.style.transform = 'scale(1)';\n                e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';\n              }}\n            >\n              {/* 脸谱图片占位 */}\n              <div style={{\n                aspectRatio: '1',\n                background: `linear-gradient(135deg, ${mask.mainColors[0]} 0%, ${mask.mainColors[1] || mask.mainColors[0]} 100%)`,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: 'white',\n                fontSize: '2rem',\n                fontWeight: 'bold',\n                position: 'relative'\n              }}>\n                <div style={{\n                  position: 'absolute',\n                  inset: 0,\n                  background: 'linear-gradient(to top, rgba(0,0,0,0.2) 0%, transparent 100%)'\n                }} />\n                <span style={{ position: 'relative', zIndex: 10 }}>\n                  {mask.character}\n                </span>\n\n                {/* 分类标签 */}\n                <div style={{\n                  position: 'absolute',\n                  top: '0.75rem',\n                  left: '0.75rem',\n                  backgroundColor: 'rgba(0,0,0,0.7)',\n                  color: 'white',\n                  padding: '0.25rem 0.5rem',\n                  borderRadius: '9999px',\n                  fontSize: '0.75rem',\n                  fontWeight: '500'\n                }}>\n                  {mask.roleCategory}\n                </div>\n\n                <div style={{\n                  position: 'absolute',\n                  top: '0.75rem',\n                  right: '0.75rem',\n                  backgroundColor: 'rgba(0,0,0,0.7)',\n                  color: 'white',\n                  padding: '0.25rem 0.5rem',\n                  borderRadius: '9999px',\n                  fontSize: '0.75rem',\n                  fontWeight: '500'\n                }}>\n                  {mask.colorCategory}\n                </div>\n              </div>\n\n              {/* 脸谱信息 */}\n              <div style={{ padding: '1rem' }}>\n                <h3 style={{\n                  fontSize: '1.125rem',\n                  fontWeight: '600',\n                  color: '#1F2937',\n                  marginBottom: '0.5rem',\n                  fontFamily: '\"Noto Serif SC\", serif'\n                }}>\n                  {mask.name}\n                </h3>\n                <p style={{\n                  fontSize: '0.875rem',\n                  color: '#6B7280',\n                  marginBottom: '0.75rem',\n                  display: '-webkit-box',\n                  WebkitLineClamp: 2,\n                  WebkitBoxOrient: 'vertical',\n                  overflow: 'hidden'\n                }}>\n                  {mask.culturalBackground.personality}\n                </p>\n\n                {/* 主要颜色 */}\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  marginBottom: '0.75rem'\n                }}>\n                  <span style={{ fontSize: '0.75rem', color: '#6B7280' }}>主要色彩:</span>\n                  <div style={{ display: 'flex', gap: '0.25rem' }}>\n                    {mask.mainColors.slice(0, 3).map((color, index) => (\n                      <div\n                        key={index}\n                        style={{\n                          width: '1rem',\n                          height: '1rem',\n                          borderRadius: '50%',\n                          backgroundColor: color,\n                          border: '1px solid #D1D5DB'\n                        }}\n                        title={color}\n                      />\n                    ))}\n                  </div>\n                </div>\n\n                {/* 标签 */}\n                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.25rem' }}>\n                  {mask.tags.slice(0, 3).map((tag, index) => (\n                    <span\n                      key={index}\n                      style={{\n                        padding: '0.25rem 0.5rem',\n                        fontSize: '0.75rem',\n                        backgroundColor: '#F3F4F6',\n                        color: '#6B7280',\n                        borderRadius: '9999px'\n                      }}\n                    >\n                      {tag}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </main>\n\n      {/* 简单的模态框 */}\n      {selectedMask && (\n        <div\n          style={{\n            position: 'fixed',\n            inset: 0,\n            zIndex: 50,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            backdropFilter: 'blur(4px)'\n          }}\n          onClick={handleCloseModal}\n        >\n          <div\n            style={{\n              backgroundColor: 'white',\n              borderRadius: '0.75rem',\n              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',\n              border: '2px solid #F59E0B',\n              maxWidth: '90vw',\n              maxHeight: '90vh',\n              overflow: 'auto',\n              position: 'relative'\n            }}\n            onClick={(e) => e.stopPropagation()}\n          >\n            {/* 关闭按钮 */}\n            <button\n              onClick={handleCloseModal}\n              style={{\n                position: 'absolute',\n                top: '1rem',\n                right: '1rem',\n                backgroundColor: 'transparent',\n                border: 'none',\n                fontSize: '1.5rem',\n                cursor: 'pointer',\n                zIndex: 10,\n                color: '#6B7280'\n              }}\n            >\n              ×\n            </button>\n\n            <div style={{\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: '1.5rem',\n              padding: '1.5rem'\n            }}>\n              {/* 脸谱图片 */}\n              <div>\n                <div style={{\n                  aspectRatio: '1',\n                  background: `linear-gradient(135deg, ${selectedMask.mainColors[0]} 0%, ${selectedMask.mainColors[1] || selectedMask.mainColors[0]} 100%)`,\n                  borderRadius: '0.5rem',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  color: 'white',\n                  fontSize: '3rem',\n                  fontWeight: 'bold',\n                  marginBottom: '1rem'\n                }}>\n                  {selectedMask.character}\n                </div>\n                <div style={{ display: 'flex', gap: '0.5rem' }}>\n                  {selectedMask.mainColors.map((color, index) => (\n                    <div\n                      key={index}\n                      style={{\n                        width: '2rem',\n                        height: '2rem',\n                        borderRadius: '50%',\n                        backgroundColor: color,\n                        border: '2px solid #D1D5DB'\n                      }}\n                      title={color}\n                    />\n                  ))}\n                </div>\n              </div>\n\n              {/* 脸谱信息 */}\n              <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n                <div>\n                  <h2 style={{\n                    fontSize: '1.5rem',\n                    fontWeight: 'bold',\n                    color: '#1F2937',\n                    marginBottom: '0.5rem',\n                    fontFamily: '\"Noto Serif SC\", serif'\n                  }}>\n                    {selectedMask.name}\n                  </h2>\n                  <p style={{ color: '#6B7280', fontWeight: '500' }}>\n                    {selectedMask.character}\n                  </p>\n                </div>\n\n                <div>\n                  <h3 style={{\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#1F2937',\n                    marginBottom: '0.5rem'\n                  }}>\n                    基本信息\n                  </h3>\n                  <div style={{ fontSize: '0.875rem', display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>\n                    <p><span style={{ fontWeight: '500' }}>行当:</span> {selectedMask.roleCategory}</p>\n                    <p><span style={{ fontWeight: '500' }}>颜色分类:</span> {selectedMask.colorCategory}</p>\n                    <p><span style={{ fontWeight: '500' }}>绘制难度:</span> {selectedMask.difficulty}</p>\n                  </div>\n                </div>\n\n                <div>\n                  <h3 style={{\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#1F2937',\n                    marginBottom: '0.5rem'\n                  }}>\n                    文化背景\n                  </h3>\n                  <div style={{ fontSize: '0.875rem', color: '#6B7280', display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>\n                    <p><span style={{ fontWeight: '500' }}>历史起源:</span> {selectedMask.culturalBackground.origin}</p>\n                    <p><span style={{ fontWeight: '500' }}>性格特点:</span> {selectedMask.culturalBackground.personality}</p>\n                    <p><span style={{ fontWeight: '500' }}>象征意义:</span> {selectedMask.culturalBackground.symbolism}</p>\n                  </div>\n                </div>\n\n                <div>\n                  <h3 style={{\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#1F2937',\n                    marginBottom: '0.5rem'\n                  }}>\n                    相关剧目\n                  </h3>\n                  <div style={{ fontSize: '0.875rem', display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>\n                    {selectedMask.relatedOperas.map((opera, index) => (\n                      <div key={index}>\n                        <p style={{ fontWeight: '500' }}>{opera.name}</p>\n                        <p style={{ color: '#6B7280' }}>{opera.description}</p>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.5rem', marginBottom: '1.5rem' }}>\n                  {selectedMask.tags.map((tag, index) => (\n                    <span\n                      key={index}\n                      style={{\n                        padding: '0.5rem 0.75rem',\n                        fontSize: '0.75rem',\n                        backgroundColor: 'rgba(245, 158, 11, 0.2)',\n                        color: '#F59E0B',\n                        border: '1px solid #F59E0B',\n                        borderRadius: '9999px'\n                      }}\n                    >\n                      {tag}\n                    </span>\n                  ))}\n                </div>\n\n                {/* 查看详情按钮 */}\n                <button\n                  onClick={() => handleViewDetails(selectedMask)}\n                  style={{\n                    width: '100%',\n                    backgroundColor: '#B91C1C',\n                    color: 'white',\n                    padding: '0.75rem 1.5rem',\n                    borderRadius: '0.5rem',\n                    border: 'none',\n                    cursor: 'pointer',\n                    fontSize: '1rem',\n                    fontWeight: '600',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.backgroundColor = '#991B1B';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.backgroundColor = '#B91C1C';\n                  }}\n                >\n                  查看完整详情 →\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IACnE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;IAClB;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAO,IAAI,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;IAChC;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAI,OAAO;YACV,WAAW;YACX,iBAAiB;YACjB,YAAY;QACd;;0BAEE,8OAAC;gBAAO,OAAO;oBACb,iBAAiB;oBACjB,cAAc;oBACd,WAAW;oBACX,UAAU;oBACV,KAAK;oBACL,QAAQ;gBACV;0BACE,cAAA,8OAAC;oBAAI,OAAO;wBACV,UAAU;wBACV,QAAQ;wBACR,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,YAAY;oBACd;8BACE,cAAA,8OAAC;wBAAG,OAAO;4BACT,UAAU;4BACV,YAAY;4BACZ,OAAO;4BACP,YAAY;wBACd;kCAAG;;;;;;;;;;;;;;;;0BAOP,8OAAC;gBAAK,OAAO;oBAAE,SAAS;gBAAc;;kCAEpC,8OAAC;wBAAQ,OAAO;4BACd,WAAW;4BACX,SAAS;4BACT,YAAY;4BACZ,cAAc;4BACd,cAAc;wBAChB;;0CACE,8OAAC;gCAAG,OAAO;oCACT,UAAU;oCACV,YAAY;oCACZ,OAAO;oCACP,YAAY;oCACZ,cAAc;gCAChB;0CAAG;;;;;;0CAGH,8OAAC;gCAAE,OAAO;oCACR,UAAU;oCACV,OAAO;oCACP,UAAU;oCACV,QAAQ;gCACV;0CAAG;;;;;;;;;;;;kCAML,8OAAC;wBAAI,OAAO;4BACV,SAAS;4BACT,qBAAqB;4BACrB,KAAK;4BACL,UAAU;4BACV,QAAQ;wBACV;kCACG,oHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC,qBACf,8OAAC;gCAEC,SAAS,IAAM,gBAAgB;gCAC/B,OAAO;oCACL,iBAAiB;oCACjB,cAAc;oCACd,WAAW;oCACX,QAAQ;oCACR,UAAU;oCACV,QAAQ;oCACR,YAAY;oCACZ,UAAU;gCACZ;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oCAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gCACpC;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oCAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gCACpC;;kDAGA,8OAAC;wCAAI,OAAO;4CACV,aAAa;4CACb,YAAY,CAAC,wBAAwB,EAAE,KAAK,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,UAAU,CAAC,EAAE,IAAI,KAAK,UAAU,CAAC,EAAE,CAAC,MAAM,CAAC;4CACjH,SAAS;4CACT,YAAY;4CACZ,gBAAgB;4CAChB,OAAO;4CACP,UAAU;4CACV,YAAY;4CACZ,UAAU;wCACZ;;0DACE,8OAAC;gDAAI,OAAO;oDACV,UAAU;oDACV,OAAO;oDACP,YAAY;gDACd;;;;;;0DACA,8OAAC;gDAAK,OAAO;oDAAE,UAAU;oDAAY,QAAQ;gDAAG;0DAC7C,KAAK,SAAS;;;;;;0DAIjB,8OAAC;gDAAI,OAAO;oDACV,UAAU;oDACV,KAAK;oDACL,MAAM;oDACN,iBAAiB;oDACjB,OAAO;oDACP,SAAS;oDACT,cAAc;oDACd,UAAU;oDACV,YAAY;gDACd;0DACG,KAAK,YAAY;;;;;;0DAGpB,8OAAC;gDAAI,OAAO;oDACV,UAAU;oDACV,KAAK;oDACL,OAAO;oDACP,iBAAiB;oDACjB,OAAO;oDACP,SAAS;oDACT,cAAc;oDACd,UAAU;oDACV,YAAY;gDACd;0DACG,KAAK,aAAa;;;;;;;;;;;;kDAKvB,8OAAC;wCAAI,OAAO;4CAAE,SAAS;wCAAO;;0DAC5B,8OAAC;gDAAG,OAAO;oDACT,UAAU;oDACV,YAAY;oDACZ,OAAO;oDACP,cAAc;oDACd,YAAY;gDACd;0DACG,KAAK,IAAI;;;;;;0DAEZ,8OAAC;gDAAE,OAAO;oDACR,UAAU;oDACV,OAAO;oDACP,cAAc;oDACd,SAAS;oDACT,iBAAiB;oDACjB,iBAAiB;oDACjB,UAAU;gDACZ;0DACG,KAAK,kBAAkB,CAAC,WAAW;;;;;;0DAItC,8OAAC;gDAAI,OAAO;oDACV,SAAS;oDACT,YAAY;oDACZ,KAAK;oDACL,cAAc;gDAChB;;kEACE,8OAAC;wDAAK,OAAO;4DAAE,UAAU;4DAAW,OAAO;wDAAU;kEAAG;;;;;;kEACxD,8OAAC;wDAAI,OAAO;4DAAE,SAAS;4DAAQ,KAAK;wDAAU;kEAC3C,KAAK,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACvC,8OAAC;gEAEC,OAAO;oEACL,OAAO;oEACP,QAAQ;oEACR,cAAc;oEACd,iBAAiB;oEACjB,QAAQ;gEACV;gEACA,OAAO;+DARF;;;;;;;;;;;;;;;;0DAeb,8OAAC;gDAAI,OAAO;oDAAE,SAAS;oDAAQ,UAAU;oDAAQ,KAAK;gDAAU;0DAC7D,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC;wDAEC,OAAO;4DACL,SAAS;4DACT,UAAU;4DACV,iBAAiB;4DACjB,OAAO;4DACP,cAAc;wDAChB;kEAEC;uDATI;;;;;;;;;;;;;;;;;+BA5HR,KAAK,EAAE;;;;;;;;;;;;;;;;YAgJnB,8BACC,8OAAC;gBACC,OAAO;oBACL,UAAU;oBACV,OAAO;oBACP,QAAQ;oBACR,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,iBAAiB;oBACjB,gBAAgB;gBAClB;gBACA,SAAS;0BAET,cAAA,8OAAC;oBACC,OAAO;wBACL,iBAAiB;wBACjB,cAAc;wBACd,WAAW;wBACX,QAAQ;wBACR,UAAU;wBACV,WAAW;wBACX,UAAU;wBACV,UAAU;oBACZ;oBACA,SAAS,CAAC,IAAM,EAAE,eAAe;;sCAGjC,8OAAC;4BACC,SAAS;4BACT,OAAO;gCACL,UAAU;gCACV,KAAK;gCACL,OAAO;gCACP,iBAAiB;gCACjB,QAAQ;gCACR,UAAU;gCACV,QAAQ;gCACR,QAAQ;gCACR,OAAO;4BACT;sCACD;;;;;;sCAID,8OAAC;4BAAI,OAAO;gCACV,SAAS;gCACT,qBAAqB;gCACrB,KAAK;gCACL,SAAS;4BACX;;8CAEE,8OAAC;;sDACC,8OAAC;4CAAI,OAAO;gDACV,aAAa;gDACb,YAAY,CAAC,wBAAwB,EAAE,aAAa,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,aAAa,UAAU,CAAC,EAAE,IAAI,aAAa,UAAU,CAAC,EAAE,CAAC,MAAM,CAAC;gDACzI,cAAc;gDACd,SAAS;gDACT,YAAY;gDACZ,gBAAgB;gDAChB,OAAO;gDACP,UAAU;gDACV,YAAY;gDACZ,cAAc;4CAChB;sDACG,aAAa,SAAS;;;;;;sDAEzB,8OAAC;4CAAI,OAAO;gDAAE,SAAS;gDAAQ,KAAK;4CAAS;sDAC1C,aAAa,UAAU,CAAC,GAAG,CAAC,CAAC,OAAO,sBACnC,8OAAC;oDAEC,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,iBAAiB;wDACjB,QAAQ;oDACV;oDACA,OAAO;mDARF;;;;;;;;;;;;;;;;8CAeb,8OAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,eAAe;wCAAU,KAAK;oCAAS;;sDACpE,8OAAC;;8DACC,8OAAC;oDAAG,OAAO;wDACT,UAAU;wDACV,YAAY;wDACZ,OAAO;wDACP,cAAc;wDACd,YAAY;oDACd;8DACG,aAAa,IAAI;;;;;;8DAEpB,8OAAC;oDAAE,OAAO;wDAAE,OAAO;wDAAW,YAAY;oDAAM;8DAC7C,aAAa,SAAS;;;;;;;;;;;;sDAI3B,8OAAC;;8DACC,8OAAC;oDAAG,OAAO;wDACT,UAAU;wDACV,YAAY;wDACZ,OAAO;wDACP,cAAc;oDAChB;8DAAG;;;;;;8DAGH,8OAAC;oDAAI,OAAO;wDAAE,UAAU;wDAAY,SAAS;wDAAQ,eAAe;wDAAU,KAAK;oDAAS;;sEAC1F,8OAAC;;8EAAE,8OAAC;oEAAK,OAAO;wEAAE,YAAY;oEAAM;8EAAG;;;;;;gEAAU;gEAAE,aAAa,YAAY;;;;;;;sEAC5E,8OAAC;;8EAAE,8OAAC;oEAAK,OAAO;wEAAE,YAAY;oEAAM;8EAAG;;;;;;gEAAY;gEAAE,aAAa,aAAa;;;;;;;sEAC/E,8OAAC;;8EAAE,8OAAC;oEAAK,OAAO;wEAAE,YAAY;oEAAM;8EAAG;;;;;;gEAAY;gEAAE,aAAa,UAAU;;;;;;;;;;;;;;;;;;;sDAIhF,8OAAC;;8DACC,8OAAC;oDAAG,OAAO;wDACT,UAAU;wDACV,YAAY;wDACZ,OAAO;wDACP,cAAc;oDAChB;8DAAG;;;;;;8DAGH,8OAAC;oDAAI,OAAO;wDAAE,UAAU;wDAAY,OAAO;wDAAW,SAAS;wDAAQ,eAAe;wDAAU,KAAK;oDAAS;;sEAC5G,8OAAC;;8EAAE,8OAAC;oEAAK,OAAO;wEAAE,YAAY;oEAAM;8EAAG;;;;;;gEAAY;gEAAE,aAAa,kBAAkB,CAAC,MAAM;;;;;;;sEAC3F,8OAAC;;8EAAE,8OAAC;oEAAK,OAAO;wEAAE,YAAY;oEAAM;8EAAG;;;;;;gEAAY;gEAAE,aAAa,kBAAkB,CAAC,WAAW;;;;;;;sEAChG,8OAAC;;8EAAE,8OAAC;oEAAK,OAAO;wEAAE,YAAY;oEAAM;8EAAG;;;;;;gEAAY;gEAAE,aAAa,kBAAkB,CAAC,SAAS;;;;;;;;;;;;;;;;;;;sDAIlG,8OAAC;;8DACC,8OAAC;oDAAG,OAAO;wDACT,UAAU;wDACV,YAAY;wDACZ,OAAO;wDACP,cAAc;oDAChB;8DAAG;;;;;;8DAGH,8OAAC;oDAAI,OAAO;wDAAE,UAAU;wDAAY,SAAS;wDAAQ,eAAe;wDAAU,KAAK;oDAAS;8DACzF,aAAa,aAAa,CAAC,GAAG,CAAC,CAAC,OAAO,sBACtC,8OAAC;;8EACC,8OAAC;oEAAE,OAAO;wEAAE,YAAY;oEAAM;8EAAI,MAAM,IAAI;;;;;;8EAC5C,8OAAC;oEAAE,OAAO;wEAAE,OAAO;oEAAU;8EAAI,MAAM,WAAW;;;;;;;2DAF1C;;;;;;;;;;;;;;;;sDAQhB,8OAAC;4CAAI,OAAO;gDAAE,SAAS;gDAAQ,UAAU;gDAAQ,KAAK;gDAAU,cAAc;4CAAS;sDACpF,aAAa,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC3B,8OAAC;oDAEC,OAAO;wDACL,SAAS;wDACT,UAAU;wDACV,iBAAiB;wDACjB,OAAO;wDACP,QAAQ;wDACR,cAAc;oDAChB;8DAEC;mDAVI;;;;;;;;;;sDAgBX,8OAAC;4CACC,SAAS,IAAM,kBAAkB;4CACjC,OAAO;gDACL,OAAO;gDACP,iBAAiB;gDACjB,OAAO;gDACP,SAAS;gDACT,cAAc;gDACd,QAAQ;gDACR,QAAQ;gDACR,UAAU;gDACV,YAAY;gDACZ,YAAY;4CACd;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4CAC1C;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4CAC1C;sDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 2126, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2147, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2154, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2161, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/server/route-modules/app-page/vendored/contexts/app-router-context.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].AppRouterContext\n"], "names": ["module", "exports", "require", "vendored", "AppRouterContext"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,WAAW,CAACC,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2168, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/server/route-modules/app-page/vendored/contexts/hooks-client-context.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].HooksClientContext\n"], "names": ["module", "exports", "require", "vendored", "HooksClientContext"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,WAAW,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2175, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/client/components/router-reducer/reducers/get-segment-value.ts"], "sourcesContent": ["import type { Segment } from '../../../../server/app-render/types'\n\nexport function getSegmentValue(segment: Segment) {\n  return Array.isArray(segment) ? segment[1] : segment\n}\n"], "names": ["getSegmentValue", "segment", "Array", "isArray"], "mappings": ";;;+BAEgBA,mBAAAA;;;eAAAA;;;AAAT,SAASA,gBAAgBC,OAAgB;IAC9C,OAAOC,MAAMC,OAAO,CAACF,WAAWA,OAAO,CAAC,EAAE,GAAGA;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2200, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/shared/lib/segment.ts"], "sourcesContent": ["import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n"], "names": ["DEFAULT_SEGMENT_KEY", "PAGE_SEGMENT_KEY", "addSearchParamsIfPageSegment", "isGroupSegment", "isParallelRouteSegment", "segment", "endsWith", "startsWith", "searchParams", "isPageSegment", "includes", "stringified<PERSON><PERSON>y", "JSON", "stringify"], "mappings": ";;;;;;;;;;;;;;;;;IA4BaA,mBAAmB,EAAA;eAAnBA;;IADAC,gBAAgB,EAAA;eAAhBA;;IAhBGC,4BAA4B,EAAA;eAA5BA;;IATAC,cAAc,EAAA;eAAdA;;IAKAC,sBAAsB,EAAA;eAAtBA;;;AALT,SAASD,eAAeE,OAAe;IAC5C,sCAAsC;IACtC,OAAOA,OAAO,CAAC,EAAE,KAAK,OAAOA,QAAQC,QAAQ,CAAC;AAChD;AAEO,SAASF,uBAAuBC,OAAe;IACpD,OAAOA,QAAQE,UAAU,CAAC,QAAQF,YAAY;AAChD;AAEO,SAASH,6BACdG,OAAgB,EAChBG,YAA2D;IAE3D,MAAMC,gBAAgBJ,QAAQK,QAAQ,CAACT;IAEvC,IAAIQ,eAAe;QACjB,MAAME,mBAAmBC,KAAKC,SAAS,CAACL;QACxC,OAAOG,qBAAqB,OACxBV,mBAAmB,MAAMU,mBACzBV;IACN;IAEA,OAAOI;AACT;AAEO,MAAMJ,mBAAmB;AACzB,MAAMD,sBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2256, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/client/components/redirect-status-code.ts"], "sourcesContent": ["export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n"], "names": ["RedirectStatusCode"], "mappings": ";;;+BAAYA,sBAAAA;;;eAAAA;;;AAAL,IAAKA,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;WAAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2284, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/client/components/redirect-error.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n"], "names": ["REDIRECT_ERROR_CODE", "RedirectType", "isRedirectError", "error", "digest", "split", "errorCode", "type", "destination", "slice", "join", "status", "at", "statusCode", "Number", "isNaN", "RedirectStatusCode"], "mappings": ";;;;;;;;;;;;;;;IAEaA,mBAAmB,EAAA;eAAnBA;;IAEDC,YAAY,EAAA;eAAZA;;IAgBIC,eAAe,EAAA;eAAfA;;;oCApBmB;AAE5B,MAAMF,sBAAsB;AAE5B,IAAKC,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;WAAAA;;AAgBL,SAASC,gBAAgBC,KAAc;IAC5C,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IAEA,MAAMA,SAASD,MAAMC,MAAM,CAACC,KAAK,CAAC;IAClC,MAAM,CAACC,WAAWC,KAAK,GAAGH;IAC1B,MAAMI,cAAcJ,OAAOK,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;IAC7C,MAAMC,SAASP,OAAOQ,EAAE,CAAC,CAAC;IAE1B,MAAMC,aAAaC,OAAOH;IAE1B,OACEL,cAAcN,uBACbO,CAAAA,SAAS,aAAaA,SAAS,MAAK,KACrC,OAAOC,gBAAgB,YACvB,CAACO,MAAMF,eACPA,cAAcG,oBAAAA,kBAAkB;AAEpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2340, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/client/components/redirect.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nconst actionAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/action-async-storage.external') as typeof import('../../server/app-render/action-async-storage.external')\n      ).actionAsyncStorage\n    : undefined\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  type ??= actionAsyncStorage?.getStore()?.isAction\n    ? RedirectType.push\n    : RedirectType.replace\n\n  throw getRedirectError(url, type, RedirectStatusCode.TemporaryRedirect)\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n"], "names": ["getRedirectError", "getRedirectStatusCodeFromError", "getRedirectTypeFromError", "getURLFromRedirectError", "permanentRedirect", "redirect", "actionAsyncStorage", "window", "require", "undefined", "url", "type", "statusCode", "RedirectStatusCode", "TemporaryRedirect", "error", "Error", "REDIRECT_ERROR_CODE", "digest", "getStore", "isAction", "RedirectType", "push", "replace", "PermanentRedirect", "isRedirectError", "split", "slice", "join", "Number", "at"], "mappings": ";;;;;;;;;;;;;;;;;;IAegBA,gBAAgB,EAAA;eAAhBA;;IA6EAC,8BAA8B,EAAA;eAA9BA;;IARAC,wBAAwB,EAAA;eAAxBA;;IARAC,uBAAuB,EAAA;eAAvBA;;IAhBAC,iBAAiB,EAAA;eAAjBA;;IAvBAC,QAAQ,EAAA;eAARA;;;oCArCmB;+BAM5B;AAEP,MAAMC,qBACJ,OAAOC,WAAW,qBAEZC,QAAQ,2KACRF,kBAAkB,GACpBG;AAEC,SAAST,iBACdU,GAAW,EACXC,IAAkB,EAClBC,UAAqE;IAArEA,IAAAA,eAAAA,KAAAA,GAAAA,aAAiCC,oBAAAA,kBAAkB,CAACC,iBAAiB;IAErE,MAAMC,QAAQ,OAAA,cAA8B,CAA9B,IAAIC,MAAMC,eAAAA,mBAAmB,GAA7B,qBAAA;eAAA;oBAAA;sBAAA;IAA6B;IAC3CF,MAAMG,MAAM,GAAMD,eAAAA,mBAAmB,GAAC,MAAGN,OAAK,MAAGD,MAAI,MAAGE,aAAW;IACnE,OAAOG;AACT;AAcO,SAASV,SACd,2BAA2B,GAC3BK,GAAW,EACXC,IAAmB;QAEVL;IAATK,QAAAA,OAAAA,OAAAA,OAASL,CAAAA,sBAAAA,OAAAA,KAAAA,IAAAA,CAAAA,+BAAAA,mBAAoBa,QAAQ,EAAA,KAAA,OAAA,KAAA,IAA5Bb,6BAAgCc,QAAQ,IAC7CC,eAAAA,YAAY,CAACC,IAAI,GACjBD,eAAAA,YAAY,CAACE,OAAO;IAExB,MAAMvB,iBAAiBU,KAAKC,MAAME,oBAAAA,kBAAkB,CAACC,iBAAiB;AACxE;AAaO,SAASV,kBACd,2BAA2B,GAC3BM,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,SAAAA,KAAAA,GAAAA,OAAqBU,eAAAA,YAAY,CAACE,OAAO;IAEzC,MAAMvB,iBAAiBU,KAAKC,MAAME,oBAAAA,kBAAkB,CAACW,iBAAiB;AACxE;AAUO,SAASrB,wBAAwBY,KAAc;IACpD,IAAI,CAACU,CAAAA,GAAAA,eAAAA,eAAe,EAACV,QAAQ,OAAO;IAEpC,wEAAwE;IACxE,kBAAkB;IAClB,OAAOA,MAAMG,MAAM,CAACQ,KAAK,CAAC,KAAKC,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;AACnD;AAEO,SAAS1B,yBAAyBa,KAAoB;IAC3D,IAAI,CAACU,CAAAA,GAAAA,eAAAA,eAAe,EAACV,QAAQ;QAC3B,MAAM,OAAA,cAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOD,MAAMG,MAAM,CAACQ,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEO,SAASzB,+BAA+Bc,KAAoB;IACjE,IAAI,CAACU,CAAAA,GAAAA,eAAAA,eAAe,EAACV,QAAQ;QAC3B,MAAM,OAAA,cAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOa,OAAOd,MAAMG,MAAM,CAACQ,KAAK,CAAC,KAAKI,EAAE,CAAC,CAAC;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2438, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/client/components/http-access-fallback/http-access-fallback.ts"], "sourcesContent": ["export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n"], "names": ["HTTPAccessErrorStatus", "HTTP_ERROR_FALLBACK_ERROR_CODE", "getAccessFallbackErrorTypeByStatus", "getAccessFallbackHTTPStatus", "isHTTPAccessFallbackError", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Set", "Object", "values", "error", "digest", "prefix", "httpStatus", "split", "has", "Number", "status"], "mappings": ";;;;;;;;;;;;;;;;;IAAaA,qBAAqB,EAAA;eAArBA;;IAQAC,8BAA8B,EAAA;eAA9BA;;IAuCGC,kCAAkC,EAAA;eAAlCA;;IAPAC,2BAA2B,EAAA;eAA3BA;;IAnBAC,yBAAyB,EAAA;eAAzBA;;;AArBT,MAAMJ,wBAAwB;IACnCK,WAAW;IACXC,WAAW;IACXC,cAAc;AAChB;AAEA,MAAMC,gBAAgB,IAAIC,IAAIC,OAAOC,MAAM,CAACX;AAErC,MAAMC,iCAAiC;AAavC,SAASG,0BACdQ,KAAc;IAEd,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IACA,MAAM,CAACC,QAAQC,WAAW,GAAGH,MAAMC,MAAM,CAACG,KAAK,CAAC;IAEhD,OACEF,WAAWb,kCACXO,cAAcS,GAAG,CAACC,OAAOH;AAE7B;AAEO,SAASZ,4BACdS,KAA8B;IAE9B,MAAMG,aAAaH,MAAMC,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7C,OAAOE,OAAOH;AAChB;AAEO,SAASb,mCACdiB,MAAc;IAEd,OAAQA;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE;IACJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2514, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/client/components/not-found.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};404`\n\nexport function notFound(): never {\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n\n  throw error\n}\n"], "names": ["notFound", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "error", "Error", "digest"], "mappings": ";;;+BAsBgBA,YAAAA;;;eAAAA;;;oCAnBT;AAEP;;;;;;;;;;;;;CAaC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,4CAA4C;IAC5C,MAAMG,QAAQ,OAAA,cAAiB,CAAjB,IAAIC,MAAMH,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BE,MAAkCE,MAAM,GAAGJ;IAE7C,MAAME;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2561, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/client/components/forbidden.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `forbidden` docs\n/**\n * @experimental\n * This function allows you to render the [forbidden.js file](https://nextjs.org/docs/app/api-reference/file-conventions/forbidden)\n * within a route segment as well as inject a tag.\n *\n * `forbidden()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * Read more: [Next.js Docs: `forbidden`](https://nextjs.org/docs/app/api-reference/functions/forbidden)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};403`\n\nexport function forbidden(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`forbidden()\\` is experimental and only allowed to be enabled when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["forbidden", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;+BAqBgBA,aAAAA;;;eAAAA;;;oCAlBT;AAEP,6BAA6B;AAC7B;;;;;;;;;;;CAWC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,IAAI,CAACG,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,gHADG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAID,MAAML,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2614, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/client/components/unauthorized.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `unauthorized` docs\n/**\n * @experimental\n * This function allows you to render the [unauthorized.js file](https://nextjs.org/docs/app/api-reference/file-conventions/unauthorized)\n * within a route segment as well as inject a tag.\n *\n * `unauthorized()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n *\n * Read more: [Next.js Docs: `unauthorized`](https://nextjs.org/docs/app/api-reference/functions/unauthorized)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};401`\n\nexport function unauthorized(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`unauthorized()\\` is experimental and only allowed to be used when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["unauthorized", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;+BAsBgBA,gBAAAA;;;eAAAA;;;oCAnBT;AAEP,gCAAgC;AAChC;;;;;;;;;;;;CAYC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,IAAI,CAACG,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,gHADG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAID,MAAML,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2668, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/server/dynamic-rendering-utils.ts"], "sourcesContent": ["export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(public readonly expression: string) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`\n    )\n  }\n}\n\ntype AbortListeners = Array<(err: unknown) => void>\nconst abortListenersBySignal = new WeakMap<AbortSignal, AbortListeners>()\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for dynamicIO where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  expression: string\n): Promise<T> {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(expression))\n  } else {\n    const hangingPromise = new Promise<T>((_, reject) => {\n      const boundRejection = reject.bind(\n        null,\n        new HangingPromiseRejectionError(expression)\n      )\n      let currentListeners = abortListenersBySignal.get(signal)\n      if (currentListeners) {\n        currentListeners.push(boundRejection)\n      } else {\n        const listeners = [boundRejection]\n        abortListenersBySignal.set(signal, listeners)\n        signal.addEventListener(\n          'abort',\n          () => {\n            for (let i = 0; i < listeners.length; i++) {\n              listeners[i]()\n            }\n          },\n          { once: true }\n        )\n      }\n    })\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject)\n    return hangingPromise\n  }\n}\n\nfunction ignoreReject() {}\n"], "names": ["isHangingPromiseRejectionError", "makeHangingPromise", "err", "digest", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "Error", "constructor", "expression", "abortListenersBySignal", "WeakMap", "signal", "aborted", "Promise", "reject", "hanging<PERSON>romise", "_", "boundRejection", "bind", "currentListeners", "get", "push", "listeners", "set", "addEventListener", "i", "length", "once", "catch", "ignoreReject"], "mappings": ";;;;;;;;;;;;;;IAAgBA,8BAA8B,EAAA;eAA9BA;;IAgCAC,kBAAkB,EAAA;eAAlBA;;;AAhCT,SAASD,+BACdE,GAAY;IAEZ,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAIC,MAAM,KAAKC;AACxB;AAEA,MAAMA,4BAA4B;AAElC,MAAMC,qCAAqCC;IAGzCC,YAA4BC,UAAkB,CAAE;QAC9C,KAAK,CACH,CAAC,qBAAqB,EAAEA,WAAW,qGAAqG,EAAEA,WAAW,qJAAqJ,CAAC,GAAA,IAAA,CAFnRA,UAAAA,GAAAA,YAAAA,IAAAA,CAFZL,MAAAA,GAASC;IAMzB;AACF;AAGA,MAAMK,yBAAyB,IAAIC;AAS5B,SAAST,mBACdU,MAAmB,EACnBH,UAAkB;IAElB,IAAIG,OAAOC,OAAO,EAAE;QAClB,OAAOC,QAAQC,MAAM,CAAC,IAAIT,6BAA6BG;IACzD,OAAO;QACL,MAAMO,iBAAiB,IAAIF,QAAW,CAACG,GAAGF;YACxC,MAAMG,iBAAiBH,OAAOI,IAAI,CAChC,MACA,IAAIb,6BAA6BG;YAEnC,IAAIW,mBAAmBV,uBAAuBW,GAAG,CAACT;YAClD,IAAIQ,kBAAkB;gBACpBA,iBAAiBE,IAAI,CAACJ;YACxB,OAAO;gBACL,MAAMK,YAAY;oBAACL;iBAAe;gBAClCR,uBAAuBc,GAAG,CAACZ,QAAQW;gBACnCX,OAAOa,gBAAgB,CACrB,SACA;oBACE,IAAK,IAAIC,IAAI,GAAGA,IAAIH,UAAUI,MAAM,EAAED,IAAK;wBACzCH,SAAS,CAACG,EAAE;oBACd;gBACF,GACA;oBAAEE,MAAM;gBAAK;YAEjB;QACF;QACA,2GAA2G;QAC3G,6GAA6G;QAC7G,yFAAyF;QACzFZ,eAAea,KAAK,CAACC;QACrB,OAAOd;IACT;AACF;AAEA,SAASc,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2739, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/server/lib/router-utils/is-postpone.ts"], "sourcesContent": ["const REACT_POSTPONE_TYPE: symbol = Symbol.for('react.postpone')\n\nexport function isPostpone(error: any): boolean {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    error.$$typeof === REACT_POSTPONE_TYPE\n  )\n}\n"], "names": ["isPostpone", "REACT_POSTPONE_TYPE", "Symbol", "for", "error", "$$typeof"], "mappings": ";;;+BAEgBA,cAAAA;;;eAAAA;;;AAFhB,MAAMC,sBAA8BC,OAAOC,GAAG,CAAC;AAExC,SAASH,WAAWI,KAAU;IACnC,OACE,OAAOA,UAAU,YACjBA,UAAU,QACVA,MAAMC,QAAQ,KAAKJ;AAEvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2758, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/shared/lib/lazy-dynamic/bailout-to-csr.ts"], "sourcesContent": ["// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n"], "names": ["BailoutToCSRError", "isBailoutToCSRError", "BAILOUT_TO_CSR", "Error", "constructor", "reason", "digest", "err"], "mappings": "AAAA,+GAA+G;;;;;;;;;;;;;;;IAIlGA,iBAAiB,EAAA;eAAjBA;;IASGC,mBAAmB,EAAA;eAAnBA;;;AAZhB,MAAMC,iBAAiB;AAGhB,MAAMF,0BAA0BG;IAGrCC,YAA4BC,MAAc,CAAE;QAC1C,KAAK,CAAE,wCAAqCA,SAAAA,IAAAA,CADlBA,MAAAA,GAAAA,QAAAA,IAAAA,CAFZC,MAAAA,GAASJ;IAIzB;AACF;AAGO,SAASD,oBAAoBM,GAAY;IAC9C,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2798, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/client/components/is-next-router-error.ts"], "sourcesContent": ["import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n"], "names": ["isNextRouterError", "error", "isRedirectError", "isHTTPAccessFallbackError"], "mappings": ";;;+BAWgBA,qBAAAA;;;eAAAA;;;oCART;+BAC6C;AAO7C,SAASA,kBACdC,KAAc;IAEd,OAAOC,CAAAA,GAAAA,eAAAA,eAAe,EAACD,UAAUE,CAAAA,GAAAA,oBAAAA,yBAAyB,EAACF;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2825, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/client/components/hooks-server-context.ts"], "sourcesContent": ["const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n"], "names": ["DynamicServerError", "isDynamicServerError", "DYNAMIC_ERROR_CODE", "Error", "constructor", "description", "digest", "err"], "mappings": ";;;;;;;;;;;;;;IAEaA,kBAAkB,EAAA;eAAlBA;;IAQGC,oBAAoB,EAAA;eAApBA;;;AAVhB,MAAMC,qBAAqB;AAEpB,MAAMF,2BAA2BG;IAGtCC,YAA4BC,WAAmB,CAAE;QAC/C,KAAK,CAAE,2BAAwBA,cAAAA,IAAAA,CADLA,WAAAA,GAAAA,aAAAA,IAAAA,CAF5BC,MAAAA,GAAoCJ;IAIpC;AACF;AAEO,SAASD,qBAAqBM,GAAY;IAC/C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,CAAE,CAAA,YAAYA,GAAE,KAChB,OAAOA,IAAID,MAAM,KAAK,UACtB;QACA,OAAO;IACT;IAEA,OAAOC,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2871, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/client/components/static-generation-bailout.ts"], "sourcesContent": ["const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n"], "names": ["StaticGenBailoutError", "isStaticGenBailoutError", "NEXT_STATIC_GEN_BAILOUT", "Error", "code", "error"], "mappings": ";;;;;;;;;;;;;;IAEaA,qBAAqB,EAAA;eAArBA;;IAIGC,uBAAuB,EAAA;eAAvBA;;;AANhB,MAAMC,0BAA0B;AAEzB,MAAMF,8BAA8BG;;QAApC,KAAA,IAAA,OAAA,IAAA,CACWC,IAAAA,GAAOF;;AACzB;AAEO,SAASD,wBACdI,KAAc;IAEd,IAAI,OAAOA,UAAU,YAAYA,UAAU,QAAQ,CAAE,CAAA,UAAUA,KAAI,GAAI;QACrE,OAAO;IACT;IAEA,OAAOA,MAAMD,IAAI,KAAKF;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2917, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/lib/metadata/metadata-constants.tsx"], "sourcesContent": ["export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\n"], "names": ["METADATA_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME"], "mappings": ";;;;;;;;;;;;;;;IAAaA,sBAAsB,EAAA;eAAtBA;;IAEAC,oBAAoB,EAAA;eAApBA;;IADAC,sBAAsB,EAAA;eAAtBA;;;AADN,MAAMF,yBAAyB;AAC/B,MAAME,yBAAyB;AAC/B,MAAMD,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2951, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/lib/scheduler.ts"], "sourcesContent": ["export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = (cb: ScheduledFn<void>) => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = (cb: ScheduledFn<void>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n"], "names": ["atLeastOneTask", "scheduleImmediate", "scheduleOnNextTick", "waitAtLeastOneReactRenderTask", "cb", "Promise", "resolve", "then", "process", "env", "NEXT_RUNTIME", "setTimeout", "nextTick", "setImmediate", "r"], "mappings": ";;;;;;;;;;;;;;;;IA4CgBA,cAAc,EAAA;eAAdA;;IAbHC,iBAAiB,EAAA;eAAjBA;;IAtBAC,kBAAkB,EAAA;eAAlBA;;IAgDGC,6BAA6B,EAAA;eAA7BA;;;AAhDT,MAAMD,qBAAqB,CAACE;IACjC,6EAA6E;IAC7E,4EAA4E;IAC5E,uCAAuC;IACvC,EAAE;IACF,kLAAkL;IAClL,EAAE;IACFC,QAAQC,OAAO,GAAGC,IAAI,CAAC;QACrB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;aAElC;YACLF,QAAQI,QAAQ,CAACR;QACnB;IACF;AACF;AAQO,MAAMH,oBAAoB,CAACG;IAChC,IAAII,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;SAElC;QACLG,aAAaT;IACf;AACF;AAOO,SAASJ;IACd,OAAO,IAAIK,QAAc,CAACC,UAAYL,kBAAkBK;AAC1D;AAWO,SAASH;IACd,IAAIK,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;SAElC;QACL,OAAO,IAAIL,QAAQ,CAACS,IAAMD,aAAaC;IACzC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3018, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/server/app-render/dynamic-rendering.ts"], "sourcesContent": ["/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../lib/metadata/metadata-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicErrorWithStack: null | Error\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspenseAboveBody: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasAllowedDynamic: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspenseAboveBody: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasAllowedDynamic: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender-ppr') {\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0\n\n      // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */\nexport function trackFallbackParamAccessed(\n  store: WorkStore,\n  expression: string\n): void {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return\n\n  postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking)\n}\n\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(\n  _store: WorkStore,\n  workUnitStore: void | WorkUnitStore\n) {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n    // TODO: it makes no sense to have these work unit store types during a dev render.\n    if (\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-client' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      workUnitStore.revalidate = 0\n    }\n    if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  // It is important that we set this tracking value after aborting. Aborts are executed\n  // synchronously except for the case where you abort during render itself. By setting this\n  // value late we can use it to determine if any of the aborted tasks are the task that\n  // called the sync IO expression in the first place.\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n    // It is important that we set this tracking value after aborting. Aborts are executed\n    // synchronously except for the case where you abort during render itself. By setting this\n    // value late we can use it to determine if any of the aborted tasks are the task that\n    // called the sync IO expression in the first place.\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n      }\n    }\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createPostponedAbortSignal(reason: string): AbortSignal {\n  assertPostpone()\n  const controller = new AbortController()\n  // We get our hands on a postpone instance by calling postpone and catching the throw\n  try {\n    React.unstable_postpone(reason)\n  } catch (x: unknown) {\n    controller.abort(x)\n  }\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: PrerenderStoreModern\n): AbortSignal {\n  const controller = new AbortController()\n\n  if (workUnitStore.cacheSignal) {\n    // If we have a cacheSignal it means we're in a prospective render. If the input\n    // we're waiting on is coming from another cache, we do want to wait for it so that\n    // we can resolve this cache entry too.\n    workUnitStore.cacheSignal.inputReady().then(() => {\n      controller.abort()\n    })\n  } else {\n    // Otherwise we're in the final render and we should already have all our caches\n    // filled. We might still be waiting on some microtasks so we wait one tick before\n    // giving up. When we give up, we still want to render the content of this cache\n    // as deeply as we can so that we can suspend as deeply as possible in the tree\n    // or not at all if we don't end up waiting for the input.\n    scheduleOnNextTick(() => controller.abort())\n  }\n\n  return controller.signal\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n\n  if (\n    workStore &&\n    workStore.isStaticGeneration &&\n    workStore.fallbackRouteParams &&\n    workStore.fallbackRouteParams.size > 0\n  ) {\n    // There are fallback route params, we should track these as dynamic\n    // accesses.\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (workUnitStore) {\n      // We're prerendering with dynamicIO or PPR or both\n      if (workUnitStore.type === 'prerender-client') {\n        // We are in a prerender with dynamicIO semantics\n        // We are going to hang here and never resolve. This will cause the currently\n        // rendering component to effectively be a dynamic hole\n        React.use(makeHangingPromise(workUnitStore.renderSignal, expression))\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // We're prerendering with PPR\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        throwToInterruptStaticGeneration(expression, workStore, workUnitStore)\n      }\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasSuspenseAfterBodyOrHtmlRegex =\n  /\\n\\s+at (?:body|html) \\(<anonymous>\\)[\\s\\S]*?\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  workStore: WorkStore,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (hasSuspenseAfterBodyOrHtmlRegex.test(componentStack)) {\n    // This prerender has a Suspense boundary above the body which\n    // effectively opts the page into allowing 100% dynamic rendering\n    dynamicValidation.hasAllowedDynamic = true\n    dynamicValidation.hasSuspenseAboveBody = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    // this error had a Suspense boundary above it so we don't need to report it as a source\n    // of disallowed\n    dynamicValidation.hasAllowedDynamic = true\n    return\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    // This task was the task that called the sync error.\n    dynamicValidation.dynamicErrors.push(\n      clientDynamic.syncDynamicErrorWithStack\n    )\n    return\n  } else {\n    const message = `Route \"${workStore.route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentOrOwnerStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\n/**\n * In dev mode, we prefer using the owner stack, otherwise the provided\n * component stack is used.\n */\nfunction createErrorWithComponentOrOwnerStack(\n  message: string,\n  componentStack: string\n) {\n  const ownerStack =\n    process.env.NODE_ENV !== 'production' && React.captureOwnerStack\n      ? React.captureOwnerStack()\n      : null\n\n  const error = new Error(message)\n  error.stack = error.name + ': ' + message + (ownerStack ?? componentStack)\n  return error\n}\n\nexport enum PreludeState {\n  Full = 0,\n  Empty = 1,\n  Errored = 2,\n}\n\nfunction logDisallowedDynamicError(workStore: WorkStore, error: Error): void {\n  console.error(error)\n\n  if (!workStore.dev) {\n    if (workStore.hasReadableErrorStacks) {\n      console.error(\n        `To get a more detailed stack trace and pinpoint the issue, start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.`\n      )\n    } else {\n      console.error(`To get a more detailed stack trace and pinpoint the issue, try one of the following:\n  - Start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.\n  - Rerun the production build with \\`next build --debug-prerender\\` to generate better stack traces.`)\n    }\n  }\n}\n\nexport function throwIfDisallowedDynamic(\n  workStore: WorkStore,\n  prelude: PreludeState,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState\n): void {\n  if (workStore.invalidDynamicUsageError) {\n    logDisallowedDynamicError(workStore, workStore.invalidDynamicUsageError)\n    throw new StaticGenBailoutError()\n  }\n\n  if (prelude !== PreludeState.Full) {\n    if (dynamicValidation.hasSuspenseAboveBody) {\n      // This route has opted into allowing fully dynamic rendering\n      // by including a Suspense boundary above the body. In this case\n      // a lack of a shell is not considered disallowed so we simply return\n      return\n    }\n\n    if (serverDynamic.syncDynamicErrorWithStack) {\n      // There is no shell and the server did something sync dynamic likely\n      // leading to an early termination of the prerender before the shell\n      // could be completed. We terminate the build/validating render.\n      logDisallowedDynamicError(\n        workStore,\n        serverDynamic.syncDynamicErrorWithStack\n      )\n      throw new StaticGenBailoutError()\n    }\n\n    // We didn't have any sync bailouts but there may be user code which\n    // blocked the root. We would have captured these during the prerender\n    // and can log them here and then terminate the build/validating render\n    const dynamicErrors = dynamicValidation.dynamicErrors\n    if (dynamicErrors.length > 0) {\n      for (let i = 0; i < dynamicErrors.length; i++) {\n        logDisallowedDynamicError(workStore, dynamicErrors[i])\n      }\n\n      throw new StaticGenBailoutError()\n    }\n\n    // If we got this far then the only other thing that could be blocking\n    // the root is dynamic Viewport. If this is dynamic then\n    // you need to opt into that by adding a Suspense boundary above the body\n    // to indicate your are ok with fully dynamic rendering.\n    if (dynamicValidation.hasDynamicViewport) {\n      console.error(\n        `Route \"${workStore.route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) without explicitly allowing fully dynamic rendering. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-viewport`\n      )\n      throw new StaticGenBailoutError()\n    }\n\n    if (prelude === PreludeState.Empty) {\n      // If we ever get this far then we messed up the tracking of invalid dynamic.\n      // We still adhere to the constraint that you must produce a shell but invite the\n      // user to report this as a bug in Next.js.\n      console.error(\n        `Route \"${workStore.route}\" did not produce a static shell and Next.js was unable to determine a reason. This is a bug in Next.js.`\n      )\n      throw new StaticGenBailoutError()\n    }\n  } else {\n    if (\n      dynamicValidation.hasAllowedDynamic === false &&\n      dynamicValidation.hasDynamicMetadata\n    ) {\n      console.error(\n        `Route \"${workStore.route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) when the rest of the route does not. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-metadata`\n      )\n      throw new StaticGenBailoutError()\n    }\n  }\n}\n"], "names": ["Postpone", "PreludeState", "abortAndThrowOnSynchronousRequestDataAccess", "abortOnSynchronousPlatformIOAccess", "accessedDynamicData", "annotateDynamicAccess", "consumeDynamicAccess", "createDynamicTrackingState", "createDynamicValidationState", "createHangingInputAbortSignal", "createPostponedAbortSignal", "formatDynamicAPIAccesses", "getFirstDynamicReason", "isDynamicPostpone", "isPrerenderInterruptedError", "markCurrentScopeAsDynamic", "postponeWithTracking", "throwIfDisallowedDynamic", "throwToInterruptStaticGeneration", "trackAllowedDynamicAccess", "trackDynamicDataInDynamicRender", "trackFallbackParamAccessed", "trackSynchronousPlatformIOAccessInDev", "trackSynchronousRequestDataAccessInDev", "useDynamicRouteParams", "hasPostpone", "React", "unstable_postpone", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicErrorWithStack", "hasSuspenseAboveBody", "hasDynamicMetadata", "hasDynamicViewport", "hasAllowedDynamic", "dynamicErrors", "trackingState", "expression", "store", "workUnitStore", "type", "forceDynamic", "forceStatic", "dynamicShouldError", "StaticGenBailoutError", "route", "dynamicTracking", "revalidate", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "process", "env", "NODE_ENV", "usedDynamic", "prerenderStore", "workUnitAsyncStorage", "getStore", "_store", "abortOnSynchronousDynamicDataAccess", "reason", "error", "createPrerenderInterruptedError", "controller", "abort", "push", "Error", "undefined", "errorWithStack", "requestStore", "prerenderPhase", "prerenderSignal", "signal", "aborted", "assertPostpone", "createPostponeReason", "message", "isDynamicPostponeReason", "includes", "NEXT_PRERENDER_INTERRUPTED", "digest", "length", "serverDynamic", "clientDynamic", "filter", "access", "map", "split", "slice", "line", "join", "AbortController", "x", "cacheSignal", "inputReady", "then", "scheduleOnNextTick", "workStore", "workAsyncStorage", "isStaticGeneration", "fallbackRouteParams", "size", "use", "makeHangingPromise", "renderSignal", "hasSuspenseRegex", "hasSuspenseAfterBodyOrHtmlRegex", "hasMetadataRegex", "RegExp", "METADATA_BOUNDARY_NAME", "hasViewportRegex", "VIEWPORT_BOUNDARY_NAME", "hasOutletRegex", "OUTLET_BOUNDARY_NAME", "componentStack", "dynamicValidation", "test", "createErrorWithComponentOrOwnerStack", "ownerStack", "captureOwnerStack", "name", "logDisallowedDynamicError", "console", "dev", "hasReadableErrorStacks", "prelude", "invalidDynamicUsageError", "i"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgVeA,QAAQ,EAAA;eAARA;;IA2TJC,YAAY,EAAA;eAAZA;;IApWIC,2CAA2C,EAAA;eAA3CA;;IArCAC,kCAAkC,EAAA;eAAlCA;;IAwKAC,mBAAmB,EAAA;eAAnBA;;IA4GAC,qBAAqB,EAAA;eAArBA;;IAtGAC,oBAAoB,EAAA;eAApBA;;IA/WAC,0BAA0B,EAAA;eAA1BA;;IAUAC,4BAA4B,EAAA;eAA5BA;;IAmbAC,6BAA6B,EAAA;eAA7BA;;IAjBAC,0BAA0B,EAAA;eAA1BA;;IAlDAC,wBAAwB,EAAA;eAAxBA;;IAtWAC,qBAAqB,EAAA;eAArBA;;IAgSAC,iBAAiB,EAAA;eAAjBA;;IAwCAC,2BAA2B,EAAA;eAA3BA;;IA3TAC,yBAAyB,EAAA;eAAzBA;;IAuPAC,oBAAoB,EAAA;eAApBA;;IAwUAC,wBAAwB,EAAA;eAAxBA;;IA/eAC,gCAAgC,EAAA;eAAhCA;;IA+ZAC,yBAAyB,EAAA;eAAzBA;;IAtYAC,+BAA+B,EAAA;eAA/BA;;IAzCAC,0BAA0B,EAAA;eAA1BA;;IAmHAC,qCAAqC,EAAA;eAArCA;;IAiDHC,sCAAsC,EAAA;eAAtCA;;IA+NGC,qBAAqB,EAAA;eAArBA;;;8DA1hBE;oCAEiB;yCACG;8CACD;0CACJ;uCACE;mCAK5B;2BAC4B;;;;;;AAEnC,MAAMC,cAAc,OAAOC,OAAAA,OAAK,CAACC,iBAAiB,KAAK;AAwChD,SAASpB,2BACdqB,sBAA2C;IAE3C,OAAO;QACLA;QACAC,iBAAiB,EAAE;QACnBC,2BAA2B;IAC7B;AACF;AAEO,SAAStB;IACd,OAAO;QACLuB,sBAAsB;QACtBC,oBAAoB;QACpBC,oBAAoB;QACpBC,mBAAmB;QACnBC,eAAe,EAAE;IACnB;AACF;AAEO,SAASvB,sBACdwB,aAAmC;QAE5BA;IAAP,OAAA,CAAOA,kCAAAA,cAAcP,eAAe,CAAC,EAAE,KAAA,OAAA,KAAA,IAAhCO,gCAAkCC,UAAU;AACrD;AASO,SAAStB,0BACduB,KAAgB,EAChBC,aAAuE,EACvEF,UAAkB;IAElB,IAAIE,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,2DAA2D;IAC3D,IAAIF,MAAMG,YAAY,IAAIH,MAAMI,WAAW,EAAE;IAE7C,IAAIJ,MAAMK,kBAAkB,EAAE;QAC5B,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEN,MAAMO,KAAK,CAAC,8EAA8E,EAAER,WAAW,4HAA4H,CAAC,GADzO,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIE,eAAe;QACjB,IAAIA,cAAcC,IAAI,KAAK,iBAAiB;YAC1CxB,qBACEsB,MAAMO,KAAK,EACXR,YACAE,cAAcO,eAAe;QAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;YACpDD,cAAcQ,UAAU,GAAG;YAE3B,uGAAuG;YACvG,MAAMC,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,iDAAiD,EAAER,WAAW,2EAA2E,CAAC,GADrJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEZ;YACAC,MAAMY,uBAAuB,GAAGb;YAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;YAEnC,MAAMJ;QACR,OAAO,IACLK,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBhB,iBACAA,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAUO,SAASnC,2BACdiB,KAAgB,EAChBD,UAAkB;IAElB,MAAMoB,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,IAAI,CAACF,kBAAkBA,eAAejB,IAAI,KAAK,iBAAiB;IAEhExB,qBAAqBsB,MAAMO,KAAK,EAAER,YAAYoB,eAAeX,eAAe;AAC9E;AAQO,SAAS5B,iCACdmB,UAAkB,EAClBC,KAAgB,EAChBmB,cAAoC;IAEpC,uGAAuG;IACvG,MAAMT,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,mDAAmD,EAAER,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;eAAA;oBAAA;sBAAA;IAEZ;IAEAoB,eAAeV,UAAU,GAAG;IAE5BT,MAAMY,uBAAuB,GAAGb;IAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;IAEnC,MAAMJ;AACR;AASO,SAAS5B,gCACdwC,MAAiB,EACjBrB,aAAmC;IAEnC,IAAIA,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;QACA,mFAAmF;QACnF,IACED,cAAcC,IAAI,KAAK,eACvBD,cAAcC,IAAI,KAAK,sBACvBD,cAAcC,IAAI,KAAK,oBACvB;YACAD,cAAcQ,UAAU,GAAG;QAC7B;QACA,IACEM,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBhB,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAEA,SAASK,oCACPhB,KAAa,EACbR,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMK,SAAS,CAAC,MAAM,EAAEjB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;IAE9G,MAAM0B,QAAQC,gCAAgCF;IAE9CL,eAAeQ,UAAU,CAACC,KAAK,CAACH;IAEhC,MAAMjB,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBjB,eAAe,CAACsC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBlB,sBAAsB,GACzC,IAAIwC,QAAQhB,KAAK,GACjBiB;YACJhC;QACF;IACF;AACF;AAEO,SAASlC,mCACd0C,KAAa,EACbR,UAAkB,EAClBiC,cAAqB,EACrBb,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtDe,oCAAoChB,OAAOR,YAAYoB;IACvD,sFAAsF;IACtF,0FAA0F;IAC1F,sFAAsF;IACtF,oDAAoD;IACpD,IAAIX,iBAAiB;QACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;YACtDgB,gBAAgBhB,yBAAyB,GAAGwC;QAC9C;IACF;AACF;AAEO,SAAShD,sCACdiD,YAA0B;IAE1B,oFAAoF;IACpF,oDAAoD;IACpDA,aAAaC,cAAc,GAAG;AAChC;AAYO,SAAStE,4CACd2C,KAAa,EACbR,UAAkB,EAClBiC,cAAqB,EACrBb,cAAoC;IAEpC,MAAMgB,kBAAkBhB,eAAeQ,UAAU,CAACS,MAAM;IACxD,IAAID,gBAAgBE,OAAO,KAAK,OAAO;QACrC,8FAA8F;QAC9F,mFAAmF;QACnF,wFAAwF;QACxF,4FAA4F;QAC5F,0BAA0B;QAC1Bd,oCAAoChB,OAAOR,YAAYoB;QACvD,sFAAsF;QACtF,0FAA0F;QAC1F,sFAAsF;QACtF,oDAAoD;QACpD,MAAMX,kBAAkBW,eAAeX,eAAe;QACtD,IAAIA,iBAAiB;YACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;gBACtDgB,gBAAgBhB,yBAAyB,GAAGwC;YAC9C;QACF;IACF;IACA,MAAMN,gCACJ,CAAC,MAAM,EAAEnB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;AAEnG;AAGO,MAAMd,yCACXD;AASK,SAAStB,SAAS,EAAE8D,MAAM,EAAEjB,KAAK,EAAiB;IACvD,MAAMY,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,MAAMb,kBACJW,kBAAkBA,eAAejB,IAAI,KAAK,kBACtCiB,eAAeX,eAAe,GAC9B;IACN9B,qBAAqB6B,OAAOiB,QAAQhB;AACtC;AAEO,SAAS9B,qBACd6B,KAAa,EACbR,UAAkB,EAClBS,eAA4C;IAE5C8B;IACA,IAAI9B,iBAAiB;QACnBA,gBAAgBjB,eAAe,CAACsC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBlB,sBAAsB,GACzC,IAAIwC,QAAQhB,KAAK,GACjBiB;YACJhC;QACF;IACF;IAEAX,OAAAA,OAAK,CAACC,iBAAiB,CAACkD,qBAAqBhC,OAAOR;AACtD;AAEA,SAASwC,qBAAqBhC,KAAa,EAAER,UAAkB;IAC7D,OACE,CAAC,MAAM,EAAEQ,MAAM,iEAAiE,EAAER,WAAW,EAAE,CAAC,GAChG,CAAC,+EAA+E,CAAC,GACjF,CAAC,iFAAiF,CAAC;AAEvF;AAEO,SAASxB,kBAAkBmC,GAAY;IAC5C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,OAAQA,IAAY8B,OAAO,KAAK,UAChC;QACA,OAAOC,wBAAyB/B,IAAY8B,OAAO;IACrD;IACA,OAAO;AACT;AAEA,SAASC,wBAAwBjB,MAAc;IAC7C,OACEA,OAAOkB,QAAQ,CACb,sEAEFlB,OAAOkB,QAAQ,CACb;AAGN;AAEA,IAAID,wBAAwBF,qBAAqB,OAAO,YAAY,OAAO;IACzE,MAAM,OAAA,cAEL,CAFK,IAAIT,MACR,2FADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,MAAMa,6BAA6B;AAEnC,SAASjB,gCAAgCc,OAAe;IACtD,MAAMf,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMU,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC7Bf,MAAcmB,MAAM,GAAGD;IACzB,OAAOlB;AACT;AAMO,SAASjD,4BACdiD,KAAc;IAEd,OACE,OAAOA,UAAU,YACjBA,UAAU,QACTA,MAAcmB,MAAM,KAAKD,8BAC1B,UAAUlB,SACV,aAAaA,SACbA,iBAAiBK;AAErB;AAEO,SAAShE,oBACdyB,eAAqC;IAErC,OAAOA,gBAAgBsD,MAAM,GAAG;AAClC;AAEO,SAAS7E,qBACd8E,aAAmC,EACnCC,aAAmC;IAEnC,oEAAoE;IACpE,0EAA0E;IAC1E,SAAS;IACTD,cAAcvD,eAAe,CAACsC,IAAI,IAAIkB,cAAcxD,eAAe;IACnE,OAAOuD,cAAcvD,eAAe;AACtC;AAEO,SAASlB,yBACdkB,eAAqC;IAErC,OAAOA,gBACJyD,MAAM,CACL,CAACC,SACC,OAAOA,OAAOnC,KAAK,KAAK,YAAYmC,OAAOnC,KAAK,CAAC+B,MAAM,GAAG,GAE7DK,GAAG,CAAC,CAAC,EAAEnD,UAAU,EAAEe,KAAK,EAAE;QACzBA,QAAQA,MACLqC,KAAK,CAAC,MACP,wEAAwE;QACxE,qEAAqE;QACrE,uDAAuD;SACtDC,KAAK,CAAC,GACNJ,MAAM,CAAC,CAACK;YACP,kDAAkD;YAClD,IAAIA,KAAKX,QAAQ,CAAC,uBAAuB;gBACvC,OAAO;YACT;YAEA,oDAAoD;YACpD,IAAIW,KAAKX,QAAQ,CAAC,mBAAmB;gBACnC,OAAO;YACT;YAEA,kDAAkD;YAClD,IAAIW,KAAKX,QAAQ,CAAC,YAAY;gBAC5B,OAAO;YACT;YAEA,OAAO;QACT,GACCY,IAAI,CAAC;QACR,OAAO,CAAC,0BAA0B,EAAEvD,WAAW,GAAG,EAAEe,OAAO;IAC7D;AACJ;AAEA,SAASwB;IACP,IAAI,CAACnD,aAAa;QAChB,MAAM,OAAA,cAEL,CAFK,IAAI2C,MACR,CAAC,gIAAgI,CAAC,GAD9H,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAMO,SAAS1D,2BAA2BoD,MAAc;IACvDc;IACA,MAAMX,aAAa,IAAI4B;IACvB,qFAAqF;IACrF,IAAI;QACFnE,OAAAA,OAAK,CAACC,iBAAiB,CAACmC;IAC1B,EAAE,OAAOgC,GAAY;QACnB7B,WAAWC,KAAK,CAAC4B;IACnB;IACA,OAAO7B,WAAWS,MAAM;AAC1B;AAOO,SAASjE,8BACd8B,aAAmC;IAEnC,MAAM0B,aAAa,IAAI4B;IAEvB,IAAItD,cAAcwD,WAAW,EAAE;QAC7B,gFAAgF;QAChF,mFAAmF;QACnF,uCAAuC;QACvCxD,cAAcwD,WAAW,CAACC,UAAU,GAAGC,IAAI,CAAC;YAC1ChC,WAAWC,KAAK;QAClB;IACF,OAAO;QACL,gFAAgF;QAChF,kFAAkF;QAClF,gFAAgF;QAChF,+EAA+E;QAC/E,0DAA0D;QAC1DgC,CAAAA,GAAAA,WAAAA,kBAAkB,EAAC,IAAMjC,WAAWC,KAAK;IAC3C;IAEA,OAAOD,WAAWS,MAAM;AAC1B;AAEO,SAASrE,sBACdgC,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBjB,eAAe,CAACsC,IAAI,CAAC;YACnCf,OAAON,gBAAgBlB,sBAAsB,GACzC,IAAIwC,QAAQhB,KAAK,GACjBiB;YACJhC;QACF;IACF;AACF;AAEO,SAASb,sBAAsBa,UAAkB;IACtD,MAAM8D,YAAYC,0BAAAA,gBAAgB,CAACzC,QAAQ;IAE3C,IACEwC,aACAA,UAAUE,kBAAkB,IAC5BF,UAAUG,mBAAmB,IAC7BH,UAAUG,mBAAmB,CAACC,IAAI,GAAG,GACrC;QACA,oEAAoE;QACpE,YAAY;QACZ,MAAMhE,gBAAgBmB,8BAAAA,oBAAoB,CAACC,QAAQ;QACnD,IAAIpB,eAAe;YACjB,mDAAmD;YACnD,IAAIA,cAAcC,IAAI,KAAK,oBAAoB;gBAC7C,iDAAiD;gBACjD,6EAA6E;gBAC7E,uDAAuD;gBACvDd,OAAAA,OAAK,CAAC8E,GAAG,CAACC,CAAAA,GAAAA,uBAAAA,kBAAkB,EAAClE,cAAcmE,YAAY,EAAErE;YAC3D,OAAO,IAAIE,cAAcC,IAAI,KAAK,iBAAiB;gBACjD,8BAA8B;gBAC9BxB,qBACEmF,UAAUtD,KAAK,EACfR,YACAE,cAAcO,eAAe;YAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;gBACpDtB,iCAAiCmB,YAAY8D,WAAW5D;YAC1D;QACF;IACF;AACF;AAEA,MAAMoE,mBAAmB;AACzB,MAAMC,kCACJ;AACF,MAAMC,mBAAmB,IAAIC,OAC3B,CAAC,UAAU,EAAEC,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,mBAAmB,IAAIF,OAC3B,CAAC,UAAU,EAAEG,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,iBAAiB,IAAIJ,OAAO,CAAC,UAAU,EAAEK,mBAAAA,oBAAoB,CAAC,QAAQ,CAAC;AAEtE,SAAShG,0BACdgF,SAAoB,EACpBiB,cAAsB,EACtBC,iBAAyC,EACzChC,aAAmC;IAEnC,IAAI6B,eAAeI,IAAI,CAACF,iBAAiB;QACvC,kGAAkG;QAClG;IACF,OAAO,IAAIP,iBAAiBS,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBrF,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAIgF,iBAAiBM,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBpF,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAI2E,gCAAgCU,IAAI,CAACF,iBAAiB;QAC/D,8DAA8D;QAC9D,iEAAiE;QACjEC,kBAAkBnF,iBAAiB,GAAG;QACtCmF,kBAAkBtF,oBAAoB,GAAG;QACzC;IACF,OAAO,IAAI4E,iBAAiBW,IAAI,CAACF,iBAAiB;QAChD,wFAAwF;QACxF,gBAAgB;QAChBC,kBAAkBnF,iBAAiB,GAAG;QACtC;IACF,OAAO,IAAImD,cAAcvD,yBAAyB,EAAE;QAClD,qDAAqD;QACrDuF,kBAAkBlF,aAAa,CAACgC,IAAI,CAClCkB,cAAcvD,yBAAyB;QAEzC;IACF,OAAO;QACL,MAAMgD,UAAU,CAAC,OAAO,EAAEqB,UAAUtD,KAAK,CAAC,2NAA2N,CAAC;QACtQ,MAAMkB,QAAQwD,qCAAqCzC,SAASsC;QAC5DC,kBAAkBlF,aAAa,CAACgC,IAAI,CAACJ;QACrC;IACF;AACF;AAEA;;;CAGC,GACD,SAASwD,qCACPzC,OAAe,EACfsC,cAAsB;IAEtB,MAAMI,aACJnE,QAAQC,GAAG,CAACC,QAAQ,gCAAK,gBAAgB7B,OAAAA,OAAK,CAAC+F,iBAAiB,GAC5D/F,OAAAA,OAAK,CAAC+F,iBAAiB,KACvB;IAEN,MAAM1D,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMU,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC/Bf,MAAMX,KAAK,GAAGW,MAAM2D,IAAI,GAAG,OAAO5C,UAAW0C,CAAAA,cAAcJ,cAAa;IACxE,OAAOrD;AACT;AAEO,IAAK9D,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;;WAAAA;;AAMZ,SAAS0H,0BAA0BxB,SAAoB,EAAEpC,KAAY;IACnE6D,QAAQ7D,KAAK,CAACA;IAEd,IAAI,CAACoC,UAAU0B,GAAG,EAAE;QAClB,IAAI1B,UAAU2B,sBAAsB,EAAE;YACpCF,QAAQ7D,KAAK,CACX,CAAC,iIAAiI,EAAEoC,UAAUtD,KAAK,CAAC,2CAA2C,CAAC;QAEpM,OAAO;YACL+E,QAAQ7D,KAAK,CAAC,CAAC;0EACqD,EAAEoC,UAAUtD,KAAK,CAAC;qGACS,CAAC;QAClG;IACF;AACF;AAEO,SAAS5B,yBACdkF,SAAoB,EACpB4B,OAAqB,EACrBV,iBAAyC,EACzCjC,aAAmC;IAEnC,IAAIe,UAAU6B,wBAAwB,EAAE;QACtCL,0BAA0BxB,WAAWA,UAAU6B,wBAAwB;QACvE,MAAM,IAAIpF,yBAAAA,qBAAqB;IACjC;IAEA,IAAImF,YAAAA,GAA+B;QACjC,IAAIV,kBAAkBtF,oBAAoB,EAAE;YAC1C,6DAA6D;YAC7D,gEAAgE;YAChE,qEAAqE;YACrE;QACF;QAEA,IAAIqD,cAActD,yBAAyB,EAAE;YAC3C,qEAAqE;YACrE,oEAAoE;YACpE,gEAAgE;YAChE6F,0BACExB,WACAf,cAActD,yBAAyB;YAEzC,MAAM,IAAIc,yBAAAA,qBAAqB;QACjC;QAEA,oEAAoE;QACpE,sEAAsE;QACtE,uEAAuE;QACvE,MAAMT,gBAAgBkF,kBAAkBlF,aAAa;QACrD,IAAIA,cAAcgD,MAAM,GAAG,GAAG;YAC5B,IAAK,IAAI8C,IAAI,GAAGA,IAAI9F,cAAcgD,MAAM,EAAE8C,IAAK;gBAC7CN,0BAA0BxB,WAAWhE,aAAa,CAAC8F,EAAE;YACvD;YAEA,MAAM,IAAIrF,yBAAAA,qBAAqB;QACjC;QAEA,sEAAsE;QACtE,wDAAwD;QACxD,yEAAyE;QACzE,wDAAwD;QACxD,IAAIyE,kBAAkBpF,kBAAkB,EAAE;YACxC2F,QAAQ7D,KAAK,CACX,CAAC,OAAO,EAAEoC,UAAUtD,KAAK,CAAC,8QAA8Q,CAAC;YAE3S,MAAM,IAAID,yBAAAA,qBAAqB;QACjC;QAEA,IAAImF,YAAAA,GAAgC;YAClC,6EAA6E;YAC7E,iFAAiF;YACjF,2CAA2C;YAC3CH,QAAQ7D,KAAK,CACX,CAAC,OAAO,EAAEoC,UAAUtD,KAAK,CAAC,wGAAwG,CAAC;YAErI,MAAM,IAAID,yBAAAA,qBAAqB;QACjC;IACF,OAAO;QACL,IACEyE,kBAAkBnF,iBAAiB,KAAK,SACxCmF,kBAAkBrF,kBAAkB,EACpC;YACA4F,QAAQ7D,KAAK,CACX,CAAC,OAAO,EAAEoC,UAAUtD,KAAK,CAAC,8PAA8P,CAAC;YAE3R,MAAM,IAAID,yBAAAA,qBAAqB;QACjC;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3590, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/client/components/unstable-rethrow.server.ts"], "sourcesContent": ["import { isHangingPromiseRejectionError } from '../../server/dynamic-rendering-utils'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering'\nimport { isDynamicServerError } from './hooks-server-context'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (\n    isNextRouterError(error) ||\n    isBailoutToCSRError(error) ||\n    isDynamicServerError(error) ||\n    isDynamicPostpone(error) ||\n    isPostpone(error) ||\n    isHangingPromiseRejectionError(error)\n  ) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n"], "names": ["unstable_rethrow", "error", "isNextRouterError", "isBailoutToCSRError", "isDynamicServerError", "isDynamicPostpone", "isPostpone", "isHangingPromiseRejectionError", "Error", "cause"], "mappings": ";;;+BAOg<PERSON>,oBAAAA;;;eAAAA;;;uCAP+B;4BACpB;8BACS;mCACF;kCACA;oCACG;AAE9B,SAASA,iBAAiBC,KAAc;IAC7C,IACEC,CAAAA,GAAAA,mBAAAA,iBAAiB,EAACD,UAClBE,CAAAA,GAAAA,cAAAA,mBAAmB,EAACF,UACpBG,CAAAA,GAAAA,oBAAAA,oBAAoB,EAACH,UACrBI,CAAAA,GAAAA,kBAAAA,iBAAiB,EAACJ,UAClBK,CAAAA,GAAAA,YAAAA,UAAU,EAACL,UACXM,CAAAA,GAAAA,uBAAAA,8BAA8B,EAACN,QAC/B;QACA,MAAMA;IACR;IAEA,IAAIA,iBAAiBO,SAAS,WAAWP,OAAO;QAC9CD,iBAAiBC,MAAMQ,KAAK;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3626, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/client/components/unstable-rethrow.ts"], "sourcesContent": ["/**\n * This function should be used to rethrow internal Next.js errors so that they can be handled by the framework.\n * When wrapping an API that uses errors to interrupt control flow, you should use this function before you do any error handling.\n * This function will rethrow the error if it is a Next.js error so it can be handled, otherwise it will do nothing.\n *\n * Read more: [Next.js Docs: `unstable_rethrow`](https://nextjs.org/docs/app/api-reference/functions/unstable_rethrow)\n */\nexport const unstable_rethrow =\n  typeof window === 'undefined'\n    ? (\n        require('./unstable-rethrow.server') as typeof import('./unstable-rethrow.server')\n      ).unstable_rethrow\n    : (\n        require('./unstable-rethrow.browser') as typeof import('./unstable-rethrow.browser')\n      ).unstable_rethrow\n"], "names": ["unstable_rethrow", "window", "require"], "mappings": "AAAA;;;;;;CAMC;;;+BACY<PERSON>,oBAAAA;;;eAAAA;;;AAAN,MAAMA,mBACX,OAAOC,WAAW,qBAEZC,QAAQ,wHACRF,gBAAgB,GAEhBE,QAAQ,8BACRF,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3655, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/client/components/navigation.react-server.ts"], "sourcesContent": ["/** @internal */\nclass ReadonlyURLSearchParamsError extends <PERSON>rror {\n  constructor() {\n    super(\n      'Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams'\n    )\n  }\n}\n\nclass ReadonlyURLSearchParams extends URLSearchParams {\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  append() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  delete() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  set() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  sort() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n}\n\nexport { redirect, permanentRedirect } from './redirect'\nexport { RedirectType } from './redirect-error'\nexport { notFound } from './not-found'\nexport { forbidden } from './forbidden'\nexport { unauthorized } from './unauthorized'\nexport { unstable_rethrow } from './unstable-rethrow'\nexport { ReadonlyURLSearchParams }\n"], "names": ["ReadonlyURLSearchParams", "RedirectType", "forbidden", "notFound", "permanentRedirect", "redirect", "unauthorized", "unstable_rethrow", "ReadonlyURLSearchParamsError", "Error", "constructor", "URLSearchParams", "append", "delete", "set", "sort"], "mappings": "AAAA,cAAc;;;;;;;;;;;;;;;;;;;;IAkCLA,uBAAuB,EAAA;eAAvBA;;IALAC,YAAY,EAAA;eAAZA,eAAAA,YAAY;;IAEZC,SAAS,EAAA;eAATA,WAAAA,SAAS;;IADTC,QAAQ,EAAA;eAARA,UAAAA,QAAQ;;IAFEC,iBAAiB,EAAA;eAAjBA,UAAAA,iBAAiB;;IAA3BC,QAAQ,EAAA;eAARA,UAAAA,QAAQ;;IAIRC,YAAY,EAAA;eAAZA,cAAAA,YAAY;;IACZC,gBAAgB,EAAA;eAAhBA,iBAAAA,gBAAgB;;;0BALmB;+BACf;0BACJ;2BACC;8BACG;iCACI;AAhCjC,MAAMC,qCAAqCC;IACzCC,aAAc;QACZ,KAAK,CACH;IAEJ;AACF;AAEA,MAAMV,gCAAgCW;IACpC,wKAAwK,GACxKC,SAAS;QACP,MAAM,IAAIJ;IACZ;IACA,wKAAwK,GACxKK,SAAS;QACP,MAAM,IAAIL;IACZ;IACA,wKAAwK,GACxKM,MAAM;QACJ,MAAM,IAAIN;IACZ;IACA,wKAAwK,GACxKO,OAAO;QACL,MAAM,IAAIP;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3738, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/server/route-modules/app-page/vendored/contexts/server-inserted-html.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].ServerInsertedHtml\n"], "names": ["module", "exports", "require", "vendored", "ServerInsertedHtml"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,WAAW,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3745, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/client/components/bailout-to-client-rendering.ts"], "sourcesContent": ["import { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { workAsyncStorage } from '../../server/app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../../server/app-render/work-unit-async-storage.external'\n\nexport function bailoutToClientRendering(reason: string): void | never {\n  const workStore = workAsyncStorage.getStore()\n\n  if (workStore?.forceStatic) return\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        throw new BailoutToCSRError(reason)\n      default:\n    }\n  }\n}\n"], "names": ["bailoutToClientRendering", "reason", "workStore", "workAsyncStorage", "getStore", "forceStatic", "workUnitStore", "workUnitAsyncStorage", "type", "BailoutToCSRError"], "mappings": ";;;+BAIgBA,4BAAAA;;;eAAAA;;;8BAJkB;0CACD;8CACI;AAE9B,SAASA,yBAAyBC,MAAc;IACrD,MAAMC,YAAYC,0BAAAA,gBAAgB,CAACC,QAAQ;IAE3C,IAAIF,aAAAA,OAAAA,KAAAA,IAAAA,UAAWG,WAAW,EAAE;IAE5B,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACH,QAAQ;IAEnD,IAAIE,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAA6B,CAA7B,IAAIC,cAAAA,iBAAiB,CAACR,SAAtB,qBAAA;2BAAA;gCAAA;kCAAA;gBAA4B;YACpC;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3789, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/client/components/navigation.ts"], "sourcesContent": ["import type { Flight<PERSON>outerState } from '../../server/app-render/types'\nimport type { Params } from '../../server/request/params'\n\nimport { useContext, useMemo } from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  type AppRouterInstance,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { getSegmentValue } from './router-reducer/reducers/get-segment-value'\nimport { PAGE_SEGMENT_KEY, DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment'\nimport { ReadonlyURLSearchParams } from './navigation.react-server'\n\nconst useDynamicRouteParams =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/dynamic-rendering') as typeof import('../../server/app-render/dynamic-rendering')\n      ).useDynamicRouteParams\n    : undefined\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you *read* the current URL's search parameters.\n *\n * Learn more about [`URLSearchParams` on MDN](https://developer.mozilla.org/docs/Web/API/URLSearchParams)\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useSearchParams } from 'next/navigation'\n *\n * export default function Page() {\n *   const searchParams = useSearchParams()\n *   searchParams.get('foo') // returns 'bar' when ?foo=bar\n *   // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSearchParams`](https://nextjs.org/docs/app/api-reference/functions/use-search-params)\n */\n// Client components API\nexport function useSearchParams(): ReadonlyURLSearchParams {\n  const searchParams = useContext(SearchParamsContext)\n\n  // In the case where this is `null`, the compat types added in\n  // `next-env.d.ts` will add a new overload that changes the return type to\n  // include `null`.\n  const readonlySearchParams = useMemo(() => {\n    if (!searchParams) {\n      // When the router is not ready in pages, we won't have the search params\n      // available.\n      return null\n    }\n\n    return new ReadonlyURLSearchParams(searchParams)\n  }, [searchParams]) as ReadonlyURLSearchParams\n\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { bailoutToClientRendering } =\n      require('./bailout-to-client-rendering') as typeof import('./bailout-to-client-rendering')\n    // TODO-APP: handle dynamic = 'force-static' here and on the client\n    bailoutToClientRendering('useSearchParams()')\n  }\n\n  return readonlySearchParams\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the current URL's pathname.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { usePathname } from 'next/navigation'\n *\n * export default function Page() {\n *  const pathname = usePathname() // returns \"/dashboard\" on /dashboard?foo=bar\n *  // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `usePathname`](https://nextjs.org/docs/app/api-reference/functions/use-pathname)\n */\n// Client components API\nexport function usePathname(): string {\n  useDynamicRouteParams?.('usePathname()')\n\n  // In the case where this is `null`, the compat types added in `next-env.d.ts`\n  // will add a new overload that changes the return type to include `null`.\n  return useContext(PathnameContext) as string\n}\n\n// Client components API\nexport {\n  ServerInsertedHTMLContext,\n  useServerInsertedHTML,\n} from '../../shared/lib/server-inserted-html.shared-runtime'\n\n/**\n *\n * This hook allows you to programmatically change routes inside [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components).\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useRouter } from 'next/navigation'\n *\n * export default function Page() {\n *  const router = useRouter()\n *  // ...\n *  router.push('/dashboard') // Navigate to /dashboard\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useRouter`](https://nextjs.org/docs/app/api-reference/functions/use-router)\n */\n// Client components API\nexport function useRouter(): AppRouterInstance {\n  const router = useContext(AppRouterContext)\n  if (router === null) {\n    throw new Error('invariant expected app router to be mounted')\n  }\n\n  return router\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read a route's dynamic params filled in by the current URL.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useParams } from 'next/navigation'\n *\n * export default function Page() {\n *   // on /dashboard/[team] where pathname is /dashboard/nextjs\n *   const { team } = useParams() // team === \"nextjs\"\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useParams`](https://nextjs.org/docs/app/api-reference/functions/use-params)\n */\n// Client components API\nexport function useParams<T extends Params = Params>(): T {\n  useDynamicRouteParams?.('useParams()')\n\n  return useContext(PathParamsContext) as T\n}\n\n/** Get the canonical parameters from the current level to the leaf node. */\n// Client components API\nfunction getSelectedLayoutSegmentPath(\n  tree: FlightRouterState,\n  parallelRouteKey: string,\n  first = true,\n  segmentPath: string[] = []\n): string[] {\n  let node: FlightRouterState\n  if (first) {\n    // Use the provided parallel route key on the first parallel route\n    node = tree[1][parallelRouteKey]\n  } else {\n    // After first parallel route prefer children, if there's no children pick the first parallel route.\n    const parallelRoutes = tree[1]\n    node = parallelRoutes.children ?? Object.values(parallelRoutes)[0]\n  }\n\n  if (!node) return segmentPath\n  const segment = node[0]\n\n  let segmentValue = getSegmentValue(segment)\n\n  if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) {\n    return segmentPath\n  }\n\n  segmentPath.push(segmentValue)\n\n  return getSelectedLayoutSegmentPath(\n    node,\n    parallelRouteKey,\n    false,\n    segmentPath\n  )\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segments **below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n *\n * import { useSelectedLayoutSegments } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segments = useSelectedLayoutSegments()\n *\n *   return (\n *     <ul>\n *       {segments.map((segment, index) => (\n *         <li key={index}>{segment}</li>\n *       ))}\n *     </ul>\n *   )\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegments`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segments)\n */\n// Client components API\nexport function useSelectedLayoutSegments(\n  parallelRouteKey: string = 'children'\n): string[] {\n  useDynamicRouteParams?.('useSelectedLayoutSegments()')\n\n  const context = useContext(LayoutRouterContext)\n  // @ts-expect-error This only happens in `pages`. Type is overwritten in navigation.d.ts\n  if (!context) return null\n\n  return getSelectedLayoutSegmentPath(context.parentTree, parallelRouteKey)\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segment **one level below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n * import { useSelectedLayoutSegment } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segment = useSelectedLayoutSegment()\n *\n *   return <p>Active segment: {segment}</p>\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegment`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segment)\n */\n// Client components API\nexport function useSelectedLayoutSegment(\n  parallelRouteKey: string = 'children'\n): string | null {\n  useDynamicRouteParams?.('useSelectedLayoutSegment()')\n\n  const selectedLayoutSegments = useSelectedLayoutSegments(parallelRouteKey)\n\n  if (!selectedLayoutSegments || selectedLayoutSegments.length === 0) {\n    return null\n  }\n\n  const selectedLayoutSegment =\n    parallelRouteKey === 'children'\n      ? selectedLayoutSegments[0]\n      : selectedLayoutSegments[selectedLayoutSegments.length - 1]\n\n  // if the default slot is showing, we return null since it's not technically \"selected\" (it's a fallback)\n  // and returning an internal value like `__DEFAULT__` would be confusing.\n  return selectedLayoutSegment === DEFAULT_SEGMENT_KEY\n    ? null\n    : selectedLayoutSegment\n}\n\n// Shared components APIs\nexport {\n  notFound,\n  forbidden,\n  unauthorized,\n  redirect,\n  permanentRedirect,\n  RedirectType,\n  ReadonlyURLSearchParams,\n  unstable_rethrow,\n} from './navigation.react-server'\n"], "names": ["ReadonlyURLSearchParams", "RedirectType", "ServerInsertedHTMLContext", "forbidden", "notFound", "permanentRedirect", "redirect", "unauthorized", "unstable_rethrow", "useParams", "usePathname", "useRouter", "useSearchParams", "useSelectedLayoutSegment", "useSelectedLayoutSegments", "useServerInsertedHTML", "useDynamicRouteParams", "window", "require", "undefined", "searchParams", "useContext", "SearchParamsContext", "readonlySearchParams", "useMemo", "bailoutToClientRendering", "PathnameContext", "router", "AppRouterContext", "Error", "PathParamsContext", "getSelectedLayoutSegmentPath", "tree", "parallelRouteKey", "first", "segmentPath", "node", "parallelRoutes", "children", "Object", "values", "segment", "segmentValue", "getSegmentValue", "startsWith", "PAGE_SEGMENT_KEY", "push", "context", "LayoutRouterContext", "parentTree", "selectedLayoutSegments", "length", "selectedLayoutSegment", "DEFAULT_SEGMENT_KEY"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;IA0REA,uBAAuB,EAAA;eAAvBA,uBAAAA,uBAAuB;;IADvBC,YAAY,EAAA;eAAZA,uBAAAA,YAAY;;IApLZC,yBAAyB,EAAA;eAAzBA,iCAAAA,yBAAyB;;IAgLzBC,SAAS,EAAA;eAATA,uBAAAA,SAAS;;IADTC,QAAQ,EAAA;eAARA,uBAAAA,QAAQ;;IAIRC,iBAAiB,EAAA;eAAjBA,uBAAAA,iBAAiB;;IADjBC,QAAQ,EAAA;eAARA,uBAAAA,QAAQ;;IADRC,YAAY,EAAA;eAAZA,uBAAAA,YAAY;;IAKZC,gBAAgB,EAAA;eAAhBA,uBAAAA,gBAAgB;;IApIFC,SAAS,EAAA;eAATA;;IA5DAC,WAAW,EAAA;eAAXA;;IAiCAC,SAAS,EAAA;eAATA;;IA9EAC,eAAe,EAAA;eAAfA;;IA6MAC,wBAAwB,EAAA;eAAxBA;;IA/BAC,yBAAyB,EAAA;eAAzBA;;IAtHdC,qBAAqB,EAAA;eAArBA,iCAAAA,qBAAqB;;;uBAnGa;+CAK7B;iDAKA;iCACyB;yBACsB;uCACd;iDAuFjC;AArFP,MAAMC,wBACJ,OAAOC,WAAW,qBAEZC,QAAQ,kHACRF,qBAAqB,GACvBG;AAuBC,SAASP;IACd,MAAMQ,eAAeC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,iCAAAA,mBAAmB;IAEnD,8DAA8D;IAC9D,0EAA0E;IAC1E,kBAAkB;IAClB,MAAMC,uBAAuBC,CAAAA,GAAAA,OAAAA,OAAO,EAAC;QACnC,IAAI,CAACJ,cAAc;YACjB,yEAAyE;YACzE,aAAa;YACb,OAAO;QACT;QAEA,OAAO,IAAIpB,uBAAAA,uBAAuB,CAACoB;IACrC,GAAG;QAACA;KAAa;IAEjB,IAAI,OAAOH,WAAW,kBAAa;QACjC,iEAAiE;QACjE,MAAM,EAAEQ,wBAAwB,EAAE,GAChCP,QAAQ;QACV,mEAAmE;QACnEO,yBAAyB;IAC3B;IAEA,OAAOF;AACT;AAoBO,SAASb;IACdM,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,8EAA8E;IAC9E,0EAA0E;IAC1E,OAAOK,CAAAA,GAAAA,OAAAA,UAAU,EAACK,iCAAAA,eAAe;AACnC;AA2BO,SAASf;IACd,MAAMgB,SAASN,CAAAA,GAAAA,OAAAA,UAAU,EAACO,+BAAAA,gBAAgB;IAC1C,IAAID,WAAW,MAAM;QACnB,MAAM,OAAA,cAAwD,CAAxD,IAAIE,MAAM,gDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAuD;IAC/D;IAEA,OAAOF;AACT;AAoBO,SAASlB;IACdO,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,OAAOK,CAAAA,GAAAA,OAAAA,UAAU,EAACS,iCAAAA,iBAAiB;AACrC;AAEA,0EAA0E,GAC1E,wBAAwB;AACxB,SAASC,6BACPC,IAAuB,EACvBC,gBAAwB,EACxBC,KAAY,EACZC,WAA0B;IAD1BD,IAAAA,UAAAA,KAAAA,GAAAA,QAAQ;IACRC,IAAAA,gBAAAA,KAAAA,GAAAA,cAAwB,EAAE;IAE1B,IAAIC;IACJ,IAAIF,OAAO;QACT,kEAAkE;QAClEE,OAAOJ,IAAI,CAAC,EAAE,CAACC,iBAAiB;IAClC,OAAO;QACL,oGAAoG;QACpG,MAAMI,iBAAiBL,IAAI,CAAC,EAAE;YACvBK;QAAPD,OAAOC,CAAAA,2BAAAA,eAAeC,QAAQ,KAAA,OAAvBD,2BAA2BE,OAAOC,MAAM,CAACH,eAAe,CAAC,EAAE;IACpE;IAEA,IAAI,CAACD,MAAM,OAAOD;IAClB,MAAMM,UAAUL,IAAI,CAAC,EAAE;IAEvB,IAAIM,eAAeC,CAAAA,GAAAA,iBAAAA,eAAe,EAACF;IAEnC,IAAI,CAACC,gBAAgBA,aAAaE,UAAU,CAACC,SAAAA,gBAAgB,GAAG;QAC9D,OAAOV;IACT;IAEAA,YAAYW,IAAI,CAACJ;IAEjB,OAAOX,6BACLK,MACAH,kBACA,OACAE;AAEJ;AA4BO,SAASrB,0BACdmB,gBAAqC;IAArCA,IAAAA,qBAAAA,KAAAA,GAAAA,mBAA2B;IAE3BjB,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,MAAM+B,UAAU1B,CAAAA,GAAAA,OAAAA,UAAU,EAAC2B,+BAAAA,mBAAmB;IAC9C,wFAAwF;IACxF,IAAI,CAACD,SAAS,OAAO;IAErB,OAAOhB,6BAA6BgB,QAAQE,UAAU,EAAEhB;AAC1D;AAqBO,SAASpB,yBACdoB,gBAAqC;IAArCA,IAAAA,qBAAAA,KAAAA,GAAAA,mBAA2B;IAE3BjB,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,MAAMkC,yBAAyBpC,0BAA0BmB;IAEzD,IAAI,CAACiB,0BAA0BA,uBAAuBC,MAAM,KAAK,GAAG;QAClE,OAAO;IACT;IAEA,MAAMC,wBACJnB,qBAAqB,aACjBiB,sBAAsB,CAAC,EAAE,GACzBA,sBAAsB,CAACA,uBAAuBC,MAAM,GAAG,EAAE;IAE/D,yGAAyG;IACzG,yEAAyE;IACzE,OAAOC,0BAA0BC,SAAAA,mBAAmB,GAChD,OACAD;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3973, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}