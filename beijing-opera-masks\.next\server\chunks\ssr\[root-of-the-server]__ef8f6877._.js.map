{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/noto_serif_sc_f659c15d.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"noto_serif_sc_f659c15d-module__keljFq__className\",\n  \"variable\": \"noto_serif_sc_f659c15d-module__keljFq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/noto_serif_sc_f659c15d.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Noto_Serif_SC%22,%22arguments%22:[{%22variable%22:%22--font-noto-serif-sc%22,%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22500%22,%22600%22,%22700%22,%22900%22],%22display%22:%22swap%22}],%22variableName%22:%22notoSerifSC%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Noto Serif SC', 'Noto Serif SC Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,6JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,6JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,6JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/noto_sans_sc_58f21a7.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"noto_sans_sc_58f21a7-module__JDfuSa__className\",\n  \"variable\": \"noto_sans_sc_58f21a7-module__JDfuSa__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/noto_sans_sc_58f21a7.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Noto_Sans_SC%22,%22arguments%22:[{%22variable%22:%22--font-noto-sans-sc%22,%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22500%22,%22600%22,%22700%22],%22display%22:%22swap%22}],%22variableName%22:%22notoSansSC%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Noto Sans SC', 'Noto Sans SC Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,2JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,2JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,2JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/ma_shan_zheng_42a091c0.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"ma_shan_zheng_42a091c0-module__lEOk8q__className\",\n  \"variable\": \"ma_shan_zheng_42a091c0-module__lEOk8q__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/ma_shan_zheng_42a091c0.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22<PERSON><PERSON>_<PERSON>_<PERSON>%22,%22arguments%22:[{%22variable%22:%22--font-ma-shan-zheng%22,%22subsets%22:[%22latin%22],%22weight%22:[%22400%22],%22display%22:%22swap%22}],%22variableName%22:%22maShangZheng%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'<PERSON>', '<PERSON>'\",\n        fontWeight: 400,\nfontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,6JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,YAAY;QACpB,WAAW;IAEP;AACJ;AAEA,IAAI,6JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,6JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/performance/PerformanceMonitor.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const PerformanceMonitor = registerClientReference(\n    function() { throw new Error(\"Attempted to call PerformanceMonitor() from the server but PerformanceMonitor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/performance/PerformanceMonitor.tsx <module evaluation>\",\n    \"PerformanceMonitor\",\n);\nexport const performanceUtils = registerClientReference(\n    function() { throw new Error(\"Attempted to call performanceUtils() from the server but performanceUtils is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/performance/PerformanceMonitor.tsx <module evaluation>\",\n    \"performanceUtils\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,mFACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,mFACA", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/performance/PerformanceMonitor.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const PerformanceMonitor = registerClientReference(\n    function() { throw new Error(\"Attempted to call PerformanceMonitor() from the server but PerformanceMonitor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/performance/PerformanceMonitor.tsx\",\n    \"PerformanceMonitor\",\n);\nexport const performanceUtils = registerClientReference(\n    function() { throw new Error(\"Attempted to call performanceUtils() from the server but performanceUtils is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/performance/PerformanceMonitor.tsx\",\n    \"performanceUtils\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,+DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,+DACA", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/providers/ThemeProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/ThemeProvider.tsx <module evaluation>\",\n    \"ThemeProvider\",\n);\nexport const themeColors = registerClientReference(\n    function() { throw new Error(\"Attempted to call themeColors() from the server but themeColors is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/ThemeProvider.tsx <module evaluation>\",\n    \"themeColors\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/ThemeProvider.tsx <module evaluation>\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,4EACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4EACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,4EACA", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/providers/ThemeProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/ThemeProvider.tsx\",\n    \"ThemeProvider\",\n);\nexport const themeColors = registerClientReference(\n    function() { throw new Error(\"Attempted to call themeColors() from the server but themeColors is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/ThemeProvider.tsx\",\n    \"themeColors\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/ThemeProvider.tsx\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,wDACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,wDACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,wDACA", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport { Noto_Serif_SC, Noto_Sans_SC, <PERSON><PERSON><PERSON>_<PERSON> } from \"next/font/google\";\nimport { PerformanceMonitor } from \"@/components/performance/PerformanceMonitor\";\nimport { ThemeProvider } from \"@/components/providers/ThemeProvider\";\n// import \"./globals.css\";\n\n// 传统戏曲风格字体配置\nconst notoSerifSC = Noto_Serif_SC({\n  variable: \"--font-noto-serif-sc\",\n  subsets: [\"latin\"],\n  weight: [\"400\", \"500\", \"600\", \"700\", \"900\"],\n  display: 'swap',\n});\n\nconst notoSansSC = Noto_Sans_SC({\n  variable: \"--font-noto-sans-sc\",\n  subsets: [\"latin\"],\n  weight: [\"400\", \"500\", \"600\", \"700\"],\n  display: 'swap',\n});\n\nconst ma<PERSON><PERSON><PERSON>heng = <PERSON>_Shan_Zheng({\n  variable: \"--font-ma-shan-zheng\",\n  subsets: [\"latin\"],\n  weight: [\"400\"],\n  display: 'swap',\n});\n\nexport const metadata: Metadata = {\n  title: \"京剧脸谱文化展示平台 - 探索中国传统戏曲艺术\",\n  description: \"深入了解京剧脸谱的文化内涵，探索生旦净丑四大行当的艺术魅力。包含15个经典脸谱的详细介绍、绘制动画演示和文化背景解析。\",\n  keywords: \"京剧,脸谱,中国传统文化,戏曲,生旦净丑,文化艺术,传统艺术\",\n  authors: [{ name: \"京剧脸谱文化展示平台\" }],\n  creator: \"京剧脸谱文化展示平台\",\n  publisher: \"京剧脸谱文化展示平台\",\n  robots: \"index, follow\",\n  openGraph: {\n    title: \"京剧脸谱文化展示平台\",\n    description: \"探索中国传统京剧脸谱艺术的文化魅力\",\n    type: \"website\",\n    locale: \"zh_CN\",\n    siteName: \"京剧脸谱文化展示平台\"\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"京剧脸谱文化展示平台\",\n    description: \"探索中国传统京剧脸谱艺术的文化魅力\"\n  },\n  viewport: \"width=device-width, initial-scale=1\",\n  themeColor: \"#B91C1C\"\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"zh-CN\">\n      <head>\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n        <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossOrigin=\"anonymous\" />\n        <link rel=\"dns-prefetch\" href=\"https://via.placeholder.com\" />\n      </head>\n      <body\n        className={`${notoSerifSC.variable} ${notoSansSC.variable} ${maShangZheng.variable} antialiased`}\n        style={{\n          fontFamily: 'var(--font-noto-serif-sc), \"Noto Serif SC\", \"SimSun\", \"宋体\", serif',\n          fontSize: '16px',\n          lineHeight: '1.6'\n        }}\n      >\n        <PerformanceMonitor />\n        <ThemeProvider>\n          {children}\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;;;;;;;AAyBO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAa;KAAE;IACjC,SAAS;IACT,WAAW;IACX,QAAQ;IACR,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA,UAAU;IACV,YAAY;AACd;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;;0BACT,8OAAC;;kCACC,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;wBAA4B,aAAY;;;;;;kCACpE,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;;;;;;;0BAEhC,8OAAC;gBACC,WAAW,GAAG,iJAAA,CAAA,UAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,+IAAA,CAAA,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,iJAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,YAAY,CAAC;gBAChG,OAAO;oBACL,YAAY;oBACZ,UAAU;oBACV,YAAY;gBACd;;kCAEA,8OAAC,uJAAA,CAAA,qBAAkB;;;;;kCACnB,8OAAC,gJAAA,CAAA,gBAAa;kCACX;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}