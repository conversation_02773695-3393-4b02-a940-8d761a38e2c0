"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/mask/[id]/page",{

/***/ "(app-pages-browser)/./src/app/mask/[id]/page.tsx":
/*!************************************!*\
  !*** ./src/app/mask/[id]/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MaskDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _data_masks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/masks */ \"(app-pages-browser)/./src/data/masks.ts\");\n/* harmony import */ var _services_maskService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/maskService */ \"(app-pages-browser)/./src/services/maskService.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/providers/ThemeProvider */ \"(app-pages-browser)/./src/components/providers/ThemeProvider.tsx\");\n/* harmony import */ var _components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/navigation/SimpleNavbar */ \"(app-pages-browser)/./src/components/navigation/SimpleNavbar.tsx\");\n/* harmony import */ var _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useLocalStorage */ \"(app-pages-browser)/./src/hooks/useLocalStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction MaskDetailPage() {\n    var _mask_images, _mask_images1;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const maskId = params.id;\n    const [mask, setMask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAnimation, setShowAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { colors } = (0,_components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const { toggleFavorite, isFavorite } = (0,_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useFavorites)();\n    const { addToRecentlyViewed } = (0,_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useRecentlyViewed)();\n    // 加载脸谱数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MaskDetailPage.useEffect\": ()=>{\n            const loadMask = {\n                \"MaskDetailPage.useEffect.loadMask\": async ()=>{\n                    try {\n                        let maskData = _data_masks__WEBPACK_IMPORTED_MODULE_3__.operaMasks; // 默认使用静态数据\n                        if ((0,_lib_supabase__WEBPACK_IMPORTED_MODULE_5__.isSupabaseConfigured)()) {\n                            try {\n                                maskData = await _services_maskService__WEBPACK_IMPORTED_MODULE_4__.MaskService.getAllApprovedMasks();\n                            } catch (error) {\n                                console.error('Error loading masks from database:', error);\n                            }\n                        }\n                        const foundMask = maskData.find({\n                            \"MaskDetailPage.useEffect.loadMask.foundMask\": (m)=>m.id === maskId\n                        }[\"MaskDetailPage.useEffect.loadMask.foundMask\"]);\n                        setMask(foundMask || null);\n                        // 添加到最近浏览记录\n                        if (foundMask) {\n                            addToRecentlyViewed(foundMask.id);\n                        }\n                    } catch (error) {\n                        console.error('Error loading mask:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"MaskDetailPage.useEffect.loadMask\"];\n            loadMask();\n        }\n    }[\"MaskDetailPage.useEffect\"], [\n        maskId,\n        addToRecentlyViewed\n    ]);\n    // 加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                backgroundColor: colors.background,\n                color: colors.textPrimary\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__.SimpleNavbar, {\n                    showBackButton: true,\n                    title: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        minHeight: 'calc(100vh - 80px)'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '3rem',\n                                    marginBottom: '1rem',\n                                    animation: 'pulse 2s infinite'\n                                },\n                                children: \"\\uD83C\\uDFAD\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"正在加载脸谱信息...\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this);\n    }\n    // 脸谱未找到\n    if (!mask) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                backgroundColor: colors.background,\n                color: colors.textPrimary\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__.SimpleNavbar, {\n                    showBackButton: true,\n                    title: \"脸谱未找到\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        minHeight: 'calc(100vh - 80px)'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontSize: '2rem',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"脸谱未找到\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    color: colors.textSecondary,\n                                    marginBottom: '2rem'\n                                },\n                                children: \"抱歉，您访问的脸谱不存在。\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/'),\n                                style: {\n                                    backgroundColor: colors.primary,\n                                    color: 'white',\n                                    padding: '0.75rem 1.5rem',\n                                    borderRadius: '0.5rem',\n                                    border: 'none',\n                                    cursor: 'pointer',\n                                    fontSize: '1rem'\n                                },\n                                children: \"返回首页\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            backgroundColor: colors.background,\n            color: colors.textPrimary\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__.SimpleNavbar, {\n                showBackButton: true,\n                title: mask.name\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '1200px',\n                    margin: '0 auto',\n                    padding: '2rem'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'grid',\n                        gridTemplateColumns: '1fr 1fr',\n                        gap: '3rem',\n                        '@media (max-width: 768px)': {\n                            gridTemplateColumns: '1fr'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: colors.backgroundSecondary,\n                                    borderRadius: '12px',\n                                    padding: '2rem',\n                                    textAlign: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: ((_mask_images = mask.images) === null || _mask_images === void 0 ? void 0 : _mask_images.fullSize) || mask.imageUrl || ((_mask_images1 = mask.images) === null || _mask_images1 === void 0 ? void 0 : _mask_images1.thumbnail),\n                                        alt: mask.name,\n                                        style: {\n                                            width: '100%',\n                                            maxWidth: '400px',\n                                            height: 'auto',\n                                            borderRadius: '8px',\n                                            marginBottom: '1rem'\n                                        },\n                                        onError: (e)=>{\n                                            const target = e.target;\n                                            target.src = \"https://via.placeholder.com/400x400/DC143C/FFFFFF?text=\".concat(encodeURIComponent(mask.name));\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAnimation(!showAnimation),\n                                        style: {\n                                            backgroundColor: colors.primary,\n                                            color: 'white',\n                                            padding: '0.75rem 1.5rem',\n                                            borderRadius: '0.5rem',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            fontSize: '1rem',\n                                            marginTop: '1rem'\n                                        },\n                                        children: showAnimation ? '隐藏绘制过程' : '观看绘制过程'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: colors.backgroundSecondary,\n                                        borderRadius: '12px',\n                                        padding: '2rem',\n                                        marginBottom: '2rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            style: {\n                                                fontSize: '1.5rem',\n                                                fontWeight: 'bold',\n                                                marginBottom: '1rem',\n                                                color: colors.textPrimary\n                                            },\n                                            children: \"基本信息\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                marginBottom: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"角色：\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" \",\n                                                mask.character\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                marginBottom: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"行当：\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" \",\n                                                mask.roleCategory\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                marginBottom: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"色彩分类：\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" \",\n                                                mask.colorCategory\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this),\n                                        mask.mainColors && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                marginBottom: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"主要色彩：\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        gap: '0.5rem',\n                                                        marginTop: '0.5rem'\n                                                    },\n                                                    children: mask.mainColors.map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                width: '30px',\n                                                                height: '30px',\n                                                                borderRadius: '50%',\n                                                                backgroundColor: color,\n                                                                border: '2px solid rgba(0,0,0,0.1)'\n                                                            },\n                                                            title: color\n                                                        }, index, false, {\n                                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: colors.backgroundSecondary,\n                                        borderRadius: '12px',\n                                        padding: '2rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            style: {\n                                                fontSize: '1.5rem',\n                                                fontWeight: 'bold',\n                                                marginBottom: '1rem',\n                                                color: colors.textPrimary\n                                            },\n                                            children: \"文化背景\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                lineHeight: '1.6'\n                                            },\n                                            children: typeof mask.culturalBackground === 'string' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: mask.culturalBackground\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '1rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"历史起源：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                style: {\n                                                                    marginTop: '0.5rem'\n                                                                },\n                                                                children: mask.culturalBackground.origin\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '1rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"性格特点：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                style: {\n                                                                    marginTop: '0.5rem'\n                                                                },\n                                                                children: mask.culturalBackground.personality\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '1rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"象征意义：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                style: {\n                                                                    marginTop: '0.5rem'\n                                                                },\n                                                                children: mask.culturalBackground.symbolism\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\mask\\\\[id]\\\\page.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\n_s(MaskDetailPage, \"1OydzzeE+F7DM5O1RwNKSjUnD/s=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__.useTheme,\n        _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useFavorites,\n        _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useRecentlyViewed\n    ];\n});\n_c = MaskDetailPage;\nvar _c;\n$RefreshReg$(_c, \"MaskDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/mask/[id]/page.tsx\n"));

/***/ })

});