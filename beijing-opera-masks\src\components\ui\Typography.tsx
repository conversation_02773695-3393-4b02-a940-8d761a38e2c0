'use client';

import React from 'react';

// 字体样式配置
export const fontStyles = {
  // 主标题 - 使用马善政字体（传统书法风格）
  mainTitle: {
    fontFamily: 'var(--font-ma-shan-zheng), "<PERSON>", "<PERSON>Ti", "楷体", cursive',
    fontSize: '2.5rem',
    fontWeight: '400',
    lineHeight: '1.2',
    color: '#B91C1C',
    textShadow: '2px 2px 4px rgba(0,0,0,0.1)'
  },
  
  // 页面标题 - 使用思源宋体
  pageTitle: {
    fontFamily: 'var(--font-noto-serif-sc), "Noto Serif SC", "SimSun", "宋体", serif',
    fontSize: '2rem',
    fontWeight: '700',
    lineHeight: '1.3',
    color: '#1F2937'
  },
  
  // 章节标题
  sectionTitle: {
    fontFamily: 'var(--font-noto-serif-sc), "Noto Serif SC", "SimSun", "宋体", serif',
    fontSize: '1.5rem',
    fontWeight: '600',
    lineHeight: '1.4',
    color: '#374151'
  },
  
  // 子标题
  subtitle: {
    fontFamily: 'var(--font-noto-serif-sc), "Noto Serif SC", "SimSun", "宋体", serif',
    fontSize: '1.25rem',
    fontWeight: '500',
    lineHeight: '1.5',
    color: '#4B5563'
  },
  
  // 正文 - 使用思源黑体
  body: {
    fontFamily: 'var(--font-noto-sans-sc), "Noto Sans SC", "Microsoft YaHei", "微软雅黑", sans-serif',
    fontSize: '1rem',
    fontWeight: '400',
    lineHeight: '1.6',
    color: '#6B7280'
  },
  
  // 重要文本
  emphasis: {
    fontFamily: 'var(--font-noto-serif-sc), "Noto Serif SC", "SimSun", "宋体", serif',
    fontSize: '1rem',
    fontWeight: '600',
    lineHeight: '1.6',
    color: '#374151'
  },
  
  // 小字说明
  caption: {
    fontFamily: 'var(--font-noto-sans-sc), "Noto Sans SC", "Microsoft YaHei", "微软雅黑", sans-serif',
    fontSize: '0.875rem',
    fontWeight: '400',
    lineHeight: '1.5',
    color: '#9CA3AF'
  },
  
  // 按钮文字
  button: {
    fontFamily: 'var(--font-noto-sans-sc), "Noto Sans SC", "Microsoft YaHei", "微软雅黑", sans-serif',
    fontSize: '0.875rem',
    fontWeight: '500',
    lineHeight: '1.4'
  }
};

// 字体组件
interface TypographyProps {
  variant: keyof typeof fontStyles;
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  as?: keyof React.JSX.IntrinsicElements;
}

export function Typography({ 
  variant, 
  children, 
  className, 
  style, 
  as = 'div' 
}: TypographyProps) {
  const Component = as;
  const variantStyle = fontStyles[variant];
  
  return (
    <Component
      className={className}
      style={{
        ...variantStyle,
        ...style
      }}
    >
      {children}
    </Component>
  );
}

// 预定义的字体组件
export function MainTitle({ children, ...props }: Omit<TypographyProps, 'variant'>) {
  return <Typography variant="mainTitle" as="h1" {...props}>{children}</Typography>;
}

export function PageTitle({ children, ...props }: Omit<TypographyProps, 'variant'>) {
  return <Typography variant="pageTitle" as="h1" {...props}>{children}</Typography>;
}

export function SectionTitle({ children, ...props }: Omit<TypographyProps, 'variant'>) {
  return <Typography variant="sectionTitle" as="h2" {...props}>{children}</Typography>;
}

export function Subtitle({ children, ...props }: Omit<TypographyProps, 'variant'>) {
  return <Typography variant="subtitle" as="h3" {...props}>{children}</Typography>;
}

export function BodyText({ children, ...props }: Omit<TypographyProps, 'variant'>) {
  return <Typography variant="body" as="p" {...props}>{children}</Typography>;
}

export function EmphasisText({ children, ...props }: Omit<TypographyProps, 'variant'>) {
  return <Typography variant="emphasis" as="span" {...props}>{children}</Typography>;
}

export function CaptionText({ children, ...props }: Omit<TypographyProps, 'variant'>) {
  return <Typography variant="caption" as="span" {...props}>{children}</Typography>;
}
