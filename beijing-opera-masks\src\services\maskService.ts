import { supabase, isSupabaseConfigured } from '@/lib/supabase';
import { Database } from '@/types/database';
import { OperaMask } from '@/types/mask';
import { operaMasks } from '@/data/masks';

type MaskRow = Database['public']['Tables']['masks']['Row'];
type MaskInsert = Database['public']['Tables']['masks']['Insert'];
type MaskUpdate = Database['public']['Tables']['masks']['Update'];

export class MaskService {
  // 获取所有已审核的脸谱
  static async getAllApprovedMasks(): Promise<OperaMask[]> {
    // 如果Supabase未配置，返回静态数据
    if (!isSupabaseConfigured()) {
      console.warn('Supabase not configured, using static mask data');
      return operaMasks;
    }

    try {
      const { data, error } = await supabase
        .from('masks')
        .select(`
          *,
          drawing_steps (
            id,
            step_number,
            title,
            description,
            duration,
            svg_path
          )
        `)
        .eq('is_approved', true)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching masks:', error);
        // 如果数据库查询失败，回退到静态数据
        console.warn('Falling back to static mask data');
        return operaMasks;
      }

      return this.transformToOperaMasks(data);
    } catch (error) {
      console.error('Error in getAllApprovedMasks:', error);
      // 如果出现异常，回退到静态数据
      console.warn('Falling back to static mask data due to error');
      return operaMasks;
    }
  }

  // 根据ID获取脸谱
  static async getMaskById(id: string): Promise<OperaMask | null> {
    const { data, error } = await supabase
      .from('masks')
      .select(`
        *,
        drawing_steps (
          id,
          step_number,
          title,
          description,
          duration,
          svg_path
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // 未找到记录
      }
      console.error('Error fetching mask:', error);
      throw error;
    }

    return this.transformToOperaMask(data);
  }

  // 根据角色类型筛选脸谱
  static async getMasksByRoleType(roleType: string): Promise<OperaMask[]> {
    const { data, error } = await supabase
      .from('masks')
      .select(`
        *,
        drawing_steps (
          id,
          step_number,
          title,
          description,
          duration,
          svg_path
        )
      `)
      .eq('role_type', roleType)
      .eq('is_approved', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching masks by role type:', error);
      throw error;
    }

    return this.transformToOperaMasks(data);
  }

  // 搜索脸谱
  static async searchMasks(query: string): Promise<OperaMask[]> {
    const { data, error } = await supabase
      .from('masks')
      .select(`
        *,
        drawing_steps (
          id,
          step_number,
          title,
          description,
          duration,
          svg_path
        )
      `)
      .or(`name.ilike.%${query}%,character.ilike.%${query}%,cultural_background.ilike.%${query}%`)
      .eq('is_approved', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error searching masks:', error);
      throw error;
    }

    return this.transformToOperaMasks(data);
  }

  // 创建新脸谱
  static async createMask(maskData: Omit<MaskInsert, 'id'>): Promise<string> {
    // 生成基于角色名称的ID
    const generateMaskId = (character: string): string => {
      const timestamp = Date.now().toString(36);
      const randomStr = Math.random().toString(36).substring(2, 8);
      const cleanCharacter = character.toLowerCase().replace(/[^a-z0-9]/g, '');
      return `${cleanCharacter}_${timestamp}_${randomStr}`;
    };

    const maskId = generateMaskId(maskData.character);
    const maskWithId = { ...maskData, id: maskId };

    const { data, error } = await supabase
      .from('masks')
      .insert(maskWithId)
      .select('id')
      .single();

    if (error) {
      console.error('Error creating mask:', error);
      throw error;
    }

    return data.id;
  }

  // 更新脸谱
  static async updateMask(id: string, updates: MaskUpdate): Promise<void> {
    const { error } = await supabase
      .from('masks')
      .update(updates)
      .eq('id', id);

    if (error) {
      console.error('Error updating mask:', error);
      throw error;
    }
  }

  // 删除脸谱
  static async deleteMask(id: string): Promise<void> {
    const { error } = await supabase
      .from('masks')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting mask:', error);
      throw error;
    }
  }

  // 增加浏览次数
  static async incrementViewCount(id: string): Promise<void> {
    const { error } = await supabase
      .from('masks')
      .update({ views_count: supabase.sql`views_count + 1` })
      .eq('id', id);

    if (error) {
      console.error('Error incrementing view count:', error);
      // 不抛出错误，因为这不是关键功能
    }
  }

  // 获取用户创建的脸谱
  static async getUserMasks(userId: string): Promise<OperaMask[]> {
    const { data, error } = await supabase
      .from('masks')
      .select(`
        *,
        drawing_steps (
          id,
          step_number,
          title,
          description,
          duration,
          svg_path
        )
      `)
      .eq('created_by', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching user masks:', error);
      throw error;
    }

    return this.transformToOperaMasks(data);
  }

  // 获取热门脸谱
  static async getPopularMasks(limit: number = 10): Promise<OperaMask[]> {
    const { data, error } = await supabase
      .from('masks')
      .select(`
        *,
        drawing_steps (
          id,
          step_number,
          title,
          description,
          duration,
          svg_path
        )
      `)
      .eq('is_approved', true)
      .order('likes_count', { ascending: false })
      .order('views_count', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching popular masks:', error);
      throw error;
    }

    return this.transformToOperaMasks(data);
  }

  // 转换数据库记录为OperaMask类型
  private static transformToOperaMasks(data: any[]): OperaMask[] {
    return data.map(item => this.transformToOperaMask(item));
  }

  private static transformToOperaMask(data: any): OperaMask {
    return {
      id: data.id,
      name: data.name,
      character: data.character,
      roleType: data.role_type,
      colorTheme: data.color_theme,
      imageUrl: data.image_url,
      thumbnailUrl: data.thumbnail_url,
      culturalBackground: data.cultural_background,
      personalityTraits: data.personality_traits,
      storyDescription: data.story_description,
      dynasty: data.dynasty,
      isOfficial: data.is_official,
      isApproved: data.is_approved,
      createdBy: data.created_by,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      likesCount: data.likes_count,
      viewsCount: data.views_count,
      drawingSteps: data.drawing_steps?.map((step: any) => ({
        id: step.id,
        stepNumber: step.step_number,
        title: step.title,
        description: step.description,
        duration: step.duration,
        svgPath: step.svg_path
      })) || []
    };
  }
}
