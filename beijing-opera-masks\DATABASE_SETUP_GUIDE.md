# 数据库集成配置指南
## Supabase + 用户认证 + 社区功能

### 🚀 功能概述

本次升级将京剧脸谱平台从静态展示升级为完整的社区化平台，包含：

#### ✅ 已实现的功能
1. **Supabase数据库集成**
   - 完整的数据库表结构设计
   - 数据迁移脚本
   - 行级安全策略（RLS）
   - 自动触发器和统计功能

2. **用户认证系统**
   - 用户注册/登录
   - 用户资料管理
   - 会话管理
   - 权限控制

3. **用户自定义脸谱功能**
   - 脸谱添加表单
   - 图片上传支持
   - 审核机制
   - 用户贡献统计

4. **社区互动功能**
   - 点赞系统
   - 收藏功能
   - 用户贡献积分
   - 脸谱浏览统计

---

## 📋 配置步骤

### 步骤1: 创建Supabase项目

1. **注册Supabase账户**
   - 访问 https://supabase.com/
   - 点击 "Start your project"
   - 使用GitHub或邮箱注册

2. **创建新项目**
   - 点击 "New Project"
   - 选择组织（或创建新组织）
   - 填写项目信息：
     - Name: `beijing-opera-masks`
     - Database Password: 设置强密码
     - Region: 选择最近的区域（如Singapore）
   - 点击 "Create new project"

3. **等待项目初始化**
   - 项目创建需要2-3分钟
   - 完成后会显示项目仪表板

### 步骤2: 获取项目配置信息

1. **获取项目URL和API密钥**
   - 在项目仪表板中，点击左侧 "Settings"
   - 选择 "API"
   - 复制以下信息：
     - Project URL
     - anon public key
     - service_role key（保密）

2. **配置环境变量**
   - 编辑 `.env.local` 文件
   - 替换占位符为实际值：
   ```env
   NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   NEXT_PUBLIC_APP_URL=https://received-title-pairs-employees.trycloudflare.com
   ```

### 步骤3: 执行数据库迁移

1. **创建数据库表结构**
   - 在Supabase仪表板中，点击左侧 "SQL Editor"
   - 点击 "New query"
   - 复制 `supabase/migrations/001_initial_schema.sql` 的内容
   - 粘贴到编辑器中
   - 点击 "Run" 执行

2. **导入初始数据**
   - 创建新的SQL查询
   - 复制 `supabase/migrations/002_seed_data.sql` 的内容
   - 粘贴并执行

3. **验证数据库结构**
   - 点击左侧 "Table Editor"
   - 确认以下表已创建：
     - `masks` - 脸谱数据表
     - `drawing_steps` - 绘制步骤表
     - `user_profiles` - 用户资料表
     - `user_likes` - 用户点赞表
     - `user_favorites` - 用户收藏表

### 步骤4: 配置存储桶（可选）

1. **创建图片存储桶**
   - 点击左侧 "Storage"
   - 点击 "Create a new bucket"
   - Bucket name: `mask-images`
   - 设置为 Public bucket
   - 点击 "Create bucket"

2. **配置存储策略**
   - 在存储桶设置中配置上传权限
   - 允许认证用户上传图片

### 步骤5: 配置认证设置

1. **启用邮箱认证**
   - 点击左侧 "Authentication"
   - 选择 "Settings"
   - 确认 "Enable email confirmations" 已启用

2. **配置邮件模板（可选）**
   - 在 "Email Templates" 中自定义邮件模板
   - 设置品牌信息和样式

---

## 🔧 代码集成

### 已创建的核心文件

1. **数据库配置**
   - `src/lib/supabase.ts` - Supabase客户端配置
   - `src/types/database.ts` - 数据库类型定义

2. **服务层**
   - `src/services/maskService.ts` - 脸谱数据操作
   - `src/services/authService.ts` - 用户认证服务
   - `src/services/userInteractionService.ts` - 用户交互服务

3. **认证系统**
   - `src/contexts/AuthContext.tsx` - 认证上下文
   - `src/components/auth/AuthModal.tsx` - 登录/注册模态框

4. **用户界面**
   - `src/components/navigation/Navbar.tsx` - 导航栏
   - `src/components/mask/AddMaskForm.tsx` - 脸谱添加表单

### 需要更新的现有文件

1. **主页面集成**
   ```typescript
   // src/app/page.tsx 需要更新以使用数据库数据
   import { MaskService } from '@/services/maskService';
   import { useAuth } from '@/contexts/AuthContext';
   ```

2. **脸谱详情页**
   ```typescript
   // src/app/mask/[id]/page.tsx 需要更新
   // 添加点赞、收藏功能
   // 集成浏览统计
   ```

---

## 🧪 测试和验证

### 功能测试清单

#### 数据库连接测试
- [ ] 应用启动无错误
- [ ] 可以获取脸谱列表
- [ ] 脸谱详情页正常显示

#### 用户认证测试
- [ ] 用户注册功能
- [ ] 邮箱验证（如果启用）
- [ ] 用户登录功能
- [ ] 用户登出功能
- [ ] 会话持久化

#### 脸谱管理测试
- [ ] 添加新脸谱
- [ ] 脸谱审核状态
- [ ] 用户贡献统计
- [ ] 脸谱编辑功能

#### 社区功能测试
- [ ] 点赞功能
- [ ] 收藏功能
- [ ] 浏览统计
- [ ] 用户积分系统

### 性能测试
- [ ] 页面加载速度
- [ ] 数据库查询性能
- [ ] 图片加载优化
- [ ] 移动端响应性

---

## 📊 数据库表结构说明

### masks 表（脸谱主表）
```sql
- id: UUID (主键)
- name: 脸谱名称
- character: 角色名称
- role_type: 角色类型 (sheng/dan/jing/chou)
- color_theme: 颜色主题数组
- image_url: 图片URL
- cultural_background: 文化背景
- personality_traits: 性格特征数组
- is_official: 是否官方脸谱
- is_approved: 是否已审核
- created_by: 创建者ID
- likes_count: 点赞数
- views_count: 浏览数
```

### user_profiles 表（用户资料）
```sql
- id: UUID (主键)
- user_id: 关联auth.users
- username: 用户名
- display_name: 显示名称
- contribution_points: 贡献积分
- masks_contributed: 贡献脸谱数量
```

### 其他表
- `drawing_steps`: 绘制步骤
- `user_likes`: 用户点赞记录
- `user_favorites`: 用户收藏记录

---

## 🔐 安全配置

### 行级安全策略（RLS）
- ✅ 已配置完整的RLS策略
- ✅ 用户只能编辑自己的内容
- ✅ 公开内容对所有人可见
- ✅ 未审核内容仅创建者可见

### API安全
- ✅ 使用anon key进行客户端操作
- ✅ 敏感操作使用service role key
- ✅ 自动会话管理

---

## 🚀 部署和上线

### 环境变量配置
确保生产环境中正确配置：
```env
NEXT_PUBLIC_SUPABASE_URL=your-production-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-production-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-production-service-key
NEXT_PUBLIC_APP_URL=your-production-domain
```

### 域名配置
- 在Supabase项目设置中添加生产域名
- 配置CORS设置
- 更新认证重定向URL

---

## 📞 技术支持

### 常见问题

**Q: 数据库连接失败**
A: 检查环境变量配置，确保URL和密钥正确

**Q: 用户注册后无法登录**
A: 检查邮箱验证设置，确认用户已验证邮箱

**Q: 图片上传失败**
A: 检查存储桶配置和权限设置

**Q: 脸谱不显示**
A: 检查RLS策略，确认数据已正确迁移

### 监控和维护
- 定期检查数据库性能
- 监控用户活动和错误日志
- 备份重要数据
- 更新安全策略

**配置状态**: ⚠️ 需要手动配置Supabase项目  
**代码状态**: ✅ 已完成集成代码  
**功能状态**: ✅ 准备就绪，等待数据库配置
