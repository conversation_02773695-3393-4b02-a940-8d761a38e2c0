export interface Database {
  public: {
    Tables: {
      masks: {
        Row: {
          id: string;
          name: string;
          character: string;
          role_type: 'sheng' | 'dan' | 'jing' | 'chou';
          color_theme: string[];
          image_url: string;
          thumbnail_url?: string;
          cultural_background: string;
          personality_traits: string[];
          story_description?: string;
          dynasty?: string;
          is_official: boolean;
          is_approved: boolean;
          created_by?: string;
          created_at: string;
          updated_at: string;
          likes_count: number;
          views_count: number;
        };
        Insert: {
          id: string;
          name: string;
          character: string;
          role_type: 'sheng' | 'dan' | 'jing' | 'chou';
          color_theme: string[];
          image_url: string;
          thumbnail_url?: string;
          cultural_background: string;
          personality_traits: string[];
          story_description?: string;
          dynasty?: string;
          is_official?: boolean;
          is_approved?: boolean;
          created_by?: string;
          created_at?: string;
          updated_at?: string;
          likes_count?: number;
          views_count?: number;
        };
        Update: {
          id?: string;
          name?: string;
          character?: string;
          role_type?: 'sheng' | 'dan' | 'jing' | 'chou';
          color_theme?: string[];
          image_url?: string;
          thumbnail_url?: string;
          cultural_background?: string;
          personality_traits?: string[];
          story_description?: string;
          dynasty?: string;
          is_official?: boolean;
          is_approved?: boolean;
          created_by?: string;
          created_at?: string;
          updated_at?: string;
          likes_count?: number;
          views_count?: number;
        };
      };
      drawing_steps: {
        Row: {
          id: string;
          mask_id: string;
          step_number: number;
          title: string;
          description: string;
          duration: number;
          svg_path?: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          mask_id: string;
          step_number: number;
          title: string;
          description: string;
          duration: number;
          svg_path?: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          mask_id?: string;
          step_number?: number;
          title?: string;
          description?: string;
          duration?: number;
          svg_path?: string;
          created_at?: string;
        };
      };
      user_profiles: {
        Row: {
          id: string;
          user_id: string;
          username: string;
          display_name?: string;
          avatar_url?: string;
          bio?: string;
          contribution_points: number;
          masks_contributed: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          username: string;
          display_name?: string;
          avatar_url?: string;
          bio?: string;
          contribution_points?: number;
          masks_contributed?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          username?: string;
          display_name?: string;
          avatar_url?: string;
          bio?: string;
          contribution_points?: number;
          masks_contributed?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      user_likes: {
        Row: {
          id: string;
          user_id: string;
          mask_id: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          mask_id: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          mask_id?: string;
          created_at?: string;
        };
      };
      user_favorites: {
        Row: {
          id: string;
          user_id: string;
          mask_id: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          mask_id: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          mask_id?: string;
          created_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      role_type: 'sheng' | 'dan' | 'jing' | 'chou';
    };
  };
}
