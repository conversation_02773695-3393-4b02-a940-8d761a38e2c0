{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/utils/cn.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\n\n/**\n * 合并和条件化CSS类名的工具函数\n * 基于clsx库，提供更好的类名处理\n */\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\n/**\n * 创建条件类名的辅助函数\n */\nexport function conditionalClass(condition: boolean, trueClass: string, falseClass?: string) {\n  return condition ? trueClass : (falseClass || '');\n}\n\n/**\n * 创建变体类名映射的辅助函数\n */\nexport function createVariantClasses<T extends string>(\n  variants: Record<T, string>,\n  defaultVariant: T\n) {\n  return (variant?: T) => variants[variant || defaultVariant];\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAMO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,KAAK;AACd;AAKO,SAAS,iBAAiB,SAAkB,EAAE,SAAiB,EAAE,UAAmB;IACzF,OAAO,YAAY,YAAa,cAAc;AAChD;AAKO,SAAS,qBACd,QAA2B,EAC3B,cAAiB;IAEjB,OAAO,CAAC,UAAgB,QAAQ,CAAC,WAAW,eAAe;AAC7D", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/layout/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/utils/cn';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function Layout({ children, className }: LayoutProps) {\n  return (\n    <div className={cn('min-h-screen bg-background', className)}>\n      {children}\n    </div>\n  );\n}\n\ninterface HeaderProps {\n  children: React.ReactNode;\n  className?: string;\n  sticky?: boolean;\n}\n\nexport function Header({ children, className, sticky = true }: HeaderProps) {\n  return (\n    <header className={cn(\n      'bg-white border-b-2 border-golden-yellow shadow-md',\n      sticky && 'sticky top-0 z-40',\n      className\n    )}>\n      <div className=\"chinese-pattern absolute inset-0 opacity-30\" />\n      <div className=\"relative z-10\">\n        {children}\n      </div>\n    </header>\n  );\n}\n\ninterface NavProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function Nav({ children, className }: NavProps) {\n  return (\n    <nav className={cn('flex items-center justify-between px-6 py-4', className)}>\n      {children}\n    </nav>\n  );\n}\n\ninterface MainProps {\n  children: React.ReactNode;\n  className?: string;\n  container?: boolean;\n}\n\nexport function Main({ children, className, container = true }: MainProps) {\n  return (\n    <main className={cn(\n      'flex-1',\n      container && 'container mx-auto px-6 py-8',\n      className\n    )}>\n      {children}\n    </main>\n  );\n}\n\ninterface SectionProps {\n  children: React.ReactNode;\n  className?: string;\n  title?: string;\n  subtitle?: string;\n  background?: 'default' | 'pattern' | 'gradient';\n}\n\nexport function Section({ \n  children, \n  className, \n  title, \n  subtitle,\n  background = 'default'\n}: SectionProps) {\n  const backgroundClasses = {\n    default: '',\n    pattern: 'chinese-pattern',\n    gradient: 'bg-gradient-to-br from-palace-red/5 via-transparent to-golden-yellow/5'\n  };\n  \n  return (\n    <section className={cn(\n      'py-12',\n      backgroundClasses[background],\n      className\n    )}>\n      {(title || subtitle) && (\n        <div className=\"text-center mb-12\">\n          {title && (\n            <h2 className=\"text-3xl font-bold text-ink-black font-serif mb-4\">\n              {title}\n            </h2>\n          )}\n          {subtitle && (\n            <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n              {subtitle}\n            </p>\n          )}\n        </div>\n      )}\n      {children}\n    </section>\n  );\n}\n\ninterface ContainerProps {\n  children: React.ReactNode;\n  className?: string;\n  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';\n}\n\nexport function Container({ children, className, size = 'lg' }: ContainerProps) {\n  const sizeClasses = {\n    sm: 'max-w-2xl',\n    md: 'max-w-4xl',\n    lg: 'max-w-6xl',\n    xl: 'max-w-7xl',\n    full: 'max-w-full'\n  };\n  \n  return (\n    <div className={cn(\n      'mx-auto px-6',\n      sizeClasses[size],\n      className\n    )}>\n      {children}\n    </div>\n  );\n}\n\ninterface GridProps {\n  children: React.ReactNode;\n  className?: string;\n  cols?: 1 | 2 | 3 | 4 | 5 | 6;\n  gap?: 'sm' | 'md' | 'lg' | 'xl';\n  responsive?: boolean;\n}\n\nexport function Grid({ \n  children, \n  className, \n  cols = 3, \n  gap = 'md',\n  responsive = true \n}: GridProps) {\n  const colClasses = {\n    1: 'grid-cols-1',\n    2: 'grid-cols-2',\n    3: 'grid-cols-3',\n    4: 'grid-cols-4',\n    5: 'grid-cols-5',\n    6: 'grid-cols-6'\n  };\n  \n  const gapClasses = {\n    sm: 'gap-4',\n    md: 'gap-6',\n    lg: 'gap-8',\n    xl: 'gap-12'\n  };\n  \n  const responsiveClasses = responsive \n    ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'\n    : colClasses[cols];\n  \n  return (\n    <div className={cn(\n      'grid',\n      responsive ? responsiveClasses : colClasses[cols],\n      gapClasses[gap],\n      className\n    )}>\n      {children}\n    </div>\n  );\n}\n\ninterface FooterProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function Footer({ children, className }: FooterProps) {\n  return (\n    <footer className={cn(\n      'bg-ink-black text-white border-t-2 border-golden-yellow',\n      className\n    )}>\n      <div className=\"chinese-pattern absolute inset-0 opacity-10\" />\n      <div className=\"relative z-10\">\n        {children}\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AACA;;;AAOO,SAAS,OAAO,KAAoC;QAApC,EAAE,QAAQ,EAAE,SAAS,EAAe,GAApC;IACrB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;kBAC9C;;;;;;AAGP;KANgB;AAcT,SAAS,OAAO,KAAmD;QAAnD,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,IAAI,EAAe,GAAnD;IACrB,qBACE,6LAAC;QAAO,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAClB,sDACA,UAAU,qBACV;;0BAEA,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;MAbgB;AAoBT,SAAS,IAAI,KAAiC;QAAjC,EAAE,QAAQ,EAAE,SAAS,EAAY,GAAjC;IAClB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;kBAC/D;;;;;;AAGP;MANgB;AAcT,SAAS,KAAK,KAAoD;QAApD,EAAE,QAAQ,EAAE,SAAS,EAAE,YAAY,IAAI,EAAa,GAApD;IACnB,qBACE,6LAAC;QAAK,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAChB,UACA,aAAa,+BACb;kBAEC;;;;;;AAGP;MAVgB;AAoBT,SAAS,QAAQ,KAMT;QANS,EACtB,QAAQ,EACR,SAAS,EACT,KAAK,EACL,QAAQ,EACR,aAAa,SAAS,EACT,GANS;IAOtB,MAAM,oBAAoB;QACxB,SAAS;QACT,SAAS;QACT,UAAU;IACZ;IAEA,qBACE,6LAAC;QAAQ,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACnB,SACA,iBAAiB,CAAC,WAAW,EAC7B;;YAEC,CAAC,SAAS,QAAQ,mBACjB,6LAAC;gBAAI,WAAU;;oBACZ,uBACC,6LAAC;wBAAG,WAAU;kCACX;;;;;;oBAGJ,0BACC,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;YAKR;;;;;;;AAGP;MApCgB;AA4CT,SAAS,UAAU,KAAoD;QAApD,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,IAAI,EAAkB,GAApD;IACxB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACf,gBACA,WAAW,CAAC,KAAK,EACjB;kBAEC;;;;;;AAGP;MAlBgB;AA4BT,SAAS,KAAK,KAMT;QANS,EACnB,QAAQ,EACR,SAAS,EACT,OAAO,CAAC,EACR,MAAM,IAAI,EACV,aAAa,IAAI,EACP,GANS;IAOnB,MAAM,aAAa;QACjB,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,MAAM,aAAa;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,oBAAoB,aACtB,6DACA,UAAU,CAAC,KAAK;IAEpB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACf,QACA,aAAa,oBAAoB,UAAU,CAAC,KAAK,EACjD,UAAU,CAAC,IAAI,EACf;kBAEC;;;;;;AAGP;MArCgB;AA4CT,SAAS,OAAO,KAAoC;QAApC,EAAE,QAAQ,EAAE,SAAS,EAAe,GAApC;IACrB,qBACE,6LAAC;QAAO,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAClB,2DACA;;0BAEA,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;MAZgB", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/utils/cn';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  variant?: 'default' | 'elevated' | 'outlined' | 'traditional';\n  padding?: 'none' | 'sm' | 'md' | 'lg';\n  onClick?: () => void;\n  hover?: boolean;\n}\n\nexport function Card({ \n  children, \n  className, \n  variant = 'default',\n  padding = 'md',\n  onClick,\n  hover = false\n}: CardProps) {\n  const baseClasses = 'rounded-lg transition-all duration-300';\n  \n  const variantClasses = {\n    default: 'bg-white border border-gray-200',\n    elevated: 'bg-white shadow-lg border border-gray-100',\n    outlined: 'bg-transparent border-2 border-golden-yellow',\n    traditional: 'bg-white border-2 border-golden-yellow chinese-pattern relative overflow-hidden'\n  };\n  \n  const paddingClasses = {\n    none: '',\n    sm: 'p-3',\n    md: 'p-6',\n    lg: 'p-8'\n  };\n  \n  const hoverClasses = hover ? 'hover:shadow-xl hover:scale-105 cursor-pointer' : '';\n  \n  const classes = cn(\n    baseClasses,\n    variantClasses[variant],\n    paddingClasses[padding],\n    hoverClasses,\n    className\n  );\n  \n  return (\n    <div className={classes} onClick={onClick}>\n      {variant === 'traditional' && (\n        <div className=\"absolute inset-0 bg-gradient-to-br from-transparent via-golden-yellow/5 to-palace-red/5 pointer-events-none\" />\n      )}\n      <div className=\"relative z-10\">\n        {children}\n      </div>\n    </div>\n  );\n}\n\ninterface CardHeaderProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function CardHeader({ children, className }: CardHeaderProps) {\n  return (\n    <div className={cn('mb-4', className)}>\n      {children}\n    </div>\n  );\n}\n\ninterface CardTitleProps {\n  children: React.ReactNode;\n  className?: string;\n  level?: 1 | 2 | 3 | 4;\n}\n\nexport function CardTitle({ children, className, level = 2 }: CardTitleProps) {\n  const Tag = `h${level}` as keyof JSX.IntrinsicElements;\n  \n  const levelClasses = {\n    1: 'text-2xl font-bold',\n    2: 'text-xl font-semibold',\n    3: 'text-lg font-medium',\n    4: 'text-base font-medium'\n  };\n  \n  return (\n    <Tag className={cn(\n      'text-ink-black font-serif',\n      levelClasses[level],\n      className\n    )}>\n      {children}\n    </Tag>\n  );\n}\n\ninterface CardContentProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function CardContent({ children, className }: CardContentProps) {\n  return (\n    <div className={cn('text-gray-600', className)}>\n      {children}\n    </div>\n  );\n}\n\ninterface CardFooterProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function CardFooter({ children, className }: CardFooterProps) {\n  return (\n    <div className={cn('mt-4 pt-4 border-t border-gray-200', className)}>\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AACA;;;AAWO,SAAS,KAAK,KAOT;QAPS,EACnB,QAAQ,EACR,SAAS,EACT,UAAU,SAAS,EACnB,UAAU,IAAI,EACd,OAAO,EACP,QAAQ,KAAK,EACH,GAPS;IAQnB,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,UAAU;QACV,UAAU;QACV,aAAa;IACf;IAEA,MAAM,iBAAiB;QACrB,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe,QAAQ,mDAAmD;IAEhF,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACf,aACA,cAAc,CAAC,QAAQ,EACvB,cAAc,CAAC,QAAQ,EACvB,cACA;IAGF,qBACE,6LAAC;QAAI,WAAW;QAAS,SAAS;;YAC/B,YAAY,+BACX,6LAAC;gBAAI,WAAU;;;;;;0BAEjB,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;KA5CgB;AAmDT,SAAS,WAAW,KAAwC;QAAxC,EAAE,QAAQ,EAAE,SAAS,EAAmB,GAAxC;IACzB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;kBACxB;;;;;;AAGP;MANgB;AAcT,SAAS,UAAU,KAAkD;QAAlD,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAkB,GAAlD;IACxB,MAAM,MAAM,AAAC,IAAS,OAAN;IAEhB,MAAM,eAAe;QACnB,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACf,6BACA,YAAY,CAAC,MAAM,EACnB;kBAEC;;;;;;AAGP;MAnBgB;AA0BT,SAAS,YAAY,KAAyC;QAAzC,EAAE,QAAQ,EAAE,SAAS,EAAoB,GAAzC;IAC1B,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;kBACjC;;;;;;AAGP;MANgB;AAaT,SAAS,WAAW,KAAwC;QAAxC,EAAE,QAAQ,EAAE,SAAS,EAAmB,GAAxC;IACzB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;MANgB", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/mask/MaskCard.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Image from 'next/image';\nimport { OperaMask } from '@/types/mask';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';\nimport { cn } from '@/utils/cn';\n\ninterface MaskCardProps {\n  mask: OperaMask;\n  onClick?: (mask: OperaMask) => void;\n  className?: string;\n  showDetails?: boolean;\n  variant?: 'default' | 'compact' | 'featured';\n}\n\nexport function MaskCard({ \n  mask, \n  onClick, \n  className,\n  showDetails = true,\n  variant = 'default'\n}: MaskCardProps) {\n  const handleClick = () => {\n    onClick?.(mask);\n  };\n  \n  const isClickable = !!onClick;\n  \n  return (\n    <Card\n      className={cn(\n        'group overflow-hidden',\n        isClickable && 'cursor-pointer',\n        variant === 'featured' && 'border-2 border-golden-yellow shadow-lg',\n        className\n      )}\n      variant={variant === 'featured' ? 'traditional' : 'elevated'}\n      padding=\"none\"\n      hover={isClickable}\n      onClick={handleClick}\n    >\n      {/* 脸谱图片 */}\n      <div className=\"relative aspect-square overflow-hidden\">\n        <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent z-10\" />\n        <Image\n          src={mask.images.thumbnail}\n          alt={mask.name}\n          fill\n          className=\"object-cover transition-transform duration-300 group-hover:scale-110\"\n          sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n        />\n        \n        {/* 角色分类标签 */}\n        <div className=\"absolute top-3 left-3 z-20\">\n          <span className={cn(\n            'px-2 py-1 text-xs font-medium rounded-full text-white',\n            getRoleCategoryColor(mask.roleCategory)\n          )}>\n            {mask.roleCategory}\n          </span>\n        </div>\n        \n        {/* 颜色分类标签 */}\n        <div className=\"absolute top-3 right-3 z-20\">\n          <span className={cn(\n            'px-2 py-1 text-xs font-medium rounded-full text-white',\n            getColorCategoryColor(mask.colorCategory)\n          )}>\n            {mask.colorCategory}\n          </span>\n        </div>\n        \n        {/* 受欢迎程度 */}\n        <div className=\"absolute bottom-3 right-3 z-20 flex items-center space-x-1\">\n          {Array.from({ length: 5 }).map((_, i) => (\n            <svg\n              key={i}\n              className={cn(\n                'w-3 h-3',\n                i < Math.floor(mask.popularity / 2) ? 'text-golden-yellow' : 'text-gray-300'\n              )}\n              fill=\"currentColor\"\n              viewBox=\"0 0 20 20\"\n            >\n              <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n            </svg>\n          ))}\n        </div>\n      </div>\n      \n      {/* 脸谱信息 */}\n      {showDetails && (\n        <div className=\"p-4\">\n          <CardHeader className=\"mb-2\">\n            <CardTitle level={3} className=\"text-lg group-hover:text-palace-red transition-colors\">\n              {mask.name}\n            </CardTitle>\n            <p className=\"text-sm text-gray-600 font-medium\">\n              {mask.character}\n            </p>\n          </CardHeader>\n          \n          <CardContent>\n            <p className=\"text-sm text-gray-600 line-clamp-2 mb-3\">\n              {mask.culturalBackground.personality}\n            </p>\n            \n            {/* 主要颜色 */}\n            <div className=\"flex items-center space-x-2 mb-3\">\n              <span className=\"text-xs text-gray-500\">主要色彩:</span>\n              <div className=\"flex space-x-1\">\n                {mask.mainColors.slice(0, 3).map((color, index) => (\n                  <div\n                    key={index}\n                    className=\"w-4 h-4 rounded-full border border-gray-300\"\n                    style={{ backgroundColor: color }}\n                    title={color}\n                  />\n                ))}\n              </div>\n            </div>\n            \n            {/* 标签 */}\n            <div className=\"flex flex-wrap gap-1\">\n              {mask.tags.slice(0, 3).map((tag, index) => (\n                <span\n                  key={index}\n                  className=\"px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full\"\n                >\n                  {tag}\n                </span>\n              ))}\n            </div>\n          </CardContent>\n        </div>\n      )}\n    </Card>\n  );\n}\n\n// 获取行当分类颜色\nfunction getRoleCategoryColor(category: string): string {\n  const colors = {\n    '生': 'bg-blue-500',\n    '旦': 'bg-pink-500',\n    '净': 'bg-red-500',\n    '丑': 'bg-yellow-500'\n  };\n  return colors[category as keyof typeof colors] || 'bg-gray-500';\n}\n\n// 获取颜色分类颜色\nfunction getColorCategoryColor(category: string): string {\n  const colors = {\n    '红脸': 'bg-red-600',\n    '黑脸': 'bg-gray-800',\n    '白脸': 'bg-gray-400',\n    '蓝脸': 'bg-blue-600',\n    '绿脸': 'bg-green-600',\n    '黄脸': 'bg-yellow-600',\n    '金脸': 'bg-yellow-500',\n    '银脸': 'bg-gray-300'\n  };\n  return colors[category as keyof typeof colors] || 'bg-gray-500';\n}\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AANA;;;;;AAgBO,SAAS,SAAS,KAMT;QANS,EACvB,IAAI,EACJ,OAAO,EACP,SAAS,EACT,cAAc,IAAI,EAClB,UAAU,SAAS,EACL,GANS;IAOvB,MAAM,cAAc;QAClB,oBAAA,8BAAA,QAAU;IACZ;IAEA,MAAM,cAAc,CAAC,CAAC;IAEtB,qBACE,6LAAC,mIAAA,CAAA,OAAI;QACH,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,yBACA,eAAe,kBACf,YAAY,cAAc,2CAC1B;QAEF,SAAS,YAAY,aAAa,gBAAgB;QAClD,SAAQ;QACR,OAAO;QACP,SAAS;;0BAGT,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,KAAK,MAAM,CAAC,SAAS;wBAC1B,KAAK,KAAK,IAAI;wBACd,IAAI;wBACJ,WAAU;wBACV,OAAM;;;;;;kCAIR,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAChB,yDACA,qBAAqB,KAAK,YAAY;sCAErC,KAAK,YAAY;;;;;;;;;;;kCAKtB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAChB,yDACA,sBAAsB,KAAK,aAAa;sCAEvC,KAAK,aAAa;;;;;;;;;;;kCAKvB,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;gCAEC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,WACA,IAAI,KAAK,KAAK,CAAC,KAAK,UAAU,GAAG,KAAK,uBAAuB;gCAE/D,MAAK;gCACL,SAAQ;0CAER,cAAA,6LAAC;oCAAK,GAAE;;;;;;+BARH;;;;;;;;;;;;;;;;YAeZ,6BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC,mIAAA,CAAA,YAAS;gCAAC,OAAO;gCAAG,WAAU;0CAC5B,KAAK,IAAI;;;;;;0CAEZ,6LAAC;gCAAE,WAAU;0CACV,KAAK,SAAS;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC;gCAAE,WAAU;0CACV,KAAK,kBAAkB,CAAC,WAAW;;;;;;0CAItC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC;wCAAI,WAAU;kDACZ,KAAK,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACvC,6LAAC;gDAEC,WAAU;gDACV,OAAO;oDAAE,iBAAiB;gDAAM;gDAChC,OAAO;+CAHF;;;;;;;;;;;;;;;;0CAUb,6LAAC;gCAAI,WAAU;0CACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,6LAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYvB;KA3HgB;AA6HhB,WAAW;AACX,SAAS,qBAAqB,QAAgB;IAC5C,MAAM,SAAS;QACb,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,OAAO,MAAM,CAAC,SAAgC,IAAI;AACpD;AAEA,WAAW;AACX,SAAS,sBAAsB,QAAgB;IAC7C,MAAM,SAAS;QACb,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA,OAAO,MAAM,CAAC,SAAgC,IAAI;AACpD", "debugId": null}}, {"offset": {"line": 629, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/utils/cn';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'traditional';\n  size?: 'sm' | 'md' | 'lg';\n  loading?: boolean;\n  icon?: React.ReactNode;\n  iconPosition?: 'left' | 'right';\n  fullWidth?: boolean;\n}\n\nexport function Button({\n  children,\n  className,\n  variant = 'primary',\n  size = 'md',\n  loading = false,\n  icon,\n  iconPosition = 'left',\n  fullWidth = false,\n  disabled,\n  ...props\n}: ButtonProps) {\n  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n  \n  const variantClasses = {\n    primary: 'bg-palace-red text-white hover:bg-red-700 focus:ring-palace-red shadow-md hover:shadow-lg',\n    secondary: 'bg-golden-yellow text-ink-black hover:bg-yellow-500 focus:ring-golden-yellow shadow-md hover:shadow-lg',\n    outline: 'border-2 border-palace-red text-palace-red hover:bg-palace-red hover:text-white focus:ring-palace-red',\n    ghost: 'text-palace-red hover:bg-palace-red/10 focus:ring-palace-red',\n    traditional: 'bg-gradient-to-r from-palace-red to-cinnabar-red text-white border-2 border-golden-yellow hover:from-red-700 hover:to-red-600 focus:ring-golden-yellow shadow-lg hover:shadow-xl relative overflow-hidden'\n  };\n  \n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-sm rounded-md',\n    md: 'px-4 py-2 text-base rounded-lg',\n    lg: 'px-6 py-3 text-lg rounded-xl'\n  };\n  \n  const iconSizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-5 h-5',\n    lg: 'w-6 h-6'\n  };\n  \n  const classes = cn(\n    baseClasses,\n    variantClasses[variant],\n    sizeClasses[size],\n    fullWidth && 'w-full',\n    className\n  );\n  \n  return (\n    <button\n      className={classes}\n      disabled={disabled || loading}\n      {...props}\n    >\n      {variant === 'traditional' && (\n        <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-golden-yellow/20 to-transparent transform -skew-x-12 translate-x-full group-hover:translate-x-[-200%] transition-transform duration-1000\" />\n      )}\n      \n      {loading && (\n        <svg\n          className={cn('animate-spin mr-2', iconSizeClasses[size])}\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          />\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          />\n        </svg>\n      )}\n      \n      {!loading && icon && iconPosition === 'left' && (\n        <span className={cn('mr-2', iconSizeClasses[size])}>\n          {icon}\n        </span>\n      )}\n      \n      <span className=\"relative z-10\">{children}</span>\n      \n      {!loading && icon && iconPosition === 'right' && (\n        <span className={cn('ml-2', iconSizeClasses[size])}>\n          {icon}\n        </span>\n      )}\n    </button>\n  );\n}\n\n// 图标按钮组件\ninterface IconButtonProps extends Omit<ButtonProps, 'children'> {\n  icon: React.ReactNode;\n  'aria-label': string;\n}\n\nexport function IconButton({\n  icon,\n  className,\n  size = 'md',\n  variant = 'ghost',\n  ...props\n}: IconButtonProps) {\n  const sizeClasses = {\n    sm: 'p-1.5',\n    md: 'p-2',\n    lg: 'p-3'\n  };\n  \n  return (\n    <Button\n      className={cn('!px-0 !py-0', sizeClasses[size], className)}\n      variant={variant}\n      size={size}\n      {...props}\n    >\n      {icon}\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;;;AAWO,SAAS,OAAO,KAWT;QAXS,EACrB,QAAQ,EACR,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,IAAI,EACJ,eAAe,MAAM,EACrB,YAAY,KAAK,EACjB,QAAQ,EACR,GAAG,OACS,GAXS;IAYrB,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACf,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB,aAAa,UACb;IAGF,qBACE,6LAAC;QACC,WAAW;QACX,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,YAAY,+BACX,6LAAC;gBAAI,WAAU;;;;;;YAGhB,yBACC,6LAAC;gBACC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB,eAAe,CAAC,KAAK;gBACxD,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAKP,CAAC,WAAW,QAAQ,iBAAiB,wBACpC,6LAAC;gBAAK,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,eAAe,CAAC,KAAK;0BAC9C;;;;;;0BAIL,6LAAC;gBAAK,WAAU;0BAAiB;;;;;;YAEhC,CAAC,WAAW,QAAQ,iBAAiB,yBACpC,6LAAC;gBAAK,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,eAAe,CAAC,KAAK;0BAC9C;;;;;;;;;;;;AAKX;KAzFgB;AAiGT,SAAS,WAAW,KAMT;QANS,EACzB,IAAI,EACJ,SAAS,EACT,OAAO,IAAI,EACX,UAAU,OAAO,EACjB,GAAG,OACa,GANS;IAOzB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,eAAe,WAAW,CAAC,KAAK,EAAE;QAChD,SAAS;QACT,MAAM;QACL,GAAG,KAAK;kBAER;;;;;;AAGP;MAvBgB", "debugId": null}}, {"offset": {"line": 766, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/mask/MaskGrid.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { OperaMask, DisplayMode } from '@/types/mask';\nimport { MaskCard } from './MaskCard';\nimport { Grid } from '@/components/layout/Layout';\nimport { Button } from '@/components/ui/Button';\nimport { cn } from '@/utils/cn';\n\ninterface MaskGridProps {\n  masks: OperaMask[];\n  onMaskClick?: (mask: OperaMask) => void;\n  className?: string;\n  loading?: boolean;\n  emptyMessage?: string;\n  displayMode?: DisplayMode;\n  onDisplayModeChange?: (mode: DisplayMode) => void;\n}\n\nexport function MaskGrid({\n  masks,\n  onMaskClick,\n  className,\n  loading = false,\n  emptyMessage = '没有找到符合条件的脸谱',\n  displayMode = 'grid',\n  onDisplayModeChange\n}: MaskGridProps) {\n  const [sortBy, setSortBy] = useState<'name' | 'popularity' | 'difficulty'>('popularity');\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');\n  \n  // 排序脸谱\n  const sortedMasks = React.useMemo(() => {\n    return [...masks].sort((a, b) => {\n      let comparison = 0;\n      \n      switch (sortBy) {\n        case 'name':\n          comparison = a.name.localeCompare(b.name);\n          break;\n        case 'popularity':\n          comparison = a.popularity - b.popularity;\n          break;\n        case 'difficulty':\n          const difficultyOrder = { easy: 1, medium: 2, hard: 3 };\n          comparison = difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty];\n          break;\n      }\n      \n      return sortOrder === 'asc' ? comparison : -comparison;\n    });\n  }, [masks, sortBy, sortOrder]);\n  \n  const handleSortChange = (newSortBy: typeof sortBy) => {\n    if (sortBy === newSortBy) {\n      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortBy(newSortBy);\n      setSortOrder('desc');\n    }\n  };\n  \n  if (loading) {\n    return (\n      <div className={cn('space-y-6', className)}>\n        <MaskGridSkeleton />\n      </div>\n    );\n  }\n  \n  if (masks.length === 0) {\n    return (\n      <div className={cn('text-center py-12', className)}>\n        <div className=\"text-6xl mb-4\">🎭</div>\n        <h3 className=\"text-xl font-semibold text-gray-600 mb-2\">\n          {emptyMessage}\n        </h3>\n        <p className=\"text-gray-500\">\n          尝试调整筛选条件或搜索其他关键词\n        </p>\n      </div>\n    );\n  }\n  \n  return (\n    <div className={cn('space-y-6', className)}>\n      {/* 工具栏 */}\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-white p-4 rounded-lg border border-gray-200\">\n        <div className=\"flex items-center space-x-4\">\n          <span className=\"text-sm text-gray-600\">\n            共找到 <span className=\"font-semibold text-palace-red\">{masks.length}</span> 个脸谱\n          </span>\n        </div>\n        \n        <div className=\"flex items-center space-x-4\">\n          {/* 显示模式切换 */}\n          {onDisplayModeChange && (\n            <div className=\"flex items-center space-x-1 border border-gray-300 rounded-lg p-1\">\n              <Button\n                variant={displayMode === 'grid' ? 'primary' : 'ghost'}\n                size=\"sm\"\n                onClick={() => onDisplayModeChange('grid')}\n                className=\"!px-2 !py-1\"\n              >\n                <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\" />\n                </svg>\n              </Button>\n              <Button\n                variant={displayMode === 'list' ? 'primary' : 'ghost'}\n                size=\"sm\"\n                onClick={() => onDisplayModeChange('list')}\n                className=\"!px-2 !py-1\"\n              >\n                <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\" clipRule=\"evenodd\" />\n                </svg>\n              </Button>\n            </div>\n          )}\n          \n          {/* 排序选项 */}\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm text-gray-600\">排序:</span>\n            <Button\n              variant={sortBy === 'popularity' ? 'secondary' : 'ghost'}\n              size=\"sm\"\n              onClick={() => handleSortChange('popularity')}\n              className=\"text-sm\"\n            >\n              受欢迎度\n              {sortBy === 'popularity' && (\n                <span className=\"ml-1\">\n                  {sortOrder === 'desc' ? '↓' : '↑'}\n                </span>\n              )}\n            </Button>\n            <Button\n              variant={sortBy === 'name' ? 'secondary' : 'ghost'}\n              size=\"sm\"\n              onClick={() => handleSortChange('name')}\n              className=\"text-sm\"\n            >\n              名称\n              {sortBy === 'name' && (\n                <span className=\"ml-1\">\n                  {sortOrder === 'desc' ? '↓' : '↑'}\n                </span>\n              )}\n            </Button>\n            <Button\n              variant={sortBy === 'difficulty' ? 'secondary' : 'ghost'}\n              size=\"sm\"\n              onClick={() => handleSortChange('difficulty')}\n              className=\"text-sm\"\n            >\n              难度\n              {sortBy === 'difficulty' && (\n                <span className=\"ml-1\">\n                  {sortOrder === 'desc' ? '↓' : '↑'}\n                </span>\n              )}\n            </Button>\n          </div>\n        </div>\n      </div>\n      \n      {/* 脸谱网格 */}\n      {displayMode === 'grid' ? (\n        <Grid cols={4} gap=\"lg\" responsive>\n          {sortedMasks.map((mask) => (\n            <MaskCard\n              key={mask.id}\n              mask={mask}\n              onClick={onMaskClick}\n            />\n          ))}\n        </Grid>\n      ) : (\n        <div className=\"space-y-4\">\n          {sortedMasks.map((mask) => (\n            <MaskCard\n              key={mask.id}\n              mask={mask}\n              onClick={onMaskClick}\n              variant=\"compact\"\n              className=\"flex flex-row items-center\"\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n\n// 骨架屏组件\nfunction MaskGridSkeleton() {\n  return (\n    <Grid cols={4} gap=\"lg\" responsive>\n      {Array.from({ length: 8 }).map((_, i) => (\n        <div key={i} className=\"animate-pulse\">\n          <div className=\"bg-gray-200 aspect-square rounded-lg mb-4\" />\n          <div className=\"space-y-2\">\n            <div className=\"h-4 bg-gray-200 rounded w-3/4\" />\n            <div className=\"h-3 bg-gray-200 rounded w-1/2\" />\n            <div className=\"h-3 bg-gray-200 rounded w-full\" />\n          </div>\n        </div>\n      ))}\n    </Grid>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;;;AAPA;;;;;;AAmBO,SAAS,SAAS,KAQT;QARS,EACvB,KAAK,EACL,WAAW,EACX,SAAS,EACT,UAAU,KAAK,EACf,eAAe,aAAa,EAC5B,cAAc,MAAM,EACpB,mBAAmB,EACL,GARS;;IASvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwC;IAC3E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAE3D,OAAO;IACP,MAAM,cAAc,6JAAA,CAAA,UAAK,CAAC,OAAO;yCAAC;YAChC,OAAO;mBAAI;aAAM,CAAC,IAAI;iDAAC,CAAC,GAAG;oBACzB,IAAI,aAAa;oBAEjB,OAAQ;wBACN,KAAK;4BACH,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;4BACxC;wBACF,KAAK;4BACH,aAAa,EAAE,UAAU,GAAG,EAAE,UAAU;4BACxC;wBACF,KAAK;4BACH,MAAM,kBAAkB;gCAAE,MAAM;gCAAG,QAAQ;gCAAG,MAAM;4BAAE;4BACtD,aAAa,eAAe,CAAC,EAAE,UAAU,CAAC,GAAG,eAAe,CAAC,EAAE,UAAU,CAAC;4BAC1E;oBACJ;oBAEA,OAAO,cAAc,QAAQ,aAAa,CAAC;gBAC7C;;QACF;wCAAG;QAAC;QAAO;QAAQ;KAAU;IAE7B,MAAM,mBAAmB,CAAC;QACxB,IAAI,WAAW,WAAW;YACxB,aAAa,cAAc,QAAQ,SAAS;QAC9C,OAAO;YACL,UAAU;YACV,aAAa;QACf;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAC9B,cAAA,6LAAC;;;;;;;;;;IAGP;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB;;8BACtC,6LAAC;oBAAI,WAAU;8BAAgB;;;;;;8BAC/B,6LAAC;oBAAG,WAAU;8BACX;;;;;;8BAEH,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAKnC;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAE9B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;;gCAAwB;8CAClC,6LAAC;oCAAK,WAAU;8CAAiC,MAAM,MAAM;;;;;;gCAAQ;;;;;;;;;;;;kCAI7E,6LAAC;wBAAI,WAAU;;4BAEZ,qCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,gBAAgB,SAAS,YAAY;wCAC9C,MAAK;wCACL,SAAS,IAAM,oBAAoB;wCACnC,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAe,SAAQ;sDACnD,cAAA,6LAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;kDAGZ,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,gBAAgB,SAAS,YAAY;wCAC9C,MAAK;wCACL,SAAS,IAAM,oBAAoB;wCACnC,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAe,SAAQ;sDACnD,cAAA,6LAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAgJ,UAAS;;;;;;;;;;;;;;;;;;;;;;0CAO5L,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,WAAW,eAAe,cAAc;wCACjD,MAAK;wCACL,SAAS,IAAM,iBAAiB;wCAChC,WAAU;;4CACX;4CAEE,WAAW,8BACV,6LAAC;gDAAK,WAAU;0DACb,cAAc,SAAS,MAAM;;;;;;;;;;;;kDAIpC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,WAAW,SAAS,cAAc;wCAC3C,MAAK;wCACL,SAAS,IAAM,iBAAiB;wCAChC,WAAU;;4CACX;4CAEE,WAAW,wBACV,6LAAC;gDAAK,WAAU;0DACb,cAAc,SAAS,MAAM;;;;;;;;;;;;kDAIpC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,WAAW,eAAe,cAAc;wCACjD,MAAK;wCACL,SAAS,IAAM,iBAAiB;wCAChC,WAAU;;4CACX;4CAEE,WAAW,8BACV,6LAAC;gDAAK,WAAU;0DACb,cAAc,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASzC,gBAAgB,uBACf,6LAAC,yIAAA,CAAA,OAAI;gBAAC,MAAM;gBAAG,KAAI;gBAAK,UAAU;0BAC/B,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC,yIAAA,CAAA,WAAQ;wBAEP,MAAM;wBACN,SAAS;uBAFJ,KAAK,EAAE;;;;;;;;;qCAOlB,6LAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC,yIAAA,CAAA,WAAQ;wBAEP,MAAM;wBACN,SAAS;wBACT,SAAQ;wBACR,WAAU;uBAJL,KAAK,EAAE;;;;;;;;;;;;;;;;AAW1B;GA9KgB;KAAA;AAgLhB,QAAQ;AACR,SAAS;IACP,qBACE,6LAAC,yIAAA,CAAA,OAAI;QAAC,MAAM;QAAG,KAAI;QAAK,UAAU;kBAC/B,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;gBAAY,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;eALT;;;;;;;;;;AAWlB;MAfS", "debugId": null}}, {"offset": {"line": 1183, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/mask/MaskFilter.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { MaskFilter, RoleCategory, ColorCategory } from '@/types/mask';\nimport { Button } from '@/components/ui/Button';\nimport { cn } from '@/utils/cn';\n\ninterface MaskFilterProps {\n  filter: MaskFilter;\n  onFilterChange: (filter: MaskFilter) => void;\n  className?: string;\n}\n\nexport function MaskFilterComponent({ filter, onFilterChange, className }: MaskFilterProps) {\n  const roleCategories: RoleCategory[] = ['生', '旦', '净', '丑'];\n  const colorCategories: ColorCategory[] = ['红脸', '黑脸', '白脸', '蓝脸', '绿脸', '黄脸', '金脸', '银脸'];\n  const difficulties = ['easy', 'medium', 'hard'] as const;\n  \n  const handleRoleChange = (role: RoleCategory | undefined) => {\n    onFilterChange({\n      ...filter,\n      roleCategory: filter.roleCategory === role ? undefined : role\n    });\n  };\n  \n  const handleColorChange = (color: ColorCategory | undefined) => {\n    onFilterChange({\n      ...filter,\n      colorCategory: filter.colorCategory === color ? undefined : color\n    });\n  };\n  \n  const handleDifficultyChange = (difficulty: 'easy' | 'medium' | 'hard' | undefined) => {\n    onFilterChange({\n      ...filter,\n      difficulty: filter.difficulty === difficulty ? undefined : difficulty\n    });\n  };\n  \n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    onFilterChange({\n      ...filter,\n      searchTerm: e.target.value || undefined\n    });\n  };\n  \n  const clearFilters = () => {\n    onFilterChange({});\n  };\n  \n  const hasActiveFilters = filter.roleCategory || filter.colorCategory || filter.difficulty || filter.searchTerm;\n  \n  return (\n    <div className={cn('bg-white rounded-lg border-2 border-golden-yellow p-6 shadow-md', className)}>\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-ink-black font-serif\">筛选脸谱</h3>\n        {hasActiveFilters && (\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={clearFilters}\n            className=\"text-palace-red hover:text-red-700\"\n          >\n            清除筛选\n          </Button>\n        )}\n      </div>\n      \n      {/* 搜索框 */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          搜索脸谱\n        </label>\n        <input\n          type=\"text\"\n          placeholder=\"输入脸谱名称、角色或关键词...\"\n          value={filter.searchTerm || ''}\n          onChange={handleSearchChange}\n          className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-palace-red focus:border-transparent\"\n        />\n      </div>\n      \n      {/* 行当分类 */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n          按行当分类\n        </label>\n        <div className=\"flex flex-wrap gap-2\">\n          {roleCategories.map((role) => (\n            <Button\n              key={role}\n              variant={filter.roleCategory === role ? 'primary' : 'outline'}\n              size=\"sm\"\n              onClick={() => handleRoleChange(role)}\n              className=\"text-sm\"\n            >\n              {role}\n            </Button>\n          ))}\n        </div>\n      </div>\n      \n      {/* 颜色分类 */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n          按颜色分类\n        </label>\n        <div className=\"grid grid-cols-2 sm:grid-cols-4 gap-2\">\n          {colorCategories.map((color) => (\n            <Button\n              key={color}\n              variant={filter.colorCategory === color ? 'primary' : 'outline'}\n              size=\"sm\"\n              onClick={() => handleColorChange(color)}\n              className=\"text-sm\"\n            >\n              {color}\n            </Button>\n          ))}\n        </div>\n      </div>\n      \n      {/* 难度筛选 */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n          绘制难度\n        </label>\n        <div className=\"flex gap-2\">\n          {difficulties.map((difficulty) => (\n            <Button\n              key={difficulty}\n              variant={filter.difficulty === difficulty ? 'secondary' : 'outline'}\n              size=\"sm\"\n              onClick={() => handleDifficultyChange(difficulty)}\n              className=\"text-sm\"\n            >\n              {getDifficultyLabel(difficulty)}\n            </Button>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nfunction getDifficultyLabel(difficulty: 'easy' | 'medium' | 'hard'): string {\n  const labels = {\n    easy: '简单',\n    medium: '中等',\n    hard: '困难'\n  };\n  return labels[difficulty];\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AALA;;;;AAaO,SAAS,oBAAoB,KAAsD;QAAtD,EAAE,MAAM,EAAE,cAAc,EAAE,SAAS,EAAmB,GAAtD;IAClC,MAAM,iBAAiC;QAAC;QAAK;QAAK;QAAK;KAAI;IAC3D,MAAM,kBAAmC;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IACzF,MAAM,eAAe;QAAC;QAAQ;QAAU;KAAO;IAE/C,MAAM,mBAAmB,CAAC;QACxB,eAAe;YACb,GAAG,MAAM;YACT,cAAc,OAAO,YAAY,KAAK,OAAO,YAAY;QAC3D;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,eAAe;YACb,GAAG,MAAM;YACT,eAAe,OAAO,aAAa,KAAK,QAAQ,YAAY;QAC9D;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,eAAe;YACb,GAAG,MAAM;YACT,YAAY,OAAO,UAAU,KAAK,aAAa,YAAY;QAC7D;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,eAAe;YACb,GAAG,MAAM;YACT,YAAY,EAAE,MAAM,CAAC,KAAK,IAAI;QAChC;IACF;IAEA,MAAM,eAAe;QACnB,eAAe,CAAC;IAClB;IAEA,MAAM,mBAAmB,OAAO,YAAY,IAAI,OAAO,aAAa,IAAI,OAAO,UAAU,IAAI,OAAO,UAAU;IAE9G,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;;0BACpF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAkD;;;;;;oBAC/D,kCACC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBACC,MAAK;wBACL,aAAY;wBACZ,OAAO,OAAO,UAAU,IAAI;wBAC5B,UAAU;wBACV,WAAU;;;;;;;;;;;;0BAKd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC,qIAAA,CAAA,SAAM;gCAEL,SAAS,OAAO,YAAY,KAAK,OAAO,YAAY;gCACpD,MAAK;gCACL,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CAET;+BANI;;;;;;;;;;;;;;;;0BAab,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,sBACpB,6LAAC,qIAAA,CAAA,SAAM;gCAEL,SAAS,OAAO,aAAa,KAAK,QAAQ,YAAY;gCACtD,MAAK;gCACL,SAAS,IAAM,kBAAkB;gCACjC,WAAU;0CAET;+BANI;;;;;;;;;;;;;;;;0BAab,6LAAC;;kCACC,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,2BACjB,6LAAC,qIAAA,CAAA,SAAM;gCAEL,SAAS,OAAO,UAAU,KAAK,aAAa,cAAc;gCAC1D,MAAK;gCACL,SAAS,IAAM,uBAAuB;gCACtC,WAAU;0CAET,mBAAmB;+BANf;;;;;;;;;;;;;;;;;;;;;;AAanB;KAlIgB;AAoIhB,SAAS,mBAAmB,UAAsC;IAChE,MAAM,SAAS;QACb,MAAM;QACN,QAAQ;QACR,MAAM;IACR;IACA,OAAO,MAAM,CAAC,WAAW;AAC3B", "debugId": null}}, {"offset": {"line": 1434, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/components/ui/Modal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport { cn } from '@/utils/cn';\nimport { Button, IconButton } from './Button';\n\ninterface ModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  children: React.ReactNode;\n  title?: string;\n  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';\n  closeOnOverlayClick?: boolean;\n  showCloseButton?: boolean;\n  className?: string;\n}\n\nexport function Modal({\n  isOpen,\n  onClose,\n  children,\n  title,\n  size = 'md',\n  closeOnOverlayClick = true,\n  showCloseButton = true,\n  className\n}: ModalProps) {\n  // 处理ESC键关闭\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape' && isOpen) {\n        onClose();\n      }\n    };\n    \n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n    \n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n  \n  if (!isOpen) return null;\n  \n  const sizeClasses = {\n    sm: 'max-w-md',\n    md: 'max-w-lg',\n    lg: 'max-w-2xl',\n    xl: 'max-w-4xl',\n    full: 'max-w-[95vw] max-h-[95vh]'\n  };\n  \n  const handleOverlayClick = (e: React.MouseEvent) => {\n    if (e.target === e.currentTarget && closeOnOverlayClick) {\n      onClose();\n    }\n  };\n  \n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n      {/* 背景遮罩 */}\n      <div \n        className=\"absolute inset-0 bg-black/50 backdrop-blur-sm\"\n        onClick={handleOverlayClick}\n      />\n      \n      {/* 模态框内容 */}\n      <div className={cn(\n        'relative bg-white rounded-xl shadow-2xl border-2 border-golden-yellow max-h-[90vh] overflow-hidden',\n        sizeClasses[size],\n        className\n      )}>\n        {/* 装饰性边框 */}\n        <div className=\"absolute inset-0 bg-gradient-to-br from-golden-yellow/10 via-transparent to-palace-red/10 pointer-events-none\" />\n        \n        {/* 头部 */}\n        {(title || showCloseButton) && (\n          <div className=\"relative z-10 flex items-center justify-between p-6 border-b border-golden-yellow/30\">\n            {title && (\n              <h2 className=\"text-xl font-bold text-ink-black font-serif\">\n                {title}\n              </h2>\n            )}\n            {showCloseButton && (\n              <IconButton\n                icon={\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                }\n                onClick={onClose}\n                variant=\"ghost\"\n                aria-label=\"关闭\"\n              />\n            )}\n          </div>\n        )}\n        \n        {/* 内容区域 */}\n        <div className=\"relative z-10 overflow-y-auto max-h-[calc(90vh-8rem)]\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n}\n\ninterface ModalHeaderProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function ModalHeader({ children, className }: ModalHeaderProps) {\n  return (\n    <div className={cn('p-6 border-b border-golden-yellow/30', className)}>\n      {children}\n    </div>\n  );\n}\n\ninterface ModalBodyProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function ModalBody({ children, className }: ModalBodyProps) {\n  return (\n    <div className={cn('p-6', className)}>\n      {children}\n    </div>\n  );\n}\n\ninterface ModalFooterProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function ModalFooter({ children, className }: ModalFooterProps) {\n  return (\n    <div className={cn('p-6 border-t border-golden-yellow/30 flex justify-end space-x-3', className)}>\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAiBO,SAAS,MAAM,KAST;QATS,EACpB,MAAM,EACN,OAAO,EACP,QAAQ,EACR,KAAK,EACL,OAAO,IAAI,EACX,sBAAsB,IAAI,EAC1B,kBAAkB,IAAI,EACtB,SAAS,EACE,GATS;;IAUpB,WAAW;IACX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,MAAM;gDAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,YAAY,QAAQ;wBAChC;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,WAAW;gBACrC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;mCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;oBACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;0BAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,IAAI,qBAAqB;YACvD;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,6LAAC;gBAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACf,sGACA,WAAW,CAAC,KAAK,EACjB;;kCAGA,6LAAC;wBAAI,WAAU;;;;;;oBAGd,CAAC,SAAS,eAAe,mBACxB,6LAAC;wBAAI,WAAU;;4BACZ,uBACC,6LAAC;gCAAG,WAAU;0CACX;;;;;;4BAGJ,iCACC,6LAAC,qIAAA,CAAA,aAAU;gCACT,oBACE,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;gCAGzE,SAAS;gCACT,SAAQ;gCACR,cAAW;;;;;;;;;;;;kCAOnB,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAKX;GA5FgB;KAAA;AAmGT,SAAS,YAAY,KAAyC;QAAzC,EAAE,QAAQ,EAAE,SAAS,EAAoB,GAAzC;IAC1B,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;kBACxD;;;;;;AAGP;MANgB;AAaT,SAAS,UAAU,KAAuC;QAAvC,EAAE,QAAQ,EAAE,SAAS,EAAkB,GAAvC;IACxB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,OAAO;kBACvB;;;;;;AAGP;MANgB;AAaT,SAAS,YAAY,KAAyC;QAAzC,EAAE,QAAQ,EAAE,SAAS,EAAoB,GAAzC;IAC1B,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;kBACnF;;;;;;AAGP;MANgB", "debugId": null}}, {"offset": {"line": 1631, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/data/masks.ts"], "sourcesContent": ["import { OperaMask } from '@/types/mask';\n\nexport const operaMasks: OperaMask[] = [\n  {\n    id: 'guanyu',\n    name: '关羽脸谱',\n    character: '关羽',\n    roleCategory: '净',\n    colorCategory: '红脸',\n    mainColors: ['#DC143C', '#FFD700', '#000000'],\n    culturalBackground: {\n      origin: '三国时期蜀汉名将，被尊为武圣',\n      personality: '忠义勇武，刚正不阿，义薄云天',\n      symbolism: '忠诚、正义、勇敢的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '红色': '忠勇正义，赤胆忠心',\n      '金色': '神圣威严，地位崇高',\n      '黑色': '刚毅坚定，不可动摇'\n    },\n    relatedOperas: [\n      { name: '单刀会', description: '关羽单刀赴会的故事', period: '三国' },\n      { name: '华容道', description: '关羽义释曹操', period: '三国' },\n      { name: '走麦城', description: '关羽败走麦城', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=关羽',\n      fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=关羽脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹红色底色', duration: 1000, color: '#DC143C' },\n      { id: 2, name: '眉毛', description: '绘制浓眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1200, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘鼻梁线条', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加金色装饰', duration: 1000, color: '#FFD700' }\n    ],\n    difficulty: 'medium',\n    popularity: 10,\n    tags: ['三国', '武将', '忠义', '经典']\n  },\n  {\n    id: 'baogong',\n    name: '包拯脸谱',\n    character: '包拯',\n    roleCategory: '净',\n    colorCategory: '黑脸',\n    mainColors: ['#000000', '#FFFFFF', '#FFD700'],\n    culturalBackground: {\n      origin: '北宋名臣，以清廉公正著称',\n      personality: '铁面无私，执法如山，清正廉洁',\n      symbolism: '公正执法，清廉为民的象征',\n      historicalPeriod: '北宋时期'\n    },\n    colorMeaning: {\n      '黑色': '公正严明，铁面无私',\n      '白色': '清廉正直，一尘不染',\n      '金色': '威严庄重，地位尊崇'\n    },\n    relatedOperas: [\n      { name: '铡美案', description: '包拯铡驸马陈世美', period: '宋代' },\n      { name: '打龙袍', description: '包拯怒斥宋仁宗', period: '宋代' },\n      { name: '赤桑镇', description: '包拯审案的故事', period: '宋代' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/000000/FFFFFF?text=包拯',\n      fullSize: 'https://via.placeholder.com/600x600/000000/FFFFFF?text=包拯脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹黑色底色', duration: 1000, color: '#000000' },\n      { id: 2, name: '额头', description: '绘制白色月牙', duration: 800, color: '#FFFFFF' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1000, color: '#FFFFFF' },\n      { id: 4, name: '鼻翼', description: '描绘鼻翼线条', duration: 600, color: '#FFFFFF' },\n      { id: 5, name: '装饰', description: '添加金色细节', duration: 800, color: '#FFD700' }\n    ],\n    difficulty: 'easy',\n    popularity: 9,\n    tags: ['宋代', '清官', '正义', '经典']\n  },\n  {\n    id: 'caocao',\n    name: '曹操脸谱',\n    character: '曹操',\n    roleCategory: '净',\n    colorCategory: '白脸',\n    mainColors: ['#FFFFFF', '#000000', '#DC143C'],\n    culturalBackground: {\n      origin: '东汉末年政治家、军事家、文学家',\n      personality: '奸诈狡猾，野心勃勃，但才华横溢',\n      symbolism: '奸诈、权谋、复杂人性的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '白色': '奸诈狡猾，阴险毒辣',\n      '黑色': '深沉城府，心机深重',\n      '红色': '暴戾之气，杀伐果断'\n    },\n    relatedOperas: [\n      { name: '捉放曹', description: '陈宫捉放曹操', period: '三国' },\n      { name: '击鼓骂曹', description: '祢衡击鼓骂曹', period: '三国' },\n      { name: '群英会', description: '曹操与群雄斗智', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/FFFFFF/000000?text=曹操',\n      fullSize: 'https://via.placeholder.com/600x600/FFFFFF/000000?text=曹操脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹白色底色', duration: 1000, color: '#FFFFFF' },\n      { id: 2, name: '眉毛', description: '绘制黑色浓眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1200, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘鼻梁阴影', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加红色细节', duration: 1000, color: '#DC143C' }\n    ],\n    difficulty: 'medium',\n    popularity: 8,\n    tags: ['三国', '奸雄', '复杂', '经典']\n  },\n  {\n    id: 'zhangfei',\n    name: '张飞脸谱',\n    character: '张飞',\n    roleCategory: '净',\n    colorCategory: '黑脸',\n    mainColors: ['#000000', '#FFFFFF', '#DC143C'],\n    culturalBackground: {\n      origin: '三国时期蜀汉名将，刘备义弟',\n      personality: '勇猛粗犷，嫉恶如仇，忠义豪爽',\n      symbolism: '勇猛、正直、豪爽的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '黑色': '刚直勇猛，正气凛然',\n      '白色': '纯真豪爽，心无城府',\n      '红色': '热血沸腾，义气冲天'\n    },\n    relatedOperas: [\n      { name: '长坂坡', description: '张飞大战长坂坡', period: '三国' },\n      { name: '古城会', description: '张飞误会关羽', period: '三国' },\n      { name: '芦花荡', description: '张飞智取芦花荡', period: '三国' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/000000/FFFFFF?text=张飞',\n      fullSize: 'https://via.placeholder.com/600x600/000000/FFFFFF?text=张飞脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹黑色底色', duration: 1000, color: '#000000' },\n      { id: 2, name: '眉毛', description: '绘制白色粗眉', duration: 800, color: '#FFFFFF' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1000, color: '#FFFFFF' },\n      { id: 4, name: '鼻翼', description: '描绘鼻翼线条', duration: 600, color: '#FFFFFF' },\n      { id: 5, name: '装饰', description: '添加红色装饰', duration: 800, color: '#DC143C' }\n    ],\n    difficulty: 'easy',\n    popularity: 8,\n    tags: ['三国', '武将', '勇猛', '豪爽']\n  },\n  {\n    id: 'doulujin',\n    name: '窦尔敦脸谱',\n    character: '窦尔敦',\n    roleCategory: '净',\n    colorCategory: '蓝脸',\n    mainColors: ['#0066CC', '#FFFFFF', '#FFD700'],\n    culturalBackground: {\n      origin: '清代绿林好汉，盗侠传奇人物',\n      personality: '刚强勇猛，侠肝义胆，不畏强权',\n      symbolism: '刚强、勇敢、反抗精神的象征',\n      historicalPeriod: '清代'\n    },\n    colorMeaning: {\n      '蓝色': '刚强勇猛，桀骜不驯',\n      '白色': '正直豪爽，光明磊落',\n      '金色': '英雄气概，不凡身份'\n    },\n    relatedOperas: [\n      { name: '盗御马', description: '窦尔敦盗取御马', period: '清代' },\n      { name: '连环套', description: '窦尔敦中计被擒', period: '清代' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/0066CC/FFFFFF?text=窦尔敦',\n      fullSize: 'https://via.placeholder.com/600x600/0066CC/FFFFFF?text=窦尔敦脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹蓝色底色', duration: 1000, color: '#0066CC' },\n      { id: 2, name: '眉毛', description: '绘制白色眉毛', duration: 800, color: '#FFFFFF' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1000, color: '#FFFFFF' },\n      { id: 4, name: '鼻梁', description: '描绘鼻梁线条', duration: 600, color: '#FFFFFF' },\n      { id: 5, name: '装饰', description: '添加金色装饰', duration: 800, color: '#FFD700' }\n    ],\n    difficulty: 'medium',\n    popularity: 7,\n    tags: ['清代', '绿林', '侠客', '勇猛']\n  },\n  {\n    id: 'yangqilang',\n    name: '杨七郎脸谱',\n    character: '杨七郎',\n    roleCategory: '生',\n    colorCategory: '红脸',\n    mainColors: ['#DC143C', '#FFD700', '#000000'],\n    culturalBackground: {\n      origin: '北宋杨家将中的七子杨延嗣',\n      personality: '英勇善战，忠君爱国，血性男儿',\n      symbolism: '忠勇、牺牲、家国情怀的象征',\n      historicalPeriod: '北宋时期'\n    },\n    colorMeaning: {\n      '红色': '忠勇热血，为国捐躯',\n      '金色': '英雄本色，光耀门第',\n      '黑色': '刚毅果敢，义无反顾'\n    },\n    relatedOperas: [\n      { name: '杨家将', description: '杨家将抗辽的故事', period: '宋代' },\n      { name: '四郎探母', description: '杨四郎探望母亲', period: '宋代' },\n      { name: '穆桂英挂帅', description: '穆桂英率军出征', period: '宋代' }\n    ],\n    images: {\n      thumbnail: 'https://via.placeholder.com/300x300/DC143C/FFFFFF?text=杨七郎',\n      fullSize: 'https://via.placeholder.com/600x600/DC143C/FFFFFF?text=杨七郎脸谱'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹红色底色', duration: 1000, color: '#DC143C' },\n      { id: 2, name: '眉毛', description: '绘制黑色剑眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画眼部轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘鼻梁线条', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加金色装饰', duration: 800, color: '#FFD700' }\n    ],\n    difficulty: 'medium',\n    popularity: 7,\n    tags: ['宋代', '杨家将', '忠勇', '英雄']\n  },\n  {\n    id: 'yangguifei',\n    name: '杨贵妃脸谱',\n    character: '杨贵妃',\n    roleCategory: '旦',\n    colorCategory: '红脸',\n    mainColors: ['#FFB6C1', '#FFD700', '#DC143C'],\n    culturalBackground: {\n      origin: '唐代著名美女，唐玄宗宠妃',\n      personality: '美丽动人，聪慧机敏，但也任性娇纵',\n      symbolism: '美丽、爱情、悲剧的象征',\n      historicalPeriod: '唐代'\n    },\n    colorMeaning: {\n      '粉红色': '娇美动人，温柔如水',\n      '金色': '富贵荣华，地位尊贵',\n      '红色': '热情如火，爱情炽烈'\n    },\n    relatedOperas: [\n      { name: '贵妃醉酒', description: '杨贵妃醉酒的故事', period: '唐代' },\n      { name: '长生殿', description: '唐玄宗与杨贵妃的爱情', period: '唐代' },\n      { name: '马嵬坡', description: '杨贵妃马嵬坡之死', period: '唐代' }\n    ],\n    images: {\n      thumbnail: '/masks/yangguifei-thumb.jpg',\n      fullSize: '/masks/yangguifei-full.jpg'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹粉色底色', duration: 1000, color: '#FFB6C1' },\n      { id: 2, name: '眉毛', description: '绘制柳叶眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画凤眼轮廓', duration: 1200, color: '#000000' },\n      { id: 4, name: '唇部', description: '描绘樱桃小口', duration: 600, color: '#DC143C' },\n      { id: 5, name: '装饰', description: '添加金色花钿', duration: 1000, color: '#FFD700' }\n    ],\n    difficulty: 'hard',\n    popularity: 9,\n    tags: ['唐代', '美女', '爱情', '悲剧']\n  },\n  {\n    id: 'wusong',\n    name: '武松脸谱',\n    character: '武松',\n    roleCategory: '净',\n    colorCategory: '红脸',\n    mainColors: ['#DC143C', '#000000', '#FFD700'],\n    culturalBackground: {\n      origin: '水浒传中的英雄好汉，行者武松',\n      personality: '勇猛无畏，嫉恶如仇，义薄云天',\n      symbolism: '正义、勇敢、反抗精神的象征',\n      historicalPeriod: '北宋时期'\n    },\n    colorMeaning: {\n      '红色': '正义凛然，热血沸腾',\n      '黑色': '刚毅果敢，不屈不挠',\n      '金色': '英雄本色，光明磊落'\n    },\n    relatedOperas: [\n      { name: '武松打虎', description: '武松景阳冈打虎', period: '宋代' },\n      { name: '狮子楼', description: '武松杀西门庆', period: '宋代' },\n      { name: '十字坡', description: '武松遇孙二娘', period: '宋代' }\n    ],\n    images: {\n      thumbnail: '/masks/wusong-thumb.jpg',\n      fullSize: '/masks/wusong-full.jpg'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹红色底色', duration: 1000, color: '#DC143C' },\n      { id: 2, name: '眉毛', description: '绘制浓黑剑眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画虎目轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘挺直鼻梁', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加金色装饰', duration: 800, color: '#FFD700' }\n    ],\n    difficulty: 'medium',\n    popularity: 9,\n    tags: ['水浒', '英雄', '正义', '勇猛']\n  },\n  {\n    id: 'jianggan',\n    name: '蒋干脸谱',\n    character: '蒋干',\n    roleCategory: '丑',\n    colorCategory: '白脸',\n    mainColors: ['#FFFFFF', '#000000', '#808080'],\n    culturalBackground: {\n      origin: '三国时期人物，曹操谋士',\n      personality: '自作聪明，好事多磨，常弄巧成拙',\n      symbolism: '愚蠢、自负、滑稽的象征',\n      historicalPeriod: '三国时期'\n    },\n    colorMeaning: {\n      '白色': '愚蠢无知，自以为是',\n      '黑色': '心机不深，容易上当',\n      '灰色': '平庸无能，不值一提'\n    },\n    relatedOperas: [\n      { name: '群英会', description: '蒋干中计盗书', period: '三国' },\n      { name: '借东风', description: '诸葛亮借东风', period: '三国' }\n    ],\n    images: {\n      thumbnail: '/masks/jianggan-thumb.jpg',\n      fullSize: '/masks/jianggan-full.jpg'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹白色底色', duration: 1000, color: '#FFFFFF' },\n      { id: 2, name: '眉毛', description: '绘制细眉', duration: 600, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画小眼轮廓', duration: 800, color: '#000000' },\n      { id: 4, name: '鼻部', description: '描绘尖鼻', duration: 400, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加滑稽装饰', duration: 600, color: '#808080' }\n    ],\n    difficulty: 'easy',\n    popularity: 6,\n    tags: ['三国', '丑角', '滑稽', '愚蠢']\n  },\n  {\n    id: 'liubei',\n    name: '刘备脸谱',\n    character: '刘备',\n    roleCategory: '生',\n    colorCategory: '红脸',\n    mainColors: ['#DC143C', '#FFD700', '#000000'],\n    culturalBackground: {\n      origin: '三国时期蜀汉开国皇帝',\n      personality: '仁德宽厚，礼贤下士，志向远大',\n      symbolism: '仁德、理想、领袖风范的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '红色': '仁德之心，爱民如子',\n      '金色': '帝王之相，天命所归',\n      '黑色': '深沉稳重，胸怀大志'\n    },\n    relatedOperas: [\n      { name: '三顾茅庐', description: '刘备三顾茅庐请诸葛亮', period: '三国' },\n      { name: '甘露寺', description: '刘备招亲', period: '三国' },\n      { name: '白帝城', description: '刘备托孤', period: '三国' }\n    ],\n    images: {\n      thumbnail: '/masks/liubei-thumb.jpg',\n      fullSize: '/masks/liubei-full.jpg'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹红色底色', duration: 1000, color: '#DC143C' },\n      { id: 2, name: '眉毛', description: '绘制慈眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画慈目轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '胡须', description: '描绘长须', duration: 1200, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加金色装饰', duration: 800, color: '#FFD700' }\n    ],\n    difficulty: 'medium',\n    popularity: 8,\n    tags: ['三国', '帝王', '仁德', '领袖']\n  },\n  {\n    id: 'huangzhong',\n    name: '黄忠脸谱',\n    character: '黄忠',\n    roleCategory: '净',\n    colorCategory: '黄脸',\n    mainColors: ['#FFD700', '#000000', '#DC143C'],\n    culturalBackground: {\n      origin: '三国时期蜀汉五虎上将之一',\n      personality: '老当益壮，勇猛善射，忠心耿耿',\n      symbolism: '老骥伏枥、壮心不已的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '黄色': '老成持重，经验丰富',\n      '黑色': '刚毅坚定，不服老迈',\n      '红色': '壮心不已，热血依然'\n    },\n    relatedOperas: [\n      { name: '定军山', description: '黄忠定军山斩夏侯渊', period: '三国' },\n      { name: '战长沙', description: '黄忠战关羽', period: '三国' }\n    ],\n    images: {\n      thumbnail: '/masks/huangzhong-thumb.jpg',\n      fullSize: '/masks/huangzhong-full.jpg'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹黄色底色', duration: 1000, color: '#FFD700' },\n      { id: 2, name: '眉毛', description: '绘制白眉', duration: 800, color: '#FFFFFF' },\n      { id: 3, name: '眼部', description: '勾画老目轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '胡须', description: '描绘白须', duration: 1200, color: '#FFFFFF' },\n      { id: 5, name: '装饰', description: '添加红色装饰', duration: 800, color: '#DC143C' }\n    ],\n    difficulty: 'medium',\n    popularity: 7,\n    tags: ['三国', '老将', '勇猛', '忠诚']\n  },\n  {\n    id: 'machao',\n    name: '马超脸谱',\n    character: '马超',\n    roleCategory: '净',\n    colorCategory: '银脸',\n    mainColors: ['#C0C0C0', '#000000', '#DC143C'],\n    culturalBackground: {\n      origin: '三国时期蜀汉五虎上将之一，西凉马腾之子',\n      personality: '英勇善战，威风凛凛，有万夫不当之勇',\n      symbolism: '英勇、威武、西北豪杰的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '银色': '英武不凡，光芒四射',\n      '黑色': '刚毅果敢，威风凛凛',\n      '红色': '热血沸腾，勇猛无敌'\n    },\n    relatedOperas: [\n      { name: '战渭南', description: '马超大战曹操', period: '三国' },\n      { name: '取成都', description: '马超助刘备取成都', period: '三国' }\n    ],\n    images: {\n      thumbnail: '/masks/machao-thumb.jpg',\n      fullSize: '/masks/machao-full.jpg'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹银色底色', duration: 1000, color: '#C0C0C0' },\n      { id: 2, name: '眉毛', description: '绘制黑色剑眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画鹰目轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘挺直鼻梁', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加红色装饰', duration: 800, color: '#DC143C' }\n    ],\n    difficulty: 'hard',\n    popularity: 7,\n    tags: ['三国', '西凉', '英武', '威猛']\n  },\n  {\n    id: 'zhaoyun',\n    name: '赵云脸谱',\n    character: '赵云',\n    roleCategory: '生',\n    colorCategory: '白脸',\n    mainColors: ['#FFFFFF', '#000000', '#4169E1'],\n    culturalBackground: {\n      origin: '三国时期蜀汉五虎上将之一，常山赵子龙',\n      personality: '英勇善战，忠心耿耿，智勇双全',\n      symbolism: '忠诚、勇敢、完美武将的象征',\n      historicalPeriod: '东汉末年-三国时期'\n    },\n    colorMeaning: {\n      '白色': '纯洁忠诚，品格高尚',\n      '黑色': '刚毅果敢，意志坚定',\n      '蓝色': '冷静睿智，深谋远虑'\n    },\n    relatedOperas: [\n      { name: '长坂坡', description: '赵云长坂坡救阿斗', period: '三国' },\n      { name: '截江夺斗', description: '赵云截江救阿斗', period: '三国' }\n    ],\n    images: {\n      thumbnail: '/masks/zhaoyun-thumb.jpg',\n      fullSize: '/masks/zhaoyun-full.jpg'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹白色底色', duration: 1000, color: '#FFFFFF' },\n      { id: 2, name: '眉毛', description: '绘制黑色剑眉', duration: 800, color: '#000000' },\n      { id: 3, name: '眼部', description: '勾画英目轮廓', duration: 1000, color: '#000000' },\n      { id: 4, name: '鼻梁', description: '描绘挺直鼻梁', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加蓝色装饰', duration: 800, color: '#4169E1' }\n    ],\n    difficulty: 'medium',\n    popularity: 9,\n    tags: ['三国', '完美', '忠诚', '英勇']\n  },\n  {\n    id: 'sunwukong',\n    name: '孙悟空脸谱',\n    character: '孙悟空',\n    roleCategory: '净',\n    colorCategory: '金脸',\n    mainColors: ['#FFD700', '#DC143C', '#000000'],\n    culturalBackground: {\n      origin: '西游记中的齐天大圣，花果山美猴王',\n      personality: '机智勇敢，神通广大，桀骜不驯',\n      symbolism: '反抗精神、智慧勇敢的象征',\n      historicalPeriod: '神话传说'\n    },\n    colorMeaning: {\n      '金色': '神通广大，法力无边',\n      '红色': '火眼金睛，热情如火',\n      '黑色': '桀骜不驯，不畏权威'\n    },\n    relatedOperas: [\n      { name: '大闹天宫', description: '孙悟空大闹天宫', period: '神话' },\n      { name: '三打白骨精', description: '孙悟空三打白骨精', period: '神话' },\n      { name: '真假美猴王', description: '真假美猴王大战', period: '神话' }\n    ],\n    images: {\n      thumbnail: '/masks/sunwukong-thumb.jpg',\n      fullSize: '/masks/sunwukong-full.jpg'\n    },\n    drawingSteps: [\n      { id: 1, name: '底色', description: '涂抹金色底色', duration: 1000, color: '#FFD700' },\n      { id: 2, name: '眉毛', description: '绘制火焰眉', duration: 1000, color: '#DC143C' },\n      { id:3, name: '眼部', description: '勾画火眼金睛', duration: 1200, color: '#DC143C' },\n      { id: 4, name: '鼻部', description: '描绘猴鼻', duration: 600, color: '#000000' },\n      { id: 5, name: '装饰', description: '添加神话装饰', duration: 1000, color: '#000000' }\n    ],\n    difficulty: 'hard',\n    popularity: 10,\n    tags: ['西游记', '神话', '反抗', '智慧']\n  }\n];\n\n// 按分类组织的脸谱数据\nexport const masksByRole = {\n  '生': operaMasks.filter(mask => mask.roleCategory === '生'),\n  '旦': operaMasks.filter(mask => mask.roleCategory === '旦'),\n  '净': operaMasks.filter(mask => mask.roleCategory === '净'),\n  '丑': operaMasks.filter(mask => mask.roleCategory === '丑')\n};\n\nexport const masksByColor = {\n  '红脸': operaMasks.filter(mask => mask.colorCategory === '红脸'),\n  '黑脸': operaMasks.filter(mask => mask.colorCategory === '黑脸'),\n  '白脸': operaMasks.filter(mask => mask.colorCategory === '白脸'),\n  '蓝脸': operaMasks.filter(mask => mask.colorCategory === '蓝脸'),\n  '绿脸': operaMasks.filter(mask => mask.colorCategory === '绿脸'),\n  '黄脸': operaMasks.filter(mask => mask.colorCategory === '黄脸'),\n  '金脸': operaMasks.filter(mask => mask.colorCategory === '金脸'),\n  '银脸': operaMasks.filter(mask => mask.colorCategory === '银脸')\n};\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,aAA0B;IACrC;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAa,QAAQ;YAAK;YACtD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;SACpD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;SAC9E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAY,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;YACpD;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;SACrD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAQ,aAAa;gBAAU,QAAQ;YAAK;YACpD;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;SACrD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;SAC9E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;YACpD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;SACrD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;YACpD;gBAAE,MAAM;gBAAO,aAAa;gBAAW,QAAQ;YAAK;SACrD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAY,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAQ,aAAa;gBAAW,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAS,aAAa;gBAAW,QAAQ;YAAK;SACvD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAO;YAAM;SAAK;IACjC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,OAAO;YACP,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAQ,aAAa;gBAAY,QAAQ;YAAK;YACtD;gBAAE,MAAM;gBAAO,aAAa;gBAAc,QAAQ;YAAK;YACvD;gBAAE,MAAM;gBAAO,aAAa;gBAAY,QAAQ;YAAK;SACtD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAS,UAAU;gBAAK,OAAO;YAAU;YAC3E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;SAC9E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAQ,aAAa;gBAAW,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;SACpD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;SACpD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAQ,aAAa;gBAAc,QAAQ;YAAK;YACxD;gBAAE,MAAM;gBAAO,aAAa;gBAAQ,QAAQ;YAAK;YACjD;gBAAE,MAAM;gBAAO,aAAa;gBAAQ,QAAQ;YAAK;SAClD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAM,OAAO;YAAU;YAC3E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAa,QAAQ;YAAK;YACtD;gBAAE,MAAM;gBAAO,aAAa;gBAAS,QAAQ;YAAK;SACnD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAM,OAAO;YAAU;YAC3E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAU,QAAQ;YAAK;YACnD;gBAAE,MAAM;gBAAO,aAAa;gBAAY,QAAQ;YAAK;SACtD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAO,aAAa;gBAAY,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAQ,aAAa;gBAAW,QAAQ;YAAK;SACtD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAK,OAAO;YAAU;SAC7E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;YAAM;SAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,cAAc;QACd,eAAe;QACf,YAAY;YAAC;YAAW;YAAW;SAAU;QAC7C,oBAAoB;YAClB,QAAQ;YACR,aAAa;YACb,WAAW;YACX,kBAAkB;QACpB;QACA,cAAc;YACZ,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,eAAe;YACb;gBAAE,MAAM;gBAAQ,aAAa;gBAAW,QAAQ;YAAK;YACrD;gBAAE,MAAM;gBAAS,aAAa;gBAAY,QAAQ;YAAK;YACvD;gBAAE,MAAM;gBAAS,aAAa;gBAAW,QAAQ;YAAK;SACvD;QACD,QAAQ;YACN,WAAW;YACX,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAS,UAAU;gBAAM,OAAO;YAAU;YAC5E;gBAAE,IAAG;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;YAC5E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAQ,UAAU;gBAAK,OAAO;YAAU;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAM,aAAa;gBAAU,UAAU;gBAAM,OAAO;YAAU;SAC9E;QACD,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAO;YAAM;YAAM;SAAK;IACjC;CACD;AAGM,MAAM,cAAc;IACzB,KAAK,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,YAAY,KAAK;IACrD,KAAK,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,YAAY,KAAK;IACrD,KAAK,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,YAAY,KAAK;IACrD,KAAK,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,YAAY,KAAK;AACvD;AAEO,MAAM,eAAe;IAC1B,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;IACvD,MAAM,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,KAAK;AACzD", "debugId": null}}, {"offset": {"line": 2884, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/utils/maskUtils.ts"], "sourcesContent": ["import { OperaMask, MaskFilter, RoleCategory, ColorCategory } from '@/types/mask';\nimport { operaMasks } from '@/data/masks';\n\n/**\n * 根据筛选条件过滤脸谱\n */\nexport function filterMasks(masks: OperaMask[], filter: MaskFilter): OperaMask[] {\n  return masks.filter(mask => {\n    // 按行当筛选\n    if (filter.roleCategory && mask.roleCategory !== filter.roleCategory) {\n      return false;\n    }\n    \n    // 按颜色筛选\n    if (filter.colorCategory && mask.colorCategory !== filter.colorCategory) {\n      return false;\n    }\n    \n    // 按难度筛选\n    if (filter.difficulty && mask.difficulty !== filter.difficulty) {\n      return false;\n    }\n    \n    // 按搜索词筛选\n    if (filter.searchTerm) {\n      const searchTerm = filter.searchTerm.toLowerCase();\n      const searchableText = [\n        mask.name,\n        mask.character,\n        mask.culturalBackground.origin,\n        mask.culturalBackground.personality,\n        ...mask.tags\n      ].join(' ').toLowerCase();\n      \n      if (!searchableText.includes(searchTerm)) {\n        return false;\n      }\n    }\n    \n    return true;\n  });\n}\n\n/**\n * 按受欢迎程度排序脸谱\n */\nexport function sortMasksByPopularity(masks: OperaMask[], ascending = false): OperaMask[] {\n  return [...masks].sort((a, b) => {\n    return ascending ? a.popularity - b.popularity : b.popularity - a.popularity;\n  });\n}\n\n/**\n * 按名称排序脸谱\n */\nexport function sortMasksByName(masks: OperaMask[], ascending = true): OperaMask[] {\n  return [...masks].sort((a, b) => {\n    return ascending ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name);\n  });\n}\n\n/**\n * 获取所有行当分类\n */\nexport function getAllRoleCategories(): RoleCategory[] {\n  return ['生', '旦', '净', '丑'];\n}\n\n/**\n * 获取所有颜色分类\n */\nexport function getAllColorCategories(): ColorCategory[] {\n  return ['红脸', '黑脸', '白脸', '蓝脸', '绿脸', '黄脸', '金脸', '银脸'];\n}\n\n/**\n * 根据行当分组脸谱\n */\nexport function groupMasksByRole(masks: OperaMask[]): Record<RoleCategory, OperaMask[]> {\n  const groups: Record<RoleCategory, OperaMask[]> = {\n    '生': [],\n    '旦': [],\n    '净': [],\n    '丑': []\n  };\n  \n  masks.forEach(mask => {\n    groups[mask.roleCategory].push(mask);\n  });\n  \n  return groups;\n}\n\n/**\n * 根据颜色分组脸谱\n */\nexport function groupMasksByColor(masks: OperaMask[]): Record<ColorCategory, OperaMask[]> {\n  const groups: Record<ColorCategory, OperaMask[]> = {\n    '红脸': [],\n    '黑脸': [],\n    '白脸': [],\n    '蓝脸': [],\n    '绿脸': [],\n    '黄脸': [],\n    '金脸': [],\n    '银脸': []\n  };\n  \n  masks.forEach(mask => {\n    groups[mask.colorCategory].push(mask);\n  });\n  \n  return groups;\n}\n\n/**\n * 获取随机脸谱\n */\nexport function getRandomMasks(count: number, excludeIds: string[] = []): OperaMask[] {\n  const availableMasks = operaMasks.filter(mask => !excludeIds.includes(mask.id));\n  const shuffled = [...availableMasks].sort(() => Math.random() - 0.5);\n  return shuffled.slice(0, count);\n}\n\n/**\n * 根据ID获取脸谱\n */\nexport function getMaskById(id: string): OperaMask | undefined {\n  return operaMasks.find(mask => mask.id === id);\n}\n\n/**\n * 获取相关脸谱（基于标签相似度）\n */\nexport function getRelatedMasks(maskId: string, count: number = 3): OperaMask[] {\n  const currentMask = getMaskById(maskId);\n  if (!currentMask) return [];\n  \n  const otherMasks = operaMasks.filter(mask => mask.id !== maskId);\n  \n  // 计算标签相似度\n  const masksWithScore = otherMasks.map(mask => {\n    const commonTags = mask.tags.filter(tag => currentMask.tags.includes(tag));\n    const score = commonTags.length;\n    return { mask, score };\n  });\n  \n  // 按相似度排序并返回前几个\n  return masksWithScore\n    .sort((a, b) => b.score - a.score)\n    .slice(0, count)\n    .map(item => item.mask);\n}\n\n/**\n * 获取脸谱统计信息\n */\nexport function getMaskStatistics() {\n  const roleStats = groupMasksByRole(operaMasks);\n  const colorStats = groupMasksByColor(operaMasks);\n  \n  return {\n    total: operaMasks.length,\n    byRole: Object.entries(roleStats).map(([role, masks]) => ({\n      category: role,\n      count: masks.length\n    })),\n    byColor: Object.entries(colorStats).map(([color, masks]) => ({\n      category: color,\n      count: masks.length\n    })),\n    averagePopularity: operaMasks.reduce((sum, mask) => sum + mask.popularity, 0) / operaMasks.length,\n    difficultyDistribution: {\n      easy: operaMasks.filter(m => m.difficulty === 'easy').length,\n      medium: operaMasks.filter(m => m.difficulty === 'medium').length,\n      hard: operaMasks.filter(m => m.difficulty === 'hard').length\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AACA;;AAKO,SAAS,YAAY,KAAkB,EAAE,MAAkB;IAChE,OAAO,MAAM,MAAM,CAAC,CAAA;QAClB,QAAQ;QACR,IAAI,OAAO,YAAY,IAAI,KAAK,YAAY,KAAK,OAAO,YAAY,EAAE;YACpE,OAAO;QACT;QAEA,QAAQ;QACR,IAAI,OAAO,aAAa,IAAI,KAAK,aAAa,KAAK,OAAO,aAAa,EAAE;YACvE,OAAO;QACT;QAEA,QAAQ;QACR,IAAI,OAAO,UAAU,IAAI,KAAK,UAAU,KAAK,OAAO,UAAU,EAAE;YAC9D,OAAO;QACT;QAEA,SAAS;QACT,IAAI,OAAO,UAAU,EAAE;YACrB,MAAM,aAAa,OAAO,UAAU,CAAC,WAAW;YAChD,MAAM,iBAAiB;gBACrB,KAAK,IAAI;gBACT,KAAK,SAAS;gBACd,KAAK,kBAAkB,CAAC,MAAM;gBAC9B,KAAK,kBAAkB,CAAC,WAAW;mBAChC,KAAK,IAAI;aACb,CAAC,IAAI,CAAC,KAAK,WAAW;YAEvB,IAAI,CAAC,eAAe,QAAQ,CAAC,aAAa;gBACxC,OAAO;YACT;QACF;QAEA,OAAO;IACT;AACF;AAKO,SAAS,sBAAsB,KAAkB;QAAE,YAAA,iEAAY;IACpE,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,OAAO,YAAY,EAAE,UAAU,GAAG,EAAE,UAAU,GAAG,EAAE,UAAU,GAAG,EAAE,UAAU;IAC9E;AACF;AAKO,SAAS,gBAAgB,KAAkB;QAAE,YAAA,iEAAY;IAC9D,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,OAAO,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;IAC/E;AACF;AAKO,SAAS;IACd,OAAO;QAAC;QAAK;QAAK;QAAK;KAAI;AAC7B;AAKO,SAAS;IACd,OAAO;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;AACzD;AAKO,SAAS,iBAAiB,KAAkB;IACjD,MAAM,SAA4C;QAChD,KAAK,EAAE;QACP,KAAK,EAAE;QACP,KAAK,EAAE;QACP,KAAK,EAAE;IACT;IAEA,MAAM,OAAO,CAAC,CAAA;QACZ,MAAM,CAAC,KAAK,YAAY,CAAC,CAAC,IAAI,CAAC;IACjC;IAEA,OAAO;AACT;AAKO,SAAS,kBAAkB,KAAkB;IAClD,MAAM,SAA6C;QACjD,MAAM,EAAE;QACR,MAAM,EAAE;QACR,MAAM,EAAE;QACR,MAAM,EAAE;QACR,MAAM,EAAE;QACR,MAAM,EAAE;QACR,MAAM,EAAE;QACR,MAAM,EAAE;IACV;IAEA,MAAM,OAAO,CAAC,CAAA;QACZ,MAAM,CAAC,KAAK,aAAa,CAAC,CAAC,IAAI,CAAC;IAClC;IAEA,OAAO;AACT;AAKO,SAAS,eAAe,KAAa;QAAE,aAAA,iEAAuB,EAAE;IACrE,MAAM,iBAAiB,uHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAA,OAAQ,CAAC,WAAW,QAAQ,CAAC,KAAK,EAAE;IAC7E,MAAM,WAAW;WAAI;KAAe,CAAC,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK;IAChE,OAAO,SAAS,KAAK,CAAC,GAAG;AAC3B;AAKO,SAAS,YAAY,EAAU;IACpC,OAAO,uHAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;AAC7C;AAKO,SAAS,gBAAgB,MAAc;QAAE,QAAA,iEAAgB;IAC9D,MAAM,cAAc,YAAY;IAChC,IAAI,CAAC,aAAa,OAAO,EAAE;IAE3B,MAAM,aAAa,uHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAEzD,UAAU;IACV,MAAM,iBAAiB,WAAW,GAAG,CAAC,CAAA;QACpC,MAAM,aAAa,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,YAAY,IAAI,CAAC,QAAQ,CAAC;QACrE,MAAM,QAAQ,WAAW,MAAM;QAC/B,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,eAAe;IACf,OAAO,eACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG,OACT,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;AAC1B;AAKO,SAAS;IACd,MAAM,YAAY,iBAAiB,uHAAA,CAAA,aAAU;IAC7C,MAAM,aAAa,kBAAkB,uHAAA,CAAA,aAAU;IAE/C,OAAO;QACL,OAAO,uHAAA,CAAA,aAAU,CAAC,MAAM;QACxB,QAAQ,OAAO,OAAO,CAAC,WAAW,GAAG,CAAC;gBAAC,CAAC,MAAM,MAAM;mBAAM;gBACxD,UAAU;gBACV,OAAO,MAAM,MAAM;YACrB;;QACA,SAAS,OAAO,OAAO,CAAC,YAAY,GAAG,CAAC;gBAAC,CAAC,OAAO,MAAM;mBAAM;gBAC3D,UAAU;gBACV,OAAO,MAAM,MAAM;YACrB;;QACA,mBAAmB,uHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE,KAAK,uHAAA,CAAA,aAAU,CAAC,MAAM;QACjG,wBAAwB;YACtB,MAAM,uHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,QAAQ,MAAM;YAC5D,QAAQ,uHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,UAAU,MAAM;YAChE,MAAM,uHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,QAAQ,MAAM;QAC9D;IACF;AACF", "debugId": null}}, {"offset": {"line": 3058, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/2-AIprogram/3-Augment/1-test1/beijing-opera-masks/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useMemo } from 'react';\nimport { Layout, Header, Nav, Main, Section, Container } from '@/components/layout/Layout';\nimport { MaskGrid } from '@/components/mask/MaskGrid';\nimport { MaskFilterComponent } from '@/components/mask/MaskFilter';\nimport { Button } from '@/components/ui/Button';\nimport { Modal, ModalBody } from '@/components/ui/Modal';\nimport { operaMasks } from '@/data/masks';\nimport { filterMasks } from '@/utils/maskUtils';\nimport { MaskFilter, OperaMask, DisplayMode } from '@/types/mask';\n\nexport default function Home() {\n  const [filter, setFilter] = useState<MaskFilter>({});\n  const [displayMode, setDisplayMode] = useState<DisplayMode>('grid');\n  const [selectedMask, setSelectedMask] = useState<OperaMask | null>(null);\n  const [showFilter, setShowFilter] = useState(false);\n\n  // 过滤脸谱\n  const filteredMasks = useMemo(() => {\n    return filterMasks(operaMasks, filter);\n  }, [filter]);\n\n  const handleMaskClick = (mask: OperaMask) => {\n    setSelectedMask(mask);\n  };\n\n  const handleCloseModal = () => {\n    setSelectedMask(null);\n  };\n\n  return (\n    <Layout>\n      <Header>\n        <Nav>\n          <div className=\"flex items-center space-x-4\">\n            <h1 className=\"text-2xl font-bold text-ink-black font-serif\">\n              京剧脸谱文化展示平台\n            </h1>\n          </div>\n          <div className=\"flex items-center space-x-4\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => setShowFilter(!showFilter)}\n              className=\"lg:hidden\"\n            >\n              筛选\n            </Button>\n          </div>\n        </Nav>\n      </Header>\n\n      <Main>\n        {/* 英雄区域 */}\n        <Section\n          title=\"探索京剧脸谱的艺术魅力\"\n          subtitle=\"深入了解中国传统戏曲文化，感受脸谱艺术的独特魅力与深厚内涵\"\n          background=\"gradient\"\n          className=\"text-center\"\n        >\n          <div className=\"flex justify-center space-x-4\">\n            <Button variant=\"traditional\" size=\"lg\">\n              开始探索\n            </Button>\n            <Button variant=\"outline\" size=\"lg\">\n              了解更多\n            </Button>\n          </div>\n        </Section>\n\n        <Container>\n          <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n            {/* 筛选侧边栏 */}\n            <div className={`lg:col-span-1 ${showFilter ? 'block' : 'hidden lg:block'}`}>\n              <div className=\"sticky top-24\">\n                <MaskFilterComponent\n                  filter={filter}\n                  onFilterChange={setFilter}\n                />\n              </div>\n            </div>\n\n            {/* 主要内容区域 */}\n            <div className=\"lg:col-span-3\">\n              <MaskGrid\n                masks={filteredMasks}\n                onMaskClick={handleMaskClick}\n                displayMode={displayMode}\n                onDisplayModeChange={setDisplayMode}\n              />\n            </div>\n          </div>\n        </Container>\n      </Main>\n\n      {/* 脸谱详情模态框 */}\n      {selectedMask && (\n        <Modal\n          isOpen={!!selectedMask}\n          onClose={handleCloseModal}\n          title={selectedMask.name}\n          size=\"xl\"\n        >\n          <ModalBody>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* 脸谱图片 */}\n              <div className=\"space-y-4\">\n                <div className=\"aspect-square bg-gray-100 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-gray-500\">脸谱图片占位</span>\n                </div>\n                <div className=\"flex space-x-2\">\n                  {selectedMask.mainColors.map((color, index) => (\n                    <div\n                      key={index}\n                      className=\"w-8 h-8 rounded-full border-2 border-gray-300\"\n                      style={{ backgroundColor: color }}\n                      title={color}\n                    />\n                  ))}\n                </div>\n              </div>\n\n              {/* 脸谱信息 */}\n              <div className=\"space-y-6\">\n                <div>\n                  <h3 className=\"text-lg font-semibold text-ink-black mb-2\">基本信息</h3>\n                  <div className=\"space-y-2 text-sm\">\n                    <p><span className=\"font-medium\">角色:</span> {selectedMask.character}</p>\n                    <p><span className=\"font-medium\">行当:</span> {selectedMask.roleCategory}</p>\n                    <p><span className=\"font-medium\">颜色分类:</span> {selectedMask.colorCategory}</p>\n                    <p><span className=\"font-medium\">绘制难度:</span> {selectedMask.difficulty}</p>\n                  </div>\n                </div>\n\n                <div>\n                  <h3 className=\"text-lg font-semibold text-ink-black mb-2\">文化背景</h3>\n                  <div className=\"space-y-2 text-sm text-gray-600\">\n                    <p><span className=\"font-medium\">历史起源:</span> {selectedMask.culturalBackground.origin}</p>\n                    <p><span className=\"font-medium\">性格特点:</span> {selectedMask.culturalBackground.personality}</p>\n                    <p><span className=\"font-medium\">象征意义:</span> {selectedMask.culturalBackground.symbolism}</p>\n                  </div>\n                </div>\n\n                <div>\n                  <h3 className=\"text-lg font-semibold text-ink-black mb-2\">色彩寓意</h3>\n                  <div className=\"space-y-1 text-sm text-gray-600\">\n                    {Object.entries(selectedMask.colorMeaning).map(([color, meaning]) => (\n                      <p key={color}><span className=\"font-medium\">{color}:</span> {meaning}</p>\n                    ))}\n                  </div>\n                </div>\n\n                <div>\n                  <h3 className=\"text-lg font-semibold text-ink-black mb-2\">相关剧目</h3>\n                  <div className=\"space-y-2\">\n                    {selectedMask.relatedOperas.map((opera, index) => (\n                      <div key={index} className=\"text-sm\">\n                        <p className=\"font-medium\">{opera.name}</p>\n                        <p className=\"text-gray-600\">{opera.description}</p>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                <div className=\"flex flex-wrap gap-2\">\n                  {selectedMask.tags.map((tag, index) => (\n                    <span\n                      key={index}\n                      className=\"px-3 py-1 text-xs bg-golden-yellow/20 text-golden-yellow border border-golden-yellow rounded-full\"\n                    >\n                      {tag}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </ModalBody>\n        </Modal>\n      )}\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACnE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,OAAO;IACP,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;uCAAE;YAC5B,OAAO,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD,EAAE,uHAAA,CAAA,aAAU,EAAE;QACjC;sCAAG;QAAC;KAAO;IAEX,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;IAClB;IAEA,qBACE,6LAAC,yIAAA,CAAA,SAAM;;0BACL,6LAAC,yIAAA,CAAA,SAAM;0BACL,cAAA,6LAAC,yIAAA,CAAA,MAAG;;sCACF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;0CAA+C;;;;;;;;;;;sCAI/D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAOP,6LAAC,yIAAA,CAAA,OAAI;;kCAEH,6LAAC,yIAAA,CAAA,UAAO;wBACN,OAAM;wBACN,UAAS;wBACT,YAAW;wBACX,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAc,MAAK;8CAAK;;;;;;8CAGxC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;;;;;;;;;;;;kCAMxC,6LAAC,yIAAA,CAAA,YAAS;kCACR,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAW,AAAC,iBAAyD,OAAzC,aAAa,UAAU;8CACtD,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,2IAAA,CAAA,sBAAmB;4CAClB,QAAQ;4CACR,gBAAgB;;;;;;;;;;;;;;;;8CAMtB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yIAAA,CAAA,WAAQ;wCACP,OAAO;wCACP,aAAa;wCACb,aAAa;wCACb,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ9B,8BACC,6LAAC,oIAAA,CAAA,QAAK;gBACJ,QAAQ,CAAC,CAAC;gBACV,SAAS;gBACT,OAAO,aAAa,IAAI;gBACxB,MAAK;0BAEL,cAAA,6LAAC,oIAAA,CAAA,YAAS;8BACR,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;kDAElC,6LAAC;wCAAI,WAAU;kDACZ,aAAa,UAAU,CAAC,GAAG,CAAC,CAAC,OAAO,sBACnC,6LAAC;gDAEC,WAAU;gDACV,OAAO;oDAAE,iBAAiB;gDAAM;gDAChC,OAAO;+CAHF;;;;;;;;;;;;;;;;0CAUb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA4C;;;;;;0DAC1D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EAAE,6LAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAU;4DAAE,aAAa,SAAS;;;;;;;kEACnE,6LAAC;;0EAAE,6LAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAU;4DAAE,aAAa,YAAY;;;;;;;kEACtE,6LAAC;;0EAAE,6LAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAY;4DAAE,aAAa,aAAa;;;;;;;kEACzE,6LAAC;;0EAAE,6LAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAY;4DAAE,aAAa,UAAU;;;;;;;;;;;;;;;;;;;kDAI1E,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA4C;;;;;;0DAC1D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EAAE,6LAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAY;4DAAE,aAAa,kBAAkB,CAAC,MAAM;;;;;;;kEACrF,6LAAC;;0EAAE,6LAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAY;4DAAE,aAAa,kBAAkB,CAAC,WAAW;;;;;;;kEAC1F,6LAAC;;0EAAE,6LAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAY;4DAAE,aAAa,kBAAkB,CAAC,SAAS;;;;;;;;;;;;;;;;;;;kDAI5F,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA4C;;;;;;0DAC1D,6LAAC;gDAAI,WAAU;0DACZ,OAAO,OAAO,CAAC,aAAa,YAAY,EAAE,GAAG,CAAC;wDAAC,CAAC,OAAO,QAAQ;yEAC9D,6LAAC;;0EAAc,6LAAC;gEAAK,WAAU;;oEAAe;oEAAM;;;;;;;4DAAQ;4DAAE;;uDAAtD;;;;;;;;;;;;;;;;;kDAKd,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA4C;;;;;;0DAC1D,6LAAC;gDAAI,WAAU;0DACZ,aAAa,aAAa,CAAC,GAAG,CAAC,CAAC,OAAO,sBACtC,6LAAC;wDAAgB,WAAU;;0EACzB,6LAAC;gEAAE,WAAU;0EAAe,MAAM,IAAI;;;;;;0EACtC,6LAAC;gEAAE,WAAU;0EAAiB,MAAM,WAAW;;;;;;;uDAFvC;;;;;;;;;;;;;;;;kDAQhB,6LAAC;wCAAI,WAAU;kDACZ,aAAa,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC3B,6LAAC;gDAEC,WAAU;0DAET;+CAHI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAc3B;GA1KwB;KAAA", "debugId": null}}]}