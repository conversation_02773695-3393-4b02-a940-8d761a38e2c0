[{"name": "generate-buildid", "duration": 163, "timestamp": 87969160716, "id": 4, "parentId": 1, "tags": {}, "startTime": 1753146836314, "traceId": "bb784680c364cc63"}, {"name": "load-custom-routes", "duration": 1590, "timestamp": 87969160950, "id": 5, "parentId": 1, "tags": {}, "startTime": 1753146836314, "traceId": "bb784680c364cc63"}, {"name": "create-dist-dir", "duration": 392, "timestamp": 87969227453, "id": 6, "parentId": 1, "tags": {}, "startTime": 1753146836381, "traceId": "bb784680c364cc63"}, {"name": "create-pages-mapping", "duration": 224, "timestamp": 87969452753, "id": 7, "parentId": 1, "tags": {}, "startTime": 1753146836606, "traceId": "bb784680c364cc63"}, {"name": "collect-app-paths", "duration": 1901, "timestamp": 87969453015, "id": 8, "parentId": 1, "tags": {}, "startTime": 1753146836606, "traceId": "bb784680c364cc63"}, {"name": "create-app-mapping", "duration": 4384, "timestamp": 87969454946, "id": 9, "parentId": 1, "tags": {}, "startTime": 1753146836608, "traceId": "bb784680c364cc63"}, {"name": "public-dir-conflict-check", "duration": 745, "timestamp": 87969460007, "id": 10, "parentId": 1, "tags": {}, "startTime": 1753146836613, "traceId": "bb784680c364cc63"}, {"name": "generate-routes-manifest", "duration": 2767, "timestamp": 87969460979, "id": 11, "parentId": 1, "tags": {}, "startTime": 1753146836614, "traceId": "bb784680c364cc63"}, {"name": "create-entrypoints", "duration": 27922, "timestamp": 87969480991, "id": 14, "parentId": 1, "tags": {}, "startTime": 1753146836634, "traceId": "bb784680c364cc63"}, {"name": "generate-webpack-config", "duration": 434101, "timestamp": 87969508977, "id": 15, "parentId": 13, "tags": {}, "startTime": 1753146836662, "traceId": "bb784680c364cc63"}, {"name": "next-trace-entrypoint-plugin", "duration": 1718, "timestamp": 87970076866, "id": 17, "parentId": 16, "tags": {}, "startTime": 1753146837230, "traceId": "bb784680c364cc63"}, {"name": "add-entry", "duration": 343966, "timestamp": 87970084129, "id": 20, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Ffavicon.ico%2Froute&name=app%2Ffavicon.ico%2Froute&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=E%3A%5C2-AIprogram%5C3-Augment%5C1-test1%5Cbeijing-opera-masks%5Csrc%5Capp&appPaths=%2Ffavicon.ico&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!"}, "startTime": 1753146837237, "traceId": "bb784680c364cc63"}, {"name": "add-entry", "duration": 384245, "timestamp": 87970084184, "id": 23, "parentId": 18, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1753146837237, "traceId": "bb784680c364cc63"}, {"name": "add-entry", "duration": 384206, "timestamp": 87970084242, "id": 27, "parentId": 18, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1753146837238, "traceId": "bb784680c364cc63"}, {"name": "add-entry", "duration": 384265, "timestamp": 87970084194, "id": 24, "parentId": 18, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1753146837237, "traceId": "bb784680c364cc63"}, {"name": "add-entry", "duration": 387600, "timestamp": 87970084162, "id": 21, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Frobots.txt%2Froute&name=app%2Frobots.txt%2Froute&pagePath=private-next-app-dir%2Frobots.ts&appDir=E%3A%5C2-AIprogram%5C3-Augment%5C1-test1%5Cbeijing-opera-masks%5Csrc%5Capp&appPaths=%2Frobots&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!"}, "startTime": 1753146837237, "traceId": "bb784680c364cc63"}, {"name": "build-module-tsx", "duration": 19573, "timestamp": 87970466595, "id": 29, "parentId": 16, "tags": {"name": "E:\\2-AI<PERSON><PERSON>ram\\3-Augment\\1-test1\\beijing-opera-masks\\src\\app\\page.tsx", "layer": "rsc"}, "startTime": 1753146837620, "traceId": "bb784680c364cc63"}, {"name": "build-module-tsx", "duration": 27896, "timestamp": 87970464276, "id": 28, "parentId": 16, "tags": {"name": "E:\\2-AI<PERSON><PERSON>ram\\3-Augment\\1-test1\\beijing-opera-masks\\src\\app\\layout.tsx", "layer": "rsc"}, "startTime": 1753146837618, "traceId": "bb784680c364cc63"}, {"name": "add-entry", "duration": 427933, "timestamp": 87970084174, "id": 22, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fsitemap.xml%2Froute&name=app%2Fsitemap.xml%2Froute&pagePath=private-next-app-dir%2Fsitemap.ts&appDir=E%3A%5C2-AIprogram%5C3-Augment%5C1-test1%5Cbeijing-opera-masks%5Csrc%5Capp&appPaths=%2Fsitemap&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!"}, "startTime": 1753146837237, "traceId": "bb784680c364cc63"}, {"name": "build-module-tsx", "duration": 12508, "timestamp": 87970517435, "id": 30, "parentId": 16, "tags": {"name": "E:\\2-AI<PERSON><PERSON>ram\\3-Augment\\1-test1\\beijing-opera-masks\\src\\app\\mask\\[id]\\page.tsx", "layer": "rsc"}, "startTime": 1753146837671, "traceId": "bb784680c364cc63"}, {"name": "font-loader", "duration": 8829235, "timestamp": 87970587418, "id": 37, "parentId": 36, "tags": {}, "startTime": 1753146837741, "traceId": "bb784680c364cc63"}, {"name": "postcss", "duration": 40445, "timestamp": 87979891164, "id": 40, "parentId": 36, "tags": {}, "startTime": 1753146847044, "traceId": "bb784680c364cc63"}, {"name": "next-font-loader", "duration": 9344751, "timestamp": 87970587381, "id": 36, "parentId": 32, "tags": {}, "startTime": 1753146837741, "traceId": "bb784680c364cc63"}, {"name": "css-loader", "duration": 56091, "timestamp": 87979931807, "id": 41, "parentId": 32, "tags": {"astUsed": "true"}, "startTime": 1753146847085, "traceId": "bb784680c364cc63"}, {"name": "build-module", "duration": 9461646, "timestamp": 87970528363, "id": 32, "parentId": 28, "tags": {"name": "E:\\2-AIprogram\\3-Augment\\1-test1\\beijing-opera-masks\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Noto_Sans_SC\",\"arguments\":[{\"variable\":\"--font-noto-sans-sc\",\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"600\",\"700\"],\"display\":\"swap\"}],\"variableName\":\"notoSansSC\"}", "layer": "rsc"}, "startTime": 1753146837682, "traceId": "bb784680c364cc63"}, {"name": "font-loader", "duration": 9450567, "timestamp": 87970583289, "id": 35, "parentId": 34, "tags": {}, "startTime": 1753146837737, "traceId": "bb784680c364cc63"}, {"name": "postcss", "duration": 33255, "timestamp": 87980034840, "id": 42, "parentId": 34, "tags": {}, "startTime": 1753146847188, "traceId": "bb784680c364cc63"}, {"name": "next-font-loader", "duration": 9537793, "timestamp": 87970530405, "id": 34, "parentId": 31, "tags": {}, "startTime": 1753146837684, "traceId": "bb784680c364cc63"}, {"name": "css-loader", "duration": 19258, "timestamp": 87980068155, "id": 43, "parentId": 31, "tags": {"astUsed": "true"}, "startTime": 1753146847221, "traceId": "bb784680c364cc63"}, {"name": "build-module", "duration": 9569601, "timestamp": 87970519156, "id": 31, "parentId": 28, "tags": {"name": "E:\\2-AIprogram\\3-Augment\\1-test1\\beijing-opera-masks\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Noto_Serif_SC\",\"arguments\":[{\"variable\":\"--font-noto-serif-sc\",\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"600\",\"700\",\"900\"],\"display\":\"swap\"}],\"variableName\":\"notoSerifSC\"}", "layer": "rsc"}, "startTime": 1753146837672, "traceId": "bb784680c364cc63"}, {"name": "font-loader", "duration": 17330475, "timestamp": 87970588387, "id": 39, "parentId": 38, "tags": {}, "startTime": 1753146837742, "traceId": "bb784680c364cc63"}, {"name": "postcss", "duration": 11287, "timestamp": 87987919087, "id": 44, "parentId": 38, "tags": {}, "startTime": 1753146855072, "traceId": "bb784680c364cc63"}, {"name": "next-font-loader", "duration": 17342167, "timestamp": 87970588355, "id": 38, "parentId": 33, "tags": {}, "startTime": 1753146837742, "traceId": "bb784680c364cc63"}, {"name": "css-loader", "duration": 2294, "timestamp": 87987930498, "id": 45, "parentId": 33, "tags": {"astUsed": "true"}, "startTime": 1753146855084, "traceId": "bb784680c364cc63"}, {"name": "build-module", "duration": 17405226, "timestamp": 87970528575, "id": 33, "parentId": 28, "tags": {"name": "E:\\2-AIprogram\\3-Augment\\1-test1\\beijing-opera-masks\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"<PERSON><PERSON><PERSON>_Zheng\",\"arguments\":[{\"variable\":\"--font-ma-shan-zheng\",\"subsets\":[\"latin\"],\"weight\":[\"400\"],\"display\":\"swap\"}],\"variableName\":\"maShangZheng\"}", "layer": "rsc"}, "startTime": 1753146837682, "traceId": "bb784680c364cc63"}, {"name": "add-entry", "duration": 17850315, "timestamp": 87970083649, "id": 19, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=E%3A%5C2-AIprogram%5C3-Augment%5C1-test1%5Cbeijing-opera-masks%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cbuiltin%5Cglobal-not-found.js&appDir=E%3A%5C2-AIprogram%5C3-Augment%5C1-test1%5Cbeijing-opera-masks%5Csrc%5Capp&appPaths=E%3A%2F2-AIprogram%2F3-Augment%2F1-test1%2Fbeijing-opera-masks%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-not-found&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!"}, "startTime": 1753146837237, "traceId": "bb784680c364cc63"}, {"name": "add-entry", "duration": 17849764, "timestamp": 87970084207, "id": 25, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5C2-AIprogram%5C3-Augment%5C1-test1%5Cbeijing-opera-masks%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!"}, "startTime": 1753146837237, "traceId": "bb784680c364cc63"}, {"name": "add-entry", "duration": 17849751, "timestamp": 87970084225, "id": 26, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fmask%2F%5Bid%5D%2Fpage&name=app%2Fmask%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fmask%2F%5Bid%5D%2Fpage.tsx&appDir=E%3A%5C2-AIprogram%5C3-Augment%5C1-test1%5Cbeijing-opera-masks%5Csrc%5Capp&appPaths=%2Fmask%2F%5Bid%5D%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!"}, "startTime": 1753146837237, "traceId": "bb784680c364cc63"}, {"name": "build-module", "duration": 3229, "timestamp": 87987979629, "id": 70, "parentId": 16, "tags": {"name": "E:\\2-AIprogram\\3-Augment\\1-test1\\beijing-opera-masks\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C2-AIprogram%5C%5C3-Augment%5C%5C1-test1%5C%5Cbeijing-opera-masks%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Serif_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-noto-serif-sc%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSerifSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C2-AIprogram%5C%5C3-Augment%5C%5C1-test1%5C%5Cbeijing-opera-masks%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-noto-sans-sc%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C2-AIprogram%5C%5C3-Augment%5C%5C1-test1%5C%5Cbeijing-opera-masks%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Ma_Shan_Zheng%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-ma-shan-zheng%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22maShangZheng%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C2-AIprogram%5C%5C3-Augment%5C%5C1-test1%5C%5Cbeijing-opera-masks%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CPerformanceMonitor.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&server=true!", "layer": "ssr"}, "startTime": 1753146855133, "traceId": "bb784680c364cc63"}, {"name": "build-module", "duration": 507, "timestamp": 87987982888, "id": 71, "parentId": 16, "tags": {"name": "E:\\2-AIprogram\\3-Augment\\1-test1\\beijing-opera-masks\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C2-AIprogram%5C%5C3-Augment%5C%5C1-test1%5C%5Cbeijing-opera-masks%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Serif_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-noto-serif-sc%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSerifSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C2-AIprogram%5C%5C3-Augment%5C%5C1-test1%5C%5Cbeijing-opera-masks%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-noto-sans-sc%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C2-AIprogram%5C%5C3-Augment%5C%5C1-test1%5C%5Cbeijing-opera-masks%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Ma_Shan_Zheng%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-ma-shan-zheng%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22maShangZheng%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C2-AIprogram%5C%5C3-Augment%5C%5C1-test1%5C%5Cbeijing-opera-masks%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CPerformanceMonitor.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&server=true!", "layer": "rsc"}, "startTime": 1753146855136, "traceId": "bb784680c364cc63"}, {"name": "build-module-tsx", "duration": 23100, "timestamp": 87987986849, "id": 72, "parentId": 16, "tags": {"name": "E:\\2-AI<PERSON><PERSON>ram\\3-Augment\\1-test1\\beijing-opera-masks\\src\\app\\page.tsx", "layer": "ssr"}, "startTime": 1753146855140, "traceId": "bb784680c364cc63"}, {"name": "build-module-tsx", "duration": 16517, "timestamp": 87988026020, "id": 73, "parentId": 16, "tags": {"name": "E:\\2-AI<PERSON><PERSON>ram\\3-Augment\\1-test1\\beijing-opera-masks\\src\\app\\mask\\[id]\\page.tsx", "layer": "ssr"}, "startTime": 1753146855179, "traceId": "bb784680c364cc63"}, {"name": "build-module-tsx", "duration": 3642, "timestamp": 87988070922, "id": 74, "parentId": 72, "tags": {"name": "E:\\2-AI<PERSON><PERSON>ram\\3-Augment\\1-test1\\beijing-opera-masks\\src\\components\\ui\\Typography.tsx", "layer": "ssr"}, "startTime": 1753146855224, "traceId": "bb784680c364cc63"}, {"name": "make", "duration": 17995631, "timestamp": 87970083316, "id": 18, "parentId": 16, "tags": {}, "startTime": 1753146837237, "traceId": "bb784680c364cc63"}, {"name": "get-entries", "duration": 1814, "timestamp": 87988080253, "id": 76, "parentId": 75, "tags": {}, "startTime": 1753146855234, "traceId": "bb784680c364cc63"}, {"name": "node-file-trace-plugin", "duration": 151656, "timestamp": 87988085564, "id": 77, "parentId": 75, "tags": {"traceEntryCount": "14"}, "startTime": 1753146855239, "traceId": "bb784680c364cc63"}, {"name": "collect-traced-files", "duration": 707, "timestamp": 87988237235, "id": 78, "parentId": 75, "tags": {}, "startTime": 1753146855390, "traceId": "bb784680c364cc63"}, {"name": "finish-modules", "duration": 157872, "timestamp": 87988080075, "id": 75, "parentId": 17, "tags": {}, "startTime": 1753146855233, "traceId": "bb784680c364cc63"}, {"name": "chunk-graph", "duration": 7466, "timestamp": 87988255633, "id": 80, "parentId": 79, "tags": {}, "startTime": 1753146855409, "traceId": "bb784680c364cc63"}, {"name": "optimize-modules", "duration": 19, "timestamp": 87988263184, "id": 82, "parentId": 79, "tags": {}, "startTime": 1753146855416, "traceId": "bb784680c364cc63"}, {"name": "optimize-chunks", "duration": 7724, "timestamp": 87988263261, "id": 83, "parentId": 79, "tags": {}, "startTime": 1753146855417, "traceId": "bb784680c364cc63"}, {"name": "optimize-tree", "duration": 146, "timestamp": 87988271084, "id": 84, "parentId": 79, "tags": {}, "startTime": 1753146855424, "traceId": "bb784680c364cc63"}, {"name": "optimize-chunk-modules", "duration": 6993, "timestamp": 87988271304, "id": 85, "parentId": 79, "tags": {}, "startTime": 1753146855425, "traceId": "bb784680c364cc63"}, {"name": "optimize", "duration": 15247, "timestamp": 87988263150, "id": 81, "parentId": 79, "tags": {}, "startTime": 1753146855416, "traceId": "bb784680c364cc63"}, {"name": "module-hash", "duration": 9964, "timestamp": 87988293399, "id": 86, "parentId": 79, "tags": {}, "startTime": 1753146855447, "traceId": "bb784680c364cc63"}, {"name": "code-generation", "duration": 38111, "timestamp": 87988303424, "id": 87, "parentId": 79, "tags": {}, "startTime": 1753146855457, "traceId": "bb784680c364cc63"}, {"name": "hash", "duration": 6254, "timestamp": 87988346306, "id": 88, "parentId": 79, "tags": {}, "startTime": 1753146855500, "traceId": "bb784680c364cc63"}, {"name": "code-generation-jobs", "duration": 225, "timestamp": 87988352558, "id": 89, "parentId": 79, "tags": {}, "startTime": 1753146855506, "traceId": "bb784680c364cc63"}, {"name": "module-assets", "duration": 234, "timestamp": 87988352744, "id": 90, "parentId": 79, "tags": {}, "startTime": 1753146855506, "traceId": "bb784680c364cc63"}, {"name": "create-chunk-assets", "duration": 4200, "timestamp": 87988352988, "id": 91, "parentId": 79, "tags": {}, "startTime": 1753146855506, "traceId": "bb784680c364cc63"}, {"name": "minify-js", "duration": 9541, "timestamp": 87988368654, "id": 94, "parentId": 92, "tags": {"name": "../app/favicon.ico/route.js", "cache": "HIT"}, "startTime": 1753146855522, "traceId": "bb784680c364cc63"}, {"name": "minify-js", "duration": 9484, "timestamp": 87988368722, "id": 95, "parentId": 92, "tags": {"name": "../app/robots.txt/route.js", "cache": "HIT"}, "startTime": 1753146855522, "traceId": "bb784680c364cc63"}, {"name": "minify-js", "duration": 9477, "timestamp": 87988368731, "id": 96, "parentId": 92, "tags": {"name": "../app/sitemap.xml/route.js", "cache": "HIT"}, "startTime": 1753146855522, "traceId": "bb784680c364cc63"}, {"name": "minify-js", "duration": 9471, "timestamp": 87988368739, "id": 97, "parentId": 92, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1753146855522, "traceId": "bb784680c364cc63"}, {"name": "minify-js", "duration": 9458, "timestamp": 87988368753, "id": 98, "parentId": 92, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1753146855522, "traceId": "bb784680c364cc63"}, {"name": "minify-js", "duration": 4501, "timestamp": 87988373712, "id": 101, "parentId": 92, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1753146855527, "traceId": "bb784680c364cc63"}, {"name": "minify-js", "duration": 4471, "timestamp": 87988373743, "id": 102, "parentId": 92, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1753146855527, "traceId": "bb784680c364cc63"}, {"name": "minify-js", "duration": 4460, "timestamp": 87988373756, "id": 103, "parentId": 92, "tags": {"name": "985.js", "cache": "HIT"}, "startTime": 1753146855527, "traceId": "bb784680c364cc63"}, {"name": "minify-js", "duration": 711, "timestamp": 87988377506, "id": 105, "parentId": 92, "tags": {"name": "55.js", "cache": "HIT"}, "startTime": 1753146855531, "traceId": "bb784680c364cc63"}, {"name": "minify-js", "duration": 610, "timestamp": 87988377608, "id": 106, "parentId": 92, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1753146855531, "traceId": "bb784680c364cc63"}, {"name": "minify-js", "duration": 23828, "timestamp": 87988365112, "id": 93, "parentId": 92, "tags": {"name": "../app/_not-found/page.js", "cache": "MISS"}, "startTime": 1753146855518, "traceId": "bb784680c364cc63"}, {"name": "minify-js", "duration": 21666, "timestamp": 87988368759, "id": 99, "parentId": 92, "tags": {"name": "../app/page.js", "cache": "MISS"}, "startTime": 1753146855522, "traceId": "bb784680c364cc63"}, {"name": "minify-js", "duration": 19671, "timestamp": 87988372313, "id": 100, "parentId": 92, "tags": {"name": "../app/mask/[id]/page.js", "cache": "MISS"}, "startTime": 1753146855526, "traceId": "bb784680c364cc63"}, {"name": "minify-js", "duration": 14550, "timestamp": 87988377625, "id": 107, "parentId": 92, "tags": {"name": "729.js", "cache": "MISS"}, "startTime": 1753146855531, "traceId": "bb784680c364cc63"}, {"name": "minify-js", "duration": 117473, "timestamp": 87988373762, "id": 104, "parentId": 92, "tags": {"name": "45.js", "cache": "MISS"}, "startTime": 1753146855527, "traceId": "bb784680c364cc63"}, {"name": "minify-webpack-plugin-optimize", "duration": 131318, "timestamp": 87988359938, "id": 92, "parentId": 16, "tags": {"compilationName": "server", "mangle": "[object Object]"}, "startTime": 1753146855513, "traceId": "bb784680c364cc63"}, {"name": "css-minimizer-plugin", "duration": 143, "timestamp": 87988491441, "id": 108, "parentId": 16, "tags": {}, "startTime": 1753146855645, "traceId": "bb784680c364cc63"}, {"name": "create-trace-assets", "duration": 1472, "timestamp": 87988491789, "id": 109, "parentId": 17, "tags": {}, "startTime": 1753146855645, "traceId": "bb784680c364cc63"}, {"name": "seal", "duration": 254246, "timestamp": 87988247438, "id": 79, "parentId": 16, "tags": {}, "startTime": 1753146855401, "traceId": "bb784680c364cc63"}, {"name": "webpack-compilation", "duration": 18432446, "timestamp": 87970074996, "id": 16, "parentId": 13, "tags": {"name": "server"}, "startTime": 1753146837228, "traceId": "bb784680c364cc63"}, {"name": "emit", "duration": 8187, "timestamp": 87988507858, "id": 110, "parentId": 13, "tags": {}, "startTime": 1753146855661, "traceId": "bb784680c364cc63"}, {"name": "webpack-close", "duration": 177732, "timestamp": 87988517808, "id": 111, "parentId": 13, "tags": {"name": "server"}, "startTime": 1753146855671, "traceId": "bb784680c364cc63"}, {"name": "webpack-generate-error-stats", "duration": 2607, "timestamp": 87988695609, "id": 112, "parentId": 111, "tags": {}, "startTime": 1753146855849, "traceId": "bb784680c364cc63"}, {"name": "make", "duration": 446, "timestamp": 87988731524, "id": 114, "parentId": 113, "tags": {}, "startTime": 1753146855885, "traceId": "bb784680c364cc63"}, {"name": "chunk-graph", "duration": 160, "timestamp": 87988733120, "id": 116, "parentId": 115, "tags": {}, "startTime": 1753146855886, "traceId": "bb784680c364cc63"}, {"name": "optimize-modules", "duration": 11, "timestamp": 87988733369, "id": 118, "parentId": 115, "tags": {}, "startTime": 1753146855887, "traceId": "bb784680c364cc63"}, {"name": "optimize-chunks", "duration": 107, "timestamp": 87988733465, "id": 119, "parentId": 115, "tags": {}, "startTime": 1753146855887, "traceId": "bb784680c364cc63"}, {"name": "optimize-tree", "duration": 11, "timestamp": 87988733628, "id": 120, "parentId": 115, "tags": {}, "startTime": 1753146855887, "traceId": "bb784680c364cc63"}, {"name": "optimize-chunk-modules", "duration": 104, "timestamp": 87988733735, "id": 121, "parentId": 115, "tags": {}, "startTime": 1753146855887, "traceId": "bb784680c364cc63"}, {"name": "optimize", "duration": 585, "timestamp": 87988733315, "id": 117, "parentId": 115, "tags": {}, "startTime": 1753146855887, "traceId": "bb784680c364cc63"}, {"name": "module-hash", "duration": 21, "timestamp": 87988734203, "id": 122, "parentId": 115, "tags": {}, "startTime": 1753146855887, "traceId": "bb784680c364cc63"}, {"name": "code-generation", "duration": 14, "timestamp": 87988734239, "id": 123, "parentId": 115, "tags": {}, "startTime": 1753146855887, "traceId": "bb784680c364cc63"}, {"name": "hash", "duration": 113, "timestamp": 87988734309, "id": 124, "parentId": 115, "tags": {}, "startTime": 1753146855888, "traceId": "bb784680c364cc63"}, {"name": "code-generation-jobs", "duration": 77, "timestamp": 87988734421, "id": 125, "parentId": 115, "tags": {}, "startTime": 1753146855888, "traceId": "bb784680c364cc63"}, {"name": "module-assets", "duration": 27, "timestamp": 87988734484, "id": 126, "parentId": 115, "tags": {}, "startTime": 1753146855888, "traceId": "bb784680c364cc63"}, {"name": "create-chunk-assets", "duration": 18, "timestamp": 87988734518, "id": 127, "parentId": 115, "tags": {}, "startTime": 1753146855888, "traceId": "bb784680c364cc63"}, {"name": "minify-js", "duration": 55, "timestamp": 87988741596, "id": 129, "parentId": 128, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1753146855895, "traceId": "bb784680c364cc63"}, {"name": "minify-webpack-plugin-optimize", "duration": 792, "timestamp": 87988740872, "id": 128, "parentId": 113, "tags": {"compilationName": "edge-server", "mangle": "[object Object]"}, "startTime": 1753146855894, "traceId": "bb784680c364cc63"}, {"name": "css-minimizer-plugin", "duration": 8, "timestamp": 87988741725, "id": 130, "parentId": 113, "tags": {}, "startTime": 1753146855895, "traceId": "bb784680c364cc63"}, {"name": "seal", "duration": 10729, "timestamp": 87988732867, "id": 115, "parentId": 113, "tags": {}, "startTime": 1753146855886, "traceId": "bb784680c364cc63"}, {"name": "webpack-compilation", "duration": 19435, "timestamp": 87988724275, "id": 113, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1753146855878, "traceId": "bb784680c364cc63"}]