"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _data_masks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/masks */ \"(app-pages-browser)/./src/data/masks.ts\");\n/* harmony import */ var _services_maskService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/maskService */ \"(app-pages-browser)/./src/services/maskService.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/providers/ThemeProvider */ \"(app-pages-browser)/./src/components/providers/ThemeProvider.tsx\");\n/* harmony import */ var _components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/navigation/SimpleNavbar */ \"(app-pages-browser)/./src/components/navigation/SimpleNavbar.tsx\");\n/* harmony import */ var _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useLocalStorage */ \"(app-pages-browser)/./src/hooks/useLocalStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [masks, setMasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_data_masks__WEBPACK_IMPORTED_MODULE_3__.operaMasks);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedRole, setSelectedRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [showFavoritesOnly, setShowFavoritesOnly] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { colors } = (0,_components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const { favorites, toggleFavorite, isFavorite, isClient: favoritesClient } = (0,_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useFavorites)();\n    const { recentlyViewed, isClient: recentClient } = (0,_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useRecentlyViewed)();\n    // 加载脸谱数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const loadMasks = {\n                \"Home.useEffect.loadMasks\": async ()=>{\n                    if (!(0,_lib_supabase__WEBPACK_IMPORTED_MODULE_5__.isSupabaseConfigured)()) {\n                        console.log('Using static mask data');\n                        return;\n                    }\n                    setLoading(true);\n                    try {\n                        const maskData = await _services_maskService__WEBPACK_IMPORTED_MODULE_4__.MaskService.getAllApprovedMasks();\n                        setMasks(maskData);\n                    } catch (error) {\n                        console.error('Error loading masks:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"Home.useEffect.loadMasks\"];\n            loadMasks();\n        }\n    }[\"Home.useEffect\"], []);\n    // 筛选脸谱\n    const filteredMasks = masks.filter((mask)=>{\n        // 搜索筛选\n        if (searchTerm) {\n            const searchLower = searchTerm.toLowerCase();\n            const matchesName = mask.name.toLowerCase().includes(searchLower);\n            const matchesCharacter = mask.character.toLowerCase().includes(searchLower);\n            if (!matchesName && !matchesCharacter) return false;\n        }\n        // 角色筛选\n        if (selectedRole !== 'all') {\n            if (mask.roleCategory !== selectedRole) return false;\n        }\n        // 收藏筛选\n        if (showFavoritesOnly) {\n            if (!isFavorite(mask.id)) return false;\n        }\n        return true;\n    });\n    const handleMaskClick = (mask)=>{\n        // 导航到详情页面\n        router.push(\"/mask/\".concat(mask.id));\n    };\n    const handleFavoriteClick = (e, maskId)=>{\n        e.stopPropagation(); // 防止触发卡片点击\n        toggleFavorite(maskId);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            backgroundColor: colors.background,\n            color: colors.textPrimary\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_SimpleNavbar__WEBPACK_IMPORTED_MODULE_7__.SimpleNavbar, {}, void 0, false, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '2rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            marginBottom: '3rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontSize: '2.5rem',\n                                    fontWeight: 'bold',\n                                    marginBottom: '1rem',\n                                    background: 'linear-gradient(135deg, #DC2626, #B91C1C)',\n                                    WebkitBackgroundClip: 'text',\n                                    WebkitTextFillColor: 'transparent',\n                                    fontFamily: '\"Ma Shan Zheng\", cursive'\n                                },\n                                children: \"\\uD83C\\uDFAD 京剧脸谱文化展示\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: '1.125rem',\n                                    color: colors.textSecondary,\n                                    maxWidth: '600px',\n                                    margin: '0 auto'\n                                },\n                                children: \"探索中国传统京剧脸谱艺术的魅力，了解每个角色背后的文化内涵\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            maxWidth: '1200px',\n                            margin: '0 auto 3rem auto',\n                            padding: '0 2rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '1rem',\n                                    flexWrap: 'wrap',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    marginBottom: '2rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"搜索脸谱名称或角色...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        style: {\n                                            padding: '0.75rem 1rem',\n                                            borderRadius: '0.5rem',\n                                            border: \"1px solid \".concat(colors.border),\n                                            backgroundColor: colors.backgroundSecondary,\n                                            color: colors.textPrimary,\n                                            fontSize: '1rem',\n                                            minWidth: '250px'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedRole,\n                                        onChange: (e)=>setSelectedRole(e.target.value),\n                                        style: {\n                                            padding: '0.75rem 1rem',\n                                            borderRadius: '0.5rem',\n                                            border: \"1px solid \".concat(colors.border),\n                                            backgroundColor: colors.backgroundSecondary,\n                                            color: colors.textPrimary,\n                                            fontSize: '1rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"所有角色\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"生\",\n                                                children: \"生角\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"旦\",\n                                                children: \"旦角\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"净\",\n                                                children: \"净角\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"丑\",\n                                                children: \"丑角\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.5rem',\n                                            cursor: 'pointer',\n                                            fontSize: '1rem',\n                                            color: colors.textPrimary\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: showFavoritesOnly,\n                                                onChange: (e)=>setShowFavoritesOnly(e.target.checked),\n                                                style: {\n                                                    width: '18px',\n                                                    height: '18px',\n                                                    cursor: 'pointer'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 13\n                                            }, this),\n                                            \"仅显示收藏 (\",\n                                            favoritesClient ? favorites.length : 0,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center',\n                                    color: colors.textSecondary,\n                                    marginBottom: '1rem'\n                                },\n                                children: [\n                                    \"找到 \",\n                                    filteredMasks.length,\n                                    \" 个脸谱\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 9\n                            }, this),\n                            recentClient && recentlyViewed.length > 0 && !showFavoritesOnly && !searchTerm && selectedRole === 'all' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: colors.backgroundSecondary,\n                                    borderRadius: '12px',\n                                    padding: '1.5rem',\n                                    marginBottom: '2rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            fontSize: '1.125rem',\n                                            fontWeight: 'bold',\n                                            color: colors.textPrimary,\n                                            marginBottom: '1rem',\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.5rem'\n                                        },\n                                        children: \"\\uD83D\\uDD52 最近浏览\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '1rem',\n                                            overflowX: 'auto',\n                                            paddingBottom: '0.5rem'\n                                        },\n                                        children: recentlyViewed.slice(0, 5).map((maskId)=>{\n                                            var _recentMask_images, _recentMask_images1;\n                                            const recentMask = masks.find((m)=>m.id === maskId);\n                                            if (!recentMask) return null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                onClick: ()=>handleMaskClick(recentMask),\n                                                style: {\n                                                    minWidth: '120px',\n                                                    cursor: 'pointer',\n                                                    textAlign: 'center',\n                                                    transition: 'transform 0.2s ease'\n                                                },\n                                                onMouseEnter: (e)=>{\n                                                    e.currentTarget.style.transform = 'scale(1.05)';\n                                                },\n                                                onMouseLeave: (e)=>{\n                                                    e.currentTarget.style.transform = 'scale(1)';\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: ((_recentMask_images = recentMask.images) === null || _recentMask_images === void 0 ? void 0 : _recentMask_images.fullSize) || ((_recentMask_images1 = recentMask.images) === null || _recentMask_images1 === void 0 ? void 0 : _recentMask_images1.thumbnail),\n                                                        alt: recentMask.name,\n                                                        style: {\n                                                            width: '80px',\n                                                            height: '80px',\n                                                            borderRadius: '50%',\n                                                            objectFit: 'cover',\n                                                            marginBottom: '0.5rem',\n                                                            border: \"2px solid \".concat(colors.border)\n                                                        },\n                                                        onError: (e)=>{\n                                                            const target = e.target;\n                                                            target.src = \"https://via.placeholder.com/80x80/DC143C/FFFFFF?text=\".concat(encodeURIComponent(recentMask.name.charAt(0)));\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: '0.75rem',\n                                                            color: colors.textSecondary,\n                                                            whiteSpace: 'nowrap',\n                                                            overflow: 'hidden',\n                                                            textOverflow: 'ellipsis'\n                                                        },\n                                                        children: recentMask.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, maskId, true, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 7\n                    }, this),\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            padding: '2rem',\n                            color: colors.textSecondary\n                        },\n                        children: \"正在加载脸谱数据...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'grid',\n                            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                            gap: '2rem',\n                            maxWidth: '1200px',\n                            margin: '0 auto'\n                        },\n                        children: filteredMasks.map((mask)=>{\n                            var _mask_images, _mask_images1;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>handleMaskClick(mask),\n                                style: {\n                                    backgroundColor: colors.backgroundSecondary,\n                                    borderRadius: '12px',\n                                    padding: '1.5rem',\n                                    cursor: 'pointer',\n                                    transition: 'all 0.3s ease',\n                                    border: \"1px solid \".concat(colors.border),\n                                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n                                    position: 'relative'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>handleFavoriteClick(e, mask.id),\n                                        style: {\n                                            position: 'absolute',\n                                            top: '1rem',\n                                            right: '1rem',\n                                            backgroundColor: 'transparent',\n                                            border: 'none',\n                                            fontSize: '1.5rem',\n                                            cursor: 'pointer',\n                                            padding: '0.25rem',\n                                            borderRadius: '50%',\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            justifyContent: 'center',\n                                            transition: 'all 0.2s ease'\n                                        },\n                                        title: isFavorite(mask.id) ? '取消收藏' : '添加收藏',\n                                        children: isFavorite(mask.id) ? '❤️' : '🤍'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: '100%',\n                                            height: '200px',\n                                            borderRadius: '8px',\n                                            overflow: 'hidden',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: ((_mask_images = mask.images) === null || _mask_images === void 0 ? void 0 : _mask_images.fullSize) || mask.imageUrl || ((_mask_images1 = mask.images) === null || _mask_images1 === void 0 ? void 0 : _mask_images1.thumbnail),\n                                            alt: mask.name,\n                                            style: {\n                                                width: '100%',\n                                                height: '100%',\n                                                objectFit: 'cover'\n                                            },\n                                            onError: (e)=>{\n                                                // 图片加载失败时的备用处理\n                                                const target = e.target;\n                                                target.src = \"https://via.placeholder.com/300x300/DC143C/FFFFFF?text=\".concat(encodeURIComponent(mask.name));\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            fontSize: '1.25rem',\n                                            fontWeight: 'bold',\n                                            marginBottom: '0.5rem',\n                                            color: colors.textPrimary\n                                        },\n                                        children: mask.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            fontSize: '0.875rem',\n                                            color: colors.textSecondary,\n                                            marginBottom: '1rem'\n                                        },\n                                        children: [\n                                            \"角色: \",\n                                            mask.character\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, this),\n                                    (mask.colorTheme || mask.mainColors) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '0.5rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: (mask.colorTheme || mask.mainColors || []).slice(0, 3).map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: '20px',\n                                                    height: '20px',\n                                                    borderRadius: '50%',\n                                                    backgroundColor: color,\n                                                    border: '1px solid rgba(0,0,0,0.1)'\n                                                },\n                                                title: color\n                                            }, index, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, this),\n                                    (mask.personalityTraits || mask.tags) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            flexWrap: 'wrap',\n                                            gap: '0.5rem'\n                                        },\n                                        children: (mask.personalityTraits || mask.tags || []).slice(0, 3).map((trait, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.75rem',\n                                                    padding: '0.25rem 0.5rem',\n                                                    backgroundColor: colors.primary + '20',\n                                                    color: colors.primary,\n                                                    borderRadius: '12px'\n                                                },\n                                                children: trait\n                                            }, index, false, {\n                                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, mask.id, true, {\n                                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2-AIprogram\\\\3-Augment\\\\1-test1\\\\beijing-opera-masks\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"Rh5M2v2fUtdFaR9gDEv9eH0ssCg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_6__.useTheme,\n        _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useFavorites,\n        _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_8__.useRecentlyViewed\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});