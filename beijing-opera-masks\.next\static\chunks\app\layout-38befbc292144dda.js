(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{3520:(e,t,r)=>{"use strict";r.d(t,{PerformanceMonitor:()=>o});var n=r(2115);function o(){return(0,n.useEffect)(()=>{let e={};if("PerformanceObserver"in window){let t;new PerformanceObserver(t=>{t.getEntries().forEach(t=>{"first-contentful-paint"===t.name&&(e.fcp=t.startTime,console.log("FCP:",t.startTime))})}).observe({entryTypes:["paint"]}),new PerformanceObserver(t=>{let r=t.getEntries(),n=r[r.length-1];e.lcp=n.startTime,console.log("LCP:",n.startTime)}).observe({entryTypes:["largest-contentful-paint"]}),new PerformanceObserver(t=>{t.getEntries().forEach(t=>{e.fid=t.processingStart-t.startTime,console.log("FID:",e.fid)})}).observe({entryTypes:["first-input"]}),t=0,new PerformanceObserver(r=>{r.getEntries().forEach(e=>{e.hadRecentInput||(t+=e.value)}),e.cls=t,console.log("CLS:",t)}).observe({entryTypes:["layout-shift"]});let r=performance.getEntriesByType("navigation")[0];r&&(e.ttfb=r.responseStart-r.requestStart,console.log("TTFB:",e.ttfb))}let t=()=>{console.log("Performance Metrics:",e)};return window.addEventListener("beforeunload",t),document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&t()}),()=>{window.removeEventListener("beforeunload",t),document.removeEventListener("visibilitychange",t)}},[]),null}},8858:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,1071,23)),Promise.resolve().then(r.t.bind(r,8074,23)),Promise.resolve().then(r.t.bind(r,6676,23)),Promise.resolve().then(r.bind(r,3520))}},e=>{e.O(0,[96,76,358],()=>e(e.s=8858)),_N_E=e.O()}]);